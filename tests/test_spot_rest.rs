use bybit::spot::rest::BybitSpot;
use quant_common::base::{
    ContractType, Order, OrderParams, PosSide, Quote<PERSON>cy, Rest, Symbol, TimeInForce,
};
use quant_common::test_config;
use reqwest::Method;
use std::collections::HashMap;
use tracing::{debug, info};

fn log() {
    #[cfg(debug_assertions)]
    {
        tracing_subscriber::fmt()
            .pretty()
            .with_max_level(tracing::Level::DEBUG)
            .init();
    }
    #[cfg(not(debug_assertions))]
    {
        tracing_subscriber::fmt().pretty().init();
    }
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_send_request() {
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let mut map: HashMap<&str, &str> = HashMap::new();
    map.insert("accountType", "SPOT");
    let result: quant_common::Result<String> = rest
        .req(Method::GET, "/v5/account/wallet-balance", &map, true)
        .await;
    debug!("{:?}", result.expect("err"));
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_ticker() {
    tracing_subscriber::fmt().pretty().init();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest
        .get_ticker(Symbol {
            base: "AI16Z".to_string(),
            quote: QuoteCcy::USDT,
            contract_type: ContractType::Normal,
        })
        .await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_tickers() {
    tracing_subscriber::fmt().pretty().init();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest.get_tickers().await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_bbo_ticker() {
    tracing_subscriber::fmt().pretty().init();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest.get_bbo_ticker(Symbol::new("BTC")).await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_bbo_tickers() {
    tracing_subscriber::fmt().pretty().init();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest.get_bbo_tickers().await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_depth() {
    tracing_subscriber::fmt().pretty().init();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    info!("{:?}", rest.symbol_map);
    let x = rest.get_depth(Symbol::new("BTC"), Some(3)).await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_instrument() {
    tracing_subscriber::fmt().pretty().init();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest.get_instrument(Symbol::new("BTC")).await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_instruments() {
    tracing_subscriber::fmt().pretty().init();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest.get_instruments().await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_fee_rate() {
    tracing_subscriber::fmt().pretty().init();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest.get_fee_rate(Symbol::new("BTC")).await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_balance() {
    log();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest.get_balance("USDC").await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_get_balances() {
    log();
    let config = test_config();

    info!("{:#?}", config);
    let _exchange = config.exchange;

    let rest = BybitSpot::new(config).await;
    let x = rest.get_balances().await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_post_order() {
    log();
    let config = test_config();

    info!("{:#?}", config);
    let exchange = config.exchange;
    let rest = BybitSpot::new(config).await;

    let order = Order::limit_open(
        Symbol::new("BTC"),
        92800.0,
        0.0001,
        PosSide::Long,
        TimeInForce::GTC,
        exchange,
    );
    let params = OrderParams::new(false, 1);

    let x = rest.post_order(order, params).await.unwrap();
    info!("{:?}", x);

    // cancel order
    rest.cancel_order(Symbol::new("BTC"), quant_common::base::OrderId::Id(x))
        .await
        .unwrap();
    info!("cancel order success");
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_post_batch_order() {
    log();
    let config = test_config();

    info!("{:#?}", config);
    let exchange = config.exchange;
    let rest = BybitSpot::new(config).await;

    let order = Order::limit_open(
        Symbol::new("BTC"),
        92700.0,
        0.005,
        PosSide::Long,
        TimeInForce::FOK,
        exchange,
    );
    let order1 = Order::limit_open(
        Symbol::new("BTC"),
        92750.0,
        0.05,
        PosSide::Long,
        TimeInForce::GTC,
        exchange,
    );
    let is_dual_side = rest.is_dual_side().await.unwrap();
    let params = OrderParams::new(is_dual_side, 1);
    let vec = vec![order, order1];
    let x = rest.post_batch_order(vec, params).await;
    info!("{:?}", x);
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_amend_order() {
    log();
    let config = test_config();

    info!("{:#?}", config);
    let exchange = config.exchange;
    let rest = BybitSpot::new(config).await;

    let order = Order::limit_open(
        Symbol::new("BTC"),
        92777.0,
        0.05,
        PosSide::Long,
        TimeInForce::GTC,
        exchange,
    );
    let mut order_modify = order.clone();
    let is_dual_side = rest.is_dual_side().await.unwrap();
    let params = OrderParams::new(is_dual_side, 1);
    let vec = vec![order];
    let x = rest.post_batch_order(vec, params).await;
    debug!("{:?}", x);
    let a = x.unwrap().success_list.pop().unwrap().cid;
    order_modify.cid = a;
    order_modify.price = Some(68888.0);
    debug!("{:#?}", order_modify);
    let result = rest.amend_order(order_modify).await;
    debug!("{:?}", result);
}

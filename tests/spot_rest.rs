#[cfg(test)]
mod tests {
    use binance::util::{PrefixFormat, ceil};
    use log::info;
    use quant_common::base::traits::GetKlineParams;
    use std::collections::HashSet;
    use std::{fs::read_to_string, time::Duration};
    use tokio::time::sleep;
    use tracing_subscriber::fmt;

    use quant_common::Result;
    use quant_common::base::model::order::*;
    use quant_common::base::model::*;
    use quant_common::base::*;
    use quant_common::time_ms;

    use binance::spot::model::{BnRestDepth, BnSymbol};
    use binance::spot::rest::BinanceSpot;

    fn test_config() -> ExConfig {
        toml::from_str(&read_to_string("spot.toml").unwrap()).unwrap()
    }

    #[test]
    fn test_bn_order_book_deserialize() {
        let _ = fmt().pretty().try_init();

        let json = r#"{
            "lastUpdateId": 1027024,
            "bids": [
                ["4.00000000","431.00000000",[]]
            ],
            "asks": [
                ["4.00000200", "12.00000000", []]
            ]
        }"#;
        let book: BnRestDepth = sonic_rs::from_str(json).unwrap();
        info!("{book:?}");
    }

    #[test]
    fn test_get_prefix_multiplier() {
        let _ = fmt().try_init();

        for (str, prefix_mul, base) in [
            ("1000000MOGUSDT", PrefixFormat::Numeric(1_000_000), "MOG"),
            ("1000BONKUSDT", PrefixFormat::Numeric(1000), "BONK"),
            ("1000LUNCUSDT", PrefixFormat::Numeric(1000), "LUNC"),
            ("1000SHIBUSDT", PrefixFormat::Numeric(1000), "SHIB"),
            ("10SETUSDT", PrefixFormat::Numeric(10), "SET"),
            ("1INCHUSDT", PrefixFormat::Numeric(1), "1INCH"),
            ("1MBADYDOGEUSDT", PrefixFormat::MillionPrefix, "BADYDOGE"),
            ("1MDOGEBABYUSDT", PrefixFormat::MillionPrefix, "DOGEBABY"),
            ("BTCUSDT", PrefixFormat::Numeric(1), "BTC"),
        ] {
            let symbol: BnSymbol = str.parse().unwrap();
            info!("str: {str}");
            assert_eq!(symbol.prefix_mul, prefix_mul);
            assert_eq!(symbol.inner.base, base);
            assert_eq!(symbol.to_string(), str);
        }
    }

    #[tokio::test]
    async fn test_get_ticker() {
        let _ = fmt().try_init();

        let config = ExConfig::default();
        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("CAT");
        let ticker = rest.get_ticker(symbol).await.unwrap();
        info!("{:#?}", ticker);
    }

    fn to_wide_string(s: &str, len: usize) -> String {
        let mut r = String::with_capacity(len * s.len());
        r.push_str(&" ".to_string().repeat(len));
        r.push_str(s);
        r[(r.len() - len).max(0)..].to_string()
    }

    #[tokio::test]
    async fn test_get_tickers() {
        let _ = fmt().try_init();

        let config = ExConfig {
            is_testnet: false,
            exchange: Exchange::BinanceSpot,
            ..ExConfig::default()
        };
        let rest = BinanceSpot::new(config).await;
        let tickers = rest.get_tickers().await.unwrap();
        let mut tickers = tickers
            .into_iter()
            .filter(|t| t.quote_volume > 10_000_000.0 && matches!(t.symbol.quote, QuoteCcy::USDT))
            .collect::<Vec<_>>();
        tickers.sort_by(|a, b| {
            b.change_percent
                .partial_cmp(&a.change_percent)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        let (inc, dec) = tickers
            .iter()
            .fold((0., 0.), |(inc, dec), t| match t.change > 0.0 {
                true => (inc + 1., dec),
                false => (inc, dec + 1.),
            });
        let sum = inc + dec;
        let inc = inc / sum * 100.;
        let dec = dec / sum * 100.;
        for t in tickers.iter() {
            let symbol = to_wide_string(&t.symbol.to_string(), 10);
            let close = to_wide_string(&format!("{:.2e}", t.close), 10);
            let volume = to_wide_string(&(format!("{:.2e}", t.volume)), 10);
            let quote_volume = to_wide_string(&(format!("{:.2e}", t.quote_volume)), 10);
            let change = to_wide_string(&format!("{:.2}%", t.change_percent), 10);
            info!(
                "symbol: {symbol} \tchange: {change} \tclose: {close} \tvolume: {volume} \tquote_volume: {quote_volume}"
            );
        }
        info!("上涨: {inc:2.2}%, 下跌: {dec:02.2}%, 总数: {sum}");
    }

    #[tokio::test]
    async fn test_get_bbo() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("BTC");
        let ticker = rest.get_bbo_ticker(symbol).await.unwrap();
        info!("{:#?}", ticker);
    }

    #[tokio::test]
    async fn test_get_bbos() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceSpot::new(config).await;
        let bbos = rest.get_bbo_tickers().await.unwrap();
        for bbo in bbos {
            info!("{:?}", bbo);
        }
    }

    #[tokio::test]
    async fn test_get_depth() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("BTC");
        let depth = rest.get_depth(symbol, Some(5)).await.unwrap();
        info!("{:#?}", depth);
    }

    #[tokio::test]
    async fn test_get_instrument() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceSpot::new(config).await;
        for symbol in ["BNB", "BABYDOGE"] {
            let instrument = rest.get_instrument(Symbol::new(symbol)).await.unwrap();
            info!("{:#?}", instrument);
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
    }

    #[tokio::test]
    async fn test_get_klines_ext() {
        let _ = fmt().try_init();

        let config = ExConfig::default();
        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("SATS");
        let params = GetKlineParams::new(symbol, KlineInterval::Min15).limit(5);
        let klines = rest.get_klines_ext(params).await.unwrap();
        info!("{} {:?}", klines.symbol, klines.interval);
        for kline in klines.candles {
            info!("{:?}", kline);
        }
    }

    #[tokio::test]
    async fn test_get_instruments() {
        let _ = fmt().try_init();

        let config = ExConfig::default();
        let rest = BinanceSpot::new(config).await;
        let instruments = rest.get_instruments().await.unwrap();
        info!("数量: {}", instruments.len());
        info!("{:?}", instruments.first());
    }

    #[tokio::test]
    async fn test_get_symbol_infos() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceSpot::new(config).await;
        let symbol_infos = rest.symbol_infos().await?;
        for symbol_info in symbol_infos.into_iter() {
            let symbol = &symbol_info.symbol;
            let bn_base = &symbol_info.base_asset;
            let my_base = symbol.prefix_mul.to_string(&symbol.inner.base);
            assert_eq!(
                bn_base, &my_base,
                "{bn_base} => {my_base}, {symbol_info:#?}"
            );
        }
        Ok(())
    }

    #[tokio::test]
    async fn test_get_balance() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;

        // 1000CHEEMS 1MBABYDOGE
        for asset in ["CHEEMS", "BABYDOGE", "USDT"] {
            let balance = rest.get_balance(asset).await.unwrap();
            info!("{asset} => {:?}", balance);
        }
    }

    #[tokio::test]
    async fn test_get_balances() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        let balances = rest.get_balances().await.unwrap();
        for balance in balances.iter().filter(|b| b.balance > 0.) {
            info!("{:?}", balance);
        }
        let usdt = get_usdt_balance(balances);
        info!("usdt: {:?}", usdt);
    }

    #[tokio::test]
    async fn test_post_order() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config();
        let e = config.exchange;

        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("ARKM");
        let x = rest.get_instrument(symbol.clone()).await?;
        info!("symbol: {symbol}, min_notional: {}", x.min_notional);
        let bbo = rest.get_bbo_ticker(symbol.clone()).await?;
        let price = ceil(x.price_tick, bbo.ask_price + x.price_tick);
        let amount = ceil(x.amount_tick, (x.min_notional / price) * 1.1);
        let ps = PosSide::Long;

        // Market order
        let order = Order::market_open(symbol.clone(), amount, ps, e);
        let order_id = rest.post_order(order.clone(), Default::default()).await?;
        info!("Market {order_id}");

        // Limit order
        let order = Order::limit_open(symbol.clone(), price, amount, ps, TimeInForce::GTC, e);
        let order_id = rest.post_order(order.clone(), Default::default()).await?;
        info!("GTC {order_id}");

        // Limit IOC
        let order = Order::limit_open(symbol.clone(), price, amount, ps, TimeInForce::GTC, e);
        let order_id = rest.post_order(order.clone(), Default::default()).await?;
        info!("IOC {order_id}");

        // Limit FOK
        let order = Order::limit_open(symbol.clone(), price, amount, ps, TimeInForce::FOK, e);
        let order_id = rest.post_order(order.clone(), Default::default()).await?;
        info!("FOK {order_id}");

        // Limit Post Only
        let order = Order::limit_open(symbol.clone(), price, amount, ps, TimeInForce::PostOnly, e);
        let order_id = rest.post_order(order.clone(), Default::default()).await;
        assert!(order_id.is_err(), "POST_ONLY应该失败");

        // Market Short
        let order = Order::market_close(symbol.clone(), amount, ps, e);
        let order_id = rest.post_order(order.clone(), Default::default()).await?;
        info!("GTC Market Short {order_id}");

        Ok(())
    }

    #[tokio::test]
    async fn test_post_maker_order() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceSpot::new(config).await;

        let symbol = Symbol::new("CAT");
        let order = Order::market_open(symbol.clone(), 8500., PosSide::Long, exchange);
        let order_id = rest.post_order(order, Default::default()).await?;
        info!("下单成功: {order_id}");
        Ok(())
    }

    #[tokio::test]
    async fn test_market_with_price_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceSpot::new(config).await;

        let symbol = Symbol::new("BABYDOGE");

        let instrument = rest.get_instrument(symbol.clone()).await?;
        let bbo = rest.get_bbo_ticker(symbol.clone()).await?;
        info!("instrument: {instrument:#?}, bbo: {bbo:#?}");

        let order = Order {
            price: Some(1e-9),
            ..Order::market_open(symbol, 1e3, PosSide::Long, exchange)
        };
        let order_id = rest.post_order(order, Default::default()).await?;
        info!("{:#?}", order_id);
        Ok(())
    }

    #[tokio::test]
    #[ignore = "debug工具"]
    async fn test_sell() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let exchange = config.exchange;

        let rest = BinanceSpot::new(config).await;
        // Market order
        let order = Order::market_open(
            Symbol::new_with_quote("BNB", QuoteCcy::USDT),
            0.01,
            PosSide::Short,
            exchange,
        );
        let params = OrderParams::new(false, 1);
        let oid = rest
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("{oid}");
    }

    #[tokio::test]
    async fn test_buy() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let exchange = config.exchange;

        let rest = BinanceSpot::new(config).await;
        // Limit order
        let symbol = Symbol::new_with_quote("BNB", QuoteCcy::USDT);
        let order = Order::limit_open(
            symbol.clone(),
            251.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let params = OrderParams::new(false, 1);
        let oid = rest.post_order(order.clone(), params.clone()).await?;
        info!("{oid}");

        tokio::time::sleep(Duration::from_secs(1)).await;

        let order_id = OrderId::ClientOrderId(order.cid.unwrap());
        let order = rest.get_order_by_id(symbol.clone(), order_id).await?;
        info!("by cid: {:?}", order);

        // cancel order
        rest.cancel_order(symbol, OrderId::Id(oid)).await
    }

    #[tokio::test]
    #[ignore = "need proxy, 只能在正式环境中运行"]
    async fn test_capitals() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        let rsp = rest.capitals().await.unwrap();
        info!("{rsp:#?}");
    }

    #[tokio::test]
    async fn test_get_orders() -> quant_common::Result<()> {
        let _ = fmt().try_init();
        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        for i in 0..1 {
            let end = time_ms() - 1000 * 3600 * 24 * i;
            let start = end - 1000 * 3600 * 24;
            let orders = rest.get_orders(Symbol::new("ARKM"), start, end).await?;
            info!("订单数量: {}", orders.len());
            for order in orders {
                info!("{:?}", order);
            }
        }
        Ok(())
    }

    #[tokio::test]
    #[ignore = "需要代理, 只能在正式环境中运行"]
    async fn test_get_order_by_id() -> quant_common::Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config.clone()).await;
        let symbol = Symbol::new("CAT");
        let order = Order::market_open(symbol.clone(), 8500., PosSide::Long, config.exchange);
        let params = OrderParams::new(false, 1);
        let order_id = rest.post_order(order, params).await?;

        tokio::time::sleep(Duration::from_secs(1)).await;

        let order_id = OrderId::Id(order_id);
        let order = rest.get_order_by_id(symbol.clone(), order_id).await?;
        info!("by id: {:?}", order);

        let order_id = OrderId::ClientOrderId(order.cid.unwrap());
        let order = rest.get_order_by_id(symbol, order_id).await?;
        info!("by cid: {:?}", order);
        Ok(())
    }

    #[tokio::test]
    async fn test_get_open_orders() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let symbol = Symbol::new("BNB");
        let rest = BinanceSpot::new(config).await;
        let order = Order::limit_open(
            symbol.clone(),
            251.,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let params = OrderParams::new(false, 1);
        let order_id = rest
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("{:#?}", order_id);

        let orders = rest.get_open_orders(symbol.clone()).await.unwrap();
        info!("{:#?}", orders);

        // cancel order
        rest.cancel_order(symbol, OrderId::Id(order_id))
            .await
            .unwrap();
    }

    #[tokio::test]
    async fn test_get_all_open_orders() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        let orders = rest.get_all_open_orders().await.unwrap();
        for order in orders {
            info!("{:?}", order);
        }
    }

    #[tokio::test]
    #[ignore = "影响其他测试结果"]
    async fn test_close_all_open_orders() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        let orders = rest.get_all_open_orders().await.unwrap();
        let symbols: HashSet<Symbol> = orders.into_iter().map(|o| o.symbol).collect();
        for symbol in symbols {
            let _ = rest.batch_cancel_order(symbol).await;
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
    }

    #[tokio::test]
    async fn test_amend_order() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;

        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("BNB");
        let mut order = Order::limit_open(
            symbol.clone(),
            251.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let is_dual_side = rest.is_dual_side().await?;
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order.clone(), params).await?;
        info!("{:#?}", order_id);
        order.id = order_id;
        order.price = Some(252.0);
        order.amount = Some(0.02);
        tokio::time::sleep(Duration::from_secs(1)).await;
        let order_id = rest.amend_order(order).await?;
        info!("{:#?}", order_id);

        rest.cancel_order(symbol, OrderId::Id(order_id)).await
    }

    #[tokio::test]
    async fn test_cancel_order() -> Result<()> {
        let _ = fmt().with_max_level(tracing::Level::DEBUG).try_init();

        let config = test_config();
        let exchange = config.exchange;

        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("BNB");

        let order = Order::limit_open(
            symbol.clone(),
            500.0,
            0.01,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let is_dual_side = rest.is_dual_side().await?;
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order, params).await?;
        rest.cancel_order(symbol, OrderId::Id(order_id)).await
    }

    #[tokio::test]
    async fn test_cancel_order_by_cid() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;

        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("BNB");

        let order = Order::limit_open(
            symbol.clone(),
            500.0,
            0.01,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let cid = order.cid.clone().unwrap();
        let is_dual_side = rest.is_dual_side().await?;
        let params = OrderParams::new(is_dual_side, 1);
        let _order_id = rest.post_order(order, params).await?;
        rest.cancel_order(symbol, OrderId::ClientOrderId(cid)).await
    }

    #[tokio::test]
    async fn test_get_fee_rate() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        let symbol = Symbol::new("TRUMP");
        let fee_rate = rest.get_fee_rate(symbol).await.unwrap();
        info!("{:?}", fee_rate);
    }

    #[tokio::test]
    async fn test_is_dual_side() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        let is_dual_side = rest.is_dual_side().await.unwrap();
        assert!(!is_dual_side);
    }

    #[tokio::test]
    async fn test_create_listen_key() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        let listen_key = rest.create_listen_key().await.unwrap();
        info!("{}", listen_key);
    }

    #[tokio::test]
    async fn test_reflash_listen_key() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let rest = BinanceSpot::new(config).await;
        let listen_key = rest.create_listen_key().await.unwrap();
        info!("{}", listen_key);
        sleep(Duration::from_secs(5)).await;
        rest.refresh_listen_key(&listen_key).await.unwrap();
        info!("刷新成功");
    }

    #[tokio::test]
    async fn test_batch_cancel_order() {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let symbol: Symbol = Symbol::new("BNB");
        let order = Order::limit_open(
            symbol.clone(),
            300.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            config.exchange,
        );

        let rest = BinanceSpot::new(config).await;
        let order_id = rest.post_order(order, Default::default()).await.unwrap();
        info!("{:#?}", order_id);

        let order_ids = rest.batch_cancel_order(symbol).await.unwrap();
        info!("{order_ids:#?}");
    }
}

#[cfg(test)]
mod account_test {
    use std::{fs::read_to_string, time::Duration};

    use binance::spot::rest::BinanceSpot;
    use quant_common::{
        Result,
        base::{
            Chain, ExConfig, OnChainParams, SubTransfer, Transfer, TransferDirection, WalletType,
            WithDrawlParams, WithdrawalAddr,
            traits::{
                GetUserIdParams, SubTransferParams, TransferParams, WithdrawalParams, rest::Rest,
            },
        },
    };
    use tracing::info;
    use tracing_subscriber::fmt;

    fn account_config() -> ExConfig {
        toml::from_str(&read_to_string("account.toml").unwrap()).unwrap()
    }

    #[tokio::test]
    #[ignore = "需要在正式环境中运行"]
    async fn test_get_balances() {
        let _ = fmt().with_target(false).try_init();

        let config = account_config();
        let rest = BinanceSpot::new(config).await;
        let balances = rest.get_balances().await.unwrap();
        for balance in balances.iter().filter(|b| b.balance != 0.0) {
            info!("{balance:?}");
        }
    }

    #[tokio::test]
    #[ignore = "需要在正式环境中运行"]
    async fn test_get_user_id_ext() {
        let _ = fmt().with_target(false).try_init();

        let config = account_config();
        let rest = BinanceSpot::new(config).await;
        let params = GetUserIdParams::new();
        let user_id = rest.get_user_id_ext(params).await.unwrap();
        info!("{user_id}");
    }

    #[tokio::test]
    #[ignore = "只能在正式环境中运行"]
    async fn test_transfer_to_spot() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = account_config();
        let rest = BinanceSpot::new(config).await;
        let asset = "BNB";

        let balance = rest.get_balance(asset).await?;
        info!("转账前: {balance:#?}");

        let transfer = Transfer {
            asset: asset.to_string(),
            amount: 1e-9,
            from: WalletType::UsdtFuture,
            to: WalletType::Spot,
        };
        let params = TransferParams::new(transfer);
        rest.transfer_ext(params).await?;

        let balance = rest.get_balance(asset).await?;
        info!("转账后: {balance:#?}");
        Ok(())
    }

    #[tokio::test]
    #[ignore = "只能在正式环境中运行"]
    async fn test_transfer_to_margin() {
        let _ = fmt().pretty().try_init();

        let config = account_config();
        let rest = BinanceSpot::new(config).await;
        let transfer = Transfer {
            asset: "CHEEMS".to_string(),
            amount: 2.,
            from: WalletType::Spot,
            to: WalletType::Margin,
        };
        rest.transfer(transfer).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "只能在正式环境中运行"]
    async fn test_sub_transfer() {
        let _ = fmt().with_target(false).try_init();

        let config = account_config();
        let rest = BinanceSpot::new(config).await;
        let sub1 = Some("<EMAIL>".to_string());
        let sub2 = Some("<EMAIL>".to_string());
        let amount = 0.01;
        let asset = "USDT";
        let transfer = SubTransfer {
            cid: None,
            asset: asset.to_string(),
            amount,
            from: WalletType::Spot,
            to: WalletType::Spot,
            from_account: None,
            to_account: sub2.clone(),
            direction: TransferDirection::MasterToSub,
        };
        let params = SubTransferParams::new(transfer);
        rest.sub_transfer_ext(params).await.unwrap();
        info!("母转子成功");

        tokio::time::sleep(Duration::from_secs(10)).await;

        let transfer = SubTransfer {
            cid: None,
            asset: asset.to_string(),
            amount,
            from: WalletType::Spot,
            to: WalletType::Spot,
            from_account: sub2.clone(),
            to_account: None,
            direction: TransferDirection::MasterToSub,
        };
        let params = SubTransferParams::new(transfer);
        rest.sub_transfer_ext(params).await.unwrap();
        info!("子转母成功");

        let amount = 0.01;
        let asset = "USDT";
        let transfer = SubTransfer {
            cid: None,
            asset: asset.to_string(),
            amount,
            from: WalletType::Spot,
            to: WalletType::Spot,
            from_account: sub2.clone(),
            to_account: sub1.clone(),
            direction: TransferDirection::SubToSub,
        };
        let params = SubTransferParams::new(transfer);
        rest.sub_transfer_ext(params).await.unwrap();
        info!("子转子 转账成功");

        tokio::time::sleep(Duration::from_secs(10)).await;

        let transfer = SubTransfer {
            cid: None,
            asset: asset.to_string(),
            amount,
            from: WalletType::Spot,
            to: WalletType::Spot,
            from_account: sub1,
            to_account: sub2,
            direction: TransferDirection::SubToSub,
        };
        let params = SubTransferParams::new(transfer);
        rest.sub_transfer_ext(params).await.unwrap();
        info!("子转子 回转成功");
    }

    #[tokio::test]
    #[ignore = "只能在正式环境中运行"]
    async fn test_get_deposit_address() {
        let _ = fmt().with_target(false).try_init();

        let config = account_config();
        let rest = BinanceSpot::new(config).await;
        let asset = "USDT".to_string();
        let chain = Some(Chain::Bep20);
        let rsp = rest.get_deposit_address(asset, chain, Some(0.01)).await;
        let addrs = rsp.unwrap();
        for addr in addrs {
            info!("{addr:?}");
        }
    }

    #[tokio::test]
    #[ignore = "只能在正式环境中运行"]
    async fn test_withdrawal() {
        let _ = fmt().with_target(false).try_init();

        let config = account_config();
        let rest = BinanceSpot::new(config).await;
        // let uid = InternalTransferParams::Uid("**********".to_string()); // 不支持
        // let addr = WithdrawalAddr::InternalTransfer(addr);
        let on_chain = OnChainParams {
            chain: Chain::Bep20,
            address: "0xfbeacc6c2b5587950fe91a92e4fcae17e29cef13".to_string(), // 子账号
            tag: None,
        };
        let addr = WithdrawalAddr::OnChain(on_chain);
        let params = WithDrawlParams {
            cid: None,
            asset: "USDT".to_string(),
            amt: 0.01,
            addr,
        };
        let params = WithdrawalParams::new(params);
        rest.withdrawal_ext(params).await.unwrap();
    }
}

#[cfg(test)]
mod tests {
    use std::fs::read_to_string;
    use std::time::Duration;

    use log::info;
    use tracing_subscriber::fmt;

    use quant_common::base::*;
    use quant_common::*;

    use gate::spot::model::*;
    use gate::spot::rest::GateSpot;

    fn test_config() -> ExConfig {
        toml::from_str(&read_to_string("test.toml").unwrap()).unwrap()
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_request() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateSpot::new(config).await;
        let ser = CurrencyPairReq::new(Symbol::new("APE"));
        let params = sonic_rs::to_value(&ser).unwrap_or_default();

        let user_request = UserRequest {
            method: "GET".to_string(),
            path: PATH_TICKER.to_string(),
            auth: false,
            query: Some(params),
            body: None,
            url: None,
            headers: None,
        };

        let ticker = rest.request(user_request).await.unwrap();
        let rsp = sonic_rs::from_value::<Vec<GateCandlestick>>(&ticker).unwrap();
        info!("{rsp:#?}");
    }

    // 读接口
    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_fee_rate() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSpot::new(config).await;
        let fee = rest.get_fee_rate(Symbol::new("BTC")).await.unwrap();
        info!("{fee:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instrument() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new_with_quote("PENDLE", base::QuoteCcy::OTHER("ETH".to_string()));
        let instrument = rest.get_instrument(symbol).await.unwrap();
        info!("{instrument:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instruments() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateSpot::new(config).await;
        let instruments = rest.get_instruments().await.unwrap();
        info!("{:#?}", instruments.first());
    }

    #[tokio::test]
    async fn test_get_ticker() {
        let _ = fmt().pretty().try_init();
        let config = ExConfig::default();
        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new("BTC");
        let ticker = rest.get_ticker(symbol.clone()).await.unwrap();
        info!("{ticker:#?}");
    }

    #[tokio::test]
    async fn test_get_tickers() {
        let _ = fmt().pretty().try_init();
        let config = ExConfig::default();
        let rest = GateSpot::new(config).await;
        let tickers = rest.get_tickers().await.unwrap();
        info!("{tickers:#?}");
    }

    #[tokio::test]
    async fn test_get_depth() {
        let _ = fmt().pretty().try_init();
        let config = ExConfig::default();
        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new("BTC");
        let depth = rest.get_depth(symbol.clone(), Some(5)).await.unwrap();
        info!("{depth:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_bbo() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new("BTC");
        let ticker = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        info!("{ticker:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_all_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSpot::new(config).await;
        let order = rest.get_all_open_orders().await.unwrap();
        info!("{order:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let end = time_ms();
        let start = end - 24 * 3600 * 1_000;
        let order = rest.get_orders(symbol.clone(), start, end).await.unwrap();
        info!("{order:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let order = rest.get_open_orders(symbol.clone()).await.unwrap();
        info!("{order:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_balances() {
        fmt().pretty().init();
        let config = test_config();
        info!("{config:#?}");
        let rest = GateSpot::new(config).await;
        let balances = rest.get_balances().await.unwrap();
        for balance in balances {
            info!("{balance:?}");
        }
    }

    // 写接口
    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_buy_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateSpot::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");
        let order = Order::market_open(symbol.clone(), 0.1, PosSide::Long, exchange);
        let params = OrderParams::new(false, 1);
        let order = Order {
            price: None,
            order_type: OrderType::Market,
            time_in_force: TimeInForce::PostOnly,
            ..order
        };

        let cid = order.cid.clone().unwrap();
        println!("{order:?}");

        let order_id = rest
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("{order_id:#?}");

        // Cancel
        rest.cancel_order(symbol.clone(), OrderId::ClientOrderId(cid.clone()))
            .await
            .unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_price_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateSpot::new(config).await;
        let symbol: Symbol = Symbol::new("TRUMP");

        let order = Order::limit_open(symbol, 10.0, 1.0, PosSide::Long, TimeInForce::GTC, exchange);

        let mut params = OrderParams::new(false, 1);
        params.take_profit = Some(CloseTrigger {
            trigger_price: TriggerPrice::ContractPrice(12.0),
            trigger_action: TriggerAction::Limit(12.0),
        });
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("{order_id:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateSpot::new(config).await;
        let symbol: Symbol = Symbol::new("TON");

        let mut order =
            Order::limit_open(symbol, 3.0, 3.0, PosSide::Short, TimeInForce::GTC, exchange);
        order.order_type = OrderType::Limit;
        order.side = OrderSide::Sell;

        let params = OrderParams::new(false, 1);
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("{order_id:#?}");
        // let mut order = Order::market_open(Symbol::new("APE"), 3.0, PosSide::Long, exchange);

        // let params = OrderParams::new(false, 1);
        // order.price = Some(1.0);
        // order.amount = 3.09;
        // for _i in 1..=10 {
        //     // Market long open
        //     let order_id = rest.post_order(order.clone(), params.clone()).await;
        //     if let Ok(order_id) = order_id {
        //         info!("{:#?}", order_id);
        //         info!("market买入成功了");
        //         break;
        //     } else {
        //         qerror!("{:#?}", order_id);
        //         qerror!("market买入失败了捏");
        //     }
        // }
        // info!("等5s后再走");
        // tokio::time::sleep(std::time::Duration::from_secs(5)).await;

        // // Market long close
        // order.side = OrderSide::Sell;
        // order.amount = 0.00008;
        // for _i in 1..=10{
        //     // Market long open
        //     let order_id = rest
        //         .post_order(order.clone(), params.clone())
        //         .await;
        //     if let Ok(order_id) = order_id{
        //         info!("{:#?}", order_id);
        //         info!("market卖出成功了");
        //         break;
        //     }
        //     else{
        //         error!("{:#?}", order_id);
        //         error!("market卖出失败了捏");
        //     }
        // }

        //limit限定价格下单
        // let mut order = Order::limit_open(Symbol::new("BTC"), 67942.2, 0.00005,PosSide::Long,TimeInForce::IOC, exchange);//市场价买入0.01 USDT的BTC
        // order.cid = None;
        //
        // let params = OrderParams::new(false, 1);

        // for _i in 1..=10{
        //     // Market long open
        //     let order_id = rest
        //         .post_order(order.clone(), params.clone())
        //         .await;
        //     if let Ok(order_id) = order_id{
        //         info!("{:#?}", order_id);
        //         info!("下单成功了");
        //         break;
        //     }
        //     else{
        //         error!("{:#?}", order_id);
        //         error!("下单失败了捏");
        //     }
        // }
        // info!("等5s后再走");
        // tokio::time::sleep(std::time::Duration::from_secs(5)).await;

        // // Market long close
        // order.side = OrderSide::Sell;
        // order.price =Some(67854.0);
        // order.amount = 0.00005;
        // for _i in 1..=10{
        //     // Market long open
        //     let order_id = rest
        //         .post_order(order.clone(), params.clone())
        //         .await;
        //     if let Ok(order_id) = order_id{
        //         info!("{:#?}", order_id);
        //         info!("下单成功了");
        //         break;
        //     }
        //     else{
        //         error!("{:#?}", order_id);
        //         error!("下单失败了捏");
        //     }
        // }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_oids() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateSpot::new(config).await;

        let symbol: Symbol = Symbol::new("SOL");

        let order = Order::limit_open(
            symbol.clone(),
            150.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );

        let params = OrderParams::new(false, 1);
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("{order_id:#?}");

        let ids = vec![order_id];

        let res = rest
            .batch_cancel_order_by_ids(Some(symbol), Some(ids), None)
            .await
            .unwrap();
        info!("{res:?}")
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_server_time() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSpot::new(config).await;
        let time = rest.get_server_time().await;
        info!("{time:?}");
    }
    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_open_by_oids() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSpot::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");
        let rsp = rest.get_open_orders(symbol.clone()).await.unwrap();
        if rsp.is_empty() {
            return;
        }
        let mut ids: Vec<String> = rsp.into_iter().map(|o| o.id).collect();
        info!("{ids:?}");
        ids.sort();

        let rsp = rest
            .batch_cancel_order_by_ids(Some(symbol), Some(ids.clone()), None)
            .await
            .unwrap();
        let mut cancel_ids: Vec<String> =
            rsp.success_list.into_iter().filter_map(|o| o.id).collect();
        info!("{cancel_ids:?}");
        cancel_ids.sort();
        assert_eq!(ids, cancel_ids);
    }

    #[tokio::test]
    #[ignore]
    async fn test_cancel_order_null() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSpot::new(config).await;
        rest.cancel_order(Symbol::new("BTC"), OrderId::Id("1234567890".to_string()))
            .await
            .unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_order() {
        fmt().pretty().init();
        let config = test_config();

        let exchange = config.exchange;

        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol.clone(),
            150.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );

        let params = OrderParams::new(false, 1);
        let mut order_ids: Vec<String> = vec![];
        for _ in 0..2 {
            let order_id = rest
                .post_order(order.clone(), params.clone())
                .await
                .unwrap();
            info!(" {order_id:#?}");
            order_ids.push(order_id);
        }
        order_ids.sort();
        info!("成功下单 {order_ids:#?}");

        tokio::time::sleep(Duration::from_secs(1)).await;
        let rsp = rest.batch_cancel_order(symbol).await.unwrap();
        let mut ids: Vec<String> = rsp
            .success_list
            .into_iter()
            .map(|o| o.id.unwrap())
            .collect();
        ids.sort();
        info!("撤单 {ids:#?}");
        assert_eq!(order_ids, ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol.clone(),
            60000.0,
            0.00010,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let cid = order.cid.clone().unwrap();
        let is_dual_side = false;
        let params = OrderParams::new(is_dual_side, 1);

        let order_id = rest
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("下单成功:{order_id}");

        rest.cancel_order(symbol.clone(), OrderId::ClientOrderId(cid.clone()))
            .await
            .unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy, 500 Internal Server Error"]
    async fn test_amend_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateSpot::new(config).await;
        let symbol = Symbol::new("SOL");

        let order = Order::limit_open(
            symbol.clone(),
            150.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let params = OrderParams::new(false, 1);

        let order_id = rest
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("成功下单:{order_id}");

        tokio::time::sleep(Duration::from_secs(1)).await;

        let order = Order {
            id: order_id,
            price: Some(160.0),
            ..order
        };

        let order_id = rest.amend_order(order).await.unwrap();
        info!("修改成功了，订单id是: {order_id:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateSpot::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");

        let orders: Vec<_> = (0..2)
            .map(|_| {
                Order::limit_open(
                    symbol.clone(),
                    150.0,
                    0.02,
                    PosSide::Long,
                    TimeInForce::GTC,
                    exchange,
                )
            })
            .collect();
        let cids: Vec<String> = orders.iter().filter_map(|o| o.cid.to_owned()).collect();
        info!("{cids:?}");

        let params = OrderParams::new(false, 1);
        let rsp = rest.post_batch_order(orders.clone(), params).await.unwrap();
        info!("{rsp:?}");
        let order_ids: Vec<String> = rsp.success_list.into_iter().filter_map(|o| o.cid).collect();
        info!("{order_ids:?}");
        assert_eq!(cids, order_ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_spot_transfer() {
        let config = test_config();
        let exchange = GateSpot::new(config).await;

        let parmas_tsf = Transfer {
            // from: WalletType::Spot,
            // to: WalletType::UsdtFuture,
            from: WalletType::Spot,
            to: WalletType::UsdtFuture,
            amount: 10.0,
            asset: "USDT".to_string(),
        };
        exchange.transfer(parmas_tsf).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_spot_get_order_by_id() {
        let config = test_config();
        let exchange = GateSpot::new(config).await;
        let symbol = Symbol::new("UNI");
        let oid = OrderId::Id("750330014707".to_string());
        let resp = exchange.get_order_by_id(symbol, oid).await.unwrap();
        println!("{resp:?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_unified_mode() {
        let config = test_config();
        let exchange = GateSpot::new(config).await;
        // let rsp = exchange
        //     .request(UserRequest {
        //         method: "GET".to_string(),
        //         path: "/api/v4/unified/unified_mode".to_string(),
        //         auth: true,
        //         query: None,
        //         body: None,
        //         url: None,
        //         headers: None,
        //     })
        //     .await
        //     .unwrap();
        // println!("{:?}", rsp);

        let rsp = exchange.get_account_mode().await.unwrap();
        println!("{rsp:?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_unified_mode() {
        let config = test_config();
        let exchange = GateSpot::new(config).await;
        // let rsp = exchange
        //     .request(UserRequest {
        //         method: "PUT".to_string(),
        //         path: "/api/v4/unified/unified_mode".to_string(),
        //         auth: true,
        //         body: Some(json!(
        //             {
        //                 "mode": "multi_currency",
        //                 "settings": {
        //                     "usdt_futures": true
        //                 }
        //             }
        //         )),
        //         ..Default::default()
        //     })
        //     .await
        //     .unwrap();
        // println!("{:?}", rsp);

        exchange
            .set_account_mode(AccountMode::MultiCurrency)
            .await
            .unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_acct_info() {
        let config = test_config();
        let exchange = GateSpot::new(config).await;
        let data = exchange.get_account_info().await.unwrap();
        println!("{data:?}");
    }
}

mod tests {
    use hyperliquid::{
        spot::rest::HyperLiquidSpot,
        util::{self, generate_128bit_hex},
    };
    use log::info;
    use quant_common::{
        base::{
            ExConfig, Order, OrderId, OrderParams, OrderSide, OrderType, PosSide, Rest, Symbol,
            TimeInForce, Transfer, WalletType,
        },
        time_ms,
    };
    use serde::{Deserialize, Serialize};
    use std::time::Duration;
    use time::Time;
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        let toml_str = r#"
        exchange = "HyperLiquidSpot"
        key = "******************************************"
        secret ="16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808"
        passphrase = "passphrase"
        is_colo = false
        is_testnet = false
        is_unified = false
        params = { cookie = "some_cookie", code = "1234", org_id = "5678" }
    "#;
        toml::from_str(&toml_str).unwrap()
    }
    #[tokio::test]
    async fn test_create_wallet() {
        fmt().pretty().init();
        //Generated private key: 16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808
        //Wallet address: ******************************************
        let wallact_private = HyperLiquidSpot::new_wallect();
        info!("rsp={:?}", wallact_private)
    }
    #[tokio::test]
    async fn test_get_instruments() {
        fmt().pretty().init();
        let config = test_config();
        //Generated private key: 16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808
        //Wallet address: ******************************************
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex.get_instruments().await.expect("get instruments error");
        // info!("rsp={:?}", rsp)
        for v in rsp {
            info!("{:?}", v);
        }
    }
    #[tokio::test]
    async fn test_get_instrument() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .get_instrument(Symbol::new_with_quote(
                "BTC",
                quant_common::base::QuoteCcy::USDC,
            ))
            .await
            .expect("get instrument error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_depth() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .get_depth(
                Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                None,
            )
            .await
            .expect("get_ticker error");
        info!("rsp={:?}", rsp)
    }
    #[tokio::test]
    async fn test_get_bbo_ticker() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .get_bbo_ticker(Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            })
            .await
            .expect("get_bbo error");
        info!("rsp={:?}", rsp)
    }
    #[tokio::test]
    async fn test_get_ticker() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .get_ticker(Symbol {
                base: "PURR".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            })
            .await
            .expect("get_ticker error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_balances() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex.get_balances().await.expect("get balances error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let ex = HyperLiquidSpot::new(config).await;
        // Market order no support
        let mut order = Order::market_open(
            Symbol {
                base: "PURR".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            },
            65.2,
            PosSide::Long,
            exchange,
        );
        order.cid = Some(generate_128bit_hex()); //cid 必须是128位的16进制字符串
                                                 // Limit order
                                                 // order.order_type = OrderType::Limit;
                                                 // order.time_in_force = TimeInForce::GTC;
                                                 // order.time_in_force=TimeInForce::IOC;
                                                 // order.time_in_force=TimeInForce::FOK;
                                                 // order.pos_side = Some(PosSide::Short);
                                                 // order.side = OrderSide::Sell;
        order.price = Some(0.27500);
        let params = OrderParams::new(false, 1); //这个参数没用到
        let order_id = ex
            .post_order(order, params)
            .await
            .expect("post order error");
        info!("rsp={:?}", order_id)
    }

    #[tokio::test]
    async fn test_get_all_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .get_all_open_orders()
            .await
            .expect("get_all_open_orders error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .get_open_orders(Symbol {
                base: "PURR".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            })
            .await
            .expect("get open orders error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_order_by_id() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .get_order_by_id(
                Symbol {
                    base: "PURR".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                OrderId::Id("100014392378".to_string()),
            )
            .await
            .expect("get open orders error");
        info!("rsp={:?}", rsp)
    }

    //测试获取历史订单
    #[tokio::test]
    async fn test_get_orders() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let end_time = time_ms();
        let start_time = end_time - 1000 * 60 * 60 * 24 * 30;
        let rsp = ex
            .get_orders(
                Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                start_time,
                end_time,
            )
            .await
            .expect("get orders error");
        info!("rsp={:?}", rsp);
    }
    #[tokio::test]
    async fn test_get_fee_rate() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .get_fee_rate(Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            })
            .await
            .expect("get_fee_rate error");
        info!("rsp:{:?}", rsp);
    }
    #[tokio::test]
    async fn test_amend_order() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let order = quant_common::base::Order {
            id: "100036215227".to_string(),
            price: Some(0.1852),
            amount: Some(58.0),
            side: OrderSide::Buy,
            symbol: Symbol {
                base: "PURR".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            },
            order_type: OrderType::Limit,
            time_in_force: TimeInForce::GTC,
            ..Default::default()
        };
        let order_id = ex.amend_order(order).await.expect("amend order failed");
        info!("amend order rsp:{:?}", order_id) //73763613926
    }

    #[tokio::test]
    async fn test_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .cancel_order(
                Symbol {
                    base: "PURR".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                OrderId::Id("100036243536".to_string()),
            )
            .await
            .expect("cancel order by orderId error");
        info!("cancel order rsp:{:?}", rsp);
    }
    #[tokio::test]
    async fn test_batch_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config).await;
        let rsp = ex
            .batch_cancel_order(Symbol {
                base: "PURR".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            })
            .await
            .expect("batch_cancel_order error");
        info!("batch_cancel_order rsp:{:?}", rsp);
    }
    //测试批量下单
    #[tokio::test]
    async fn test_batch_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSpot::new(config.clone()).await;
        let mut order1 = Order::market_open(
            Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            },
            0.00012,
            PosSide::Long,
            config.exchange,
        );
        let mut order2 = Order::market_open(
            Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            },
            0.00012,
            PosSide::Long,
            config.exchange,
        );
        let mut order3 = Order::market_open(
            Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDC,
            },
            0.00012,
            PosSide::Long,
            config.exchange,
        );
        order1.cid = Some(generate_128bit_hex()); //cid 必须是128位的16进制字符串
        order2.cid = Some(generate_128bit_hex()); //cid 必须是128位的16进制字符串
        order3.cid = Some(generate_128bit_hex()); //cid 必须是128位的16进制字符串
        order1.order_type = OrderType::Limit;
        order2.order_type = OrderType::Limit;
        order3.order_type = OrderType::Limit;
        order1.time_in_force = TimeInForce::IOC;
        order2.time_in_force = TimeInForce::FOK;
        order3.time_in_force = TimeInForce::GTC;

        // order.pos_side=Some(PosSide::Short);
        // order.side=OrderSide::Sell;
        order1.price = Some(97133.);
        order2.price = Some(97134.);
        order3.price = Some(97135.);
        let orders = vec![order1, order2, order3];
        let params = OrderParams::new(false, 1);
        let rsp = ex
            .post_batch_order(orders, params)
            .await
            .expect("post batch order error");
        info!("rsp={:?}", rsp)
    }
}

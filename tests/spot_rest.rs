#[cfg(test)]
mod tests {
    use log::info;
    use okx::spot::rest::OkxSpot;
    use quant_common::{base::*, test_config};
    use quant_common::{time_ms, time_ns};
    use rustc_hash::FxHashMap;
    use tracing_subscriber::fmt;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_ticker() {
        fmt().pretty().init();
        let rest = OkxSpot::default();
        let symbol = Symbol::new("BTC");
        let ticker = rest.get_ticker(symbol).await.unwrap();
        info!("{:#?}", ticker);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_tickers() {
        fmt().pretty().init();
        let rest = OkxSpot::default();
        let tickers = rest.get_tickers().await.unwrap();
        info!("len: {}", tickers.len());
        for ticker in tickers.into_iter().take(5) {
            info!("{:?}", ticker);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_mark_price() {
        fmt().pretty().init();
        let exchange = OkxSpot::default();
        let symbol = Symbol::new("BTC");
        let mark_price = exchange.get_mark_price(Some(symbol)).await.unwrap();
        info!("{:?}", mark_price);

        let mark_price_all = exchange.get_mark_price(None).await.unwrap();
        info!("{:?}", mark_price_all);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_bbo_ticker() {
        fmt().pretty().init();
        let rest = OkxSpot::default();
        let symbol = Symbol::new("BTC");
        let ticker = rest.get_bbo_ticker(symbol).await.unwrap();
        info!("{:#?}", ticker);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_bbo_tickers() {
        fmt().pretty().init();
        let rest = OkxSpot::default();
        let tickers = rest.get_bbo_tickers().await.unwrap();
        for ticker in tickers {
            info!("{:?}", ticker);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_depth() {
        fmt().pretty().init();
        let rest = OkxSpot::default();
        let symbol = Symbol::new("BTC");
        let depth = rest.get_depth(symbol, Some(10)).await.unwrap();
        info!("symbol: {}", depth.symbol);
        info!("bids: {:#?}", depth.bids);
        info!("asks: {:#?}", depth.asks);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instrument() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let instrument = rest.get_instrument(symbol).await.unwrap();
        info!("{:#?}", instrument);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_order_by_id() {
        let config = test_config();
        let exchange = OkxSpot::new(config).await;
        let symbol = Symbol::new("BTC");
        let order_id = OrderId::Id("2054566743967834112".to_string());
        let order = exchange.get_order_by_id(symbol, order_id).await.unwrap();
        println!("{:?}", order);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_all_orders() {
        let config = test_config();
        let exchange = OkxSpot::new(config).await;
        let orders = exchange.get_all_open_orders().await.unwrap();
        println!("{:#?}", orders);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_orders_history() {
        let config = test_config();
        let exchange = OkxSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let start_time = 1727712000000i64;
        let end_time = time_ms();
        let orders = exchange
            .get_orders(symbol, start_time, end_time)
            .await
            .unwrap();
        println!("{:#?}", orders);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instruments() {
        fmt().pretty().init();
        let rest = OkxSpot::default();
        let instruments = rest.get_instruments().await.unwrap();
        info!("Len: {}", instruments.len());
        for instrument in instruments.into_iter().take(5) {
            info!("{:?}", instrument);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let orders = rest.get_open_orders(symbol).await.unwrap();
        info!("挂单数量:{}", orders.len());
        for order in orders {
            info!("{:?}", order);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_all_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let orders = rest.get_all_open_orders().await.unwrap();
        info!("挂单数量:{}", orders.len());
        for order in orders {
            info!("{:?}", order);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let end_time = time_ms();
        let start_time = end_time - 1000 * 3600 * 24;
        let orders = rest.get_orders(symbol, start_time, end_time).await.unwrap();
        info!("订单数量: {}", orders.len());
        for order in orders.into_iter().take(5) {
            info!("{:?}", order);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_market_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let is_combined_mode = config.is_unified;
        let params = match is_combined_mode {
            true => OrderParams {
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            false => OrderParams::default(),
        };
        let rest = OkxSpot::new(config).await;
        let order = Order::market_open(Symbol::new("SOL"), 30., PosSide::Long, exchange);
        let order = Order {
            price: Some(230.),
            ..order
        };

        let order_id = rest.post_order(order, params).await.unwrap();
        info!("{order_id}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_balances() {
        fmt().pretty().with_env_filter("okx=debug,info").init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let balances = rest.get_balances().await.unwrap();
        for balance in balances {
            if balance.available_balance.abs() > 0.01 {
                info!("{:?}", balance);
            }
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_limit_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let is_combined_mode = config.is_unified;
        let params = match is_combined_mode {
            true => OrderParams {
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            false => OrderParams::default(),
        };
        let rest = OkxSpot::new(config).await;
        let order = Order::limit_close(
            Symbol::new("SOL"),
            230.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let order_id = rest.post_order(order, params).await.unwrap();
        info!("{order_id}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_all_fail_batch_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = OkxSpot::new(config).await;
        let orders = vec![
            Order::market_open(Symbol::new("SOL"), 10000., PosSide::Short, exchange),
            Order::market_open(Symbol::new("SOL"), 10000., PosSide::Short, exchange),
        ];
        let cids: Vec<String> = orders.iter().filter_map(|o| o.cid.to_owned()).collect();
        info!("{cids:?}");
        let params = OrderParams::new(true, 1);
        let rsp = rest.post_batch_order(orders, params).await.unwrap();
        let fail_cids: Vec<String> = rsp
            .failure_list
            .iter()
            .filter_map(|o| o.cid.to_owned())
            .collect();
        for o in rsp.failure_list {
            info!("fail: {:?}", o.cid);
        }
        assert_eq!(cids, fail_cids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_part_fail_batch_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let is_combined_mode = config.is_unified;
        let params = match is_combined_mode {
            true => OrderParams {
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            false => OrderParams::default(),
        };
        let rest = OkxSpot::new(config).await;
        let orders = vec![
            Order {
                price: Some(200.),
                ..Order::market_open(Symbol::new("SOL"), 0.1, PosSide::Long, exchange)
            },
            Order::market_open(Symbol::new("SOL"), 10000., PosSide::Short, exchange),
        ];
        if is_combined_mode {
            let cids: Vec<String> = orders.iter().filter_map(|o| o.cid.to_owned()).collect();
            info!("cids: {:?}", cids);

            let rsp = rest.post_batch_order(orders, params).await.unwrap();
            let fail_cids: Vec<String> = rsp
                .failure_list
                .iter()
                .filter_map(|o| o.cid.to_owned())
                .collect();
            info!("fail: {:?}", rsp.failure_list);
            assert_eq!(cids, fail_cids);
        } else {
            let succ = orders[0].cid.clone();
            let fail = orders[1].cid.clone();
            info!("{succ:?}, {fail:?}");
            let rsp = rest.post_batch_order(orders, params).await.unwrap();
            info!("succ: {:?}", rsp.success_list);
            info!("fail: {:?}", rsp.failure_list);
            assert_eq!(succ, rsp.success_list[0].cid);
            assert_eq!(fail, rsp.failure_list[0].cid);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_batch_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let is_combined_mode = config.is_unified;
        let params = match is_combined_mode {
            true => OrderParams {
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            false => OrderParams::default(),
        };
        let rest = OkxSpot::new(config).await;
        let orders = vec![
            Order {
                price: Some(200.),
                ..Order::market_open(Symbol::new("SOL"), 0.1, PosSide::Long, exchange)
            },
            Order {
                price: Some(200.),
                ..Order::market_open(Symbol::new("SOL"), 0.1, PosSide::Long, exchange)
            },
        ];
        let cids: Vec<String> = orders.iter().filter_map(|o| o.cid.to_owned()).collect();
        info!("{cids:?}");
        let rsp = rest.post_batch_order(orders, params).await.unwrap();
        info!("succ: {:?}", rsp.success_list);
        info!("fail: {:?}", rsp.failure_list);
        let cancel_cids: Vec<String> = rsp
            .success_list
            .iter()
            .filter_map(|o| o.cid.to_owned())
            .collect();
        assert_eq!(cids, cancel_cids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_order_by_ids() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let is_combined_mode = config.is_unified;
        let params = match is_combined_mode {
            true => OrderParams {
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            false => OrderParams::default(),
        };
        let rest = OkxSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let orders = vec![
            Order::limit_open(
                symbol.clone(),
                1.0,
                0.001,
                PosSide::Long,
                TimeInForce::GTC,
                exchange,
            ),
            Order::limit_close(
                symbol.clone(),
                2.0,
                0.001,
                PosSide::Short,
                TimeInForce::GTC,
                exchange,
            ),
        ];
        let rsp = rest.post_batch_order(orders, params).await.unwrap();
        info!("下单成功: {:?}", rsp.success_list);
        info!("下单失败: {:?}", rsp.failure_list);
        let ids = rsp
            .success_list
            .iter()
            .map(|r| r.id.clone().unwrap())
            .collect();
        let rsp = rest
            .batch_cancel_order_by_ids(Some(symbol), Some(ids), None)
            .await
            .unwrap();
        info!("撤单: {:?}", rsp.success_list);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_auto_cancel_open_order() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let orders = rest.get_all_open_orders().await.unwrap();
        let info: FxHashMap<Symbol, Vec<String>> =
            orders.into_iter().fold(FxHashMap::default(), |mut m, o| {
                m.entry(o.symbol).or_default().push(o.id);
                m
            });
        for (symbol, ids) in info {
            let rsp = rest
                .batch_cancel_order_by_ids(Some(symbol), Some(ids), None)
                .await
                .unwrap();
            for o in rsp.success_list {
                info!("{:?}", o);
            }
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_amend_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let params = match config.is_unified {
            true => OrderParams {
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            false => OrderParams::default(),
        };
        let rest = OkxSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let mut order = Order::limit_open(
            symbol.clone(),
            100.0,
            0.001,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("{order_id}");
        order.id = order_id.clone();
        order.price = Some(110.0);
        order.cid = Some(time_ns().to_string());
        let amend_order_id = rest.amend_order(order).await.unwrap();
        info!("{amend_order_id}");
        assert_eq!(order_id, amend_order_id);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let is_combined_mode = config.is_unified;
        let params = match is_combined_mode {
            true => OrderParams {
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            false => OrderParams::default(),
        };
        let rest = OkxSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol.clone(),
            100.0,
            0.001,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let order_id = rest.post_order(order, params).await.unwrap();
        info!("{:?}", order_id);
        let id = OrderId::Id(order_id);
        rest.cancel_order(symbol, id).await.unwrap();
        info!("撤单成功");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_fee_rate() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let symbol = Symbol::new("SOL");
        let fee_rate = rest.get_fee_rate(symbol).await.unwrap();
        info!("{:?}", fee_rate);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_dual_side() {
        fmt().pretty().init();
        let config = test_config();
        let dual_side = !config.is_unified;
        let rest = OkxSpot::new(config).await;
        rest.set_dual_side(dual_side).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_is_dual_side() {
        fmt().pretty().init();
        let config: ExConfig = test_config();
        let rest = OkxSpot::new(config).await;
        let is_dual_side = rest.is_dual_side().await.unwrap();
        info!("{:?}", is_dual_side);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_user_id() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let user_id = rest.get_user_id().await.unwrap();
        info!("user_id: {user_id}");
    }

    // #[tokio::test]
    // async fn test_get_margin_ratio() {
    //     fmt().pretty().init();
    //     let config = test_config();
    //     let rest = OkxSpot::new(config).await;
    //     let margin_ratio = rest.get_margin_ratio().await.unwrap();
    //     info!("账号保证金率: {margin_ratio}");
    // }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_account_info() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxSpot::new(config).await;
        let account_info = rest.get_account_info().await.unwrap();
        info!("account info: {:?}", account_info);
    }
}

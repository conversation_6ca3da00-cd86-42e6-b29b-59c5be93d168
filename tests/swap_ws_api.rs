#[cfg(test)]
mod tests {
    use async_channel::Sender;
    use async_channel::bounded;
    use async_channel::unbounded;
    use binance::util::is_ed25519_secret;
    use binance::util::sign_ws_api_params;
    use quant_common::Result;
    use quant_common::base::traits::ws_api::*;
    use quant_common::base::*;
    use quant_common::qerror;
    use serde_json::json;
    use std::fs::read_to_string;
    use tokio::spawn;
    use tracing::info;
    use tracing_subscriber::fmt;

    use binance::swap::ws_api::BinanceSwapWsApi;

    fn test_config() -> ExConfig {
        let config: ExConfig = toml::from_str(&read_to_string("swap.toml").unwrap()).unwrap();
        if is_ed25519_secret(&config.secret) {
            panic!("bn合约没有测试 ed25519环境");
        }
        config
    }

    trait TestAsyncHandler {
        async fn handle_post_order(&self, req_id: u64, order: PlaceOrderResult) -> Result<()>;
        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_amend_order(&self, req_id: u64, order: AmendOrderResult) -> Result<()>;
        async fn handle_cancel_order(&self, req_id: u64, order: CancelOrderResult) -> Result<()>;
        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
    }

    struct WrapHandler<H: TestAsyncHandler> {
        handler: H,
    }

    impl<H: TestAsyncHandler> From<H> for WrapHandler<H> {
        fn from(handler: H) -> Self {
            Self { handler }
        }
    }

    #[allow(unused_variables)]
    impl<H: TestAsyncHandler + Send + Sync + 'static> AsyncResHandle for WrapHandler<H> {
        async fn handle_post_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: PlaceOrderResult,
        ) -> Result<()> {
            self.handler.handle_post_order(req_id, result).await
        }

        async fn handle_post_batch_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_post_batch_order(req_id, orders).await
        }

        async fn handle_amend_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: AmendOrderResult,
        ) -> Result<()> {
            self.handler.handle_amend_order(req_id, result).await
        }

        async fn handle_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: CancelOrderResult,
        ) -> Result<()> {
            self.handler.handle_cancel_order(req_id, result).await
        }

        async fn handle_batch_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_batch_cancel_order(req_id, orders).await
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            account_id: usize,
            req_id: u64,
            res: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler
                .handle_batch_cancel_order_by_ids(req_id, res)
                .await
        }
    }

    #[test]
    fn test_sign_ws_api_req() {
        let _ = fmt().pretty().try_init();

        let value = json!( {
            "symbol":           "BTCUSDT",
            "side":             "SELL",
            "type":             "LIMIT",
            "timeInForce":      "GTC",
            "quantity":         "0.********",
            "price":            "52000.00",
            "newOrderRespType": "ACK",
            "recvWindow":       100,
            "timestamp":        1645423376532u64,
            "apiKey":           "vmPUZE6mv9SD5VNHk4HlWFsOr6aKE2zvsw0MuIgwCIPy6utIco14y7Ju91duEh8A",
        });
        let check = "cc15477742bd704c29492d96c7ead9414dfd8e0ec4a00f947bb5bb454ddbd08a";

        let signature = sign_ws_api_params(
            "NhqPtmdSJYdKjVHjA7PZj4Mge3R5YNiP1e3UZjInClVN65XAbvqqM6A7H5fATj0j",
            &value,
        )
        .unwrap();
        assert_eq!(signature, check);
    }

    struct PostOrderHandler {
        finish_tx: Sender<Result<()>>,
    }

    impl TestAsyncHandler for PostOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {}", order_id);
                self.finish_tx.send(Ok(())).await?;
            } else {
                self.finish_tx
                    .send(Err(qerror!("下单失败: {req_id} {:?}", result.result)))
                    .await?;
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_post_order() -> Result<()> {
        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceSwapWsApi::new(config).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BNB");
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let order = Order::limit_open(symbol, 100., 0.05, ps, tif, Exchange::BinanceSwap);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap()?;

        Ok(())
    }

    #[tokio::test]
    async fn test_post_reduce_only_order() -> Result<()> {
        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceSwapWsApi::new(config).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BNB");
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let order = Order::limit_close(symbol, 800., 0.01, ps, tif, Exchange::BinanceSwap);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap()?;

        Ok(())
    }

    #[tokio::test]
    async fn test_post_post_only_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceSwapWsApi::new(config).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BNB");
        let ps = PosSide::Long;
        let tif = TimeInForce::PostOnly;
        let order = Order::limit_open(symbol, 100., 0.05, ps, tif, Exchange::BinanceSwap);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap()?;

        Ok(())
    }

    #[tokio::test]
    async fn test_open_market_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceSwapWsApi::new(config).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BNB");
        let order = Order {
            price: Some(1.0),
            ..Order::market_open(symbol, 0.01, PosSide::Long, Exchange::BinanceSwap)
        };
        let params = OrderParams::new(true, 1);
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap()?;

        Ok(())
    }

    #[tokio::test]
    #[ignore = "需要先有仓位"]
    async fn test_close_market_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();

        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceSwapWsApi::new(config).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BNB");
        let order = Order::market_close(symbol, 0.01, PosSide::Long, Exchange::BinanceSwap);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap()?;

        Ok(())
    }

    struct CancelOrderHandler {
        tx: Sender<(u64, AsyncCmd)>,
        finish_tx: Sender<()>,
    }

    impl TestAsyncHandler for CancelOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {}", order_id);
                self.tx
                    .send((
                        req_id + 1,
                        AsyncCmd::CancelOrder(CancelOrderCmd {
                            symbol: Symbol::new("BNB"),
                            order_id: OrderId::Id(order_id.clone()),
                        }),
                    ))
                    .await?;
            } else {
                info!("下单失败: {req_id} {:?}", result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_cancel_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let ws_api = BinanceSwapWsApi::new(config).await;

        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("BNB");
        let order = Order::limit_open(
            symbol,
            251.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSwap,
        );
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let handler = CancelOrderHandler { finish_tx, tx };
            match ws_api.run(0, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_cancel_order_by_cid() -> Result<()> {
        struct CancelOrderByCidHandler {
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for CancelOrderByCidHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                let order_id = &result.result.unwrap();
                info!("下单成功: {req_id} {:?}", order_id);
                let cancel_order = AsyncCmd::CancelOrder(CancelOrderCmd {
                    symbol: result.order.symbol.clone(),
                    order_id: OrderId::ClientOrderId(result.order.cid.clone().unwrap()),
                });
                self.tx.send((req_id + 1, cancel_order)).await?;
                Ok(())
            }

            async fn handle_post_batch_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量下单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_amend_order(
                &self,
                req_id: u64,
                result: AmendOrderResult,
            ) -> Result<()> {
                info!("修改订单成功: {req_id} {:?}", result);
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                self.finish_tx.send(()).await?;
                Ok(())
            }

            async fn handle_batch_cancel_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_batch_cancel_order_by_ids(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }
        }

        let _ = fmt().pretty().try_init();

        let config = test_config();
        let ws_api = BinanceSwapWsApi::new(config).await;

        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("BNB");
        let order = Order::limit_open(
            symbol,
            100.,
            0.05,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSwap,
        );
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let handler = CancelOrderByCidHandler { finish_tx, tx };
            match ws_api.run(0, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_amend_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        struct AmendOrderHandler {
            amend_order: Order,
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for AmendOrderHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                let order_id = &result.result.unwrap();
                info!("下单成功: {req_id} {:?}", order_id);
                let mut order_clone = self.amend_order.clone();
                order_clone.id = order_id.clone();
                let amend_cmd = AsyncCmd::AmendOrder(order_clone);
                self.tx.send((req_id + 1, amend_cmd)).await?;
                Ok(())
            }

            async fn handle_post_batch_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量下单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_amend_order(
                &self,
                req_id: u64,
                result: AmendOrderResult,
            ) -> Result<()> {
                info!("修改订单成功: {req_id} {:?}", result);
                self.finish_tx.send(()).await?;
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                Ok(())
            }

            async fn handle_batch_cancel_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_batch_cancel_order_by_ids(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }
        }

        let config = test_config();
        let ws_api = BinanceSwapWsApi::new(config).await;

        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("BNB");
        let order = Order::limit_open(
            symbol,
            100.,
            0.05,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSwap,
        );
        let amend_order = Order {
            price: Some(101.),
            ..order.clone()
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let amend_handler = AmendOrderHandler {
                amend_order,
                finish_tx,
                tx,
            };
            match ws_api.run(0, WrapHandler::from(amend_handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_amend_post_only_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        struct AmendOrderHandler {
            amend_order: Order,
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for AmendOrderHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                if let Ok(order_id) = &result.result {
                    info!("下单成功: {req_id} {:?}", order_id);
                    let mut order_clone = self.amend_order.clone();
                    order_clone.id = order_id.clone();
                    self.tx
                        .send((req_id + 1, AsyncCmd::AmendOrder(order_clone)))
                        .await?;
                } else {
                    info!("下单失败: {req_id} {:?}", result.result);
                }
                Ok(())
            }

            async fn handle_post_batch_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量下单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_amend_order(
                &self,
                req_id: u64,
                result: AmendOrderResult,
            ) -> Result<()> {
                info!("修改订单成功: {req_id} {:?}", result);
                self.finish_tx.send(()).await?;
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                Ok(())
            }

            async fn handle_batch_cancel_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_batch_cancel_order_by_ids(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }
        }

        let config = test_config();
        let ws_api = BinanceSwapWsApi::new(config).await;

        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("BNB");
        let order = Order::limit_open(
            symbol,
            100.,
            0.05,
            PosSide::Long,
            TimeInForce::PostOnly,
            Exchange::BinanceSwap,
        );
        let amend_order = Order {
            price: Some(101.),
            ..order.clone()
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let amend_handler = AmendOrderHandler {
                amend_order,
                finish_tx,
                tx,
            };
            match ws_api.run(0, WrapHandler::from(amend_handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use std::time::Duration;

    use async_channel::bounded;
    use async_channel::unbounded;
    use async_channel::Sender;

    use quant_common::base::traits::ws_api::*;
    use quant_common::base::*;
    use quant_common::qerror;
    use quant_common::test_config;
    use quant_common::Result;
    use tokio::spawn;
    use tokio::time::sleep;
    use tracing::error;
    use tracing::info;
    use tracing::level_filters::LevelFilter;
    use tracing_subscriber::fmt;

    use okx::swap::ws_api::*;

    trait TestAsyncHandler {
        async fn handle_post_order(&self, req_id: u64, order: PlaceOrderResult) -> Result<()>;
        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_amend_order(&self, req_id: u64, order: AmendOrderResult) -> Result<()>;
        async fn handle_cancel_order(&self, req_id: u64, order: CancelOrderResult) -> Result<()>;
        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
    }
    struct WrapHandler<H: TestAsyncHandler> {
        handler: H,
    }

    impl<H: TestAsyncHandler> From<H> for WrapHandler<H> {
        fn from(handler: H) -> Self {
            Self { handler }
        }
    }

    impl<H: TestAsyncHandler + Send + Sync + 'static> AsyncResHandle for WrapHandler<H> {
        async fn handle_post_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: PlaceOrderResult,
        ) -> Result<()> {
            self.handler.handle_post_order(req_id, result).await
        }

        async fn handle_post_batch_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_post_batch_order(req_id, orders).await
        }

        async fn handle_amend_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: AmendOrderResult,
        ) -> Result<()> {
            self.handler.handle_amend_order(req_id, result).await
        }

        async fn handle_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: CancelOrderResult,
        ) -> Result<()> {
            self.handler.handle_cancel_order(req_id, result).await
        }

        async fn handle_batch_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_batch_cancel_order(req_id, orders).await
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            _account_id: usize,
            req_id: u64,
            res: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler
                .handle_batch_cancel_order_by_ids(req_id, res)
                .await
        }
    }

    struct PostOrderHandler {
        finish_tx: Sender<()>,
    }

    impl TestAsyncHandler for PostOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {}", order_id);
                self.finish_tx.send(()).await?;
            } else {
                info!("下单失败: {req_id} {:?}", result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_order() -> Result<()> {
        fmt().pretty().with_max_level(LevelFilter::DEBUG).init();

        let config = test_config();
        let account_id = 1;
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        spawn(async move {
            let ws_api = OkxSwapWsApi::new(config).await;
            ws_api
                .run(
                    account_id,
                    WrapHandler::from(PostOrderHandler { finish_tx }),
                    rx,
                )
                .await
                .unwrap();
        });

        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol,
            202.,
            0.1,
            PosSide::Short,
            TimeInForce::GTC,
            Exchange::OkxSwap,
        );
        // let order = Order::market_open(symbol, 0.1, PosSide::Long, Exchange::OkxSwap);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_post_order() -> Result<()> {
        fmt().pretty().with_max_level(LevelFilter::DEBUG).init();

        let config = test_config();
        let account_id = 1;
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        spawn(async move {
            let ws_api = OkxSwapWsApi::new(config).await;
            ws_api
                .run(
                    account_id,
                    WrapHandler::from(PostOrderHandler { finish_tx }),
                    rx,
                )
                .await
                .unwrap();
        });
        let mut orders = Vec::new();
        let symbol = Symbol::new("SOL");
        for i in 0..10 {
            let order = Order::limit_open(
                symbol.clone(),
                100. + i as f64,
                0.1,
                PosSide::Long,
                TimeInForce::GTC,
                Exchange::OkxSwap,
            );
            orders.push(order);
        }
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::BatchPlaceOrder(BatchPlaceOrderCmd { orders, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    struct CancelOrderHandler {
        tx: Sender<(u64, AsyncCmd)>,
        finish_tx: Sender<()>,
    }

    impl TestAsyncHandler for CancelOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {}", order_id);
                self.tx
                    .send((
                        req_id + 1,
                        AsyncCmd::CancelOrder(CancelOrderCmd {
                            symbol: Symbol::new("SOL"),
                            order_id: OrderId::Id(order_id.clone()),
                        }),
                    ))
                    .await?;
            } else {
                info!("下单失败: {req_id} {:?}", result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() -> Result<()> {
        fmt().pretty().init();

        let config = test_config();
        let account_id = 1;
        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol,
            102.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::OkxSwap,
        );
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let ws_api = OkxSwapWsApi::new(config).await;
            let handler = CancelOrderHandler { finish_tx, tx };
            match ws_api.run(account_id, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    // #[tokio::test]
    // #[ignore = "need proxy"]
    // async fn test_batch_cancel_order() -> Result<()> {
    //     fmt().pretty().with_max_level(LevelFilter::DEBUG).init();

    //     let config = test_config();
    //     let (tx, rx) = bounded(10);

    //     let symbol = Symbol::new("SOL");
    //     let mut orders = Vec::new();
    //     for i in 0..10 {
    //         let order = Order::limit_open(
    //             symbol.clone(),
    //             100. + i as f64,
    //             0.1,
    //             PosSide::Long,
    //             TimeInForce::GTC,
    //             Exchange::OkxSwap,
    //         );
    //         orders.push(order);
    //     }
    //     let params = OrderParams::default();
    //     let post_cmd = AsyncCommand::PostBatchOrder { orders, params };
    //     tx.send((1, post_cmd)).await?;

    //     let (finish_tx, finish_rx) = bounded(1);
    //     spawn(async move {
    //         let ws_api = OkxSwapWsApi::new(config);
    //         let handler = CancelOrderHandler { finish_tx, tx };
    //         match ws_api.run(WrapHandler::from(handler), rx).await {
    //             Ok(()) => (),
    //             Err(e) => panic!("Error: {e}"),
    //         }
    //     });

    //     finish_rx.recv().await.unwrap();

    //     Ok(())
    // }

    struct AmendOrderHandler {
        amend_order: Order,
        tx: Sender<(u64, AsyncCmd)>,
        finish_tx: Sender<()>,
    }

    impl TestAsyncHandler for AmendOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {:?}", order_id);
                let mut order_clone = self.amend_order.clone();
                order_clone.id = order_id.clone();
                self.tx
                    .send((req_id + 1, AsyncCmd::AmendOrder(order_clone)))
                    .await?;
            } else {
                info!("下单失败: {req_id} {:?}", result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_amend_order() -> Result<()> {
        fmt().pretty().init();

        let config = test_config();
        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol,
            100.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::OkxSwap,
        );
        let amend_order = Order {
            price: Some(101.),
            ..order.clone()
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let ws_api = OkxSwapWsApi::new(config).await;
            let amend_handler = AmendOrderHandler {
                amend_order,
                finish_tx,
                tx,
            };
            let account_id = 1;
            match ws_api
                .run(account_id, WrapHandler::from(amend_handler), rx)
                .await
            {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    struct CompleteFlowHandler {
        tx: Sender<(u64, AsyncCmd)>,
        finish_tx: Sender<()>,
        amend_order: Order,
        state: std::sync::atomic::AtomicU8,
        symbol: Symbol,
    }

    impl TestAsyncHandler for CompleteFlowHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: req_id={}, order_id={}", req_id, order_id);
                let mut order_clone = self.amend_order.clone();
                order_clone.id = order_id.clone();
                let next_req_id = req_id + 1;
                info!("发送修改订单请求: req_id={}", next_req_id);
                self.tx
                    .send((next_req_id, AsyncCmd::AmendOrder(order_clone)))
                    .await?;
                self.state.store(1, std::sync::atomic::Ordering::SeqCst);
            } else {
                info!("下单失败: req_id={}, error={:?}", req_id, result.result);
                self.finish_tx.send(()).await?;
            }
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            if result.result.is_ok() {
                info!("修改订单成功: req_id={}, result={:?}", req_id, result);
                let next_req_id = req_id + 1;
                info!("发送撤单请求: req_id={}", next_req_id);
                self.tx
                    .send((
                        next_req_id,
                        AsyncCmd::CancelOrder(CancelOrderCmd {
                            symbol: self.symbol.clone(),
                            order_id: OrderId::Id(result.order.id.clone()),
                        }),
                    ))
                    .await?;
                self.state.store(2, std::sync::atomic::Ordering::SeqCst);
            } else {
                info!("修改订单失败: req_id={}, error={:?}", req_id, result.result);
                self.finish_tx.send(()).await?;
            }
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单结果: req_id={}, result={:?}", req_id, result);
            if result.result.is_ok() {
                // 开始批量下单测试
                let mut orders = Vec::new();
                for i in 0..3 {
                    let order = Order::limit_open(
                        self.symbol.clone(),
                        100. + i as f64,
                        0.1,
                        PosSide::Long,
                        TimeInForce::GTC,
                        Exchange::OkxSwap,
                    );
                    orders.push(order);
                }
                let params = OrderParams::default();
                let next_req_id = req_id + 1;
                info!("发送批量下单请求: req_id={}", next_req_id);
                self.tx
                    .send((
                        next_req_id,
                        AsyncCmd::BatchPlaceOrder(BatchPlaceOrderCmd { orders, params }),
                    ))
                    .await?;
                self.state.store(3, std::sync::atomic::Ordering::SeqCst);
            } else {
                info!("撤单失败: req_id={}, error={:?}", req_id, result.result);
                self.finish_tx.send(()).await?;
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            if let Ok(batch_rsp) = orders {
                info!("批量下单成功: req_id={}, result={:?}", req_id, batch_rsp);
                // 获取所有成功下单的订单ID
                let order_ids = batch_rsp
                    .success_list
                    .iter()
                    .filter_map(|order| order.id.clone())
                    .collect::<Vec<_>>();

                if !order_ids.is_empty() {
                    // 批量撤单
                    let next_req_id = req_id + 1;
                    info!(
                        "发送批量撤单请求: req_id={}, order_ids={:?}",
                        next_req_id, order_ids
                    );
                    // 使用 clone() 来保存 order_ids 用于日志
                    let order_ids_clone = order_ids.clone();
                    self.tx
                        .send((
                            next_req_id,
                            AsyncCmd::BatchCancelOrderByIds(BatchCancelOrderByIdsCmd {
                                symbol: Some(self.symbol.clone()),
                                ids: Some(order_ids),
                                cids: None,
                            }),
                        ))
                        .await?;
                    info!(
                        "已发送批量撤单请求: req_id={}, order_ids={:?}",
                        next_req_id, order_ids_clone
                    );
                    self.state.store(4, std::sync::atomic::Ordering::SeqCst);
                } else {
                    info!("没有成功下单的订单，无法进行批量撤单: req_id={}", req_id);
                    self.finish_tx.send(()).await?;
                }
            } else {
                info!(
                    "批量下单失败: req_id={}, error={:?}",
                    req_id,
                    orders.unwrap_err()
                );
                self.finish_tx.send(()).await?;
            }
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: req_id={}, result={:?}", req_id, orders);
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            match orders {
                Ok(batch_rsp) => {
                    info!("批量撤单成功: req_id={}, result={:?}", req_id, batch_rsp);
                    self.state.store(5, std::sync::atomic::Ordering::SeqCst);
                    self.finish_tx.send(()).await?;
                }
                Err(e) => {
                    info!("批量撤单失败: req_id={}, error={:?}", req_id, e);
                    self.finish_tx.send(()).await?;
                }
            }
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_complete_order_flow() -> Result<()> {
        fmt().pretty().with_max_level(LevelFilter::DEBUG).init();

        let config = test_config();
        let account_id = 1;
        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol.clone(),
            100.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::OkxSwap,
        );

        let amend_order = Order {
            price: Some(101.),
            ..order.clone()
        };

        let params = OrderParams::default();
        info!("开始测试流程，发送初始下单请求: req_id=1");
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        let (finish_tx, finish_rx) = bounded(1);

        let handler = CompleteFlowHandler {
            tx: tx.clone(),
            finish_tx,
            amend_order,
            state: std::sync::atomic::AtomicU8::new(0),
            symbol,
        };

        spawn(async move {
            let ws_api = OkxSwapWsApi::new(config).await;
            match ws_api.run(account_id, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();
        info!("测试流程完成");

        Ok(())
    }

    // 用于测试心跳机制的处理器
    #[allow(unused)]
    struct HeartbeatTestHandler {
        initial_task_tx: Sender<String>, // 用于初始任务完成的信号
        heartbeat_test_tx: Sender<()>,   // 用于心跳测试完成的信号
        symbol: Symbol,
    }

    impl TestAsyncHandler for HeartbeatTestHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: req_id={}, order_id={}", req_id, order_id);
                // 发送初始任务完成信号
                self.initial_task_tx.send(order_id.to_string()).await?;
            } else {
                info!("下单失败: req_id={}, error={:?}", req_id, result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单响应: req_id={}, result={:?}", req_id, orders);
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单响应: req_id={}, result={:?}", req_id, result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单响应: req_id={}, result={:?}", req_id, result);
            // 心跳测试后续的撤单操作成功，说明连接仍然有效
            self.heartbeat_test_tx.send(()).await?;
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单响应: req_id={}, result={:?}", req_id, orders);
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单(按ID)响应: req_id={}, result={:?}", req_id, orders);
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_heartbeat_mechanism() -> Result<()> {
        fmt().pretty().with_max_level(LevelFilter::INFO).init();

        info!("开始心跳机制测试");
        let config = test_config();
        let account_id = 1;
        let (tx, rx) = bounded(10);

        // 创建用于测试的通信通道
        let (initial_task_tx, initial_task_rx) = bounded(1); // 初始任务完成信号
        let (heartbeat_test_tx, heartbeat_test_rx) = bounded(1); // 心跳测试完成信号

        let symbol = Symbol::new("SOL");

        // 启动WebSocket服务
        let handler = HeartbeatTestHandler {
            initial_task_tx,
            heartbeat_test_tx,
            symbol: symbol.clone(),
        };

        // 在后台运行WebSocket连接
        let tx_clone = tx.clone();
        spawn(async move {
            let ws_api = OkxSwapWsApi::new(config).await;
            match ws_api.run(account_id, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        // 下单请求
        let order = Order::limit_open(
            symbol.clone(),
            100.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::OkxSwap,
        );

        let params = OrderParams::default();
        info!("发送初始下单请求: req_id=1");
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd {
            order: order.clone(),
            params,
        });
        tx.send((1, post_cmd)).await?;

        // 等待初始下单成功
        info!("等待初始下单完成...");
        match initial_task_rx.recv().await {
            Ok(order_id) => {
                info!("初始下单已完成，开始进行心跳测试");

                // 等待25秒，确保心跳机制生效（PING_INTERVAL为20秒）
                info!("等待60秒，让心跳机制发送一次ping...");
                sleep(Duration::from_secs(60)).await;

                // 发送撤单请求以验证连接是否依然有效
                // 使用一个有效的订单ID，这是测试失败的关键点
                info!("发送撤单请求检验连接状态");
                let cancel_cmd = AsyncCmd::CancelOrder(CancelOrderCmd {
                    symbol,
                    // 注意：我们需要一个有效的订单ID，可以考虑在HeartbeatTestHandler中保存下单时的订单ID
                    order_id: OrderId::Id(order_id), // 使用日志中的实际订单ID
                });
                tx_clone.send((2, cancel_cmd)).await?;

                // 设置一个超时，防止测试无限等待
                let timeout = sleep(Duration::from_secs(30));
                tokio::select! {
                    _ = heartbeat_test_rx.recv() => {
                        info!("心跳测试成功！连接在发送心跳后仍然保持活跃");
                        Ok(())
                    }
                    _ = timeout => {
                        error!("等待撤单响应超时");
                        Err(qerror!("测试超时"))
                    }
                }
            }
            Err(e) => {
                error!("等待初始下单完成时发生错误: {:?}", e);
                Err(e.into())
            }
        }
    }
}

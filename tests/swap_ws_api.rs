#[cfg(test)]
mod tests {

    use async_channel::bounded;
    use gate::swap::ws_api::GateSwapWsApi;
    use quant_common::base::*;
    use quant_common::*;
    use tracing_subscriber::fmt;
    // use async_channel::unbounded;
    use std::time::Duration;

    #[tokio::test]
    #[ignore = "需要代理, 只能在正式环境中运行"]
    async fn test_wsapi() -> Result<()> {
        fmt().pretty().init();
        let h1 = DefaultAsyncResHandle;
        let config = test_config();
        let wsapi = GateSwapWsApi::new(config).await;
        let (tx, rx) = bounded(10);
        // wsapi.run(0, h1, rx).await?;

        tokio::spawn(async move {
            match wsapi.run(0, h1, rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        // let mut send_order_task = false;

        // loop {
        //     println!("Main thread is still alive");
        //     tokio::time::sleep(Duration::from_secs(10)).await;
        //     if !send_order_task {

        //         println!("发送订单任务.");
        //         let symbol = Symbol::new("TON");
        //         let mut order = Order::limit_open(
        //             symbol,
        //             4.8,
        //             2.0,
        //             PosSide::Short,
        //             TimeInForce::GTC,
        //             Exchange::GateSwap,
        //         );
        //         order.side = OrderSide::Sell;
        //         let params = OrderParams::default();
        //         let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        //         tx.send((1, post_cmd)).await?;
        //         send_order_task = true
        //     }
        // }

        // let mut cancel_order_task = false;

        // loop {
        //     println!("Main thread is still alive");
        //     tokio::time::sleep(Duration::from_secs(10)).await;
        //     if !cancel_order_task {
        //         println!("发送订单任务.");
        //         let symbol = Symbol::new("TON");
        //         let order_id = OrderId::Id("183803159913828997".to_string());
        //         let post_cmd = AsyncCmd::CancelOrder(CancelOrderCmd { symbol, order_id });
        //         tx.send((1, post_cmd)).await?;
        //         cancel_order_task = true
        //     }
        // }

        let mut amend_order_task = false;

        loop {
            println!("Main thread is still alive");
            tokio::time::sleep(Duration::from_secs(10)).await;
            if !amend_order_task {
                println!("编辑订单任务.");
                let symbol = Symbol::new("TON");
                let mut order = Order::limit_open(
                    symbol,
                    4.7,
                    3.0,
                    PosSide::Short,
                    TimeInForce::GTC,
                    Exchange::GateSwap,
                );
                order.side = OrderSide::Sell;
                order.id = "183803159913897665".to_string();
                // let params = OrderParams::default();

                let amend_cmd = AsyncCmd::AmendOrder(order);
                tx.send((1, amend_cmd)).await?;

                amend_order_task = true
            }
        }
    }

    #[tokio::test]
    async fn test_wsapi_cancel_order_null() -> Result<()> {
        let _ = fmt().pretty().try_init();
        let h1 = DefaultAsyncResHandle;
        let config = test_config();
        let wsapi = GateSwapWsApi::new(config).await;
        let (tx, rx) = bounded(10);

        tokio::spawn(async move {
            match wsapi.run(0, h1, rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        // 等待连接建立

        // 测试下单接口
        let symbol = Symbol::new("BTC");
        let cid = exchange::Exchange::GateSwap.create_cid(None);
        println!("发送撤单请求");
        let cancel_order = AsyncCmd::CancelOrder(CancelOrderCmd {
            order_id: OrderId::ClientOrderId(cid),
            symbol,
        });
        tx.send((3, cancel_order)).await?;
        tokio::time::sleep(Duration::from_secs(3)).await;

        Ok(())
    }
}

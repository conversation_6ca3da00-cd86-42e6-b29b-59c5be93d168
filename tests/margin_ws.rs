#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use log::info;
    use okx::margin::ws::OkxMarginWs;
    use quant_common::{base::*, test_config, Result};
    use tracing::level_filters::LevelFilter;
    use tracing_subscriber::fmt;

    fn custom_symbols() -> Vec<Symbol> {
        vec![Symbol::new("BTC"), Symbol::new("ETH"), Symbol::new("SOL")]
    }

    #[derive(Clone)]
    pub struct TestHandler;

    impl WsHandler for TestHandler {
        async fn on_bbo(&self, bbo: BboTicker, exchange: Exchange) -> quant_common::Result<()> {
            info!("{exchange} bbo: {:?}", bbo);
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_pub() -> Result<()> {
        fmt().pretty().init();
        let config = ExConfig::default();
        let mut ws = OkxMarginWs::new(config).await;
        let symbols = Arc::new(custom_symbols());
        let subs = Subscribes::new(
            0,
            TestHandler,
            vec![
                SubscribeChannel::MarkPrice(symbols.clone()),
                SubscribeChannel::Bbo(symbols.clone()),
                SubscribeChannel::Depth(DepthWsParams {
                    symbols: symbols.clone(),
                    levels: 5,
                }),
                SubscribeChannel::Trade(symbols.clone()),
            ],
        );
        ws.run(subs).await?;
        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_private() -> quant_common::Result<()> {
        fmt().with_max_level(LevelFilter::DEBUG).pretty().init();
        let config = test_config();
        let mut ws = OkxMarginWs::new(config).await;
        let symbols = Arc::new(custom_symbols());
        let subs = Subscribes::new(
            0,
            DefaultWsHandler,
            vec![
                // SubscribeChannel::Balance,
                SubscribeChannel::Order(symbols.clone()),
            ],
        );
        ws.run(subs).await?;
        Ok(())
    }
}

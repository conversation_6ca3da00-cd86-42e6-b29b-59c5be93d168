use log::info;
use quant_common::{Result, base::WsH<PERSON><PERSON>};

#[cfg(test)]
mod tests {
    use std::sync::Arc;
    use std::time::Duration;

    use gate::margin::ws::GateMarginWs;
    use quant_common::base::*;
    use quant_common::*;
    use tokio::time::timeout;
    use tracing_subscriber::fmt;

    use crate::MyHandler;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_all() -> Result<()> {
        fmt().pretty().init();
        let config = ExConfig::default();
        let mut ws = GateMarginWs::new(config).await;
        let symbols = vec![Symbol::new("BTC")];

        // let subs = Subscribes::default_spot(Arc::new(symbols));
        let subs = Subscribes::new(1, <PERSON><PERSON>and<PERSON>, vec![SubscribeChannel::Bbo(Arc::new(symbols))]);
        // time out 5 seconds
        match timeout(Duration::from_secs(5), ws.run(subs)).await {
            Ok(Ok(_)) => {
                println!("WebSocket finished within 5 seconds.");
            }
            Ok(Err(_)) => {
                println!("WebSocket timeout.");
            }
            Err(_) => {
                println!("WebSocket error.");
            }
        }
        Ok(())
    }
}

#[derive(Debug, Clone, Default)]
struct MyHandler;
impl WsHandler for MyHandler {
    async fn on_bbo(
        &self,
        bbo: quant_common::base::BboTicker,
        exchange: quant_common::base::Exchange,
    ) -> Result<()> {
        info!("Received BBO: {bbo:?} from exchange: {exchange:?}");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;
    use std::{fs::read_to_string, time::Duration};

    use binance::margin::rest::BinanceMargin;
    use binance::margin::ws::BinanceMarginWs;
    use quant_common::Result;
    use quant_common::base::{model::*, *};
    use tokio::time::timeout;
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        toml::from_str(&read_to_string("margin.toml").unwrap()).unwrap()
    }

    #[tokio::test]
    async fn test_pub() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig {
            is_testnet: false,
            ..ExConfig::default()
        };
        let mut ws = BinanceMarginWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("BNB"), Symbol::new("ETH")]);
        let channels = vec![
            SubscribeChannel::Bbo(symbols.clone()),
            SubscribeChannel::Depth(DepthWsParams::new(symbols.clone(), 5)),
            SubscribeChannel::Trade(symbols.clone()),
        ];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        let ret = timeout(Duration::from_secs(30), ws.run(subs)).await;
        assert!(ret.is_err());
    }

    #[tokio::test]
    #[ignore = "需要观察日志"]
    async fn test_pub2() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = ExConfig {
            is_testnet: false,
            ..ExConfig::default()
        };
        let mut ws = BinanceMarginWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("BNB"), Symbol::new("ETH")]);
        let channels = vec![
            SubscribeChannel::Bbo(symbols.clone()),
            SubscribeChannel::Depth(DepthWsParams::new(symbols.clone(), 5)),
            SubscribeChannel::Trade(symbols.clone()),
        ];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    async fn test_all() -> Result<()> {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let symbols = Arc::new(vec![Symbol::new("BTC"), Symbol::new("ETH")]);
        let mut ws = BinanceMarginWs::new(config).await;
        let subs = Subscribes::default_spot(symbols);
        let ret = timeout(Duration::from_secs(10), ws.run(subs)).await;
        assert!(ret.is_err());
        Ok(())
    }

    #[tokio::test]
    #[ignore = "需要观察日志"]
    async fn test_pub_channel() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = ExConfig {
            is_testnet: false,
            ..ExConfig::default()
        };
        let mut ws = BinanceMarginWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("BNB"), Symbol::new("ETH")]);
        let channels = vec![
            SubscribeChannel::Bbo(symbols.clone()),
            SubscribeChannel::Depth(DepthWsParams::new(symbols.clone(), 5)),
            SubscribeChannel::Trade(symbols.clone()),
        ];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    async fn test_ws_kline() {
        let _ = fmt().with_target(false).try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()
        };
        let mut ws = BinanceMarginWs::new(config.clone()).await;
        let symbols = Arc::new(
            vec!["ETH", "SATS"]
                .into_iter()
                .map(Symbol::new)
                .collect::<Vec<_>>(),
        );
        let channels = [KlineInterval::Min1, KlineInterval::Day1]
            .into_iter()
            .map(|interval| (symbols.clone(), interval))
            .map(|(symbols, interval)| SubscribeChannel::Kline(KlineWsParams { symbols, interval }))
            .collect();
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        let ret = timeout(Duration::from_secs(65), ws.run(subs)).await;
        assert!(ret.is_err());
    }

    #[tokio::test]
    #[ignore = "需要观察日志"]
    async fn test_pri() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let symbols = Arc::new(vec![Symbol::new("BTC")]);
        let channels = vec![
            SubscribeChannel::Order(symbols.clone()),
            SubscribeChannel::Balance,
        ];
        let mut ws = BinanceMarginWs::new(config).await;
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn debug_symbols_ws_depth() -> Result<()> {
        let _ = fmt().try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()
        };
        let mut ws = BinanceMarginWs::new(config.clone()).await;
        let rest = BinanceMargin::new(config).await;
        let xs = rest.get_instruments().await?;
        let symbols = xs
            .into_iter()
            .map(|x| x.symbol)
            .take(20)
            .collect::<Vec<_>>();
        let symbols = Arc::new(symbols);

        #[derive(Clone)]
        struct DepthWsHandler;

        impl WsHandler for DepthWsHandler {
            async fn on_depth(&self, depth: Depth, exchange: Exchange) -> Result<()> {
                tracing::info!(
                    "------------------- {} @ {} -------------------",
                    depth.symbol,
                    exchange
                );
                for ask in depth.asks.iter().rev() {
                    tracing::info!("asks: {:.2} @ {:.2}", ask.amount * ask.price, ask.price);
                }
                for bid in depth.bids {
                    tracing::info!("bids: {:.2} @ {:.2}", bid.amount * bid.price, bid.price);
                }
                Ok(())
            }
        }

        let subs = Subscribes::new(
            0,
            DepthWsHandler,
            vec![SubscribeChannel::Depth(DepthWsParams::new(symbols, 1))],
        );
        ws.run(subs).await
    }
}

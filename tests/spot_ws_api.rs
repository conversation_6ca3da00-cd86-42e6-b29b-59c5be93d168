#[cfg(test)]
mod tests {
    use async_channel::bounded;
    use async_channel::unbounded;
    use async_channel::Sender;

    use quant_common::base::traits::ws_api::*;
    use quant_common::base::*;
    use quant_common::test_config;
    use quant_common::Result;
    use tokio::spawn;
    use tracing::info;
    use tracing::level_filters::LevelFilter;
    use tracing_subscriber::fmt;

    use okx::spot::ws_api::*;

    trait TestAsyncHandler {
        async fn handle_post_order(&self, req_id: u64, order: PlaceOrderResult) -> Result<()>;
        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_amend_order(&self, req_id: u64, order: AmendOrderResult) -> Result<()>;
        async fn handle_cancel_order(&self, req_id: u64, order: CancelOrderResult) -> Result<()>;
        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
    }

    struct WrapHandler<H: TestAsyncHandler> {
        handler: H,
    }

    impl<H: TestAsyncHandler> From<H> for WrapHandler<H> {
        fn from(handler: H) -> Self {
            Self { handler }
        }
    }

    impl<H: TestAsyncHandler + Send + Sync + 'static> AsyncResHandle for WrapHandler<H> {
        async fn handle_post_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: PlaceOrderResult,
        ) -> Result<()> {
            self.handler.handle_post_order(req_id, result).await
        }

        async fn handle_post_batch_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_post_batch_order(req_id, orders).await
        }

        async fn handle_amend_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: AmendOrderResult,
        ) -> Result<()> {
            self.handler.handle_amend_order(req_id, result).await
        }

        async fn handle_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: CancelOrderResult,
        ) -> Result<()> {
            self.handler.handle_cancel_order(req_id, result).await
        }

        async fn handle_batch_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_batch_cancel_order(req_id, orders).await
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler
                .handle_batch_cancel_order_by_ids(req_id, orders)
                .await
        }
    }

    struct PostOrderHandler {
        finish_tx: Sender<()>,
    }

    impl TestAsyncHandler for PostOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {:?}", order_id);
                self.finish_tx.send(()).await?;
            } else {
                info!("下单失败: {req_id} {:?}", result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_order() -> Result<()> {
        fmt().pretty().with_max_level(LevelFilter::DEBUG).init();

        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let account_id = 1;
        spawn(async move {
            let ws_api = OkxSpotWsApi::new(config).await;
            ws_api
                .run(
                    account_id,
                    WrapHandler::from(PostOrderHandler { finish_tx }),
                    rx,
                )
                .await
                .unwrap();
        });

        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol,
            100.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::OkxSpot,
        );
        // let order = Order::market_open(symbol, 0.1, PosSide::Long, Exchange::OkxSpot);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_post_order() -> Result<()> {
        fmt().pretty().with_max_level(LevelFilter::DEBUG).init();

        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let account_id = 1;
        spawn(async move {
            let ws_api = OkxSpotWsApi::new(config).await;
            ws_api
                .run(
                    account_id,
                    WrapHandler::from(PostOrderHandler { finish_tx }),
                    rx,
                )
                .await
                .unwrap();
        });
        let mut orders = Vec::new();
        let symbol = Symbol::new("SOL");
        for i in 0..10 {
            let order = Order::limit_open(
                symbol.clone(),
                100. + i as f64,
                0.1,
                PosSide::Long,
                TimeInForce::GTC,
                Exchange::OkxSpot,
            );
            orders.push(order);
        }
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::BatchPlaceOrder(BatchPlaceOrderCmd { orders, params });
        tx.send((1, post_cmd)).await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    struct CancelOrderHandler {
        tx: Sender<(u64, AsyncCmd)>,
        finish_tx: Sender<()>,
    }

    impl TestAsyncHandler for CancelOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {:?}", order_id);
                let cancel_order = AsyncCmd::CancelOrder(CancelOrderCmd {
                    symbol: Symbol::new("SOL"),
                    order_id: OrderId::Id(order_id.clone()),
                });
                self.tx.send((req_id + 1, cancel_order)).await?;
            } else {
                info!("下单失败: {req_id} {:?}", result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            self.finish_tx.send(()).await?;
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() -> Result<()> {
        fmt().pretty().init();

        let config = test_config();
        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol,
            102.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::OkxSpot,
        );
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let ws_api = OkxSpotWsApi::new(config).await;
            let account_id = 1;
            let handler = CancelOrderHandler { finish_tx, tx };
            match ws_api.run(account_id, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    // #[tokio::test]
    // #[ignore = "need proxy"]
    // async fn test_batch_cancel_order() -> Result<()> {
    //     fmt().pretty().with_max_level(LevelFilter::DEBUG).init();

    //     let config = test_config();
    //     let (tx, rx) = bounded(10);

    //     let symbol = Symbol::new("SOL");
    //     let mut orders = Vec::new();
    //     for i in 0..10 {
    //         let order = Order::limit_open(
    //             symbol.clone(),
    //             100. + i as f64,
    //             0.1,
    //             PosSide::Long,
    //             TimeInForce::GTC,
    //             Exchange::OkxSpot,
    //         );
    //         orders.push(order);
    //     }
    //     let params = OrderParams::default();
    //     let post_cmd = AsyncCommand::PostBatchOrder { orders, params };
    //     tx.send((1, post_cmd)).await?;

    //     let (finish_tx, finish_rx) = bounded(1);
    //     spawn(async move {
    //         let ws_api = OkxSpotWsApi::new(config);
    //         let handler = CancelOrderHandler { finish_tx, tx };
    //         match ws_api.run(WrapHandler::from(handler), rx).await {
    //             Ok(()) => (),
    //             Err(e) => panic!("Error: {e}"),
    //         }
    //     });

    //     finish_rx.recv().await.unwrap();

    //     Ok(())
    // }

    struct AmendOrderHandler {
        amend_order: Order,
        tx: Sender<(u64, AsyncCmd)>,
        finish_tx: Sender<()>,
    }

    impl TestAsyncHandler for AmendOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {:?}", order_id);
                let mut order_clone = self.amend_order.clone();
                order_clone.id = order_id.clone();
                let amend_order = AsyncCmd::AmendOrder(order_clone);
                self.tx.send((req_id + 1, amend_order)).await?;
            } else {
                info!("下单失败: {req_id} {:?}", result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_amend_order() -> Result<()> {
        fmt().pretty().init();

        let config = test_config();
        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol,
            100.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::OkxSpot,
        );
        let amend_order = Order {
            price: Some(101.),
            ..order.clone()
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let ws_api = OkxSpotWsApi::new(config).await;
            let amend_handler = AmendOrderHandler {
                amend_order,
                finish_tx,
                tx,
            };
            let account_id = 1;
            match ws_api
                .run(account_id, WrapHandler::from(amend_handler), rx)
                .await
            {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }
}

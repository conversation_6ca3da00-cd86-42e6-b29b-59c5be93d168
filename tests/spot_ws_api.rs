#[cfg(test)]
mod tests {
    use std::time::Duration;

    use async_channel::unbounded;
    use hyperliquid::spot::ws_api::HyperLiquidSpotWsApi;
    use hyperliquid::util::generate_128bit_hex;
    use log::info;
    use quant_common::base::traits::ws_api::{AsyncResHandle, WebSocketAPI};
    use quant_common::base::{
        AmendOrderResult, AsyncCmd, BatchCancelOrderByIdsCmd, BatchOrderRsp, BatchPlaceOrderCmd,
        CancelOrderCmd, CancelOrderResult, ExConfig, Order, OrderId, OrderParams, OrderSide,
        OrderType, PlaceOrderCmd, PlaceOrderResult, PosSide, Symbol, TimeInForce,
    };
    use quant_common::error::Result;
    use quant_common::rand_i64;
    use tokio::spawn;
    use tokio::time::sleep;
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        let toml_str = r#"
        exchange = "HyperLiquidSpot"
        key = "0xbc5d5e80060572c750397ca629d9790a82cdcb16"
        secret ="16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808"
        passphrase = "passphrase"
        is_colo = false
        is_testnet = false
        is_unified = false
        params = { is_vip = "false", cookie = "some_cookie", code = "1234", org_id = "5678" }
    "#;
        toml::from_str(&toml_str).unwrap()
    }

    #[derive(Debug, Clone)]
    struct WsApiTask {
        tx: async_channel::Sender<(u64, AsyncCmd)>,
        finish_tx: async_channel::Sender<()>,
    }
    impl AsyncResHandle for WsApiTask {
        async fn handle_post_order(
            &self,
            account_id: usize,
            req_id: u64,
            res: PlaceOrderResult,
        ) -> Result<()> {
            info!(
                "post order account_id={},req_id={}, res={:?}",
                account_id, req_id, res
            );
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            account_id: usize,
            req_id: u64,
            res: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!(
                "post batch order account_id={},req_id={}, res={:?}",
                account_id, req_id, res
            );
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_amend_order(
            &self,
            account_id: usize,
            req_id: u64,
            res: AmendOrderResult,
        ) -> Result<()> {
            info!(
                "amend order account_id={},req_id={}, res={:?}",
                account_id, req_id, res
            );
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_cancel_order(
            &self,
            account_id: usize,
            req_id: u64,
            res: CancelOrderResult,
        ) -> Result<()> {
            info!(
                "cancel order account_id={},req_id={}, res={:?}",
                account_id, req_id, res
            );
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            account_id: usize,
            req_id: u64,
            res: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!(
                "batch cancel order account_id={},req_id={}, res={:?}",
                account_id, req_id, res
            );
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            account_id: usize,
            req_id: u64,
            res: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!(
                "batch cancel order by ids account_id={},req_id={}, res={:?}",
                account_id, req_id, res
            );
            self.finish_tx.send(()).await?;
            Ok(())
        }
    }

    impl WsApiTask {
        fn new(
            tx: async_channel::Sender<(u64, AsyncCmd)>,
            finish_tx: async_channel::Sender<()>,
        ) -> Self {
            Self { tx, finish_tx }
        }
        //下单
        pub(crate) async fn test_post_order(
            &self,
            position_side: PosSide,
            time_in_force: TimeInForce,
            price: f64,
            qty: f64,
        ) -> Result<()> {
            let mut order = match position_side {
                PosSide::Long => Order::market_open(
                    Symbol {
                        base: "BTC".to_string(),
                        quote: quant_common::base::QuoteCcy::USDC,
                    },
                    qty,
                    quant_common::base::PosSide::Long,
                    quant_common::base::Exchange::BitfinexSpot,
                ),
                PosSide::Short => Order::market_open(
                    Symbol {
                        base: "BTC".to_string(),
                        quote: quant_common::base::QuoteCcy::USDC,
                    },
                    qty,
                    quant_common::base::PosSide::Short,
                    quant_common::base::Exchange::BitfinexSpot,
                ),
            };
            order.order_type = OrderType::Limit; //下市价单，不管time_in_force是什么，他都会直接按提供的价格比配，比配不上会直接撤单，相当于下Limit FoK 单，
            order.cid = Some(generate_128bit_hex());
            order.time_in_force = time_in_force;
            order.price = Some(price); //注意市价单也要传价格，不然无法下单。 //97,073   // 10.40820
            let req_id = rand_i64() as u64;
            let params = OrderParams::new(false, 1);
            let order = PlaceOrderCmd { order, params };
            info!("will post order={:?}", order);
            self.tx
                .send((req_id, AsyncCmd::PlaceOrder(order)))
                .await
                .expect("send order error");
            Ok(())
        }
        //批量下单
        pub(crate) async fn test_post_batch_order(&self) -> Result<String> {
            let mut orders = vec![];
            let req_id = rand_i64() as u64;
            let mut order_ioc = Order::market_open(
                Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                0.00012,
                quant_common::base::PosSide::Long,
                quant_common::base::Exchange::BitfinexSpot,
            );
            order_ioc.cid = Some(generate_128bit_hex());
            order_ioc.order_type = OrderType::Limit;
            order_ioc.time_in_force = TimeInForce::IOC;
            order_ioc.price = Some(92401.);
            orders.push(order_ioc);
            let mut order_fok = Order::market_open(
                Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                0.00012,
                quant_common::base::PosSide::Long,
                quant_common::base::Exchange::BitfinexSpot,
            );
            order_fok.cid = Some(generate_128bit_hex());
            order_fok.order_type = OrderType::Limit;
            order_fok.time_in_force = TimeInForce::FOK;
            order_fok.price = Some(92403.);
            orders.push(order_fok);
            let mut order_gtc = Order::market_open(
                Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                0.00012,
                quant_common::base::PosSide::Long,
                quant_common::base::Exchange::BitfinexSpot,
            );
            order_gtc.cid = Some(generate_128bit_hex());
            order_gtc.order_type = OrderType::Limit;
            order_gtc.time_in_force = TimeInForce::GTC;
            order_gtc.side = OrderSide::Sell;
            order_gtc.price = Some(92406.);
            orders.push(order_gtc);
            let orders = BatchPlaceOrderCmd {
                orders,
                params: OrderParams::new(false, 1),
            };
            info!("will post batch orders={:?}", orders);
            self.tx
                .send((req_id, AsyncCmd::BatchPlaceOrder(orders)))
                .await?;
            Ok(req_id.to_string())
        }
        //修改订单
        pub(crate) async fn test_amend_order(
            &self,
            order_id: String,
            cid: Option<String>,
        ) -> Result<String> {
            let order = Order {
                id: order_id,
                price: Some(92402.),
                amount: Some(0.00014),
                order_type: OrderType::Limit,
                cid,
                side: OrderSide::Buy,
                symbol: Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                ..Default::default()
            };
            let req_id = rand_i64() as u64;
            info!("will amend order={:?}", order);
            self.tx.send((req_id, AsyncCmd::AmendOrder(order))).await?;
            Ok(req_id.to_string())
        }
        //取消订单
        pub(crate) async fn test_cancel_order(&self, order_id: OrderId) -> Result<String> {
            let req_id = rand_i64() as u64;
            let info = CancelOrderCmd {
                symbol: Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                order_id,
            };
            info!("will cancel order={:?}", info);
            self.tx.send((req_id, AsyncCmd::CancelOrder(info))).await?;
            Ok(req_id.to_string())
        }
        //批量取消订单
        pub(crate) async fn test_batch_cancel_order(&self, symbol: Symbol) -> Result<String> {
            let req_id = rand_i64() as u64;
            info!("will batch cancel order={:?}", symbol);
            self.tx
                .send((req_id, AsyncCmd::BatchCancelOrder(symbol)))
                .await?;
            Ok(req_id.to_string())
        }
        //批量取消订单id
        pub(crate) async fn test_batch_cancel_order_by_ids(
            &self,
            ids: Vec<String>,
        ) -> Result<String> {
            let req_id = rand_i64() as u64;
            let info = BatchCancelOrderByIdsCmd {
                symbol: Some(Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                }),
                ids: Some(ids),
                cids: None,
            };
            info!("will batch cancel order={:?}", info);
            self.tx
                .send((req_id, AsyncCmd::BatchCancelOrderByIds(info)))
                .await?;
            Ok(req_id.to_string())
        }
    }

    //测试下单
    #[tokio::test]
    async fn test_ws_post_order() {
        fmt().pretty().init();
        let account_id = 1;
        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let task = WsApiTask::new(tx, finish_tx);
        let handler = task.clone();
        spawn(async move {
            let ws_api = HyperLiquidSpotWsApi::new(config).await;
            ws_api
                .run(account_id, handler, rx)
                .await
                .expect("run ws api error");
        });
        sleep(Duration::from_secs(5)).await; //sleep 为了确保 ws_api客户端启动成功
        task.test_post_order(PosSide::Long, TimeInForce::GTC, 92400.0, 0.00012)
            .await
            .expect("post order error");
        finish_rx.recv().await.expect("recv error");
    }

    /// 测试批量下单
    #[tokio::test]
    async fn test_ws_post_batch_order() {
        fmt().pretty().init();
        let account_id = 1;
        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let task = WsApiTask::new(tx, finish_tx);
        let handler = task.clone();
        spawn(async move {
            sleep(Duration::from_secs(10)).await;
            task.test_post_batch_order()
                .await
                .expect("post batch order error");
            finish_rx.recv().await.expect("finish_rx reve error");
        });
        let ws_api = HyperLiquidSpotWsApi::new(config).await;
        ws_api
            .run(account_id, handler, rx)
            .await
            .expect("run ws api error");
    }

    #[tokio::test]
    async fn test_ws_amend_order() {
        fmt().pretty().init();
        let account_id = 1;
        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let task = WsApiTask::new(tx, finish_tx);
        let handler = task.clone();
        spawn(async move {
            let ws_api = HyperLiquidSpotWsApi::new(config).await;
            ws_api
                .run(account_id, handler, rx)
                .await
                .expect("run ws api error");
        });
        sleep(Duration::from_secs(5)).await;
        task.test_amend_order(
            "************".to_string(),
            Some("0xeba2c4b5f026a2b2c58a1b2e87644736".to_string()),
        )
        .await
        .expect("amend order error");
        finish_rx.recv().await.expect("recv error");
    }

    #[tokio::test]
    async fn test_ws_cancel_order() {
        fmt().pretty().init();
        let account_id = 1;
        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let task = WsApiTask::new(tx, finish_tx);
        let handler = task.clone();
        spawn(async move {
            let ws_api = HyperLiquidSpotWsApi::new(config).await;
            ws_api
                .run(account_id, handler, rx)
                .await
                .expect("run ws api error");
        });
        sleep(Duration::from_secs(5)).await;
        task.test_cancel_order(OrderId::ClientOrderId(
            "0xeba2c4b5f026a2b2c58a1b2e87644736".to_string(),
        ))
        .await
        .expect("cancel order error");
        finish_rx.recv().await.expect("recv error");
    }

    #[tokio::test]
    async fn test_ws_cancel_orders() {
        fmt().pretty().init();
        let account_id = 1;
        let config = test_config();
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let task = WsApiTask::new(tx, finish_tx);
        let handler = task.clone();
        spawn(async move {
            let ws_api = HyperLiquidSpotWsApi::new(config).await;
            ws_api
                .run(account_id, handler, rx)
                .await
                .expect("run ws api error");
        });
        sleep(Duration::from_secs(5)).await;
        //{"status":"ok","response":{"type":"order","data":{"statuses":[{"resting":{"oid":***********,"cloid":"0xf4ed0137b512d40483247544a550e63a"}}]}}}
        task.test_batch_cancel_order(Symbol {
            base: "BTC".to_string(),
            quote: quant_common::base::QuoteCcy::USDC,
        })
        .await
        .expect("batch cancel order error");
        finish_rx.recv().await.expect("recv error"); //************
    }
    #[tokio::test]
    async fn test_ws_cancel_by_ids() {
        fmt().pretty().init();
        let config = test_config();
        let account_id = 1;
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let task = WsApiTask::new(tx, finish_tx);
        let handler = task.clone();
        spawn(async move {
            let ws_api = HyperLiquidSpotWsApi::new(config).await;
            ws_api
                .run(account_id, handler, rx)
                .await
                .expect("run ws api error");
        });
        sleep(Duration::from_secs(5)).await;
        // {"status":"ok","response":{"type":"order","data":{"statuses":[{"resting":{"oid":***********,"cloid":"0x891a133adc7d290e6dc82ffd44c0288c"}}]}}}
        task.test_batch_cancel_order_by_ids(vec!["***********".to_string()])
            .await
            .expect("batch cancel order by ids error");
        finish_rx.recv().await.expect("recv error");
    }
}

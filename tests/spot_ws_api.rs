#[cfg(test)]
mod tests {
    use async_channel::Sender;
    use async_channel::bounded;
    use async_channel::unbounded;
    use binance::util::sign_ws_api_params;
    use quant_common::Result;
    use quant_common::base::traits::ws_api::*;
    use quant_common::base::*;
    use serde_json::json;
    use std::fs::read_to_string;
    use tokio::spawn;
    use tracing::info;
    use tracing_subscriber::fmt;

    use binance::spot::ws_api::BinanceSpotWsApi;

    fn test_config() -> ExConfig {
        toml::from_str(&read_to_string("spot.toml").unwrap()).unwrap()
    }

    trait TestAsyncHandler {
        async fn handle_post_order(&self, req_id: u64, order: PlaceOrderResult) -> Result<()> {
            match order.result {
                Ok(order_id) => info!("下单成功: {req_id} {:?}", order_id),
                Err(e) => info!("下单失败: {req_id} {:?}", e),
            }
            Ok(())
        }
        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            match orders {
                Ok(orders) => info!("批量下单成功: {req_id} {:?}", orders),
                Err(e) => info!("批量下单失败: {req_id} {:?}", e),
            }
            Ok(())
        }
        async fn handle_amend_order(&self, req_id: u64, order: AmendOrderResult) -> Result<()> {
            match order.result {
                Ok(order_id) => info!("修改订单成功: {req_id} {:?}", order_id),
                Err(e) => info!("修改订单失败: {req_id} {:?}", e),
            }
            Ok(())
        }
        async fn handle_cancel_order(&self, req_id: u64, order: CancelOrderResult) -> Result<()> {
            match order.result {
                Ok(order_id) => info!("撤单成功: {req_id} {:?}", order_id),
                Err(e) => info!("撤单失败: {req_id} {:?}", e),
            }
            Ok(())
        }
        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            match orders {
                Ok(orders) => info!("批量撤单成功: {req_id} {:?}", orders),
                Err(e) => info!("批量撤单失败: {req_id} {:?}", e),
            }
            Ok(())
        }
        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            match orders {
                Ok(orders) => info!("批量撤单成功: {req_id} {:?}", orders),
                Err(e) => info!("批量撤单失败: {req_id} {:?}", e),
            }
            Ok(())
        }
    }

    struct WrapHandler<H: TestAsyncHandler> {
        handler: H,
    }

    impl<H: TestAsyncHandler> From<H> for WrapHandler<H> {
        fn from(handler: H) -> Self {
            Self { handler }
        }
    }

    #[allow(unused_variables)]
    impl<H: TestAsyncHandler + Send + Sync + 'static> AsyncResHandle for WrapHandler<H> {
        async fn handle_post_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: PlaceOrderResult,
        ) -> Result<()> {
            self.handler.handle_post_order(req_id, result).await
        }

        async fn handle_post_batch_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_post_batch_order(req_id, orders).await
        }

        async fn handle_amend_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: AmendOrderResult,
        ) -> Result<()> {
            self.handler.handle_amend_order(req_id, result).await
        }

        async fn handle_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: CancelOrderResult,
        ) -> Result<()> {
            self.handler.handle_cancel_order(req_id, result).await
        }

        async fn handle_batch_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_batch_cancel_order(req_id, orders).await
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler
                .handle_batch_cancel_order_by_ids(req_id, orders)
                .await
        }
    }

    #[test]
    fn test_sign_ws_api_req() {
        let _ = fmt().pretty().try_init();

        let value = json!( {
            "symbol":           "BTCUSDT",
            "side":             "SELL",
            "type":             "LIMIT",
            "timeInForce":      "GTC",
            "quantity":         "0.********",
            "price":            "52000.00",
            "newOrderRespType": "ACK",
            "recvWindow":       100,
            "timestamp":        1645423376532u64,
            "apiKey":           "vmPUZE6mv9SD5VNHk4HlWFsOr6aKE2zvsw0MuIgwCIPy6utIco14y7Ju91duEh8A",
        });
        let check = "cc15477742bd704c29492d96c7ead9414dfd8e0ec4a00f947bb5bb454ddbd08a";

        let signature = sign_ws_api_params(
            "NhqPtmdSJYdKjVHjA7PZj4Mge3R5YNiP1e3UZjInClVN65XAbvqqM6A7H5fATj0j",
            &value,
        )
        .unwrap();
        assert_eq!(signature, check);
    }

    struct PostOrderHandler {
        finish_tx: Sender<()>,
    }

    impl TestAsyncHandler for PostOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            let order_id = result.result?;
            info!("下单成功: {req_id} {:?}", order_id);
            self.finish_tx.send(()).await?;
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_post_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol,
            50000.,
            0.0001,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSpot,
        );
        tx.send((
            1,
            AsyncCmd::PlaceOrder(PlaceOrderCmd {
                order,
                params: OrderParams::default(),
            }),
        ))
        .await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_post_only_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol,
            50000.,
            0.0001,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSpot,
        );
        let order = Order {
            time_in_force: TimeInForce::PostOnly,
            ..order
        };
        tx.send((
            1,
            AsyncCmd::PlaceOrder(PlaceOrderCmd {
                order,
                params: OrderParams::default(),
            }),
        ))
        .await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_post_market_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BTC");
        let order = Order::market_open(symbol, 0.0001, PosSide::Long, Exchange::BinanceSpot);
        tx.send((
            1,
            AsyncCmd::PlaceOrder(PlaceOrderCmd {
                order,
                params: OrderParams::default(),
            }),
        ))
        .await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_market_with_price_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        spawn(async move {
            let handler = WrapHandler::from(PostOrderHandler { finish_tx });
            ws_api.run(0, handler, rx).await.unwrap();
        });

        let symbol = Symbol::new("BTC");
        let order = Order {
            price: Some(50000.0),
            ..Order::market_open(symbol, 0.0001, PosSide::Long, Exchange::BinanceSpot)
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_cancel_order() -> Result<()> {
        let _ = fmt().try_init();

        struct CancelOrderHandler {
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for CancelOrderHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                if let Ok(order_id) = &result.result {
                    info!("下单成功: {req_id} {:?}", order_id);
                    let cancel_order = AsyncCmd::CancelOrder(CancelOrderCmd {
                        symbol: Symbol::new("BTC"),
                        order_id: OrderId::Id(order_id.clone()),
                    });
                    self.tx.send((req_id + 1, cancel_order)).await?;
                } else {
                    info!("下单失败: {req_id} {:?}", result.result);
                }
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                self.finish_tx.send(()).await?;
                Ok(())
            }
        }

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol,
            50000.,
            0.0001,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSpot,
        );
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let handler = CancelOrderHandler { finish_tx, tx };
            match ws_api.run(0, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_cancel_order_by_cid() -> Result<()> {
        struct CancelOrderByCidHandler {
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for CancelOrderByCidHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                let order_id = result.result.unwrap();
                info!("下单成功: {req_id} {:?}", order_id);
                let cancel_order = AsyncCmd::CancelOrder(CancelOrderCmd {
                    symbol: Symbol::new("BTC"),
                    order_id: OrderId::ClientOrderId(result.order.cid.clone().unwrap()),
                });
                self.tx.send((req_id + 1, cancel_order)).await?;
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                self.finish_tx.send(()).await?;
                Ok(())
            }
        }

        let _ = fmt().try_init();

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol,
            50000.,
            0.0001,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSpot,
        );
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let handler = CancelOrderByCidHandler { finish_tx, tx };
            match ws_api.run(0, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_amend_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        struct AmendOrderHandler {
            amend_order: Order,
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for AmendOrderHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                let order_id = &result.result?;
                info!("下单成功: {req_id} {:?}", order_id);
                let order = Order {
                    id: order_id.clone(),
                    ..self.amend_order.clone()
                };
                let amend_order = AsyncCmd::AmendOrder(order);
                self.tx.send((req_id + 1, amend_order)).await?;
                Ok(())
            }

            async fn handle_amend_order(
                &self,
                req_id: u64,
                result: AmendOrderResult,
            ) -> Result<()> {
                let order_id = result.result?;
                info!("修改订单成功: {req_id} {order_id}");
                self.finish_tx.send(()).await?;
                Ok(())
            }
        }

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol,
            50000.,
            0.0001,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSpot,
        );
        let amend_order = Order {
            price: Some(51000.),
            ..order.clone()
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let amend_handler = AmendOrderHandler {
                amend_order,
                finish_tx,
                tx,
            };
            match ws_api.run(0, WrapHandler::from(amend_handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_amend_post_only_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        struct AmendOrderHandler {
            amend_order: Order,
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for AmendOrderHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                let order_id = &result.result?;
                info!("下单成功: {req_id} {:?}", order_id);
                let order = Order {
                    id: order_id.clone(),
                    ..self.amend_order.clone()
                };
                let amend_order = AsyncCmd::AmendOrder(order);
                self.tx.send((req_id + 1, amend_order)).await?;
                Ok(())
            }

            async fn handle_post_batch_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量下单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_amend_order(
                &self,
                req_id: u64,
                result: AmendOrderResult,
            ) -> Result<()> {
                let order_id = result.result?;
                info!("修改订单成功: {req_id} {order_id}");
                self.finish_tx.send(()).await?;
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                Ok(())
            }

            async fn handle_batch_cancel_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_batch_cancel_order_by_ids(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }
        }

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = bounded(10);

        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol,
            50000.,
            0.0001,
            PosSide::Long,
            TimeInForce::PostOnly,
            Exchange::BinanceSpot,
        );
        let amend_order = Order {
            price: Some(51000.),
            ..order.clone()
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await?;

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let amend_handler = AmendOrderHandler {
                amend_order,
                finish_tx,
                tx,
            };
            match ws_api.run(0, WrapHandler::from(amend_handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();

        Ok(())
    }

    #[tokio::test]
    async fn test_post_timestamp_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        struct PostOrderHandler {
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for PostOrderHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                let _ = req_id;
                assert!(result.result.is_err(), "timestamp不正确，不应该成功");
                self.finish_tx.send(()).await?;
                Ok(())
            }
        }

        let config = test_config();
        let ws_api = BinanceSpotWsApi::new(config).await;

        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol,
            50000.,
            0.0001,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BinanceSpot,
        );
        let order = Order {
            timestamp: 1, // 一个过时的timestamp(且不为0)
            ..order
        };
        tx.send((
            1,
            AsyncCmd::PlaceOrder(PlaceOrderCmd {
                order,
                params: OrderParams::default(),
            }),
        ))
        .await?;

        finish_rx.recv().await.unwrap();

        Ok(())
    }
}

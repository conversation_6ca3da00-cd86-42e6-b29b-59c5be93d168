#[cfg(test)]
mod tests {
    use async_channel::bounded;
    use gate::spot::ws_api::GateSpotWsApi;
    use quant_common::base::*;
    use quant_common::*;
    use std::time::Duration;
    use tracing_subscriber::fmt;

    #[tokio::test]
    async fn test_wsapi_cancel_order_null() -> Result<()> {
        let _ = fmt().pretty().try_init();
        let h1 = DefaultAsyncResHandle;
        let config = test_config();
        let wsapi = GateSpotWsApi::new(config).await;
        let (tx, rx) = bounded(10);

        tokio::spawn(async move {
            match wsapi.run(0, h1, rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        // 等待连接建立

        // 测试下单接口
        let symbol = Symbol::new("BTC");
        let cid = exchange::Exchange::GateSpot.create_cid(None);
        println!("发送撤单请求");
        let cancel_order = AsyncCmd::CancelOrder(CancelOrderCmd {
            order_id: OrderId::ClientOrderId(cid),
            symbol,
        });
        tx.send((3, cancel_order)).await?;
        tokio::time::sleep(Duration::from_secs(3)).await;

        Ok(())
    }

    #[tokio::test]
    async fn test_wsapi_post_order() -> Result<()> {
        let _ = fmt().pretty().try_init();
        let h1 = DefaultAsyncResHandle;
        let config = test_config();
        let wsapi = GateSpotWsApi::new(config).await;
        let (tx, rx) = bounded(10);

        tokio::spawn(async move {
            match wsapi.run(0, h1, rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        // 等待连接建立

        // 测试下单接口
        let symbol = Symbol::new("BTC");
        let cid = exchange::Exchange::GateSpot.create_cid(None);
        let mut order = Order {
            id: String::new(),
            cid: Some(cid.clone()),
            timestamp: 0,
            status: OrderStatus::Open,
            symbol: symbol.clone(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            pos_side: None,
            time_in_force: TimeInForce::GTC,
            price: Some(70000.0),
            amount: Some(0.00005),
            quote_amount: None,
            filled: 0.0,
            filled_avg_price: 0.0,
            source: OrderSource::Order,
        };
        let params = OrderParams::default();
        let place_order_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd {
            order: order.clone(),
            params,
        });

        println!("发送下单请求");
        tx.send((1, place_order_cmd)).await?;
        tokio::time::sleep(Duration::from_secs(1)).await;

        println!("发送改单请求");
        order.price = Some(24000.0);
        let amend_order = AsyncCmd::AmendOrder(order);
        tx.send((2, amend_order)).await?;
        // 等待下单响应
        tokio::time::sleep(Duration::from_secs(1)).await;

        println!("发送撤单请求");
        let cancel_order = AsyncCmd::CancelOrder(CancelOrderCmd {
            order_id: OrderId::ClientOrderId(cid),
            symbol,
        });
        tx.send((3, cancel_order)).await?;
        tokio::time::sleep(Duration::from_secs(1)).await;
        Ok(())
    }

    // #[tokio::test]
    // #[ignore = "需要代理, 只能在正式环境中运行"]
    // async fn test_wsapi() -> Result<()> {
    //     let _ = fmt().pretty().try_init();
    //     let h1 = DefaultAsyncResHandle;
    //     let config = test_config();
    //     let wsapi = GateSpotWsApi::new(config).await;
    //     let (tx, rx) = bounded(10);

    //     tokio::spawn(async move {
    //         match wsapi.run(0, h1, rx).await {
    //             Ok(()) => (),
    //             Err(e) => panic!("Error: {e}"),
    //         }
    //     });

    //     // 等待连接建立
    //     tokio::time::sleep(Duration::from_secs(2)).await;

    //     // 测试下单接口
    //     let symbol = Symbol {
    //         base: "BTC".to_string(),
    //         quote: QuoteCcy::USDT,
    //     };
    //     let order = Order {
    //         id: String::new(),
    //         cid: Some(String::new()),
    //         timestamp: 0,
    //         status: OrderStatus::Open,
    //         symbol: symbol.clone(),
    //         order_type: OrderType::Limit,
    //         side: OrderSide::Buy,
    //         pos_side: Some(PosSide::Long),
    //         time_in_force: TimeInForce::GTC,
    //         price: Some(25000.0),
    //         amount: 0.001,
    //         filled: 0.0,
    //         filled_avg_price: 0.0,
    //     };
    //     let params = OrderParams::default();
    //     let place_order_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });

    //     println!("发送下单请求");
    //     tx.send((1, place_order_cmd)).await?;

    //     // 等待下单响应
    //     tokio::time::sleep(Duration::from_secs(3)).await;

    //     // 测试取消订单接口
    //     // 注意：这里需要替换为实际的订单ID
    //     let order_id = OrderId::Id("12345678".to_string()); // 替换为实际订单ID
    //     let cancel_cmd = AsyncCmd::CancelOrder(CancelOrderCmd {
    //         symbol: symbol.clone(),
    //         order_id
    //     });

    //     println!("发送取消订单请求");
    //     tx.send((2, cancel_cmd)).await?;

    //     // 等待取消订单响应
    //     tokio::time::sleep(Duration::from_secs(3)).await;

    //     // 测试修改订单接口
    //     // 注意：这里需要替换为实际的订单ID
    //     let amend_order = Order {
    //         id: "87654321".to_string(), // 替换为实际订单ID
    //         cid: Some(String::new()),
    //         timestamp: 0,
    //         status: OrderStatus::Open,
    //         symbol: symbol.clone(),
    //         order_type: OrderType::Limit,
    //         side: OrderSide::Buy,
    //         pos_side: Some(PosSide::Long),
    //         time_in_force: TimeInForce::GTC,
    //         price: Some(24000.0),
    //         amount: 0.002,
    //         filled: 0.0,
    //         filled_avg_price: 0.0,
    //     };

    //     let amend_cmd = AsyncCmd::AmendOrder(amend_order);

    //     println!("发送修改订单请求");
    //     tx.send((3, amend_cmd)).await?;

    //     // 等待修改订单响应
    //     tokio::time::sleep(Duration::from_secs(3)).await;

    //     println!("测试完成");

    //     // 保持主线程运行一段时间，以便接收响应
    //     tokio::time::sleep(Duration::from_secs(10)).await;

    //     Ok(())
    // }
}

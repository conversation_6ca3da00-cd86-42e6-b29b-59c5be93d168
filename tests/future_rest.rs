// 模拟环境无法市价单平仓，会导致测试账号余额锁定。 同时影响swap测试。

#[cfg(test)]
mod tests {
    use binance::future::rest::*;
    use binance::util::{is_ed25519_secret, min_limit_amount_price, round};
    use log::info;
    use quant_common::{Result, base::*};
    use quant_common::{qerror, time_ms};
    use reqwest::Method;
    use std::{fs::read_to_string, time::Duration};
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        let s = read_to_string("future.toml").unwrap();
        let config: ExConfig = toml::from_str(&s).unwrap();
        if is_ed25519_secret(&config.secret) {
            info!("暂时不支持ed25519");
        }
        config
    }

    async fn btc_symbol(rest: &BinanceFuture) -> Result<Symbol> {
        rest.get_instruments()
            .await?
            .into_iter()
            .find(|i| i.symbol.base == "BTC")
            .map(|i| i.symbol)
            .ok_or_else(|| qerror!("BTC not found"))
    }

    #[cfg(test)]
    mod read {
        use binance::swap::model::PATH_TIME;
        use quant_common::base::traits::GetKlineParams;

        use super::*;

        #[tokio::test]
        async fn test_request() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            let req = UserRequest {
                method: Method::GET.to_string(),
                path: PATH_TIME.to_string(), // TODO
                auth: false,
                query: None,
                body: None,
                url: None,
                headers: None,
            };
            let res = rest.request(req).await.unwrap();
            info!("{}", sonic_rs::to_string(&res).unwrap());
        }

        #[tokio::test]
        async fn test_get_instruments() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            let instruments = rest.get_instruments().await.unwrap();
            info!("instruments len: {}", instruments.len());
            for instrument in instruments {
                info!("{:?}", instrument);
            }
        }

        async fn btcs_symbol(rest: &BinanceFuture) -> Result<Vec<Symbol>> {
            let xs = rest.get_instruments().await?;
            Ok(xs
                .into_iter()
                .filter(|i| i.symbol.base == "BTC")
                .map(|i| i.symbol)
                .collect::<Vec<_>>())
        }

        #[tokio::test]
        async fn test_get_ticker() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            let symbol = btc_symbol(&rest).await.unwrap();
            let ticker = rest.get_ticker(symbol).await.unwrap();
            info!("{:#?}", ticker);
        }

        fn to_wide_string(s: &str, len: usize) -> String {
            let mut r = String::with_capacity(len * s.len());
            r.push_str(&" ".to_string().repeat(len));
            r.push_str(s);
            r[(r.len() - len).max(0)..].to_string()
        }

        #[tokio::test]
        async fn test_get_tickers() {
            let _ = fmt().try_init();

            let config = ExConfig {
                is_testnet: false,
                ..ExConfig::default()
            };
            let rest = BinanceFuture::new(config).await;
            let tickers = rest.get_tickers().await.unwrap();
            let mut tickers = tickers
                .into_iter()
                .filter(|t| {
                    t.quote_volume > 10_000_000.0 && matches!(t.symbol.quote, QuoteCcy::USDT)
                })
                .collect::<Vec<_>>();
            tickers.sort_by(|a, b| {
                b.change_percent
                    .partial_cmp(&a.change_percent)
                    .unwrap_or(std::cmp::Ordering::Equal)
            });
            let (inc, dec) = tickers
                .iter()
                .fold((0., 0.), |(inc, dec), t| match t.change > 0.0 {
                    true => (inc + 1., dec),
                    false => (inc, dec + 1.),
                });
            let sum = inc + dec;
            let inc = inc / sum * 100.;
            let dec = dec / sum * 100.;
            info!("上涨: {inc:02.2}%, 下跌: {dec:2.2}%, 总数: {sum}");
            for t in tickers.iter() {
                let symbol = to_wide_string(&t.symbol.to_string(), 10);
                let close = to_wide_string(&format!("{:.2e}", t.close), 10);
                let volume = to_wide_string(&(format!("{:.2e}", t.volume)), 10);
                let quote_volume = to_wide_string(&(format!("{:.2e}", t.quote_volume)), 10);
                let change = to_wide_string(&format!("{:.2}%", t.change_percent), 10);
                info!(
                    "symbol: {symbol} \tchange: {change} \tclose: {close} \tvolume: {volume} \tquote_volume: {quote_volume}"
                );
            }
        }

        #[tokio::test]
        async fn test_get_klines_ext() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            for symbol in btcs_symbol(&rest).await.unwrap() {
                let params = GetKlineParams::new(symbol, KlineInterval::Min1).limit(3);
                let klines = rest.get_klines_ext(params).await.unwrap();
                info!("{} {:?}", klines.symbol, klines.interval);
                for kline in klines.candles {
                    info!("{:?}", kline);
                }
            }
        }

        #[tokio::test]
        async fn test_get_bbo() {
            let _ = fmt().pretty().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            let symbol = btc_symbol(&rest).await.unwrap();
            let ticker = rest.get_bbo_ticker(symbol).await.unwrap();
            info!("{:?}", ticker);
        }

        #[tokio::test]
        async fn test_get_bbos() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            let tickers = rest.get_bbo_tickers().await.unwrap();
            for ticker in tickers {
                info!("{:?}", ticker);
            }
        }

        #[tokio::test]
        async fn test_get_depth() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            for symbol in btcs_symbol(&rest).await.unwrap() {
                let depth = rest.get_depth(symbol, Some(5)).await.unwrap();
                info!("============= {} =============", depth.symbol);
                for ask in depth.asks {
                    info!(
                        "asks: {:?} @ {} => {}",
                        ask.amount,
                        ask.price,
                        ask.amount * ask.price
                    );
                }
                for bid in depth.bids {
                    info!(
                        "  bids: {:?} @ {} => {}",
                        bid.amount,
                        bid.price,
                        bid.amount * bid.price
                    );
                }
            }
        }

        #[tokio::test]
        async fn test_get_instrument() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            for symbol in btcs_symbol(&rest).await.unwrap() {
                let instrument = rest.get_instrument(symbol).await.unwrap();
                info!("{:?}", instrument);
                tokio::time::sleep(Duration::from_secs(1)).await;
            }
        }

        #[tokio::test]
        async fn test_min_qty() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceFuture::new(config).await;
            let symbol = btc_symbol(&rest).await.unwrap();
            let instrument = rest.get_instrument(symbol).await.unwrap();
            assert_eq!(instrument.min_qty, 0.001);
        }

        #[tokio::test]
        async fn test_get_open_orders() {
            let _ = fmt().try_init();

            let config = test_config();
            let rest = BinanceFuture::new(config).await;
            for symbol in btcs_symbol(&rest).await.unwrap() {
                let orders = rest.get_open_orders(symbol.clone()).await.unwrap();
                info!("{}: {}", symbol, orders.len());
                for order in orders {
                    info!("{:?}", order);
                }
            }
        }

        #[tokio::test]
        async fn test_get_all_open_orders() {
            let _ = fmt().try_init();

            let config = test_config();
            let rest = BinanceFuture::new(config).await;
            let orders = rest.get_all_open_orders().await.unwrap();
            info!("{}", orders.len());
            for order in orders {
                info!("{:?}", order);
            }
        }

        #[tokio::test]
        async fn test_get_orders() {
            let _ = fmt().try_init();

            let config = test_config();
            let rest = BinanceFuture::new(config).await;
            let symbol = btc_symbol(&rest).await.unwrap();
            let orders = rest.get_orders(symbol, 0, time_ms()).await.unwrap();
            info!("订单数量: {}", orders.len());
            for order in orders {
                info!("{:?}", order);
            }
        }

        #[tokio::test]
        async fn test_get_fee_rate() {
            let _ = fmt().try_init();

            let config = test_config();
            let rest = BinanceFuture::new(config).await;
            let symbol = btc_symbol(&rest).await.unwrap();
            let fee_rate = rest.get_fee_rate(symbol).await.unwrap();
            info!("{:#?}", fee_rate);
        }

        #[tokio::test]
        async fn test_get_position() {
            let _ = fmt().try_init();

            let config = test_config();
            let rest = BinanceFuture::new(config).await;
            let symbol = btc_symbol(&rest).await.unwrap();
            let positions = rest.get_position(symbol).await.unwrap();
            for position in positions {
                info!("{:#?}", position);
            }
        }

        #[tokio::test]
        async fn test_get_positions() -> Result<()> {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let rest = BinanceFuture::new(config).await;
            let positions = rest.get_positions().await?;
            for position in positions.iter().filter(|p| p.amount != 0.0) {
                info!("{:#?}", position);
            }
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_post_order() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let ps = PosSide::Long;
        let params = OrderParams::default();

        // //  无法平仓，建仓可能导致测试账号报废
        // // Market order
        // let order = Order::market_open(symbol.clone(), 0.01, ps, exchange);
        // let order_id = rest.post_order(order, params.clone()).await;
        // let order_id = order_id.unwrap();
        // info!("{:#?}", order_id);

        // tokio::time::sleep(Duration::from_secs(3)).await;

        // Limit order
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        let tif = TimeInForce::GTC;
        let order = Order::limit_open(symbol.clone(), price, amount, ps, tif, exchange);
        let order_id = rest.post_order(order.clone(), params.clone()).await;
        let order_id = order_id.unwrap();
        info!("GTC {order_id}");

        // Limit IOC
        let tif = TimeInForce::IOC;
        let order = Order::limit_open(symbol.clone(), price, amount, ps, tif, exchange);
        let order_id = rest.post_order(order.clone(), params.clone()).await;
        let order_id = order_id.unwrap();
        info!("IOC {order_id}");

        // Limit FOK
        let tif = TimeInForce::FOK;
        let order = Order::limit_open(symbol.clone(), price, amount, ps, tif, exchange);
        let order_id = rest.post_order(order.clone(), params.clone()).await;
        assert!(order_id.is_err()); // 应该立即失败
        if let Err(e) = order_id {
            info!("{:#?}", e);
        }

        // 流动性问题，无法自动测试
        // // Market Short
        // let ps = PosSide::Short;
        // let order = Order::market_open(symbol.clone(), amount, ps, exchange);
        // let order_id = rest.post_order(order.clone(), params.clone()).await;
        // let order_id = order_id.unwrap();
        // info!("Market {order_id}");
    }

    #[tokio::test]
    async fn test_post_limit_order() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();

        let instrument = rest.get_instrument(symbol.clone()).await.unwrap();
        let ticker = rest.get_bbo_ticker(symbol.clone()).await.unwrap();

        let (price, amount) = min_limit_amount_price(&instrument, &ticker);
        info!("price: {price}, amount: {amount}");
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let order = Order::limit_open(symbol, price, amount, ps, tif, exchange);
        let order_id = rest.post_order(order, OrderParams::new(false, 2)).await;
        let order_id = order_id.unwrap();
        info!("{order_id}");
    }

    #[tokio::test]
    #[ignore = "无法平仓"]
    async fn test_market_with_price_order() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        let order = Order {
            price: Some(price),
            amount: Some(amount),
            ..Order::market_open(symbol, amount, PosSide::Long, exchange)
        };
        let order_id = rest.post_order(order, Default::default()).await.unwrap();
        info!("下单成功 {order_id}");
    }

    #[tokio::test]
    async fn test_batch_post_order() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;

        let symbol = btc_symbol(&rest).await.unwrap();
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let params = OrderParams::new(false, 1);
        let count = 5;

        // 无法平仓
        // // Market order
        // let order = Order::market_open(symbol.clone(), 0.01, ps, exchange);
        // let orders = (0..count)
        //     .map(|_| {
        //         let mut order = order.clone();
        //         order.cid = Some(exchange.create_cid(None));
        //         order
        //     })
        //     .collect();
        // let rsp = rest.post_batch_order(orders, params.clone()).await.unwrap();
        // for oid in rsp.success_list.iter() {
        //     info!("{:?}", oid);
        // }

        // Limit order
        let orders = (0..count)
            .map(|_| Order::limit_open(symbol.clone(), 2.7e-4, 1e6, ps, tif.clone(), exchange))
            .collect();
        let rsp = rest.post_batch_order(orders, params).await.unwrap();
        for oid in rsp.success_list {
            info!("{:?}", oid);
        }
    }

    #[tokio::test]
    async fn test_get_order_by_id() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let ps = PosSide::Long;
        let tif = TimeInForce::PostOnly;
        let order = Order::limit_open(symbol.clone(), 50000.0, 0.002, ps, tif, exchange);
        let cid = order.cid.clone().unwrap();
        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order, params).await.unwrap();
        info!("下单成功 {order_id}");

        tokio::time::sleep(Duration::from_secs(3)).await;

        let id = OrderId::Id(order_id.clone());
        let order = rest.get_order_by_id(symbol.clone(), id.clone()).await;
        let order = order.unwrap();
        info!("by id: {order:?}");

        let cid = OrderId::ClientOrderId(cid);
        let order = rest.get_order_by_id(symbol.clone(), cid.clone()).await;
        let order = order.unwrap();
        info!("by cid: {order:?}");

        // cancel order
        rest.cancel_order(symbol, id).await.unwrap();
        info!("撤单成功 {order_id}");
    }

    #[tokio::test]
    async fn test_amend_order() {
        let _ = fmt().try_init();

        let config = test_config();
        let ex = config.exchange;
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;

        let order = Order::limit_open(symbol.clone(), 50000.0, 0.002, ps, tif, ex);
        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("下单成功 {order_id}");

        let order = Order {
            id: order_id,
            price: Some(50001.0),
            amount: Some(0.001),
            ..order
        };

        tokio::time::sleep(Duration::from_secs(3)).await;

        let order_id = rest.amend_order(order).await.unwrap();
        info!("改单成功 {order_id}");

        // cancel order
        let id = OrderId::Id(order_id.clone());
        rest.cancel_order(symbol, id).await.unwrap();
        info!("撤单成功 {order_id}");
    }

    #[tokio::test]
    async fn test_cancel_order() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let order = Order::limit_open(symbol.clone(), 50000.0, 0.002, ps, tif, exchange);
        tokio::time::sleep(Duration::from_secs(1)).await;

        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);

        tokio::time::sleep(Duration::from_secs(2)).await;

        let order_id = rest.post_order(order, params).await.unwrap();
        let id = OrderId::Id(order_id.clone());
        rest.cancel_order(symbol, id).await.unwrap();
        info!("撤单成功 {order_id}");
    }

    #[tokio::test]
    async fn test_get_balances() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = BinanceFuture::new(config).await;
        let balances = exchange.get_balances().await?;
        for balance in balances.iter().filter(|b| b.balance != 0.0) {
            info!("{:?}", balance);
        }
        let usdt = get_usdt_balance(balances);
        info!("{:#?}", usdt);
        Ok(())
    }

    #[tokio::test]
    #[ignore = "影响其他测试函数"]
    async fn debug_close_all_positions() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;
        let positions = rest.get_positions().await.unwrap();
        for position in positions.into_iter().filter(|p| p.amount > 0.0) {
            info!("平仓 {:?}", position);
            let symbol = position.symbol.clone();
            let x = rest.get_instrument(symbol.clone()).await.unwrap();
            let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
            info!("bbo: {bbo:?}");
            let amount = round(x.amount_tick, position.amount * x.amount_multiplier);
            let price = round(x.price_tick, bbo.ask_price);
            let tif = TimeInForce::GTC;
            let order = Order::limit_close(symbol, price, amount, position.side, tif, exchange);
            let params = OrderParams::new(false, position.leverage);
            rest.post_order(order, params).await.unwrap();

            tokio::time::sleep(Duration::from_secs(1)).await;
        }
    }

    #[tokio::test]
    async fn test_get_max_leverage() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let leverage = rest.get_max_leverage(symbol.clone()).await.unwrap();
        info!("leverage: {leverage}");
    }

    #[tokio::test]
    async fn test_set_leverage() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        rest.set_leverage(symbol, 20).await.unwrap();
    }

    #[tokio::test]
    async fn test_is_dual_side() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceFuture::new(config).await;
        let is_dual_side = rest.is_dual_side().await.unwrap();
        info!("is_dual_side: {is_dual_side}");
    }

    #[tokio::test]
    async fn test_set_dual_side() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceFuture::new(config).await;
        let _ = rest.set_dual_side(false).await;
        let is_dual_side = rest.is_dual_side().await.unwrap();
        info!("is_dual_side: {is_dual_side}");
    }

    #[tokio::test]
    async fn test_batch_cancel_order() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let rsp = rest.batch_cancel_order(symbol).await.unwrap();
        for oid in rsp.success_list {
            info!("success: {oid:?}");
        }
        for oid in rsp.failure_list {
            info!("failed: {oid:?}");
        }
    }

    #[tokio::test]
    async fn test_batch_cancel_order_by_ids() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let order = Order::limit_open(symbol.clone(), 50000.0, 0.002, ps, tif, exchange);
        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);

        let orders = (0..5)
            .map(|_| {
                let mut order = order.clone();
                order.cid = Some(exchange.create_cid(None));
                order
            })
            .collect();
        let rsp = rest.post_batch_order(orders, params).await.unwrap();
        info!("{rsp:?}");

        tokio::time::sleep(Duration::from_secs(1)).await;

        let ids: Vec<String> = rsp
            .success_list
            .iter()
            .map(|o| o.id.clone().unwrap_or_default())
            .collect();
        info!("ids: {ids:?}");

        let cids: Vec<String> = rsp
            .success_list
            .iter()
            .map(|o| o.cid.clone().unwrap_or_default())
            .collect();
        info!("cids: {cids:?}");
        let rsp = rest
            .batch_cancel_order_by_ids(Some(symbol), Some(ids), Some(cids))
            .await;
        let rsp = rsp.unwrap();
        for oid in rsp.success_list {
            info!("success: {oid:?}");
        }
        for oid in rsp.failure_list {
            info!("failed: {oid:?}");
        }
    }

    #[tokio::test]
    async fn test_is_fee_discount_enabled() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceFuture::new(config).await;
        let enabled = rest.is_fee_discount_enabled().await.unwrap();
        info!("is_fee_discount_enabled: {enabled}");
    }

    #[tokio::test]
    async fn test_toggle_fee_discount_enabled() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceFuture::new(config).await;

        let enabled = rest.is_fee_discount_enabled().await.unwrap();

        tokio::time::sleep(Duration::from_secs(1)).await;

        rest.set_fee_discount_enabled(!enabled).await.unwrap();
    }

    #[tokio::test]
    async fn test_get_max_position() {
        let _ = fmt().try_init();

        let config = test_config();
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();
        let rsp = rest.get_max_position(symbol, 20).await.unwrap();
        info!("{rsp:?}");
    }

    #[tokio::test]
    #[ignore = "测试环境可能因为流动性问题，无法下单"]
    async fn test_set_margin_mode() {
        let _ = fmt().try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await.unwrap();

        // 先平仓 才能改保证金模式

        let positions = rest.get_positions().await.unwrap();
        for position in positions.into_iter().filter(|p| p.amount > 0.0) {
            info!("平仓 {:?}", position);
            let symbol = position.symbol.clone();
            let x = rest.get_instrument(symbol.clone()).await.unwrap();
            let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
            info!("bbo: {bbo:?}");
            let amount = round(x.amount_tick, position.amount * x.amount_multiplier);
            let order = Order::market_close(symbol, amount, position.side, exchange);
            let params = OrderParams::new(false, position.leverage);
            rest.post_order(order, params).await.unwrap();

            tokio::time::sleep(Duration::from_secs(1)).await;
        }

        let mode = rest.get_margin_mode(symbol.clone(), "".to_string()).await;
        let mode = mode.unwrap();
        info!("当前模式: {mode:?}");
        let coin = "USDT".to_string();
        match mode {
            MarginMode::Isolated => {
                rest.set_margin_mode(symbol, coin, mode).await.unwrap();
            }
            MarginMode::Cross => {
                let mode1 = MarginMode::Isolated;
                let s = symbol.clone();
                rest.set_margin_mode(s, coin.clone(), mode1).await.unwrap();
                tokio::time::sleep(Duration::from_secs(1)).await;
                rest.set_margin_mode(symbol, coin, mode).await.unwrap();
            }
        };
    }

    #[tokio::test]
    #[ignore = "用于重置测试账户"]
    async fn debug_reset_account() {
        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let exchange = config.exchange;
        let rest = BinanceFuture::new(config).await;

        let orders = rest.get_all_open_orders().await.unwrap();
        for order in orders.into_iter().filter(|o| o.amount.unwrap_or(0.0) > 0.0) {
            info!("撤单 {:?}", order);
            let order_id = OrderId::Id(order.id);
            rest.cancel_order(order.symbol, order_id).await.unwrap();
            tokio::time::sleep(Duration::from_secs(1)).await;
        }

        let positions = rest.get_positions().await.unwrap();
        for position in positions.into_iter().filter(|p| p.amount > 0.0) {
            info!("平仓 {:?}", position);
            let params = OrderParams::new(false, position.leverage);
            let order =
                Order::market_close(position.symbol, position.amount, position.side, exchange);
            rest.post_order(order, params).await.unwrap();
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
    }
}

#[cfg(test)]
mod tests {
    use std::collections::HashMap;

    use exchange_validator::{
        WebSocketAPIFactory, WsApiValidator, WsOrderConfig, ws_api::WsValidatorConfigBuilder,
    };
    use gate::swap::ws_api::GateSwapWsApi;
    use log::info;
    use quant_common::{
        Result,
        base::{ExConfig, OrderParams, Symbol},
        test_config,
    };
    use tracing_subscriber::fmt;

    #[tokio::test]
    async fn test_validate_gate_swap_ws_api() -> Result<()> {
        // 验证ws_api
        let _ = fmt().pretty().try_init();
        let config = test_config();

        let mut order_config = HashMap::new();
        order_config.insert(
            Symbol::new("SOL"),
            WsOrderConfig {
                order_price: 120.0,
                order_quantity: 1.0,
                order_params: OrderParams::new(false, 1), // 下单额外参数
            },
        );
        let validate_config = WsValidatorConfigBuilder::new()
            .disable_batch_cancel_order()
            .disable_batch_post_order()
            .disable_batch_cancel_order_by_ids()
            .set_order_config(order_config)
            .build();
        info!("validate_config: {validate_config:#?}");

        struct MyWsApiFactory;
        impl WebSocketAPIFactory for MyWsApiFactory {
            type Api = GateSwapWsApi;

            async fn create_ws_api(config: ExConfig) -> Self::Api {
                GateSwapWsApi::new(config).await
            }
        }

        let mut validator = WsApiValidator::<MyWsApiFactory>::new(validate_config, config);
        validator.validate_all().await?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;
    use std::sync::atomic::{self, AtomicI64};
    use std::{fs::read_to_string, time::Duration};
    use tokio::time::timeout;
    use tracing_subscriber::fmt;

    use quant_common::{
        Result,
        base::{model::*, *},
    };

    use binance::{
        future::{rest::BinanceFuture, ws::BinanceFutureWs},
        util::is_ed25519_secret,
    };

    fn test_config() -> ExConfig {
        let s = read_to_string("future.toml").unwrap();
        let config: ExConfig = toml::from_str(&s).unwrap();
        assert!(!is_ed25519_secret(&config.secret));
        config
    }

    async fn btc_symbol(rest: &BinanceFuture) -> Symbol {
        rest.get_instruments()
            .await
            .unwrap()
            .into_iter()
            .find(|i| i.symbol.base == "BTC")
            .map(|i| i.symbol)
            .unwrap()
    }

    async fn symbols(rest: &BinanceFuture) -> Vec<Symbol> {
        rest.get_instruments()
            .await
            .unwrap()
            .into_iter()
            .map(|i| i.symbol)
            .collect()
    }

    #[derive(Clone)]
    struct DepthWsHandler;

    impl WsHandler for DepthWsHandler {
        async fn on_depth(&self, depth: Depth, _: Exchange) -> Result<()> {
            tracing::info!("------------------- {} -------------------", depth.symbol);
            for ask in depth.asks.iter().rev() {
                tracing::info!("asks: {:.2} @ {:.2}", ask.amount * ask.price, ask.price);
            }
            for bid in depth.bids {
                tracing::info!("  bids: {:.2} @ {:.2}", bid.amount * bid.price, bid.price);
            }
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_ws_depth() {
        let _ = fmt().with_target(false).try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()
        };
        let rest = BinanceFuture::new(config.clone()).await;
        let mut ws = BinanceFutureWs::new(config).await;
        let symbols = Arc::new(vec![btc_symbol(&rest).await]);

        let subs = Subscribes::new(
            0,
            DepthWsHandler,
            vec![SubscribeChannel::Depth(DepthWsParams::new(symbols, 4))],
        );
        let r = tokio::time::timeout(Duration::from_secs(10), ws.run(subs)).await;
        assert!(r.is_err());
    }

    #[tokio::test]
    async fn test_symbols_ws_depth() {
        let _ = fmt().with_target(false).try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()
        };
        let mut ws = BinanceFutureWs::new(config.clone()).await;
        let rest = BinanceFuture::new(config).await;
        let symbols = Arc::new(symbols(&rest).await);

        let subs = Subscribes::new(
            0,
            DepthWsHandler,
            vec![SubscribeChannel::Depth(DepthWsParams::new(symbols, 1))],
        );
        let r = tokio::time::timeout(Duration::from_secs(10), ws.run(subs)).await;
        assert!(r.is_err());
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn debug_ws_depth_stream_rebuild() {
        let _ = fmt().with_target(false).try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()
        };
        let mut ws = BinanceFutureWs::new(config.clone()).await;
        let rest = BinanceFuture::new(config).await;
        let symbols = Arc::new(vec![btc_symbol(&rest).await]);

        #[derive(Clone)]
        struct DepthWsHandler {
            count: Arc<AtomicI64>,
        }

        impl WsHandler for DepthWsHandler {
            async fn on_depth(&self, _: Depth, _: Exchange) -> Result<()> {
                let count = self.count.fetch_add(1, atomic::Ordering::AcqRel);
                if count % 600 == 0 {
                    tracing::info!("count: {count}");
                }
                Ok(())
            }
        }

        let subs = Subscribes::new(
            0,
            DepthWsHandler {
                count: Arc::new(AtomicI64::new(1)),
            },
            vec![SubscribeChannel::Depth(DepthWsParams::new(symbols, 1))],
        );
        ws.run(subs).await.unwrap();
    }

    #[tokio::test]
    async fn test_ws() {
        let _ = fmt().with_target(false).try_init();
        let config = test_config();
        let rest = BinanceFuture::new(config.clone()).await;
        let mut ws = BinanceFutureWs::new(config).await;
        let symbols = Arc::new(symbols(&rest).await);

        let subs = Subscribes::default_swap(symbols);
        let r = timeout(Duration::from_secs(10), ws.run(subs)).await;
        assert!(r.is_err());
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn test_pri() {
        let _ = fmt().with_target(false).pretty().try_init();
        let config = test_config();
        let rest = BinanceFuture::new(config.clone()).await;
        let mut ws = BinanceFutureWs::new(config).await;
        let symbols = Arc::new(symbols(&rest).await);

        let subs = Subscribes::default_swap_pri(symbols);
        ws.run(subs).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "监控代码，手动运行"]
    async fn test_on_order() -> Result<()> {
        let _ = fmt().with_target(false).try_init();
        let config = test_config();
        let rest = BinanceFuture::new(config.clone()).await;
        let mut ws = BinanceFutureWs::new(config).await;
        let symbols = Arc::new(symbols(&rest).await);
        let channels = vec![
            // SubscribeChannel::Order(symbols.clone()),
            SubscribeChannel::OrderAndFill(symbols.clone()),
        ];

        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "监控代码，手动运行"]
    async fn test_on_position() -> Result<()> {
        let _ = fmt().with_target(false).try_init();
        let config = test_config();
        let mut ws = BinanceFutureWs::new(config.clone()).await;
        let rest = BinanceFuture::new(config).await;
        let symbols = Arc::new(symbols(&rest).await);
        let channels = vec![SubscribeChannel::Position(symbols.clone())];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "监控代码，手动运行"]
    async fn test_on_balance() -> Result<()> {
        let _ = fmt().with_target(false).try_init();
        let config = test_config();
        let mut ws = BinanceFutureWs::new(config).await;
        let channels = vec![SubscribeChannel::Balance];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    async fn test_ws_kline() {
        let _ = fmt().with_target(false).try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()
        };
        let mut ws = BinanceFutureWs::new(config.clone()).await;
        let rest = BinanceFuture::new(config).await;
        let symbols = Arc::new(vec![btc_symbol(&rest).await]);
        let channels = [KlineInterval::Min1, KlineInterval::Day1]
            .into_iter()
            .map(|interval| (symbols.clone(), interval))
            .map(|(symbols, interval)| SubscribeChannel::Kline(KlineWsParams { symbols, interval }))
            .collect();
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        let ret = timeout(Duration::from_secs(40), ws.run(subs)).await;
        assert!(ret.is_err());
    }
}

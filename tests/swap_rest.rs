#[cfg(test)]
mod tests {
    use binance::swap::model::{PATH_TIME, PosRisk};
    use binance::swap::rest::*;
    use binance::util::{ceil, min_limit_amount_price};
    use log::{info, warn};
    use quant_common::time_ms;
    use quant_common::{Result, base::*};
    use reqwest::Method;
    use rustc_hash::FxHashMap;
    use std::{fs::read_to_string, time::Duration};
    use tracing_subscriber::fmt;

    fn test_config() -> Result<ExConfig> {
        let config: ExConfig = toml::from_str(&read_to_string("swap.toml")?)?;
        if !config.is_testnet {
            warn!("实盘测试");
        }
        Ok(config)
    }

    #[cfg(test)]
    mod read {
        use quant_common::base::traits::{GetFundingRateHistoryParams, GetKlineParams};

        use super::*;

        #[tokio::test]
        async fn test_request() {
            let _ = fmt().pretty().try_init();

            let config = ExConfig::default();
            let rest = BinanceSwap::new(config).await;
            let req = UserRequest {
                method: Method::GET.to_string(),
                path: PATH_TIME.to_string(), // TODO
                auth: false,
                query: None,
                body: None,
                url: None,
                headers: None,
            };
            let res = rest.request(req).await.unwrap();
            info!("{:#?}", res);
        }

        #[tokio::test]
        async fn test_get_ticker() {
            let _ = fmt().pretty().try_init();

            let config = ExConfig::default();
            let rest = BinanceSwap::new(config).await;
            let symbol = Symbol::new("WHY");
            let ticker = rest.get_ticker(symbol).await.unwrap();
            info!("{:#?}", ticker);
        }

        fn to_wide_string(s: &str, len: usize) -> String {
            let mut r = String::with_capacity(len * s.len());
            r.push_str(&" ".to_string().repeat(len));
            r.push_str(s);
            r[(r.len() - len).max(0)..].to_string()
        }

        #[tokio::test]
        async fn test_get_tickers() {
            let _ = fmt().try_init();

            let config = ExConfig {
                is_testnet: false,
                exchange: Exchange::BinanceSwap,
                ..ExConfig::default()
            };
            let rest = BinanceSwap::new(config).await;
            let tickers = rest.get_tickers().await.unwrap();
            let mut tickers = tickers
                .into_iter()
                .filter(|t| {
                    t.quote_volume > 10_000_000.0 && matches!(t.symbol.quote, QuoteCcy::USDT)
                })
                .collect::<Vec<_>>();
            tickers.sort_by(|a, b| {
                b.change_percent
                    .partial_cmp(&a.change_percent)
                    .unwrap_or(std::cmp::Ordering::Equal)
            });
            let (inc, dec) = tickers
                .iter()
                .fold((0., 0.), |(inc, dec), t| match t.change > 0.0 {
                    true => (inc + 1., dec),
                    false => (inc, dec + 1.),
                });
            let sum = inc + dec;
            let inc = inc / sum * 100.;
            let dec = dec / sum * 100.;
            info!("上涨: {inc:02.2}%, 下跌: {dec:2.2}%, 总数: {sum}");
            for t in tickers.iter() {
                let symbol = to_wide_string(&t.symbol.to_string(), 10);
                let close = to_wide_string(&format!("{:.2e}", t.close), 10);
                let volume = to_wide_string(&(format!("{:.2e}", t.volume)), 10);
                let quote_volume = to_wide_string(&(format!("{:.2e}", t.quote_volume)), 10);
                let change = to_wide_string(&format!("{:.2}%", t.change_percent), 10);
                info!(
                    "symbol: {symbol} \tchange: {change} \tclose: {close} \tvolume: {volume} \tquote_volume: {quote_volume}"
                );
            }
        }

        #[tokio::test]
        async fn test_get_klines_ext() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceSwap::new(config).await;
            for base in ["BTC", "SATS", "RAYSOL"] {
                let symbol = Symbol::new(base);
                let params = GetKlineParams::new(symbol, KlineInterval::Min15).limit(5);
                let klines = rest.get_klines_ext(params).await.unwrap();
                info!("{} {:?}", klines.symbol, klines.interval);
                for kline in klines.candles {
                    info!("{:?}", kline);
                }
            }
        }

        #[tokio::test]
        async fn test_get_mark_price() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceSwap::new(config).await;

            let symbol = Symbol::new("PEPE");
            let mark_price = rest.get_mark_price(Some(symbol)).await.unwrap();
            info!("{:?}", mark_price);

            // let mark_prices = rest.get_mark_price(None).await.unwrap();
            // info!("mark_prices len: {}", mark_prices.len());
            // for mark_price in mark_prices {
            //     info!("{} mark_price: {}", mark_price.symbol, mark_price.price);
            // }
        }

        #[tokio::test]
        async fn test_get_bbo() {
            let _ = fmt().pretty().try_init();

            let config = ExConfig::default();
            let exchange = BinanceSwap::new(config).await;
            let symbol = Symbol::new("PEPE");
            let ticker = exchange.get_bbo_ticker(symbol).await.unwrap();
            info!("{:#?}", ticker);
        }

        #[tokio::test]
        async fn test_get_bbos() {
            let _ = fmt().pretty().try_init();

            let config = ExConfig::default();
            let exchange = BinanceSwap::new(config).await;
            let tickers = exchange.get_bbo_tickers().await.unwrap();
            info!("{:#?}", tickers);
        }

        #[tokio::test]
        async fn test_get_depth() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let rest = BinanceSwap::new(config).await;
            let symbol = Symbol::new("X");
            let depth = rest.get_depth(symbol, Some(5)).await.unwrap();
            info!("symbol: {}, ", depth.symbol);
            for ask in depth.asks {
                info!(
                    "  asks: {:?} @ {} => {}",
                    ask.amount,
                    ask.price,
                    ask.amount * ask.price
                );
            }
            info!("--------------------------------");
            for bid in depth.bids {
                info!(
                    "  bids: {:?} @ {} => {}",
                    bid.amount,
                    bid.price,
                    bid.amount * bid.price
                );
            }
        }

        #[tokio::test]
        async fn test_get_instrument() {
            let _ = fmt().pretty().try_init();

            let config = ExConfig::default();
            let rest = BinanceSwap::new(config).await;
            for symbol in ["BNB", "BABYDOGE"] {
                let instrument = rest.get_instrument(Symbol::new(symbol)).await.unwrap();
                info!("{:#?}", instrument);
                tokio::time::sleep(Duration::from_secs(1)).await;
            }
        }

        #[tokio::test]
        async fn test_min_qty() {
            let _ = fmt().pretty().try_init();

            let config = ExConfig::default();
            let rest = BinanceSwap::new(config).await;
            let instrument = rest.get_instrument(Symbol::new("PEPE")).await.unwrap();
            assert_eq!(instrument.min_qty, 1.0);
        }

        #[tokio::test]
        async fn test_get_instruments() {
            let _ = fmt().try_init();

            let config = ExConfig::default();
            let exchange = BinanceSwap::new(config).await;
            let instruments = exchange.get_instruments().await.unwrap();
            info!("instruments len: {}", instruments.len());
            for instrument in instruments {
                info!("{:?}", instrument);
            }
        }

        #[tokio::test]
        async fn test_get_funding_rates() {
            let _ = fmt().try_init();

            let config = ExConfig {
                is_testnet: false,
                ..test_config().unwrap()
            };
            let rest = BinanceSwap::new(config).await;
            let mut funding_rates = rest.get_funding_rates().await.unwrap();
            info!("funding_rates len: {}", funding_rates.len());
            funding_rates.sort_by_key(|r| r.next_funding_at);
            for funding_rate in funding_rates.iter().take(5) {
                info!("{:?}", funding_rate);
            }
        }

        #[tokio::test]
        async fn test_get_funding_rates_history_ext() {
            let _ = fmt().try_init();
            let config = ExConfig {
                is_testnet: false,
                ..test_config().unwrap()
            };
            let rest = BinanceSwap::new(config).await;
            let symbol = Symbol::new("BTC");
            let params = GetFundingRateHistoryParams::new(Some(symbol), Some(0), 100);
            let funding_rates = rest.get_funding_rates_history_ext(params).await.unwrap();
            for funding_rate in funding_rates.iter() {
                info!("{:?}", funding_rate);
            }
            info!("历史资金费率数量: {}", funding_rates.len());
        }

        #[tokio::test]
        async fn test_get_funding_rate() {
            let _ = fmt().try_init();

            let config = ExConfig {
                is_testnet: false,
                ..test_config().unwrap()
            };
            let rest = BinanceSwap::new(config).await;
            let symbol = Symbol::new("BTC");
            let funding_rate = rest.get_funding_rate(symbol).await.unwrap();
            info!("{:?}", funding_rate);
        }

        #[tokio::test]
        async fn test_get_funding_fee() {
            let _ = fmt().try_init();

            let config = test_config().unwrap();
            let rest = BinanceSwap::new(config).await;
            let end = Some(time_ms());
            let start = end.map(|e| e - 7 * 24 * 3600 * 1000);
            let symbol = Symbol::new("BTC");
            let fees = rest.get_funding_fee(symbol, start, end).await.unwrap();
            info!("数量: {}", fees.len());
            for fee in fees {
                info!("{fee:?}");
            }
        }

        #[tokio::test]
        async fn test_get_open_orders() -> Result<()> {
            let _ = fmt().try_init();

            let config = test_config()?;
            let exchange = BinanceSwap::new(config).await;
            let orders = exchange.get_open_orders(Symbol::new("BTC")).await?;
            info!("{:#?}", orders);
            Ok(())
        }

        #[tokio::test]
        async fn test_get_all_open_orders() -> Result<()> {
            let _ = fmt().pretty().try_init();

            let config = test_config()?;
            let exchange = BinanceSwap::new(config).await;
            let orders = exchange.get_all_open_orders().await?;
            info!("{:#?}", orders);
            Ok(())
        }

        #[tokio::test]
        async fn test_get_orders() -> Result<()> {
            let _ = fmt().try_init();

            let config = test_config()?;
            let rest = BinanceSwap::new(config).await;
            let orders = rest.get_orders(Symbol::new("BTC"), 0, time_ms()).await?;
            info!("订单数量: {}", orders.len());
            for order in orders {
                info!("{:?}", order);
            }
            Ok(())
        }

        #[tokio::test]
        async fn test_get_fee_rate() -> Result<()> {
            let _ = fmt().pretty().try_init();

            let config = test_config()?;
            let exchange = BinanceSwap::new(config).await;
            let symbol = Symbol::new("BTC");
            let fee_rate = exchange.get_fee_rate(symbol).await?;
            info!("{:#?}", fee_rate);
            Ok(())
        }

        #[tokio::test]
        async fn test_get_position() -> Result<()> {
            let _ = fmt().pretty().try_init();

            let config = test_config()?;
            let exchange = BinanceSwap::new(config).await;
            let btc = exchange.get_position(Symbol::new("BTC")).await?;
            info!("{:#?}", btc);
            Ok(())
        }

        #[tokio::test]
        async fn test_get_positions() -> Result<()> {
            let _ = fmt().pretty().try_init();

            let config = test_config()?;
            let rest = BinanceSwap::new(config).await;
            let positions = rest.get_positions().await?;
            for position in positions.iter().filter(|p| p.amount != 0.0) {
                info!("{:#?}", position);
            }
            Ok(())
        }

        #[tokio::test]
        async fn test_get_funding_interval() -> Result<()> {
            let _ = fmt().try_init();

            let config = ExConfig {
                is_testnet: false,
                ..Default::default()
            };

            let rest = BinanceSwap::new(config).await;
            let funds = rest.get_funding_interval().await?;
            for fund in funds {
                info!("{:?}", fund);
            }
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_post_order() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config()?;
        let exchange = config.exchange;
        let rest = BinanceSwap::new(config).await;

        // Market order
        let mut order = Order::market_open(Symbol::new("BNB"), 0.01, PosSide::Long, exchange);
        let is_dual_side = rest.is_dual_side().await?;
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order.clone(), params.clone()).await?;
        info!("{:#?}", order_id);

        tokio::time::sleep(Duration::from_secs(3)).await;

        // Limit order
        order.order_type = OrderType::Limit;
        order.price = Some(100.0);
        order.amount = Some(0.05);
        let order_id = rest.post_order(order.clone(), params.clone()).await?;
        info!("{:#?}", order_id);

        // Limit IOC
        order.time_in_force = TimeInForce::IOC;
        order.side = OrderSide::Buy;
        let order_id = rest.post_order(order.clone(), params.clone()).await?;
        info!("{:#?}", order_id);

        // Limit FOK
        order.time_in_force = TimeInForce::FOK;
        let order_id = rest.post_order(order.clone(), params.clone()).await;
        assert!(order_id.is_err()); // 应该立即失败
        if let Err(e) = order_id {
            info!("{:#?}", e);
        }

        // Market Short
        order = Order::market_open(Symbol::new("BNB"), 0.01, PosSide::Short, exchange);
        let order_id = rest.post_order(order.clone(), params.clone()).await?;
        info!("{:#?}", order_id);
        Ok(())
    }

    #[tokio::test]
    async fn test_post_limit_order() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config()?;
        let exchange = config.exchange;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let instrument = rest.get_instrument(symbol.clone()).await?;

        let ticker = rest.get_bbo_ticker(symbol.clone()).await?;
        let notional = instrument.min_notional;
        let price = ceil(instrument.price_tick, ticker.ask_price * 1.001);
        info!("price: {} => {price}", ticker.ask_price);
        let amount = instrument.amount_multiplier * (notional / price);
        let amount = (amount / instrument.amount_tick).round() * instrument.amount_tick;
        let price = (price / instrument.price_tick * instrument.price_multiplier).round()
            / (1.0 / instrument.price_tick).round();
        info!(
            "tick price:  {price}, tick: {}, multiple: {}",
            instrument.price_tick, instrument.price_multiplier
        );

        let order = Order::limit_open(
            symbol,
            price,
            amount,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let order_id = rest.post_order(order, OrderParams::new(false, 2)).await?;
        info!("{:#?}", order_id);

        Ok(())
    }

    #[tokio::test]
    async fn test_market_with_price_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let exchange = config.exchange;

        let rest = BinanceSwap::new(config).await;
        let order = Order {
            price: Some(620.0),
            ..Order::market_open(Symbol::new("BNB"), 0.01, PosSide::Long, exchange)
        };
        let order_id = rest.post_order(order, Default::default()).await?;
        info!("{}", order_id);
        Ok(())
    }

    #[tokio::test]
    async fn test_post_reduce_order() {
        let _ = fmt().with_target(false).try_init();

        let config = test_config().unwrap();
        let exchange = config.exchange;

        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BNB");
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");
        let ps = PosSide::Long;

        let order = Order::market_open(symbol.clone(), amount, ps, exchange);
        let order_id = rest.post_order(order, Default::default()).await.unwrap();
        info!("下市价单成功: {order_id}");

        tokio::time::sleep(Duration::from_secs(1)).await;

        let order = Order::market_close(symbol.clone(), amount, ps, exchange);
        info!("reduce_only: {}", order.reduce_only());
        assert!(order.reduce_only());
        let order_id = rest.post_order(order, Default::default()).await.unwrap();
        info!("reduce_only 市价单: {}", order_id);
    }

    #[tokio::test]
    async fn test_batch_post_order() {
        let _ = fmt().with_target(false).try_init();

        let config = test_config().unwrap();
        let exchange = config.exchange;
        let rest = BinanceSwap::new(config).await;

        let symbol = Symbol::new("BTC");
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");

        // Market order
        let order = Order::market_open(symbol.clone(), amount, PosSide::Long, exchange);
        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);

        let count = 5;
        let orders = (0..count)
            .map(|_| Order {
                cid: Some(exchange.create_cid(None)),
                ..order.clone()
            })
            .collect();

        let rsp = rest.post_batch_order(orders, params.clone()).await.unwrap();
        for succ in rsp.success_list.iter() {
            info!("success: 开仓订单 {:?}", succ);
        }
        for failed in rsp.failure_list.iter() {
            warn!("failed: {:?}", failed);
        }
        assert_eq!(rsp.success_list.len(), count);
        assert_eq!(rsp.failure_list.len(), 0);
        for fail in rsp.failure_list.iter() {
            warn!("开仓失败 {:?}", fail);
        }
        assert_eq!(rsp.failure_list.len(), 0);

        // Limit order
        let order = Order::limit_close(
            symbol,
            price,
            amount,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);

        let orders = (0..count)
            .map(|i| Order {
                cid: Some(exchange.create_cid(None)),
                price: order.price.map(|price| match i == 2 {
                    true => -price,
                    false => price,
                }),
                ..order.clone()
            })
            .collect();
        let rsp = rest.post_batch_order(orders, params.clone()).await.unwrap();
        for succ in rsp.success_list.iter() {
            info!("success: {:?}", succ);
        }
        assert_eq!(rsp.success_list.len(), count - 1);
        for failed in rsp.failure_list.iter() {
            info!("failed: {:?}", failed);
        }
        assert_eq!(rsp.failure_list.len(), 1);
    }

    #[tokio::test]
    async fn test_get_order_by_id() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol.clone(),
            50000.0,
            0.002,
            PosSide::Long,
            TimeInForce::PostOnly,
            Exchange::BinanceSwap,
        );
        let cid = order.cid.clone().unwrap();
        let is_dual_side = rest.is_dual_side().await?;
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order, params).await?;
        info!("{:#?}", order_id);

        tokio::time::sleep(Duration::from_secs(3)).await;

        let order = rest
            .get_order_by_id(symbol.clone(), OrderId::Id(order_id.clone()))
            .await?;
        info!("by id: {:?}", order);

        let order = rest
            .get_order_by_id(symbol.clone(), OrderId::ClientOrderId(cid))
            .await?;
        info!("by cid: {:?}", order);

        // cancel order
        rest.cancel_order(symbol, OrderId::Id(order_id)).await?;
        Ok(())
    }

    #[tokio::test]
    async fn test_amend_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let ex = config.exchange;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let mut order = Order::limit_open(
            symbol.clone(),
            50000.0,
            0.002,
            PosSide::Long,
            TimeInForce::GTC,
            ex,
        );
        let is_dual_side = rest.is_dual_side().await?;
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order.clone(), params).await?;
        info!("{:#?}", order_id);
        order.id = order_id;
        order.price = Some(50001.0);

        tokio::time::sleep(Duration::from_secs(3)).await;

        let order_id = rest.amend_order(order).await?;
        info!("{:#?}", order_id);

        // cancel order
        rest.cancel_order(symbol, OrderId::Id(order_id)).await
    }

    #[tokio::test]
    async fn test_cancel_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let exchange = config.exchange;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let tif = TimeInForce::GTC;
        let ps = PosSide::Long;
        let order = Order::limit_open(symbol.clone(), 50000.0, 0.002, ps, tif, exchange);
        tokio::time::sleep(Duration::from_secs(1)).await;

        let is_dual_side = rest.is_dual_side().await?;
        let params = OrderParams::new(is_dual_side, 1);

        tokio::time::sleep(Duration::from_secs(2)).await;

        let order_id = rest.post_order(order, params).await?;
        rest.cancel_order(symbol, OrderId::Id(order_id)).await?;
        Ok(())
    }

    #[tokio::test]
    async fn test_get_balances() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config()?;
        let exchange = BinanceSwap::new(config).await;
        let balances = exchange.get_balances().await?;
        for balance in balances.iter() {
            info!("{:?}", balance);
        }
        let usdt = get_usdt_balance(balances);
        info!("{:#?}", usdt);
        Ok(())
    }

    #[tokio::test]
    #[ignore = "影响其他测试函数"]
    async fn test_close_all_positions() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let exchange = config.exchange;
        let rest = BinanceSwap::new(config).await;
        let positions = rest.get_positions().await?;
        for position in positions.into_iter().filter(|p| p.amount > 0.0) {
            info!("平仓 {:?}", position);
            let params = OrderParams::new(false, position.leverage);
            let order =
                Order::market_close(position.symbol, position.amount, position.side, exchange);
            rest.post_order(order, params).await?;
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
        Ok(())
    }

    #[tokio::test]
    async fn test_get_max_leverage() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let exchange = BinanceSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let leverage = exchange.get_max_leverage(symbol.clone()).await?;
        info!("leverage: {leverage}");
        Ok(())
    }

    #[tokio::test]
    async fn test_set_leverage() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        rest.set_leverage(symbol, 20).await?;
        Ok(())
    }

    #[tokio::test]
    async fn test_is_dual_side() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let exchange = BinanceSwap::new(config).await;
        let is_dual_side = exchange.is_dual_side().await?;
        info!("{:#?}", is_dual_side);
        Ok(())
    }

    #[tokio::test]
    async fn test_set_dual_side() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let _ = rest.set_dual_side(false).await;
        let is_dual_side = rest.is_dual_side().await?;
        info!("{}", is_dual_side);
        Ok(())
    }

    #[tokio::test]
    #[ignore = "验证数据"]
    async fn test_monitor_funding_interval() {
        let _ = fmt().try_init();

        let config = ExConfig {
            is_testnet: false,
            ..test_config().unwrap()
        };

        let rest = BinanceSwap::new(config).await;
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(5));
        interval.tick().await;
        let funds = rest.get_funding_interval().await.unwrap();
        let mut map = FxHashMap::default();
        for fund in funds {
            info!("初始化: {} = {}", fund.symbol, fund.funding_interval_hours);
            map.insert(fund.symbol, fund.funding_interval_hours);
        }

        loop {
            interval.tick().await;
            let funds = match rest.get_funding_interval().await {
                Ok(funds) => funds,
                Err(e) => {
                    warn!("{e}");
                    continue;
                }
            };

            let mut new_map = FxHashMap::default();

            for fund in funds {
                new_map.insert(fund.symbol, fund.funding_interval_hours);
            }

            // 对比
            //   新增
            for (symbol, interval) in new_map.iter() {
                if !map.contains_key(symbol) {
                    info!("新增: {symbol} {interval}");
                }
            }

            //   减少
            for (symbol, interval) in map.iter() {
                if !new_map.contains_key(symbol) {
                    info!("减少: {symbol} {interval}");
                }
            }

            //   变更
            for (symbol, interval) in new_map.iter() {
                if let Some(old_interval) = map.get(symbol) {
                    if old_interval != interval {
                        info!("变更: {symbol} {old_interval} -> {interval}");
                    }
                }
            }

            map = new_map;
        }
    }

    #[tokio::test]
    async fn test_create_listen_key() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let exchange = BinanceSwap::new(config).await;
        let listen_key = exchange.create_listen_key().await?;
        info!("{:#?}", listen_key);
        Ok(())
    }

    #[tokio::test]
    async fn test_batch_cancel_order() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let _ = rest.batch_cancel_order(symbol).await?;
        tokio::time::sleep(Duration::from_secs(1)).await;
        Ok(())
    }

    #[tokio::test]
    async fn test_batch_cancel_order_by_ids() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let exchange = config.exchange;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol.clone(),
            50000.0,
            0.002,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let is_dual_side = rest.is_dual_side().await?;
        let params = OrderParams::new(is_dual_side, 1);

        let orders = (0..5)
            .map(|_| {
                let mut order = order.clone();
                order.cid = Some(exchange.create_cid(None));
                order
            })
            .collect();
        let rsp = rest.post_batch_order(orders, params).await?;
        info!("{rsp:?}");

        tokio::time::sleep(Duration::from_secs(1)).await;

        let ids: Vec<String> = rsp
            .success_list
            .iter()
            .map(|o| o.id.clone().unwrap_or_default())
            .collect();
        info!("ids: {ids:?}");

        let cids: Vec<String> = rsp
            .success_list
            .iter()
            .map(|o| o.cid.clone().unwrap_or_default())
            .collect();
        info!("cids: {cids:?}");
        let rsp = rest
            .batch_cancel_order_by_ids(Some(symbol), Some(ids), Some(cids))
            .await?;
        info!("batch_cancel_order_by_ids rsp: {rsp:?}");

        tokio::time::sleep(Duration::from_secs(1)).await;

        Ok(())
    }

    #[tokio::test]
    async fn test_is_fee_discount_enabled() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let enabled = rest.is_fee_discount_enabled().await?;
        info!("is_fee_discount_enabled: {enabled}");

        tokio::time::sleep(Duration::from_secs(1)).await;
        Ok(())
    }

    #[tokio::test]
    async fn test_toggle_fee_discount_enabled() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let enabled = rest.is_fee_discount_enabled().await?;
        tokio::time::sleep(Duration::from_secs(1)).await;
        rest.set_fee_discount_enabled(!enabled).await?;

        tokio::time::sleep(Duration::from_secs(1)).await;
        Ok(())
    }

    #[tokio::test]
    async fn test_get_max_position() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let rsp = rest.get_max_position(symbol, 20).await?;
        info!("{rsp:?}");

        tokio::time::sleep(Duration::from_secs(1)).await;
        Ok(())
    }

    #[tokio::test]
    #[ignore = "数据对比函数"]
    async fn cmp_get_max_position() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let instruments = rest.get_instruments().await?;
        for instrument in instruments {
            let risks: Vec<PosRisk> = rest.pos_risk(instrument.symbol.clone()).await?;
            for risk in risks {
                let rsp = rest
                    .set_bn_leverage(risk.symbol.into(), risk.leverage)
                    .await;
                match rsp {
                    Ok(rsp) => assert_eq!(risk.max_notional_value, rsp.max_notional_value),
                    Err(err) => warn!("{err}"),
                }
            }
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
        Ok(())
    }

    #[tokio::test]
    #[ignore = "修改前需要清空持仓,future可能因流动性问题无法平仓"]
    async fn test_set_margin_mode() -> Result<()> {
        let _ = fmt().pretty().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let symbol = Symbol::new("X");
        let mode = rest.get_margin_mode(symbol.clone(), "".to_string()).await?;
        info!("当前模式: {mode:?}");
        let coin = "USDT".to_string();
        match mode {
            MarginMode::Isolated => {
                rest.set_margin_mode(symbol, coin, mode).await?;
            }
            MarginMode::Cross => {
                rest.set_margin_mode(symbol.clone(), coin.clone(), MarginMode::Isolated)
                    .await?;

                tokio::time::sleep(Duration::from_secs(1)).await;

                rest.set_margin_mode(symbol, coin, mode).await?;
            }
        };
        tokio::time::sleep(Duration::from_secs(1)).await;
        Ok(())
    }

    #[tokio::test]
    async fn test_get_account_info() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config()?;
        let rest = BinanceSwap::new(config).await;
        let account_info = rest.get_account_info().await?;
        info!("{:?}", account_info);
        Ok(())
    }

    #[tokio::test]
    #[ignore = "用于重置测试账户"]
    async fn debug_reset_account() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config()?;
        let exchange = config.exchange;
        let rest = BinanceSwap::new(config).await;
        let positions = rest.get_positions().await?;
        for position in positions.into_iter().filter(|p| p.amount > 0.0) {
            info!("平仓 {:?}", position);
            let params = OrderParams::new(false, position.leverage);
            let order =
                Order::market_close(position.symbol, position.amount, position.side, exchange);
            rest.post_order(order, params).await.unwrap();
            tokio::time::sleep(Duration::from_secs(1)).await;
        }

        let orders = rest.get_all_open_orders().await?;
        for order in orders.into_iter().filter(|o| o.amount.unwrap_or(0.0) > 0.0) {
            info!("撤单 {:?}", order);
            let order_id = OrderId::Id(order.id);
            rest.cancel_order(order.symbol, order_id).await?;
            tokio::time::sleep(Duration::from_secs(1)).await;
        }

        Ok(())
    }
}

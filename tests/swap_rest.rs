#[cfg(test)]
mod tests {
    use log::info;
    // use quant_common::base::traits::PostStopOrderParams;
    use quant_common::base::*;
    use quant_common::*;

    use gate::swap::rest::GateSwap;
    use tracing_subscriber::filter::LevelFilter;
    use tracing_subscriber::fmt;

    #[tokio::test]
    async fn test_request() {
        let mut config = ExConfig::default();
        let exchange = GateSwap::new(config.clone()).await;
        let rsp = exchange
            .request(UserRequest {
                method: "GET".to_string(),
                path: "/api/v4/futures/usdt/tickers".to_string(),
                auth: true,
                query: None,
                body: None,
                url: None,
                headers: None,
            })
            .await
            .unwrap();
        println!("{rsp:#?}");
        config.is_testnet = true;
        let exchange = GateSwap::new(config).await;
        let rsp = exchange
            .request(UserRequest {
                method: "GET".to_string(),
                path: "/api/v4/futures/usdt/tickers".to_string(),
                auth: true,
                query: None,
                body: None,
                url: None,
                headers: None,
            })
            .await
            .unwrap();
        println!("{rsp:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_ticker() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let exchange = GateSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let ticker = exchange.get_ticker(symbol).await.unwrap();
        info!("{ticker:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_tickers() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let exchange = GateSwap::new(config).await;
        let tickers = exchange.get_tickers().await.unwrap();
        info!("{tickers:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_bbo() {
        let _ = fmt().pretty().try_init();
        let config = ExConfig::default();
        let exchange = GateSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let ticker = exchange.get_bbo_ticker(symbol).await.unwrap();
        info!("{ticker:#?}");
    }

    #[tokio::test]
    async fn test_get_depth() {
        let _ = fmt().pretty().try_init();
        let config = ExConfig::default();
        let exchange = GateSwap::new(config).await;
        let symbol = Symbol::new("AERGO");
        let depth = exchange.get_depth(symbol, Some(5)).await.unwrap();
        info!("{depth:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instrument() {
        let _ = fmt().pretty().try_init();
        let config = ExConfig::default();
        let exchange = GateSwap::new(config).await;
        let instrument = exchange.get_instrument(Symbol::new("NOT")).await.unwrap();
        info!("{instrument:#?}");
    }

    #[tokio::test]
    async fn test_get_mark_price() {
        let _ = fmt().pretty().try_init();
        let config = ExConfig::default();
        let rest = GateSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let mark_price = rest.get_mark_price(Some(symbol.clone())).await.unwrap();
        info!("{mark_price:#?}");
    }

    #[tokio::test]
    async fn test_get_instruments() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let balances = exchange.get_balances().await.unwrap();
        info!("{balances:#?}");
        let instruments = exchange.get_instruments().await.unwrap();
        for instrument in instruments {
            if instrument.symbol == Symbol::new("STPT") {
                info!("{instrument:#?}");
            }
        }
        // info!("{:#?}", instruments.first());
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_funding_rates() {
        let _ = fmt().pretty().try_init();
        let exchange = GateSwap::new(ExConfig::default()).await;
        let funding_rates = exchange.get_funding_rates().await.unwrap();
        info!("{funding_rates:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_order() {
        let config = test_config();
        info!("{config:#?}");
        let exchange = config.exchange;

        let rest = GateSwap::new(config).await;
        let _ = fmt()
            .pretty()
            .with_max_level(tracing_subscriber::filter::LevelFilter::DEBUG)
            .try_init();
        let mut order = Order::market_open(Symbol::new("DOT"), 1., PosSide::Long, exchange);
        // rest.set_dual_side(false).await.unwrap();
        let is_dual_side = rest.is_dual_side().await.unwrap();
        info!("{is_dual_side:#?}");
        let mut params = OrderParams::new(is_dual_side, 6);
        order.side = OrderSide::Buy;
        params.margin_mode = Some(MarginMode::Cross);
        order.order_type = OrderType::Limit;
        order.time_in_force = TimeInForce::GTC;
        order.price = Some(0.175);

        // let symbol = order.symbol.clone();
        // let cid = order.cid.clone().unwrap();

        // Market long open
        let order_id = rest
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("{order_id:#?}");
        tokio::time::sleep(std::time::Duration::from_secs(1)).await;

        // Cancel order
        // rest.cancel_order(symbol, OrderId::ClientOrderId(cid))
        //     .await
        //     .unwrap();
        // info!("{:#?}", ());

        // // Market long close
        // order.side = OrderSide::Sell;
        // let order_id = rest
        //     .post_order(order.clone(), params.clone())
        //     .await
        //     .unwrap();
        // info!("{:#?}", order_id);

        // // Market short open
        // order = Order::market_open(Symbol::new("BTC"), 0.01, PosSide::Short, exchange);
        // let order_id = rest
        //     .post_order(order.clone(), params.clone())
        //     .await
        //     .unwrap();
        // info!("{:#?}", order_id);

        // // Market short close
        // order.side = OrderSide::Buy;
        // let order_id = rest
        //     .post_order(order.clone(), params.clone())
        //     .await
        //     .unwrap();
        // info!("{:#?}", order_id);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_swap_batch_post_order() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateSwap::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");

        let mut order = Order::limit_open(
            symbol,
            140.0,
            1.0,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        order.order_type = OrderType::Limit;
        order.side = OrderSide::Buy;

        let params = OrderParams::new(false, 1);
        let orders: Vec<Order> = vec![order];
        let order_id = rest.post_batch_order(orders, params.clone()).await.unwrap();
        info!("-->{order_id:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_amend_order() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let exchange = config.exchange;

        let rest = GateSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let mut order = Order::limit_open(
            symbol.clone(),
            60000.0,
            0.01,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("{order_id:#?}");
        order.id = order_id;
        order.price = Some(60001.0);
        let order_id = rest.amend_order(order).await.unwrap();
        info!("{order_id:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;

        let rest = GateSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol.clone(),
            60000.0,
            0.01,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let cid = order.cid.clone().unwrap();
        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = rest.post_order(order, params).await.unwrap();
        info!("{order_id:#?}");
        tokio::time::sleep(std::time::Duration::from_secs(1)).await;
        rest.cancel_order(symbol, OrderId::ClientOrderId(cid))
            .await
            .unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_swap_batch_cancel_order() {
        let config = test_config();
        // let exchange = config.exchange;

        let rest = GateSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        // let order = Order::limit_open(
        //     symbol.clone(),
        //     60000.0,
        //     0.01,
        //     PosSide::Long,
        //     TimeInForce::GTC,
        //     exchange,
        // );
        // let is_dual_side = rest.is_dual_side().await.unwrap();
        // let params = OrderParams::new(is_dual_side, 1);
        // let mut order_ids: Vec<String> = vec![];
        // for _ in 0..5 {
        //     let order_id = rest
        //         .post_order(order.clone(), params.clone())
        //         .await
        //         .unwrap();
        //     order_ids.push(order_id);
        // }
        // info!("post orders {:#?}", order_ids);

        // tokio::time::sleep(std::time::Duration::from_secs(1)).await;
        let ids = rest.batch_cancel_order(symbol).await.unwrap();
        info!("cancel orders {ids:#?}");
        // assert_eq!(ids, order_ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_fee_rate() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let fee = exchange.get_fee_rate(Symbol::new("BTC")).await.unwrap();
        info!("{fee:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_swap_get_balances() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let balances = exchange.get_balances().await.unwrap();
        info!("{balances:#?}");
        let usdt = get_usdt_balance(balances);
        info!("{usdt:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_swap_get_balances_loop() {
        let _ = fmt().pretty().with_max_level(LevelFilter::DEBUG).try_init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        loop {
            let balances = exchange.get_balances().await.unwrap();
            info!("{balances:#?}");
            let usdt = get_usdt_balance(balances);
            info!("{usdt:#?}");
            tokio::time::sleep(std::time::Duration::from_secs(20)).await;
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_position() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let btc = exchange.get_position(Symbol::new("TRUMP")).await.unwrap();
        info!("{btc:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_positions() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let positions = exchange.get_positions().await.unwrap();
        info!("{:#?}", positions.first());
        let btc = positions
            .into_iter()
            .filter(|x| x.symbol == Symbol::new("BTC"))
            .collect::<Vec<_>>();
        info!("{btc:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_leverage() {
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let _ = fmt()
            .pretty()
            .with_max_level(tracing_subscriber::filter::LevelFilter::DEBUG)
            .try_init();
        let symbol = Symbol::new("TRUMP");
        let resp = exchange.set_leverage(symbol.clone(), 11).await;
        info!("{resp:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_is_dual_side() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let is_dual_side = exchange.is_dual_side().await.unwrap();
        info!("{is_dual_side:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_dual_side() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        exchange.set_dual_side(true).await.unwrap();
        let is_dual_side = exchange.is_dual_side().await.unwrap();
        info!("{is_dual_side:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_create_sub_account() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSwap::new(config).await;
        rest.create_sub_account("test_subdasgassd".to_string(), None, None)
            .await
            .unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_sub_accounts() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let accounts = exchange.get_sub_accounts().await.unwrap();
        info!("{accounts:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_create_sub_account_api_key() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let uid = "********".to_string();
        let ips = None;
        let key_info = exchange.create_sub_account_api_key(uid, ips).await.unwrap();
        info!("{key_info:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let btc = exchange.get_open_orders(Symbol::new("BTC")).await.unwrap();
        info!("{btc:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_price_order() {
        // fmt().pretty().init();
        // let config = test_config();
        // let rest = GateSwap::new(config).await;
        // let symbol: Symbol = Symbol::new("TRUMP");

        // let take_profit = Some(CloseTrigger {
        //     trigger_price: TriggerPrice::ContractPrice(12.0),
        //     trigger_action: TriggerAction::Limit(12.0),
        // });
        // let stop_loss = Some(CloseTrigger {
        //     trigger_price: TriggerPrice::ContractPrice(10.0),
        //     trigger_action: TriggerAction::Limit(10.0),
        // });
        // let order_id = rest.post_stop_order(PostStopOrderParams{
        //     symbol,
        //     is_dual_side: false,
        //     pos_side: PosSide::Long,
        //     take_profit,
        //     stop_loss,
        //     ..Default::default()
        // }).await.unwrap();
        // info!("{:#?}", order_id);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_all_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSwap::new(config).await;
        let orders = rest.get_all_open_orders().await.unwrap();
        info!("len {}", orders.len());
        info!("{orders:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateSwap::new(config).await;
        let orders = rest
            .get_orders(Symbol::new("BTC"), 0, 1731464837000)
            .await
            .unwrap();
        info!("{orders:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_max_leverage() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let symbol = Symbol::new("SOL");

        let resp = exchange.get_max_leverage(symbol).await.unwrap();
        info!("{resp}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_max_position() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let symbol = Symbol::new("BTC");

        let resp = exchange.get_max_position(symbol, 50).await.unwrap();
        info!("{resp:?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_swap_batch_cancel_by_oids() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let ids = vec!["571403319501".to_string()];
        let resp = exchange
            .batch_cancel_order_by_ids(Some(symbol), Some(ids), None)
            .await
            .unwrap();
        info!("{resp:?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_swap_get_order_by_id() {
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let oid = OrderId::Id("577315278266".to_string());

        let resp = exchange.get_order_by_id(symbol, oid).await.unwrap();
        print!("{resp:?}")
    }

    #[tokio::test]
    async fn test_get_funding_fee() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let exchange = GateSwap::new(config).await;
        let fees = exchange
            .get_funding_fee(Symbol::new("BTC"), None, None)
            .await
            .unwrap();
        info!("{fees:#?}");
    }
}

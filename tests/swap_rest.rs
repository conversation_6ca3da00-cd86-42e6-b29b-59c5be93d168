#[cfg(test)]
mod tests {
    use okx::swap::rest::*;
    use quant_common::{base::*, test_config, time_ms, time_ns};
    use tracing::info;
    use tracing_subscriber::fmt;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_request_get() {
        fmt().pretty().init();
        let rest = OkxSwap::default();
        let params = sonic_rs::from_str(r#"{"instType":"SWAP","uly":"BTC-USD"}"#).unwrap();
        let user_request = UserRequest {
            method: "GET".to_owned(),
            path: "/api/v5/public/instruments".to_owned(),
            auth: false,
            query: Some(params),
            body: None,
            url: None,
            headers: None,
        };
        let resp = rest.request(user_request).await;
        assert!(resp.is_ok());
        info!("{:?}", resp.unwrap());
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_ticker() {
        fmt().pretty().init();
        let exchange = OkxSwap::default();
        let symbol = Symbol::new("BTC");
        let ticker = exchange.get_ticker(symbol).await.unwrap();
        info!("{:?}", ticker);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_tickers() {
        let exchange = OkxSwap::default();
        let tickers = exchange.get_tickers().await.unwrap();
        info!("{:?}", tickers);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_mark_price() {
        fmt().pretty().init();
        let exchange = OkxSwap::default();
        let symbol = Symbol::new("BTC");
        let mark_price = exchange.get_mark_price(Some(symbol)).await.unwrap();
        info!("{:?}", mark_price);

        let mark_price_all = exchange.get_mark_price(None).await.unwrap();
        info!("{:?}", mark_price_all);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_bbo_tickers() {
        let exchange = OkxSwap::default();
        let tickers = exchange.get_bbo_tickers().await.unwrap();
        info!("{:?}", tickers);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_depth() {
        // let exchange = OkxSwap::default();
        fmt().pretty().init();
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        info!("{:?}", exchange.url);
        let symbol = Symbol::new("BTC");
        let depth = exchange.get_depth(symbol, Some(5)).await.unwrap();
        info!("{:?}", depth);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instrument() {
        fmt().pretty().init();
        let exchange = OkxSwap::default();
        // let config = test_config();
        // let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("DOGE");
        let instrument = exchange.get_instrument(symbol).await.unwrap();
        info!("{:#?}", instrument);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instruments() {
        fmt().pretty().init();
        let exchange = OkxSwap::default();
        let instruments = exchange.get_instruments().await.unwrap();
        info!("Len: {}", instruments.len());
        info!("{:?}", instruments);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_funding_rate() {
        fmt().pretty().init();
        let exchange = OkxSwap::default();
        let symbol = Symbol::new("BTC");
        let funding_rate = exchange.get_funding_rate(symbol).await.unwrap();
        info!("{:?}", funding_rate);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_funding_rates() {
        fmt().pretty().init();
        let exchange = OkxSwap::default();
        let funding_rates = exchange.get_funding_rates().await.unwrap();
        info!("{:?}", funding_rates);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_order_by_id() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("DOGE");
        let order_id = OrderId::Id("2402199495343112192".to_string());
        let order = exchange.get_order_by_id(symbol, order_id).await.unwrap();
        info!("{:?}", order);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_all_orders() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let orders = exchange.get_all_open_orders().await.unwrap();
        info!("{:#?}", orders);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_orders() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let start_time = 1727712000000i64;
        let end_time = time_ms();
        let orders = exchange
            .get_orders(symbol, start_time, end_time)
            .await
            .unwrap();
        info!("Len: {}", orders.len());
        info!("{:#?}", orders);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_usdt_balance() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let balance = exchange.get_usdt_balance().await.unwrap();
        info!("{:?}", balance);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_fee_rate() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let fee_rate = exchange.get_fee_rate(symbol).await.unwrap();
        info!("{:?}", fee_rate);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_balances() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let balances = exchange.get_balances().await.unwrap();
        info!("{:?}", balances);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_position() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let positions = exchange.get_position(symbol).await.unwrap();
        info!("{:?}", positions);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_positions() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let positions = exchange.get_positions().await.unwrap();
        info!("{:?}", positions);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_leverage() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        exchange.set_leverage(symbol, 1).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_order() {
        let config = test_config();
        let exchange = config.exchange;
        let swap = OkxSwap::new(config).await;
        let mut order = Order::market_open(Symbol::new("SOL"), 99.0, PosSide::Long, exchange);

        info!("{:?}", order);
        let params = OrderParams {
            is_dual_side: true,
            leverage: 3,
            margin_mode: Some(MarginMode::Cross),
            take_profit: Some(CloseTrigger {
                trigger_price: TriggerPrice::MarkPrice(200.0),
                trigger_action: TriggerAction::Market,
            }),
            stop_loss: Some(CloseTrigger {
                trigger_price: TriggerPrice::MarkPrice(50.0),
                trigger_action: TriggerAction::Market,
            }),
            ..Default::default()
        };
        let order_id = swap
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("{}", order_id);

        // Limit Order
        order.order_type = OrderType::Limit;
        order.price = Some(1.5);
        order.amount = Some(0.01);

        // let order_id = swap
        //     .post_order(order.clone(), params.clone())
        //     .await
        //     .unwrap();
        // info!("{:?}", order_id);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_batch_order() {
        let config = test_config();
        let exchange = config.exchange;
        let swap = OkxSwap::new(config).await;
        let orders = vec![
            Order::market_open(Symbol::new("SOL"), 1.0, PosSide::Long, exchange),
            Order::market_open(Symbol::new("SOL"), 2.0, PosSide::Short, exchange),
        ];
        let params = OrderParams {
            is_dual_side: true,
            leverage: 3,
            margin_mode: Some(MarginMode::Isolated),
            take_profit: None,
            stop_loss: None,
            ..Default::default()
        };
        let order_ids = swap.post_batch_order(orders, params).await.unwrap();
        info!("{:?}", order_ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_order_by_ids() {
        let config = test_config();
        // let exchange = config.exchange;
        let swap = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        // let orders = vec![
        //     Order::limit_open(
        //         Symbol::new("SOL"),
        //         1.0,
        //         0.1,
        //         PosSide::Long,
        //         TimeInForce::GTC,
        //         exchange,
        //     ),
        //     Order::limit_close(
        //         Symbol::new("SOL"),
        //         2.0,
        //         0.1,
        //         PosSide::Long,
        //         TimeInForce::GTC,
        //         exchange,
        //     ),
        // ];
        // let params = OrderParams::new(false, 1);
        // let order_ids = swap.post_batch_order(orders, params).await.unwrap();
        // info!("{:?}", order_ids);
        // let ids = order_ids
        //     .success_list
        //     .iter()
        //     .map(|r| r.id.clone().unwrap())
        //     .collect();
        let ids = vec![
            "2025528421358534656".to_string(),
            "2025526841246777344".to_string(),
        ];
        let order_ids = swap
            .batch_cancel_order_by_ids(Some(symbol), Some(ids), None)
            .await
            .unwrap();
        info!("{:?}", order_ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_amend_order() {
        let config = test_config();
        let exchange = config.exchange;
        let swap = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let mut order = Order::limit_open(
            symbol.clone(),
            100.0,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let is_dual_side = swap.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);
        let order_id = swap.post_order(order.clone(), params).await.unwrap();
        info!("{:#?}", order_id);
        order.id = order_id.clone();
        order.price = Some(110.0);
        order.cid = Some(time_ns().to_string());
        let amend_order_id = swap.amend_order(order).await.unwrap();
        info!("{:#?}", amend_order_id);
        assert_eq!(order_id, amend_order_id);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() {
        let config = test_config();
        let exchange = config.exchange;
        let swap = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let order = Order::market_open(symbol.clone(), 2.0, PosSide::Short, exchange);
        let params = OrderParams::new(true, 1);
        let order_id = swap.post_order(order, params).await.unwrap();
        info!("{:?}", order_id);
        let _ = swap.cancel_order(symbol, OrderId::Id(order_id)).await;
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_is_dual_side() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let is_dual_side = exchange.is_dual_side().await.unwrap();
        info!("{:?}", is_dual_side);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_dual_side() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        exchange.set_dual_side(true).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_max_leverage() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");
        let max_leverage = exchange.get_max_leverage(symbol).await.unwrap();
        info!("{:?}", max_leverage);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_max_position() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let leverage = 10;
        let max_position = exchange.get_max_position(symbol, leverage).await.unwrap();
        info!("{:?}", max_position);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_deposit_address() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let address = exchange
            .get_deposit_address("USDT".to_string(), None, None)
            .await
            .unwrap();
        info!("{:?}", address);

        let address = exchange
            .get_deposit_address("USDT".to_string(), Some(Chain::Trc20), None)
            .await
            .unwrap();
        info!("{:?}", address);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_withdrawal() {
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let withdrwl_params = WithDrawlParams {
            asset: "USDT".to_string(),
            amt: 0.00000001,
            addr: WithdrawalAddr::InternalTransfer(InternalTransferParams::Email(
                "<EMAIL>".to_string(),
            )),
            cid: None,
        };
        exchange.withdrawal(withdrwl_params).await.unwrap();
    }
}

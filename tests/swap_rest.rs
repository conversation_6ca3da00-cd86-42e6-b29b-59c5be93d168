mod tests {
    use hyperliquid::{
        swap::rest::HyperLiquidSwap,
        util::{self, generate_128bit_hex},
    };
    use log::info;
    use quant_common::{
        base::{
            traits::SetLeverageParams, CloseTrigger, ExConfig, Order, OrderId, OrderParams,
            OrderSide, OrderType, PosSide, Rest, Symbol, TimeInForce, Transfer, TriggerAction,
            TriggerPrice, WalletType,
        },
        time_ms,
    };
    use serde::{Deserialize, Serialize};
    use std::{collections::HashMap, time::Duration};
    use time::Time;
    use tokio::time::sleep;
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        let toml_str = r#"
        exchange = "HyperLiquidSwap"
        key = "******************************************"
        secret ="16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808"
        passphrase = "passphrase"
        is_colo = false
        is_testnet = false
        is_unified = false
        params = { is_vip = "false", cookie = "some_cookie", code = "1234", org_id = "5678" }
    "#;
        toml::from_str(&toml_str).unwrap()
    }
    #[tokio::test]
    async fn test_create_wallet() {
        fmt().pretty().init();
        //Generated private key: 16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808
        //Wallet address: ******************************************
        let wallact_private = HyperLiquidSwap::new_wallect();
        info!("rsp={:?}", wallact_private)
    }

    #[tokio::test]
    async fn test_get_instruments() {
        fmt().pretty().init();
        let config = test_config();
        //Generated private key: 16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808
        //Wallet address: ******************************************
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex.get_instruments().await.expect("get instruments error");
        for v in rsp {
            info!("{:?}", v);
        }
    }

    #[tokio::test]
    async fn test_get_instrument() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_instrument(Symbol::new_with_quote(
                "HYPE",
                quant_common::base::QuoteCcy::USDC,
            ))
            .await
            .expect("get instruments error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_depth() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_depth(
                Symbol {
                    base: "PURR".to_string(),
                    quote: quant_common::base::QuoteCcy::USDC,
                },
                None,
            )
            .await
            .expect("get_ticker error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_bbo_ticker() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_bbo_ticker(Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            })
            .await
            .expect("get_ticker error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_ticker() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_ticker(Symbol {
                base: "HYPER".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            })
            .await
            .expect("get_ticker error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_balances() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex.get_balances().await.expect("get balances error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_set_leverage() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let params = SetLeverageParams {
            symbol: Symbol::new("BTC"),
            leverage: 2,
            extra: HashMap::new(),
        };
        let params = params.extra_param("is_cross", true);
        let _ = ex.set_leverage_ext(params).await.expect("set leverage err");
    }

    #[tokio::test]
    async fn test_get_fee_rate() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_fee_rate(Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            })
            .await
            .expect("get_fee_rate error");
        info!("rsp:{:?}", rsp);
    }

    #[tokio::test]
    async fn test_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let ex = HyperLiquidSwap::new(config).await;
        // Market order no support
        let mut order = Order::market_open(
            Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            },
            0.00010,
            PosSide::Long,
            exchange,
        );
        //  let mut order = Order::market_close(
        //     Symbol {
        //         base: "BTC".to_string(),
        //         quote: quant_common::base::QuoteCcy::USDT,
        //     },
        //     0.00010,
        //     PosSide::Long,
        //     exchange,
        // );
        order.cid = Some(generate_128bit_hex()); //cid 必须是128位的16进制字符串
                                                 // Limit order
                                                 // order.order_type = OrderType::Limit;
                                                 // order.time_in_force = TimeInForce::GTC;
                                                 // order.time_in_force=TimeInForce::IOC;
                                                 // order.time_in_force=TimeInForce::FOK;
                                                 // order.pos_side=Some(PosSide::Short);
                                                 // order.side=OrderSide::Sell;
        info!("{:?}", order.cid);
        order.price = Some(103315.0);

        //不能双向和保证金模式(this parameter won't have any effect until hedge mode is introduced)，可以设置杠杆
        let mut params = OrderParams::new(false, 10);
        // params.stop_loss = Some(CloseTrigger {
        //     trigger_price: TriggerPrice::MarkPrice(84540.),
        //     trigger_action: TriggerAction::Limit(84540.0),
        // });
        // params.take_profit = Some(CloseTrigger {
        //     trigger_price: TriggerPrice::MarkPrice(84550.),
        //     trigger_action: TriggerAction::Limit(84550.0),
        // }); //设置止盈单设置成功
        //注意买的时候数量是0.00014*leverage=0.00014*10=0.0014；当开仓成功后卖的时候(相反的仓位，此时设置的不生效)，就要卖0.0014，才能平仓
        let order_id = ex
            .post_order(order, params)
            .await
            .expect("post order error");
        info!("rsp={:?}", order_id)
        // {"status":"ok","response":{"type":"order","data":{"statuses":[{"resting":{"oid":75042133368,"cloid":"0xb9cb6f8b5475d8a96fbaa7bcb1c56825"}}]}}}
    }

    #[tokio::test]
    async fn test_get_all_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_all_open_orders()
            .await
            .expect("get_all_open_orders error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_open_orders(Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            })
            .await
            .expect("get open orders error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_order_by_id() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_order_by_id(
                Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDT,
                },
                OrderId::Id("100290914837".to_string()),
            )
            .await
            .expect("get open orders error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_get_orders() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let end_time = time_ms();
        let start_time = end_time - 1000 * 60 * 60 * 24 * 30;
        let rsp = ex
            .get_orders(
                Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDT,
                },
                start_time,
                end_time,
            )
            .await
            .expect("get orders error");
        info!("rsp={:?}", rsp);
    }

    #[tokio::test]
    async fn test_amend_order() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let order = quant_common::base::Order {
            id: "100291154939".to_string(),
            price: Some(87132.),
            amount: Some(0.00013),
            side: OrderSide::Buy,
            symbol: Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            },
            order_type: OrderType::Limit,
            time_in_force: TimeInForce::GTC,
            ..Default::default()
        };
        let order_id = ex.amend_order(order).await.expect("amend order failed");
        info!("amend order rsp:{:?}", order_id) //75051925663
    }

    #[tokio::test]
    async fn test_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .cancel_order(
                Symbol {
                    base: "BTC".to_string(),
                    quote: quant_common::base::QuoteCcy::USDT,
                },
                OrderId::Id("100291154939".to_string()),
            )
            .await
            .expect("cancel order by orderId error");
        info!("cancel order rsp:{:?}", rsp);
    }

    //测试批量下单
    #[tokio::test]
    async fn test_batch_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config.clone()).await;
        let mut order1 = Order::market_open(
            Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            },
            0.00012,
            PosSide::Long,
            config.exchange,
        );
        let mut order2 = Order::market_open(
            Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            },
            0.00012,
            PosSide::Long,
            config.exchange,
        );
        let mut order3 = Order::market_open(
            Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            },
            0.00012,
            PosSide::Long,
            config.exchange,
        );
        order1.cid = Some(generate_128bit_hex()); //cid 必须是128位的16进制字符串
        order2.cid = Some(generate_128bit_hex()); //cid 必须是128位的16进制字符串
        order3.cid = Some(generate_128bit_hex()); //cid 必须是128位的16进制字符串

        order1.order_type = OrderType::Limit;
        order2.order_type = OrderType::Limit;
        order3.order_type = OrderType::Limit;

        order1.time_in_force = TimeInForce::IOC;
        order2.time_in_force = TimeInForce::FOK;
        order3.time_in_force = TimeInForce::GTC;

        // order.pos_side=Some(PosSide::Short);
        // order.side=OrderSide::Sell;
        order1.price = Some(87133.);
        order2.price = Some(87134.);
        order3.price = Some(87130.);
        let orders = vec![order1, order2, order3];
        let params = OrderParams::new(false, 1);
        let rsp = ex
            .post_batch_order(orders, params)
            .await
            .expect("post batch order error");
        info!("rsp={:?}", rsp)
    }

    #[tokio::test]
    async fn test_batch_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .batch_cancel_order(Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            })
            .await
            .expect("batch_cancel_order error");
        info!("batch_cancel_order rsp:{:?}", rsp);
    }

    #[tokio::test]
    async fn test_get_funding_rates() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_funding_rates()
            .await
            .expect("get_funding_rates error");
        info!("get_funding_rates rsp:{:?}", rsp);
    }

    #[tokio::test]
    async fn test_get_mark_price() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            // .get_mark_price(Some(Symbol {
            //     base: "BTC".to_string(),
            //     quote: quant_common::base::QuoteCcy::USDT,
            // }))
            .get_mark_price(None)
            .await
            .expect("get_mark_price error");
        info!("rsp:{:?}", rsp);
    }

    #[tokio::test]
    async fn test_get_position() {
        fmt().pretty().init();
        let config = test_config();
        let ex: HyperLiquidSwap = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_position(Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            })
            .await
            .expect("get_position error");
        info!("rsp:{:?}", rsp);
    }
    #[tokio::test]
    async fn test_get_positions() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex.get_positions().await.expect("get_positions error");
        info!("rsp:{:?}", rsp);
    }
    #[tokio::test]
    async fn test_get_max_leverage() {
        fmt().pretty().init();
        let config = test_config();
        let ex = HyperLiquidSwap::new(config).await;
        let rsp = ex
            .get_max_leverage(Symbol {
                base: "HPOS".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            })
            .await
            .expect("get_max_leverage error");
        info!("rsp:{:?}", rsp);
    }
}

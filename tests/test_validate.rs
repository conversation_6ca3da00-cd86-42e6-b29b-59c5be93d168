#[cfg(test)]
mod tests {
    use std::collections::HashMap;

    use exchange_validator::{
        ws::{WebSocketConfig, WsValidator},
        ws_api::{WsApiValidator, WsValidatorConfigBuilder},
        GridValidationConfig, RestApiValidator, SimpleGridConfig, SimpleGridStrategy,
        ValidatorConfig, WebSocketAPIFactory, WsOrderConfig,
    };
    use okx::{
        spot::rest::OkxSpot,
        swap::{rest::*, ws::OkxSwapWs, ws_api::*},
    };
    use quant_common::{base::*, test_config, Result};
    use tracing::{info, Level};
    use tracing_subscriber::fmt;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_validate_okx_swap() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        let symbol = Symbol::new("SOL");

        let grid_config = SimpleGridConfig {
            symbol: symbol.clone(),
            grid_size: 10,          // 网格数量
            price_range: 0.1,       // 价格范围百分比，如 0.1 表示上下 10%
            quantity_per_grid: 0.1, // 每个网格的数量
        };

        // 示例用法
        let validate_config = GridValidationConfig::builder()
            .disable_funding_rate()
            .disable_balance()
            .build();

        // 验证所有配置， 等用于 GridValidationConfig::default()
        // let validate_config = GridValidationConfig::all_enabled();
        // 禁用所有配置
        // let validate_config = GridValidationConfig::none();

        // 获取当前价格
        let current_price = exchange.get_ticker(symbol).await?.close;
        let mut strategy = SimpleGridStrategy::new(exchange, grid_config, current_price);

        strategy.validate(validate_config).await?;
        strategy.show_reports();
        strategy.run().await?;
        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_validate_okx_spot_rest() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let exchange = OkxSpot::new(config).await;
        // let symbol = Symbol::new("SOL");
        let symbols = vec![Symbol::new("SOL")];
        // let config = ValidatorConfig::
        let validate_config = ValidatorConfig::from_file("./tests/validation_spot_config.toml")?;
        info!("validate_config: {:#?}", validate_config);

        let mut rest_validator = RestApiValidator::new(exchange, symbols, Some(validate_config));
        // rest_validator.set_report_path("./tests/report.csv");
        rest_validator.validate_all().await?;
        rest_validator.show_report();
        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_validate_okx_swap_rest() -> Result<()> {
        fmt().pretty().with_max_level(Level::INFO).init();
        let config = test_config();
        let exchange = OkxSwap::new(config).await;
        // let symbol = Symbol::new("SOL");
        let symbols = vec![Symbol::new("SOL")];
        // let config = ValidatorConfig::
        let validate_config = ValidatorConfig::from_file("./tests/validation_swap_config.toml")?;
        info!("validate_config: {:#?}", validate_config);

        let mut rest_validator = RestApiValidator::new(exchange, symbols, Some(validate_config));
        rest_validator.set_report_path("./tests/report_swap.csv");
        rest_validator.validate_all().await?;
        // rest_validator.show_report();
        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_validate_okx_swap_ws_api() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();

        let mut order_config = HashMap::new();
        order_config.insert(
            Symbol::new("SOL"),
            WsOrderConfig {
                order_price: 100.0,
                order_quantity: 0.1,
                order_params: OrderParams::default(),
            },
        );
        let validate_config = WsValidatorConfigBuilder::new()
            .disable_batch_cancel_order()
            .set_order_config(order_config)
            .set_amend_cancel_interval(2000)
            .build();
        info!("validate_config: {:#?}", validate_config);

        struct MyWsApiFactory;
        impl WebSocketAPIFactory for MyWsApiFactory {
            type Api = OkxSwapWsApi;

            async fn create_ws_api(config: ExConfig) -> Self::Api {
                OkxSwapWsApi::new(config).await
            }
        }

        let mut validator = WsApiValidator::<MyWsApiFactory>::new(validate_config, config);
        validator.validate_all().await?;

        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_validate_okx_swap_ws() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let ws = OkxSwapWs::new(config.clone()).await; // 任何实现了WebSocket trait的结构体
        let symbols = vec![Symbol::new("SOL"), Symbol::new("BTC")];
        let rest = OkxSwap::new(config).await;

        // // 示例1：只禁用某些特定功能
        // let validate_config = WebSocketConfig::builder()
        //     .disable_mark_price()
        //     .disable_depth()
        //     .depth_levels(10)
        //     .timeout(15)
        //     .build();

        // // 示例2：只测试私有频道
        // let validate_config = WebSocketConfig::builder()
        //     .disable_public_all()
        //     .timeout(20)
        //     .build();

        // // 示例3：只测试公共频道
        // let validate_config = WebSocketConfig::builder()
        //     .disable_private_all()
        //     .depth_levels(5)
        //     .timeout(30)
        //     .build();

        // 示例4：默认配置，测试所有功能
        let validate_config = WebSocketConfig::builder()
            .disable_funding_consistency()
            // .disable_depth()
            .disable_funding()
            .disable_mark_price()
            .timeout(60)
            .build();
        let mut validator = WsValidator::new(ws, symbols, Some(validate_config), Some(rest));
        // 验证所有功能， public和private统一验证
        // validator.validate_all().await?;

        // 验证所有功能， public和private需要分开的情况
        // validator.validate_separately().await?;

        // 只验证public
        validator.validate_public().await?;

        // 只验证private
        // validator.validate_private().await?;

        validator.show_report();

        Ok(())
    }
}

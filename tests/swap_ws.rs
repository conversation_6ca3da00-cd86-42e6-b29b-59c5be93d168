#[cfg(test)]
mod tests {
    // use std::sync::mpsc::channel;

    use std::str::FromStr;
    use std::sync::Arc;
    use std::time::Duration;

    use gate::swap::ws::GateSwapWs;
    use quant_common::base::*;
    use quant_common::*;
    use tokio::time::timeout;
    use tracing_subscriber::fmt;
    // use tracing_subscriber::fmt;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_all() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let mut ws = GateSwapWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("PEPE")]);
        let subs = Subscribes::new(
            0,
            DefaultWsHandler,
            vec![SubscribeChannel::Kline(KlineWsParams {
                symbols: symbols.clone(),
                interval: KlineInterval::from_str("1m").unwrap(),
            })],
        );
        // let subs = Subscribes::default_swap(Arc::new(symbols));
        // time out 5 seconds
        match timeout(Duration::from_secs(10), ws.run(subs)).await {
            Ok(Ok(_)) => {
                println!("WebSocket finished within 5 seconds.");
            }
            Ok(Err(_)) => {
                println!("WebSocket timeout.");
            }
            Err(_) => {
                println!("WebSocket error.");
            }
        }
        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_pri() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let mut ws = GateSwapWs::new(config).await;
        let symbols = vec![Symbol::new("TRUMP")];
        let subs = Subscribes::default_swap_pri(Arc::new(symbols));
        // time out 30 seconds
        match timeout(Duration::from_secs(30), ws.run(subs)).await {
            Ok(Ok(_)) => {
                println!("WebSocket finished within 30 seconds.");
            }
            Ok(Err(_)) => {
                println!("WebSocket timeout.");
            }
            Err(_) => {
                println!("WebSocket timeout as expected.");
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use okx::swap::ws::*;
    use quant_common::{base::*, test_config};

    use tracing::{info, level_filters::LevelFilter};
    use tracing_subscriber::fmt;

    #[test]
    fn test_symbols_from() {
        let symbols = vec![Symbol::new("BTC"), Symbol::new("ETH")];
        let symbols = symbols_from(&symbols);
        assert_eq!(
            symbols,
            vec!["BTC-USDT".to_string(), "ETH-USDT".to_string()]
        );
    }

    fn custom_symbols() -> Vec<Symbol> {
        vec![
            Symbol::new("BTC"),
            // Symbol::new("ETH"),
            Symbol::new("SOL"),
            Symbol::new("DOGE"),
        ]
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_pub() -> quant_common::Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let mut ws = OkxSwapWs::new(config).await;
        info!("{:?}", ws);
        let symbols = Arc::new(custom_symbols());
        let subs = Subscribes::new(
            0,
            DefaultWsHandler,
            vec![
                SubscribeChannel::MarkPrice(symbols.clone()),
                SubscribeChannel::Funding(symbols.clone()),
                SubscribeChannel::Bbo(symbols.clone()),
                SubscribeChannel::Depth(DepthWsParams {
                    symbols: symbols.clone(),
                    levels: 5,
                }),
                SubscribeChannel::Trade(symbols.clone()),
            ],
        );
        ws.run(subs).await?;
        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_private() -> quant_common::Result<()> {
        fmt().with_max_level(LevelFilter::DEBUG).pretty().init();
        let config = test_config();
        let mut ws = OkxSwapWs::new(config).await;
        let symbols = Arc::new(custom_symbols());
        let subs = Subscribes::new(
            0,
            DefaultWsHandler,
            vec![
                SubscribeChannel::Balance,
                SubscribeChannel::Order(symbols.clone()),
                // SubscribeChannel::Position(symbols.clone()),
                // SubscribeChannel::FundingFee(symbols.clone()),
            ],
        );
        ws.run(subs).await?;
        Ok(())
    }
}

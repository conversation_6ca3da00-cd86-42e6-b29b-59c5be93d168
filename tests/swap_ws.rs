use hyperliquid::swap::ws::HyperLiquidSwapWs;

mod tests {
    use ethers::types::H160;
    use hyperliquid::{swap::ws::HyperLiquidSwapWs, util};
    use log::info;
    use quant_common::base::WebSocket;
    use quant_common::{
        base::{
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DepthWsParams, ExConfig, Order, OrderId, OrderParams, OrderSide,
            OrderType, PosSide, Rest, SubscribeChannel, Subscribes, Symbol, TimeInForce, Transfer,
            WalletType,
        },
        time_ms, Result,
    };
    use serde::{Deserialize, Serialize};
    use std::sync::Arc;
    use std::time::Duration;
    use time::Time;
    use tokio::time::sleep;
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        let toml_str = r#"
        exchange = "HyperLiquidSwap"
        key = "******************************************"
        secret ="16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808"
        passphrase = "passphrase"
        is_colo = false
        is_testnet = false
        is_unified = false
        params = { is_vip = "false", cookie = "some_cookie", code = "1234", org_id = "5678" }
    "#;
        toml::from_str(&toml_str).unwrap()
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_pub() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let mut ws = HyperLiquidSwapWs::new(config).await;
        let symbols = Arc::new(vec![
            //Symbol::new("ETH")
            Symbol {
                base: "BTC".to_string(),
                quote: quant_common::base::QuoteCcy::USDT,
            },
        ]);
        let channels = vec![
            // SubscribeChannel::MarkPrice(symbols.clone()),
            // SubscribeChannel::Funding(symbols.clone()),
            // SubscribeChannel::Bbo(symbols.clone()),
            // SubscribeChannel::Depth(DepthWsParams::new(symbols.clone(), 5)),
            // SubscribeChannel::Trade(symbols.clone()),
            SubscribeChannel::Order(symbols.clone()),
            SubscribeChannel::Balance,
            SubscribeChannel::Position(symbols.clone()),
            // SubscribeChannel::FundingFee(symbols.clone()),
        ];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await?;
        Ok(())
    }
}

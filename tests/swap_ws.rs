#[cfg(test)]
mod tests {
    use rustc_hash::FxHashSet;
    use std::{
        fs::read_to_string,
        sync::{
            Arc,
            atomic::{self, AtomicI64},
        },
        time::Duration,
    };
    use tokio::time::timeout;
    use tracing::info;
    use tracing_subscriber::fmt;

    use quant_common::{
        Result,
        base::{model::*, *},
        qerror,
    };

    use binance::{
        swap::{rest::BinanceSwap, ws::BinanceSwapWs},
        util::is_ed25519_secret,
    };

    fn test_config() -> Result<ExConfig> {
        let s = read_to_string("swap.toml")?;
        let config: ExConfig = toml::from_str(&s)?;
        if is_ed25519_secret(&config.secret) {
            return Err(qerror!("secret is ed25519 secret"));
        }
        Ok(config)
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn test_ws_depth() -> Result<()> {
        let _ = fmt().try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()?
        };
        let mut ws = BinanceSwapWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("BTC"), Symbol::new("ETH")]);

        #[derive(Clone)]
        struct DepthWsHandler;

        impl WsHandler for DepthWsHandler {
            async fn on_depth(&self, depth: Depth, _: Exchange) -> Result<()> {
                tracing::info!("{depth:?}");
                Ok(())
            }
        }

        let subs = Subscribes::new(
            0,
            DepthWsHandler,
            vec![SubscribeChannel::Depth(DepthWsParams::new(symbols, 4))],
        );
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn test_ws_price() -> Result<()> {
        let _ = fmt().try_init();
        let config = Default::default();
        let mut ws = BinanceSwapWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("PEPE")]);

        let subs = Subscribes::new(
            0,
            DefaultWsHandler,
            vec![
                SubscribeChannel::MarkPrice(symbols.clone()),
                SubscribeChannel::Trade(symbols.clone()),
            ],
        );
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn test_symbols_ws_depth() -> Result<()> {
        let _ = fmt().pretty().try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()?
        };
        let mut ws = BinanceSwapWs::new(config.clone()).await;
        let rest = BinanceSwap::new(config).await;
        let xs = rest.get_instruments().await?;
        let symbols = xs
            .into_iter()
            .map(|x| x.symbol)
            .take(20)
            .collect::<Vec<_>>();
        let symbols = Arc::new(symbols);

        #[derive(Clone)]
        struct DepthWsHandler;

        impl WsHandler for DepthWsHandler {
            async fn on_depth(&self, depth: Depth, _: Exchange) -> Result<()> {
                tracing::info!("------------------- {} -------------------", depth.symbol);
                for ask in depth.asks.iter().rev() {
                    tracing::info!("asks: {:.2} @ {:.2}", ask.amount * ask.price, ask.price);
                }
                for bid in depth.bids {
                    tracing::info!("bids: {:.2} @ {:.2}", bid.amount * bid.price, bid.price);
                }
                Ok(())
            }
        }

        let subs = Subscribes::new(
            0,
            DepthWsHandler,
            vec![SubscribeChannel::Depth(DepthWsParams::new(symbols, 1))],
        );
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn test_ws_depth_stream_rebuild() -> Result<()> {
        let _ = fmt().try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()?
        };
        let mut ws = BinanceSwapWs::new(config.clone()).await;
        let symbols = Arc::new(vec![Symbol::new("SOL")]);

        #[derive(Clone)]
        struct DepthWsHandler {
            count: Arc<AtomicI64>,
        }

        impl WsHandler for DepthWsHandler {
            async fn on_depth(&self, _: Depth, _: Exchange) -> Result<()> {
                let count = self.count.fetch_add(1, atomic::Ordering::AcqRel);
                if count % 600 == 0 {
                    tracing::info!("count: {count}");
                }
                Ok(())
            }
        }

        let subs = Subscribes::new(
            0,
            DepthWsHandler {
                count: Arc::new(AtomicI64::new(1)),
            },
            vec![SubscribeChannel::Depth(DepthWsParams::new(symbols, 1))],
        );
        ws.run(subs).await
    }

    #[tokio::test]
    async fn test_ws() {
        let _ = fmt().try_init();
        let config = test_config().unwrap();
        let mut ws = BinanceSwapWs::new(config).await;
        let symbols = ["BTC", "NEIRO", "RAYSOL"]
            .iter()
            .map(|s| Symbol::new(s))
            .collect();

        let subs = Subscribes::default_swap(Arc::new(symbols));

        let r = timeout(Duration::from_secs(10), ws.run(subs)).await;
        assert!(r.is_err());
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn test_pri() -> Result<()> {
        let _ = fmt().try_init();
        let config = test_config()?;
        let mut ws = BinanceSwapWs::new(config).await;
        let symbols = ["BNB", "ETH", "PEPE", "RAYSOL", "SOL", "BTC", "ADA"]
            .into_iter()
            .map(Symbol::new)
            .collect::<Vec<_>>();
        let symbols = Arc::new(symbols);

        let subs = Subscribes::default_swap_pri(symbols);
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "监控代码，手动运行"]
    async fn test_funding() -> Result<()> {
        let _ = fmt().try_init();
        let config = test_config()?;
        let mut ws = BinanceSwapWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("PEPE")]);
        let channels = vec![SubscribeChannel::Funding(symbols.clone())];

        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "监控代码，手动运行"]
    async fn test_funding_fee() -> Result<()> {
        let _ = fmt().try_init();
        let config = test_config()?;
        let mut ws = BinanceSwapWs::new(config.clone()).await;
        let rest = BinanceSwap::new(config).await;
        let positions = rest.get_positions().await?;
        let symbols: FxHashSet<_> = positions.into_iter().map(|p| p.symbol.clone()).collect();
        let symbols = Arc::new(symbols.into_iter().collect::<Vec<_>>());
        info!("symbols 数量: {}", symbols.len());
        let channels = vec![SubscribeChannel::FundingFee(symbols.clone())];

        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "监控代码，手动运行"]
    async fn test_on_order() -> Result<()> {
        let _ = fmt().try_init();
        let config = test_config()?;
        let mut ws = BinanceSwapWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("ALT")]);
        let channels = vec![
            // SubscribeChannel::Order(symbols.clone()),
            SubscribeChannel::OrderAndFill(symbols.clone()),
        ];

        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "监控代码，手动运行"]
    async fn test_on_position() -> Result<()> {
        let _ = fmt().try_init();
        let config = test_config()?;
        let mut ws = BinanceSwapWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("PEPE")]);
        let channels = vec![SubscribeChannel::Position(symbols.clone())];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "监控代码，手动运行"]
    async fn test_on_balance() -> Result<()> {
        let _ = fmt().try_init();
        let config = test_config()?;
        let mut ws = BinanceSwapWs::new(config).await;
        let channels = vec![SubscribeChannel::Balance];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await
    }

    #[tokio::test]
    async fn test_ws_kline() {
        let _ = fmt().with_target(false).try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config().unwrap()
        };
        let mut ws = BinanceSwapWs::new(config.clone()).await;
        let symbols = Arc::new(
            vec!["ETH", "SATS"]
                .into_iter()
                .map(Symbol::new)
                .collect::<Vec<_>>(),
        );
        let channels = [KlineInterval::Min1, KlineInterval::Day1]
            .into_iter()
            .map(|interval| (symbols.clone(), interval))
            .map(|(symbols, interval)| SubscribeChannel::Kline(KlineWsParams { symbols, interval }))
            .collect();
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        let ret = timeout(Duration::from_secs(40), ws.run(subs)).await;
        assert!(ret.is_err());
    }
}

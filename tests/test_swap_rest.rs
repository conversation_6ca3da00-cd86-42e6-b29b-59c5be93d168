use bybit::swap::rest::*;
use quant_common::Result;
use quant_common::base::Rest;
use quant_common::base::traits::{GetFundingRateHistoryParams, GetKlineParams};
use quant_common::base::*;
use quant_common::{
    base::{Quote<PERSON>cy, Symbol},
    test_config,
};
use reqwest::Method;
use std::collections::HashMap;
use std::time::Duration;
use tokio::time::sleep;
use tracing::{debug, info};

fn log() {
    let _ = tracing_subscriber::fmt()
        .pretty()
        .with_max_level(tracing::Level::DEBUG)
        .try_init();
}
#[cfg(test)]
mod pub_test {
    use super::*;

    #[tokio::test]
    async fn test_get_ticker() {
        log();

        let config = test_config();

        let rest = BybitSwap::new(config).await;
        let result = rest
            .get_ticker(Symbol {
                base: "PEPE".to_string(),
                quote: QuoteCcy::USDT,
                contract_type: ContractType::Normal,
            })
            .await
            .unwrap();
        info!("Ticker result: {:#?}", result);
    }

    #[tokio::test]
    async fn test_get_tickers() {
        log();
        let config = test_config();

        let _exchange = config.exchange;

        let rest = BybitSwap::new(config).await;

        let x = rest.get_tickers().await.unwrap();
        info!("{:#?}", x);
    }

    #[tokio::test]
    async fn test_get_bbo_ticker() {
        log();
        let config = test_config();

        let _exchange = config.exchange;

        let rest = BybitSwap::new(config).await;
        let x = rest.get_bbo_ticker(Symbol::new("PEPE")).await.unwrap();
        info!("{:#?}", x);
    }

    #[tokio::test]
    async fn test_get_bbo_tickers() {
        log();
        let config = test_config();

        let _exchange = config.exchange;

        let rest = BybitSwap::new(config).await;

        let x = rest.get_bbo_tickers().await.unwrap();
        info!("{:#?}", x);
    }

    #[tokio::test]
    async fn test_get_depth() {
        log();
        let config = test_config();

        let _exchange = config.exchange;

        let rest = BybitSwap::new(config).await;

        let x = rest.get_depth(Symbol::new("PEPE"), Some(2)).await.unwrap();
        info!("{:#?}", x);
    }

    #[tokio::test]
    async fn test_get_instrument() {
        log();
        let config = test_config();

        let _exchange = config.exchange;

        let rest = BybitSwap::new(config).await;
        // rest.init_symbol_map().await.expect("初始化symbol map失败");
        // info!("{:?}", rest.symbol_map);
        let x = rest.get_instrument(Symbol::new("PEPE")).await.unwrap();
        info!("{:#?}", x);
    }

    #[tokio::test]
    async fn test_get_instruments() {
        log();
        let config = test_config();

        let _exchange = config.exchange;

        let rest = BybitSwap::new(config).await;

        let x = rest.get_instruments().await.unwrap();
        let x1: Vec<Instrument> = x
            .into_iter()
            .filter(|i| i.symbol.to_string().starts_with("PEPE"))
            .collect();
        info!("{:#?}", x1);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_funding_rates() {
        log();
        let config = test_config();

        let _exchange = config.exchange;

        let rest = BybitSwap::new(config).await;
        let x = rest.get_funding_rates().await.unwrap();
        info!("{:#?}", x);
    }

    #[tokio::test]
    async fn test_get_mark_price() {
        log();
        let config = test_config();

        let rest = BybitSwap::new(config).await;
        let x = rest
            .get_mark_price(Some(Symbol::new("PEPE")))
            .await
            .unwrap();
        info!("{:#?}", x);
    }

    #[tokio::test]
    async fn test_get_klines_ext() {
        log();
        let config = test_config();

        let rest = BybitSwap::new(config).await;
        let x = rest
            .get_klines_ext(GetKlineParams::new(Symbol::new("PEPE"), KlineInterval::Min1).limit(2))
            .await
            .unwrap();
        info!("{:#?}", x);
    }
}

#[cfg(test)]
mod pri {
    use quant_common::time_ns;

    use super::*;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_send_request() {
        log();
        let config = test_config();

        info!("{:#?}", config);
        let _exchange = config.exchange;

        let rest = BybitSwap::new(config).await;
        let mut map: HashMap<&str, &str> = HashMap::new();
        map.insert("accountType", "SPOT");
        let result: Result<String> = rest
            .req(Method::GET, "/v5/account/wallet-balance", &map, true)
            .await;
        debug!("{:?}", result.expect("err"));
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_leverage() {
        tracing_subscriber::fmt()
            .pretty()
            .with_max_level(tracing::Level::DEBUG)
            .init();
        let config = test_config();

        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        rest.set_leverage(Symbol::new("ETH"), 7).await.unwrap();

        rest.set_leverage(Symbol::new("ETH"), 7).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_is_dual_side() {
        tracing_subscriber::fmt()
            .pretty()
            .with_max_level(tracing::Level::DEBUG)
            .init();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let result = rest.is_dual_side().await.unwrap();

        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_dual_side() {
        tracing_subscriber::fmt()
            .pretty()
            .with_max_level(tracing::Level::DEBUG)
            .init();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let result = rest.is_dual_side().await.unwrap();
        info!("{:?}", result);
        let result1 = rest.set_dual_side(true).await;
        info!("{:?}", result1);
        let result = rest.is_dual_side().await;
        info!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_fee_rate() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let result = rest.get_fee_rate(Symbol::new("BTC")).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_balance() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let rest = BybitSwap::new(config).await;

        // Test get_balance for a specific asset
        let asset = "USDT";
        let result = rest.get_balance(asset).await.unwrap();
        info!("{:#?}", result);

        // Test get_usdt_balance
        let usdt_result = rest.get_usdt_balance().await.unwrap();
        info!("usdt=={:#?}", usdt_result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_balances() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let result = rest.get_balances().await.unwrap();
        info!("{:#?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_order_by_id() {
        log();
        let config = test_config();
        let exchange = BybitSwap::new(config).await;
        let symbol = Symbol::new("BTC");

        // post_limit order btc 60000, 0.0001
        let order = Order::limit_open(
            symbol.clone(),
            60000.0,
            0.001,
            PosSide::Long,
            TimeInForce::GTC,
            Exchange::BitgetSwap,
        );
        let is_dual_side = exchange.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);
        let cid = order.cid.clone().unwrap();
        println!("{cid:#?}");
        let order_id = exchange.post_order(order.clone(), params).await.unwrap();
        println!("{order_id:#?}");

        let order_id = exchange
            .get_order_by_id(symbol.clone(), OrderId::Id(order_id))
            .await;
        println!("{order_id:#?}");

        let order_id = exchange
            .get_order_by_id(symbol.clone(), OrderId::ClientOrderId(cid.clone()))
            .await;
        println!("{order_id:#?}");

        // Cancel
        exchange
            .cancel_order(symbol, OrderId::ClientOrderId(cid))
            .await
            .unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_account_info() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let x = rest.get_account_info().await;
        debug!("{:?}", x);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_batch_order() {
        log();
        let config = test_config();
        let exchange = config.exchange;

        println!("{config:#?}");
        let rest = BybitSwap::new(config).await;
        // Market order
        let order = Order::limit_open(
            Symbol::new("SOL"),
            100.,
            0.1,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let is_dual_side = rest.is_dual_side().await.unwrap();
        let params = OrderParams::new(is_dual_side, 1);

        let mut orders: Vec<Order> = (0..5)
            .map(|_| {
                let mut order = order.clone();
                order.cid = Some(time_ns().to_string());
                order
            })
            .collect();

        let mut fail_order = order.clone();
        fail_order.amount = Some(100000.0);
        orders.push(fail_order);
        let order_id = rest.post_batch_order(orders, params.clone()).await.unwrap();
        println!("{order_id:#?}");

        // batch cancel
        let result = rest.batch_cancel_order(Symbol::new("SOL")).await;
        println!("{result:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_amend_order() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let order = Order::limit_open(
            Symbol::new("ETH"),
            3310.0,
            0.01,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let mut order_modify = order.clone();
        let params = OrderParams::new(true, 1);
        let vec = vec![order];
        let x = rest.post_batch_order(vec, params).await;
        debug!("{:?}", x);
        sleep(Duration::from_secs(5)).await;
        let a = x.unwrap().success_list.pop().unwrap().cid;
        order_modify.cid = a;
        order_modify.price = Some(3305.0);
        debug!("{:#?}", order_modify);
        let result = rest.amend_order(order_modify).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_max_leverage() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let symbol = Symbol::new("BTC");
        let result = rest.get_max_leverage(symbol).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let order = Order::limit_open(
            Symbol::new("DOGE"),
            0.06,
            100.0,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );

        let params = OrderParams::new(false, 1);
        let x = rest.post_order(order, params).await;
        debug!("{:?}", x);
        sleep(Duration::from_secs(1)).await;
        let result = rest
            .cancel_order(Symbol::new("DOGE"), OrderId::Id(x.unwrap()))
            .await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_order() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let order = Order::limit_open(
            Symbol::new("DOGE"),
            0.233,
            300.0,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );

        let order1 = Order::limit_open(
            Symbol::new("DOGE"),
            0.2331,
            300.0,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let params = OrderParams::new(true, 1);
        let vec = vec![order, order1];
        let x = rest.post_batch_order(vec, params).await;
        debug!("{:?}", x);
        sleep(Duration::from_secs(5)).await;
        let result = rest.batch_cancel_order(Symbol::new("DOGE")).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_order_by_ids() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let order = Order::limit_open(
            Symbol::new("PEPE"),
            0.018400,
            500.0,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let order1 = Order::limit_open(
            Symbol::new("PEPE"),
            0.01875,
            500.0,
            PosSide::Short,
            TimeInForce::GTC,
            exchange,
        );
        let params = OrderParams::new(true, 1);
        let vec = vec![order, order1];
        let x = rest.post_batch_order(vec, params).await;
        debug!("{:?}", x);
        sleep(Duration::from_secs(5)).await;
        let mut list = x.unwrap().success_list;
        let id = list.pop().unwrap().id.unwrap();
        let id1 = list.pop().unwrap().id.unwrap();
        let result = rest
            .batch_cancel_order_by_ids(Some(Symbol::new("ETH")), Some(vec![id, id1]), None)
            .await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_position() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let btc = Symbol::new("DOGE");
        let result = rest.get_position(btc).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_positions() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let result = rest.get_positions().await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_max_position() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let btc = Symbol::new("BTC");
        let result = rest.get_max_position(btc, 21).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_account_mode() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let result = rest.get_account_mode().await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_funding_fee() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let result = rest.get_funding_fee(Symbol::new("BTC"), None, None).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_withdrawal() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let params = WithDrawlParams {
            cid: None,
            asset: "USDT".to_string(),
            amt: 5.0,
            addr: WithdrawalAddr::InternalTransfer(InternalTransferParams::Uid(
                "*********".to_string(),
            )),
        };
        let result = rest.withdrawal(params).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_user_id() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let result = rest.get_user_id().await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_transfer() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let transfer = Transfer {
            from: WalletType::Spot,
            to: WalletType::CoinFuture,
            asset: "USDT".to_string(),
            amount: 1.0,
        };
        let result = rest.transfer(transfer).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_sub_transfer() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let master = "*********".to_string();
        let sub = "*********".to_string();
        let sub_transfer = SubTransfer {
            cid: None,
            from_account: Option::from(sub),
            to_account: Option::from(master),
            from: WalletType::Spot,
            to: WalletType::CoinFuture,
            asset: "USDT".to_string(),
            amount: 1.0,
            direction: Default::default(),
        };
        let result = rest.sub_transfer(sub_transfer).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_margin_mode() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let result = rest
            .get_margin_mode(Symbol::new("BTC"), "".to_string())
            .await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_margin_mode() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;

        let result = rest
            .set_margin_mode(Symbol::new("BTC"), "".to_string(), MarginMode::Cross)
            .await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_coin_chain() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let result = rest.get_coin_chain("USDT").await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_mark_price_kline() {
        log();
        let config = test_config();

        debug!("{:#?}", config);
        let _exchange = config.exchange;
        let rest = BybitSwap::new(config).await;
        let result = rest.get_market_price_kline(Symbol::new("BTC")).await;
        debug!("{:?}", result);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_deposit_address() {
        log();
        let config = test_config();
        let rest = BybitSwap::new(config).await;

        // 测试 USDT 的充值地址
        let usdt_addresses = rest
            .get_deposit_address("USDT".to_string(), None, None)
            .await
            .unwrap();
        debug!("USDT deposit addresses: {:#?}", usdt_addresses);
        assert!(
            !usdt_addresses.is_empty(),
            "USDT deposit addresses should not be empty"
        );
    }

    #[tokio::test]
    async fn test_get_klines_ext() {
        let config = ExConfig {
            exchange: Exchange::BybitSwap,
            key: "test_key".to_string(),
            secret: "test_secret".to_string(),
            is_testnet: true,
            ..Default::default()
        };

        let rest = BybitSwap::new(config).await;

        let params = GetKlineParams::new(Symbol::new("BTC"), KlineInterval::Min1).limit(10);

        let result = rest.get_klines_ext(params).await;
        println!("{result:?}");
        // 由于是测试网络且没有真实的API密钥，这个测试可能会失败
        // 但我们可以验证方法是否正确实现
        match result {
            Ok(kline) => {
                assert_eq!(kline.symbol.base, "BTC");
                assert_eq!(kline.interval, KlineInterval::Min1);
                assert!(!kline.candles.is_empty());
            }
            Err(_) => {
                // 在测试环境中，API调用可能会失败，这是正常的
                println!("API调用失败，这在测试环境中是正常的");
            }
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_funding_rates_history_ext() {
        log();
        let config = test_config();
        debug!("{:#?}", config);
        let rest = BybitSwap::new(config).await;
        let params = GetFundingRateHistoryParams {
            symbol: Some(Symbol::new("BTC")),
            since_secs: None,
            limit: 5,
            extra: HashMap::new(),
        };
        let result = rest.get_funding_rates_history_ext(params).await;
        debug!("{:?}", result);
    }
}

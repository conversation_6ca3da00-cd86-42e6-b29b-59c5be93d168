
# 启用或禁用各项功能检查的配置
[enabled_checks]
ticker = true                   # 检查获取最新行情ticker功能
bbo_ticker = true               # 检查获取最优买卖价(BBO)功能
depth = true                    # 检查获取订单深度数据功能
instrument = true               # 检查获取交易对信息功能
funding_rate = true             # 检查获取资金费率功能
balance = true                  # 检查获取账户余额功能
user_id = false                  # 检查获取用户ID功能
account_info = false            # 检查获取账户信息功能
leverage = true                 # 检查设置杠杆倍数功能
max_leverage = false            # 检查获取最大杠杆倍数功能
dual_side = true                # 检查双向持仓设置功能
fee_rate = true                 # 检查获取手续费率功能
fee_discount_info = false       # 检查获取手续费折扣信息功能
fee_discount_enabled = false    # 检查是否启用手续费折扣功能
deposit_address = false         # 检查获取充值地址功能
withdrawal = false              # 检查提现功能
transfer =  true                # 检查资金划转功能
margin_mode = false             # 检查设置保证金模式功能
post_order = true               # 检查下单功能
post_batch_order = true         # 检查批量下单功能
get_order_by_id = true          # 检查通过ID查询订单功能
get_open_orders = true          # 检查获取当前未成交订单功能
get_all_open_orders = true      # 检查获取所有未成交订单功能
get_orders = true               # 检查获取历史订单功能
amend_order = true              # 检查修改订单功能
cancel_order = true             # 检查取消订单功能
batch_cancel_order = true      # 检查批量取消订单功能（按条件）
batch_cancel_order_by_ids = true # 检查批量取消订单功能（按ID）
position = false                 # 检查持仓查询功能
max_position = false             # 检查最大持仓限制功能
borrow_coin = false             # 检查借币功能
get_borrow = false              # 检查获取借币信息功能
get_borrow_rate = false         # 检查获取借币利率功能
get_borrow_limits = false       # 检查获取借币限额功能
repay_coin = false              # 检查还币功能
account_mode = false            # 检查账户模式功能

# 订单相关配置
[order_config]
# 限价单价格偏离当前市价的百分比
price_offset_percentage = 3.0
# 默认下单金额（当未在order_amounts中指定时使用）
default_order_amount = 10.0
# 默认下单数量（当未在order_quantities中指定时使用）
default_order_quantity = 0.01
# 是否在测试完成后取消测试订单
cancel_test_orders = true
# 最大订单数量, 批量下单时，批量的最大数量（bn最大为5），默认/最大会生成8个订单
max_order_count = 8
# 是否需要在测试前自动取消所有订单
auto_cancel_all_orders = false
# 是否需要在测试前平仓
auto_close_position = false

# 按交易对设置下单金额（计价币种单位，如USDT）
[order_config.order_amounts]
"HYPE_USDC" = 100.0     # SOL/USDT交易对下单金额为100 USDT
"DOGS_USDC" = 10.0     # DOGS/USDT交易对下单金额为10 USDT

# 按交易对设置下单数量（基础币种单位，如SOL）
[order_config.order_quantities]
"HYPE_USDC" = 1.01    # SOL/USDT交易对下单数量为0.01 SOL
"DOGS_USDC" = 1      # DOGS/USDT交易对下单数量为1 DOGS

# 下单的额外参数
[order_config.order_params]
# 是否为双向持仓
is_dual_side = false
# 杠杆倍数
leverage = 1

# API调用相关参数
[api_params]
# API请求超时时间（毫秒）
timeout_ms = 10000
# API请求失败重试次数
retry_count = 2

# 自定义参数
[api_params.custom_params]
leverage = "5"                  # 设置杠杆倍数
margin_mode_symbol = "HYPE_USDC" # 设置保证金模式的交易对
margin_mode_coin = "USDC"       # 设置保证金模式的币种
margin_mode_type = "isolated"   # 设置保证金模式类型（isolated:逐仓, cross:全仓）

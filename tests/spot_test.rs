#[cfg(test)]
mod tests {
    use binance::{spot::ws_api::BinanceSpotWsApi, util::ceil};
    use rustc_hash::FxHashMap;
    use std::{collections::HashMap, fs::read_to_string, sync::Arc, time::Duration};
    use tokio::sync::Mutex;
    use tokio::time::sleep;
    use tracing_subscriber::fmt;

    use quant_common::Result;
    use quant_common::base::model::order::*;
    use quant_common::base::model::*;
    use quant_common::base::*;

    use binance::spot::rest::BinanceSpot;
    use binance::spot::ws::BinanceSpotWs;

    use async_channel::{Receiver, Sender, unbounded};

    fn test_config() -> ExConfig {
        toml::from_str(&read_to_string("spot.toml").unwrap()).unwrap()
    }

    #[derive(Clone)]
    struct OrderWsHandler {
        on_orders: Arc<Mutex<HashMap<String, Vec<Order>>>>,
    }

    impl WsHandler for OrderWsHandler {
        async fn on_order(&self, order: Order, _: usize) -> Result<()> {
            let mut orders_map = self.on_orders.lock().await;

            // 使用订单ID作为键
            let order_id = order.id.clone();

            // 获取或创建该订单ID的状态历史
            let order_history = orders_map.entry(order_id.clone()).or_insert_with(Vec::new);

            for old_order in order_history.iter() {
                assert_eq!(old_order.cid, order.cid);
            }

            order_history.push(order.clone());

            Ok(())
        }

        async fn on_connected(&self, exchange: Exchange, _: usize) -> Result<()> {
            tracing::info!("连接成功: {}", exchange);
            Ok(())
        }

        async fn on_disconnected(&self, exchange: Exchange, _: usize) -> Result<()> {
            tracing::info!("连接断开: {}", exchange);
            Ok(())
        }
    }

    #[allow(async_fn_in_trait)]
    trait OrderApi {
        async fn post(&self, order: Order, params: OrderParams) -> Result<String>;
    }

    #[allow(dead_code)]
    struct RestPostOrder {
        rest: BinanceSpot,
    }

    impl OrderApi for RestPostOrder {
        async fn post(&self, order: Order, params: OrderParams) -> Result<String> {
            self.rest.post_order(order, params).await
        }
    }

    trait TestAsyncHandler {
        async fn handle_post_order(&self, req_id: u64, order: PlaceOrderResult) -> Result<()>;
        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_amend_order(&self, req_id: u64, order: AmendOrderResult) -> Result<()>;
        async fn handle_cancel_order(&self, req_id: u64, order: CancelOrderResult) -> Result<()>;
        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
    }

    struct WrapHandler<H: TestAsyncHandler> {
        handler: H,
    }

    impl<H: TestAsyncHandler> From<H> for WrapHandler<H> {
        fn from(handler: H) -> Self {
            Self { handler }
        }
    }

    #[allow(unused_variables)]
    impl<H: TestAsyncHandler + Send + Sync + 'static> AsyncResHandle for WrapHandler<H> {
        async fn handle_post_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: PlaceOrderResult,
        ) -> Result<()> {
            self.handler.handle_post_order(req_id, result).await
        }

        async fn handle_post_batch_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_post_batch_order(req_id, orders).await
        }

        async fn handle_amend_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: AmendOrderResult,
        ) -> Result<()> {
            self.handler.handle_amend_order(req_id, result).await
        }

        async fn handle_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: CancelOrderResult,
        ) -> Result<()> {
            self.handler.handle_cancel_order(req_id, result).await
        }

        async fn handle_batch_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_batch_cancel_order(req_id, orders).await
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler
                .handle_batch_cancel_order_by_ids(req_id, orders)
                .await
        }
    }

    struct WsPostOrder {
        tx: Sender<(u64, AsyncCmd)>,
        finish_rx: Receiver<Result<String>>,
    }

    impl WsPostOrder {
        pub async fn new(config: ExConfig) -> Self {
            let ws = BinanceSpotWsApi::new(config).await;
            let (tx, rx) = unbounded();
            let (finish_tx, finish_rx) = unbounded();

            let finish_tx_clone = finish_tx.clone();

            tokio::spawn(async move {
                ws.run(
                    0,
                    WrapHandler::from(PostOrderHandler {
                        finish_tx: finish_tx_clone,
                    }),
                    rx,
                )
                .await
                .unwrap();
            });

            Self { tx, finish_rx }
        }
    }

    impl OrderApi for WsPostOrder {
        async fn post(&self, order: Order, params: OrderParams) -> Result<String> {
            self.tx
                .send((1, AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params })))
                .await?;
            self.finish_rx.recv().await?
        }
    }

    #[derive(Clone)]
    struct PostOrderHandler {
        finish_tx: Sender<Result<String>>,
    }

    impl TestAsyncHandler for PostOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            let order_id = result.result.clone()?;
            tracing::info!("下单成功: {req_id} {:?}", order_id);
            self.finish_tx.send(result.result).await?;
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            tracing::info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            tracing::info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            tracing::info!("撤单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            tracing::info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            tracing::info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    async fn run_order_test(
        rest: &BinanceSpot,
        on_orders: Arc<Mutex<HashMap<String, Vec<Order>>>>,
        symbol: Symbol,
        api: impl OrderApi,
        x: &Instrument,
    ) -> Result<()> {
        // 下各种类型的订单
        let bbo = rest.get_bbo_ticker(symbol.clone()).await?;
        let price = ceil(x.price_tick, bbo.ask_price + x.price_tick);
        let amount = ceil(x.amount_tick, (x.min_notional / price) * 1.1);
        let ps = PosSide::Long;
        let e = Exchange::BinanceSpot;

        // 存储下单的订单信息
        let mut placed_orders: FxHashMap<String, String> = FxHashMap::default();

        // 市价单
        let market_amount = ceil(x.amount_tick, x.min_notional / bbo.bid_price);
        let order = Order::market_open(symbol.clone(), market_amount, ps, e);
        let order_id = api.post(order.clone(), Default::default()).await?;
        tracing::info!("市价单ID: {order_id}, CID: {:?}", order.cid);
        placed_orders.insert(order.cid.clone().unwrap_or_default(), order_id);

        // 限价单 GTC
        let order = Order::limit_open(symbol.clone(), price, amount, ps, TimeInForce::GTC, e);
        let order_id = api.post(order.clone(), Default::default()).await?;
        tracing::info!("限价单GTC ID: {order_id}, CID: {:?}", order.cid);
        placed_orders.insert(order.cid.clone().unwrap_or_default(), order_id);

        // 限价单 IOC
        let order = Order::limit_open(symbol.clone(), price, amount, ps, TimeInForce::IOC, e);
        let order_id = api.post(order.clone(), Default::default()).await?;
        tracing::info!("限价单IOC ID: {order_id}, CID: {:?}", order.cid);
        placed_orders.insert(order.cid.clone().unwrap_or_default(), order_id);

        // 限价单 FOK
        let order = Order::limit_open(symbol.clone(), price, amount, ps, TimeInForce::FOK, e);
        let order_id = api.post(order.clone(), Default::default()).await?;
        tracing::info!("限价单FOK ID: {order_id}, CID: {:?}", order.cid);
        placed_orders.insert(order.cid.clone().unwrap_or_default(), order_id);

        // 等待订单更新
        tokio::time::sleep(Duration::from_secs(5)).await;
        for _ in 0..10 {
            tokio::time::sleep(Duration::from_secs(1)).await;

            let on_orders = on_orders.lock().await;
            if on_orders.len() == placed_orders.len() {
                break;
            }
        }

        // 检查收到的订单更新
        let on_orders_map = on_orders.lock().await;
        tracing::info!("成功收到{}个订单", on_orders_map.len());

        // 对比下单的ID和CID与收到的ID和CID
        let mut matched_orders = Vec::new();

        for (order_id, order_history) in on_orders_map.iter() {
            // 检查ID和CID是否匹配
            for (placed_cid, placed_id) in &placed_orders {
                let id_match = order_id == placed_id;

                // 检查CID是否匹配（使用第一个订单更新）
                let cid_match = if let Some(first_order) = order_history.first() {
                    match (&first_order.cid, placed_cid) {
                        (Some(cid1), cid2) => cid1 == cid2,
                        _ => false,
                    }
                } else {
                    false
                };

                if id_match || cid_match {
                    matched_orders.push((placed_id.clone(), placed_cid.clone()));
                    tracing::info!(
                        "找到匹配的订单: ID={}, CID={:?}, 状态历史: {:?}",
                        placed_id,
                        placed_cid,
                        order_history.iter().map(|o| &o.status).collect::<Vec<_>>()
                    );
                    break;
                }
            }
        }

        // 输出匹配结果
        tracing::info!(
            "下单总数: {}, 匹配到的订单数: {}",
            placed_orders.len(),
            matched_orders.len()
        );

        // 检查是否有未匹配的订单
        let mut unmatched_orders = Vec::new();
        for (placed_cid, placed_id) in &placed_orders {
            let is_matched = matched_orders
                .iter()
                .any(|(id, cid)| id == placed_id && cid == placed_cid);

            if !is_matched {
                unmatched_orders.push((placed_id.clone(), placed_cid.clone()));
                tracing::warn!("未找到匹配的订单: ID={}, CID={:?}", placed_id, placed_cid);
            }
        }

        // 使用断言检查结果
        assert!(!matched_orders.is_empty(), "没有找到任何匹配的订单");
        assert_eq!(
            matched_orders.len(),
            placed_orders.len(),
            "匹配的订单数量与下单数量不匹配"
        );
        assert!(
            unmatched_orders.is_empty(),
            "存在未匹配的订单: {:?}",
            unmatched_orders
        );
        drop(on_orders_map);

        // 卖出资产
        tracing::info!("清理测试...");

        // 获取账户余额
        let account = rest.get_balance(symbol.base.as_str()).await?;

        if account.available_balance * bbo.ask_price > 5.0 {
            let sell_order = Order::market_close(symbol.clone(), account.available_balance, ps, e);
            let cid = OrderId::ClientOrderId(sell_order.cid.clone().unwrap_or_default());

            let sell_id = rest.post_order(sell_order, Default::default()).await?;
            tracing::info!("卖出订单已提交: {}", sell_id);

            tokio::time::sleep(Duration::from_secs(3)).await;

            let mut interval = tokio::time::interval(Duration::from_secs(1));

            loop {
                interval.tick().await;

                let order_info = rest.get_order_by_id(symbol.clone(), cid.clone()).await?;
                tracing::info!("卖出订单状态: {:?}", order_info.status);

                if order_info.status == OrderStatus::Filled {
                    break;
                }
            }
        }

        on_orders.lock().await.clear();

        Ok(())
    }

    #[tokio::test]
    #[ignore = "debug工具"]
    async fn test_order() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config();
        let symbol = Symbol::new("BNB");

        let rest = BinanceSpot::new(config.clone()).await;
        let mut ws = BinanceSpotWs::new(config.clone()).await;

        let received_orders = Arc::new(Mutex::new(HashMap::new()));
        let handler = OrderWsHandler {
            on_orders: received_orders.clone(),
        };
        let symbols = Arc::new(vec![symbol.clone()]);
        let channels = vec![SubscribeChannel::Order(symbols.clone())];
        let subs = Subscribes::new(0, handler, channels);
        tokio::spawn(async move {
            ws.run(subs).await.unwrap();
        });

        sleep(Duration::from_secs(2)).await;

        // let api = RestPostOrder { rest: rest.clone() };
        let api = WsPostOrder::new(config.clone()).await;
        let x = rest.get_instrument(symbol.clone()).await?;
        run_order_test(&rest, received_orders, symbol, api, &x).await
    }

    // 循环持续测试
    #[tokio::test]
    #[ignore = "debug工具"]
    async fn test_order_loop() -> Result<()> {
        let _ = fmt().try_init();

        let config = test_config();

        let rest = BinanceSpot::new(config.clone()).await;
        let mut ws = BinanceSpotWs::new(config.clone()).await;

        let on_orders = Arc::new(Mutex::new(HashMap::new()));
        let handler = OrderWsHandler {
            on_orders: on_orders.clone(),
        };

        // 设置订阅的交易对
        let symbol = Symbol::new("ARKM");
        let symbols = Arc::new(vec![symbol.clone()]);

        // 设置订阅通道
        let channels = vec![SubscribeChannel::Order(symbols.clone())];
        let subs = Subscribes::new(0, handler, channels);

        // 启动WebSocket订阅
        tokio::spawn(async move {
            ws.run(subs).await.unwrap();
        });

        // 等待WebSocket连接建立
        sleep(Duration::from_secs(2)).await;

        let iterations = 300;

        let x = rest.get_instrument(symbol.clone()).await?;

        for i in 0..iterations {
            tracing::info!("开始第 {} 次测试迭代", i + 1);

            // 运行单次测试
            // let api = RestPostOrder { rest: rest.clone() };
            let api = WsPostOrder::new(config.clone()).await;
            run_order_test(&rest, on_orders.clone(), symbol.clone(), api, &x).await?;
        }

        tracing::info!("所有 {} 次测试迭代已完成", iterations);
        Ok(())
    }
}

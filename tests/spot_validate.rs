#[cfg(test)]
mod tests {
    use std::collections::HashMap;

    use exchange_validator::{
        ws::{WebSocketConfig, WsValidator},
        ws_api::WsValidatorConfigBuilder,
        GridValidationConfig, RestApiValidator, SimpleGridConfig, SimpleGridStrategy,
        ValidatorConfig, WebSocketAPIFactory, WsApiValidator, WsOrderConfig,
    };
    use hyperliquid::spot::{rest::*, ws::HyperLiquidSpotWs, ws_api::HyperLiquidSpotWsApi};
    use log::info;
    use quant_common::{base::*, Result};
    use tracing_subscriber::fmt;
    fn test_config() -> ExConfig {
        let toml_str = r#"
        exchange = "HyperLiquidSpot"
        key = "0xbc5d5e80060572c750397ca629d9790a82cdcb16"
        secret ="16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808"
        passphrase = "passphrase"
        is_colo = false
        is_testnet = false
        is_unified = false
        params = { is_vip = "false", cookie = "some_cookie", code = "1234", org_id = "5678" }
    "#;
        toml::from_str(&toml_str).unwrap()
    }

    #[tokio::test]
    async fn test_validate_hyperliquid_spot() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let exchange = HyperLiquidSpot::new(config).await;
        let symbol = Symbol {
            base: "BTC".to_string(),
            quote: QuoteCcy::USDC,
        };
        let grid_config = SimpleGridConfig {
            symbol: symbol.clone(),
            grid_size: 10,           // 网格数量
            price_range: 0.1,        // 价格范围百分比，如 0.1 表示上下 10%
            quantity_per_grid: 0.23, // 每个网格的数量
        };
        // 验证配置示例用法
        let validate_config = GridValidationConfig::builder()
            .disable_funding_rate()
            .disable_balance()
            .build();

        // 验证所有配置， 等用于 ValidationConfig::default()
        // let validate_config = ValidationConfig::all_enabled();
        // 禁用所有配置
        // let validate_config = ValidationConfig::none();

        let current_price = exchange.get_ticker(symbol).await?.close;
        let mut strategy = SimpleGridStrategy::new(exchange, grid_config, current_price);

        strategy.validate(validate_config).await?;
        strategy.show_reports();
        strategy.run().await?;
        Ok(())
    }

    // 验证Rest Trait接口
    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_validate_hyperliquid_spot_rest() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let exchange = HyperLiquidSpot::new(config).await;
        // let symbol = Symbol::new("SOL");
        let symbols = vec![
            Symbol {
                base: "BTC".to_string(),
                quote: QuoteCcy::USDC,
            },
            Symbol {
                base: "HYPE".to_string(),
                quote: QuoteCcy::USDC,
            },
        ];
        // 导入配置文件，详情参考config.json.example
        let validate_config = ValidatorConfig::from_file("./tests/validation_spot_config.toml")?;
        info!("validate_config: {:#?}", validate_config);

        let mut rest_validator = RestApiValidator::new(exchange, symbols, Some(validate_config));
        // 可以配置导出验证结果到csv文件
        rest_validator.set_report_path("./tests/report_spot.csv");
        rest_validator.validate_all().await?;
        rest_validator.show_report();
        Ok(())
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_validate_hyperliquid_spot_ws_api() -> Result<()> {
        // 验证ws_api
        fmt().pretty().init();
        let config = test_config();

        let mut order_config = HashMap::new();
        order_config.insert(
            Symbol {
                base: "HYPE".to_string(),
                quote: QuoteCcy::USDC,
            },
            WsOrderConfig {
                order_price: 10.0,
                order_quantity: 1.123,
                order_params: OrderParams::new(false, 1),
            },
        );
        let validate_config = WsValidatorConfigBuilder::new()
            .disable_batch_cancel_order()
            .set_order_config(order_config)
            .build();
        info!("validate_config: {:#?}", validate_config);

        struct MyWsApiFactory;
        impl WebSocketAPIFactory for MyWsApiFactory {
            type Api = HyperLiquidSpotWsApi;

            async fn create_ws_api(config: ExConfig) -> Self::Api {
                HyperLiquidSpotWsApi::new(config).await
            }
        }

        let mut validator = WsApiValidator::<MyWsApiFactory>::new(validate_config, config);
        validator.validate_all().await?;

        Ok(())
    }

    //ws event
    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_validate_hyperliuquid_spot_ws_event() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let rest = HyperLiquidSpot::new(config.clone()).await;
        let ws = HyperLiquidSpotWs::new(config).await; // 任何实现了WebSocket trait的结构体
        let symbols = vec![Symbol {
            base: "BTC".to_string(),
            quote: QuoteCcy::USDC,
        }];

        // 示例4：默认配置，测试所有功能
        let validate_config = WebSocketConfig::builder()
            .disable_funding()
            .disable_position()
            .build();
        let mut validator = WsValidator::new(ws, symbols, Some(validate_config), Some(rest));
        // 验证所有功能， public和private统一验证
        // validator.validate_all().await?;

        // 验证所有功能， public和private需要分开的情况
        validator.validate_separately().await?;

        // 只验证public
        // validator.validate_public().await?;

        // 只验证private
        // validator.validate_private().await?;

        validator.show_report();

        Ok(())
    }
}

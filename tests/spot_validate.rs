#[cfg(test)]
mod tests {
    use std::collections::HashMap;

    use exchange_validator::{
        WebSocketAPIFactory, WsApiValidator, WsOrderConfig, ws_api::WsValidatorConfigBuilder,
    };
    use gate::spot::ws_api::GateSpotWsApi;
    use log::info;
    use quant_common::{
        Result,
        base::{ExConfig, OrderParams, Symbol},
    };
    use tracing_subscriber::fmt;

    #[tokio::test]
    async fn test_validate_gate_spot_ws_api() -> Result<()> {
        // 验证ws_api
        fmt().pretty().init();
        let config = ExConfig {
            exchange: quant_common::base::Exchange::GateSwap,
            key: "ae306f292fbb0d67c45dab7e826b5333".to_string(),
            secret: "c1d76aefd846fe3d5ae2136d35956d2c82fa684a60d4fff89cd8c0fb729af54c".to_string(),
            ..Default::default()
        };

        let mut order_config = HashMap::new();
        order_config.insert(
            Symbol::new("SOL"),
            WsOrderConfig {
                order_price: 100.0,
                order_quantity: 0.1,
                order_params: OrderParams::new(false, 1), // 下单额外参数
            },
        );
        let validate_config = WsValidatorConfigBuilder::new()
            .disable_batch_cancel_order()
            .set_order_config(order_config)
            .build();
        info!("validate_config: {validate_config:#?}");

        struct MyWsApiFactory;
        impl WebSocketAPIFactory for MyWsApiFactory {
            type Api = GateSpotWsApi;

            async fn create_ws_api(config: ExConfig) -> Self::Api {
                GateSpotWsApi::new(config).await
            }
        }

        let mut validator = WsApiValidator::<MyWsApiFactory>::new(validate_config, config);
        validator.validate_all().await?;

        Ok(())
    }
}

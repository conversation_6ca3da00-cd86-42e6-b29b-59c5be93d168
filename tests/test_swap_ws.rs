use std::sync::Arc;

use bybit::swap::ws::BybitSwapWs;
use quant_common::base::{
    Balance, BboTicker, Depth, Exchange, Funding, FundingFee, KlineInterval, MarkPrice, Order,
    Position, Trade, WebSocket, WsHandler,
};
use quant_common::base::{
    DefaultWsHandler, SubscribeChannel, Symbol,
    model::{ExConfig, Subscribes},
};
use quant_common::{Result, time_ms};
use quant_common::{base, test_config};
use serde::{Deserialize, Serialize};
use tracing::{debug, info};

fn log() {
    #[cfg(debug_assertions)]
    {
        tracing_subscriber::fmt()
            .pretty()
            .with_max_level(tracing::Level::DEBUG)
            .init();
    }
    #[cfg(not(debug_assertions))]
    {
        tracing_subscriber::fmt().pretty().init();
    }
}

#[derive(Deserialize, Serialize, Debug, <PERSON><PERSON>, Default)]
pub struct TestWsHandler;

#[allow(unused_variables)]
impl WsHandler for TestWsHandler {
    async fn on_connected(&self, exchange: Exchange, id: usize) -> Result<()> {
        info!("{exchange} connected");
        Ok(())
    }
    async fn on_disconnected(&self, exchange: Exchange, id: usize) -> Result<()> {
        info!("{exchange} disconnected");
        Ok(())
    }
    async fn on_mark_price(&self, mark_price: MarkPrice, exchange: Exchange) -> Result<()> {
        info!("{:?}", mark_price);
        Ok(())
    }
    async fn on_bbo(&self, bbo: BboTicker, exchange: Exchange) -> Result<()> {
        info!("{:?}", bbo);
        let i = time_ms() - bbo.timestamp;
        info!("时差：{}", i);
        Ok(())
    }
    async fn on_funding_fee(&self, funding_fee: FundingFee, id: usize) -> Result<()> {
        info!("结算资金费：{:?}", funding_fee);
        Ok(())
    }
    async fn on_depth(&self, depth: Depth, exchange: Exchange) -> Result<()> {
        info!("{:?}", depth);
        let i = time_ms() - depth.timestamp;
        info!("时差：{}", i);
        Ok(())
    }
    async fn on_funding(&self, funding: Funding, exchange: Exchange) -> Result<()> {
        info!("{:?}", funding);
        Ok(())
    }
    async fn on_trade(&self, trade: Trade, exchange: Exchange) -> Result<()> {
        info!("{:?}", trade);
        let i = time_ms() - trade.timestamp;
        info!("时差：{}", i);
        Ok(())
    }
    async fn on_order(&self, order: Order, id: usize) -> Result<()> {
        info!("{:?}", order);
        Ok(())
    }
    async fn on_position(&self, position: Position, id: usize) -> Result<()> {
        info!("{:?}", position);
        Ok(())
    }
    async fn on_balance(&self, balance: Balance, id: usize) -> Result<()> {
        info!("{:?}", balance);
        Ok(())
    }
    async fn on_kline(&self, kline: base::Kline, exchange: Exchange) -> quant_common::Result<()> {
        info!("kline:{:?}", kline);
        Ok(())
    }
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_pub() -> Result<()> {
    log();
    let config = ExConfig::default();
    let mut ws = BybitSwapWs::new(config).await;
    let symbols = vec![Symbol::new("DOGE")];
    let subs = Subscribes::new(
        0,
        TestWsHandler,
        vec![
            // pub
            // SubscribeChannel::Bbo(symbols.clone().into()),
            // // SubscribeChannel::MarkPrice(symbols.clone()),
            // // SubscribeChannel::Funding(symbols.clone()),
            // SubscribeChannel::Depth(base::DepthWsParams {
            //     symbols: symbols.clone().into(),
            //     levels: 2,
            // }),
            SubscribeChannel::Kline(base::KlineWsParams {
                symbols: symbols.clone().into(),
                interval: KlineInterval::Min1,
            }),
        ],
    );
    ws.run(subs).await?;
    Ok(())
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_pri() -> Result<()> {
    log();
    let config = test_config();
    debug!("config:{:?}", config);
    let mut ws = BybitSwapWs::new(config).await;
    let symbols = [Symbol::new("DOGE"), Symbol::new("SOL")];
    let subs = Subscribes::new(
        0,
        DefaultWsHandler,
        vec![
            SubscribeChannel::Order(Vec::from(symbols.clone()).into()),
            SubscribeChannel::Balance,
            SubscribeChannel::Position(Vec::from(symbols.clone()).into()),
            SubscribeChannel::FundingFee(Vec::from(symbols.clone()).into()),
            SubscribeChannel::OrderAndFill(Vec::from(symbols.clone()).into()),
        ],
    );
    ws.run(subs).await?;
    Ok(())
}

#[tokio::test]
#[ignore = "need proxy"]
async fn test_depth() -> Result<()> {
    log();
    let config = ExConfig::default();
    debug!("config:{:?}", config);
    let mut ws = BybitSwapWs::new(config).await;
    let symbols = Arc::new(vec![Symbol::new("DOGE"), Symbol::new("SOL")]);
    let subs = Subscribes::new(
        0,
        DefaultWsHandler,
        vec![SubscribeChannel::Depth(base::DepthWsParams {
            symbols,
            levels: 6,
        })],
    );
    ws.run(subs).await?;
    Ok(())
}

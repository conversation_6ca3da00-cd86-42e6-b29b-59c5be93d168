#[cfg(test)]
mod tests {
    use quant_api::create_public_rest;
    use quant_common::base::Exchange;
    use quant_common::base::QuoteCcy;
    use quant_common::base::Rest;
    use quant_common::base::model::Symbol;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_rest() {
        tracing_subscriber::fmt().pretty().init();
        for exchange in Exchange::all() {
            tracing::info!("exchange: {:?}", exchange);
            let rest = create_public_rest(exchange).await;
            let btc = if matches!(exchange, Exchange::CryptoSpot | Exchange::CryptoSwap) {
                Symbol::new_with_quote("BTC", QuoteCcy::USD)
            } else {
                Symbol::new_with_quote("BTC", QuoteCcy::USDT)
            };
            let ticker = rest.get_ticker(btc.clone()).await;
            match ticker {
                Ok(ticker) => {
                    tracing::info!("{:?}", ticker);
                }
                Err(e) => {
                    tracing::error!("{:?}", e);
                }
            }

            let instrument = rest.get_instrument(btc.clone()).await;
            match instrument {
                Ok(instrument) => {
                    tracing::info!("{:?}", instrument);
                }
                Err(e) => {
                    tracing::error!("{:?}", e);
                }
            }

            let depth = rest.get_depth(btc.clone(), Some(5)).await;
            match depth {
                Ok(depth) => {
                    tracing::info!("{:?}", depth);
                }
                Err(e) => {
                    tracing::error!("{:?}", e);
                }
            }

            tracing::info!("{} rest test done", exchange);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_rest_public_rest() {
        tracing_subscriber::fmt().pretty().init();
        let rest = create_public_rest(Exchange::BitgetSwap).await;
        let instruments = rest.get_instruments().await;
        match instruments {
            Ok(instruments) => {
                tracing::info!("{:#?}", instruments);
            }
            Err(e) => {
                tracing::error!("{:?}", e);
            }
        }
    }
}

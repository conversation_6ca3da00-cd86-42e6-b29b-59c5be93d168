#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use quant_api::create_public_ws;
    use quant_common::base::*;
    use tracing_subscriber::fmt;

    #[tokio::test]
    #[ignore = "run long time"]
    async fn test_ws() {
        fmt().pretty().init();

        let mut ws = create_public_ws(Exchange::BinanceSwap).await.unwrap();
        let symbols = Arc::new(vec![Symbol::new("BTC")]);

        let subs = Subscribes::default_swap_pub(symbols);
        tokio::spawn(async move {
            ws.run(subs).await.unwrap();
        });

        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
    }
}

#[cfg(test)]
mod tests {
    use async_channel::Sender;
    use async_channel::bounded;
    use async_channel::unbounded;
    use binance::future::rest::BinanceFuture;
    use binance::util::is_ed25519_secret;
    use binance::util::min_limit_amount_price;
    use quant_common::Result;
    use quant_common::base::traits::ws_api::*;
    use quant_common::base::*;
    use quant_common::qerror;
    use std::fs::read_to_string;
    use tokio::spawn;
    use tracing::info;
    use tracing_subscriber::fmt;

    use binance::future::ws_api::BinanceFutureWsApi;

    fn test_config() -> ExConfig {
        let config: ExConfig = toml::from_str(&read_to_string("future.toml").unwrap()).unwrap();
        if is_ed25519_secret(&config.secret) {
            panic!("bn合约没有测试 ed25519环境");
        }
        config
    }

    async fn btc_symbol(rest: &BinanceFuture) -> Symbol {
        rest.get_instruments()
            .await
            .unwrap()
            .into_iter()
            .find(|i| i.symbol.base == "BTC")
            .map(|i| i.symbol)
            .unwrap()
    }

    trait TestAsyncHandler {
        async fn handle_post_order(&self, req_id: u64, order: PlaceOrderResult) -> Result<()>;
        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_amend_order(&self, req_id: u64, order: AmendOrderResult) -> Result<()>;
        async fn handle_cancel_order(&self, req_id: u64, order: CancelOrderResult) -> Result<()>;
        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()>;
    }

    struct WrapHandler<H: TestAsyncHandler> {
        handler: H,
    }

    impl<H: TestAsyncHandler> From<H> for WrapHandler<H> {
        fn from(handler: H) -> Self {
            Self { handler }
        }
    }

    #[allow(unused_variables)]
    impl<H: TestAsyncHandler + Send + Sync + 'static> AsyncResHandle for WrapHandler<H> {
        async fn handle_post_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: PlaceOrderResult,
        ) -> Result<()> {
            self.handler.handle_post_order(req_id, result).await
        }

        async fn handle_post_batch_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_post_batch_order(req_id, orders).await
        }

        async fn handle_amend_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: AmendOrderResult,
        ) -> Result<()> {
            self.handler.handle_amend_order(req_id, result).await
        }

        async fn handle_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            result: CancelOrderResult,
        ) -> Result<()> {
            self.handler.handle_cancel_order(req_id, result).await
        }

        async fn handle_batch_cancel_order(
            &self,
            _account_id: usize,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler.handle_batch_cancel_order(req_id, orders).await
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            account_id: usize,
            req_id: u64,
            res: Result<BatchOrderRsp>,
        ) -> Result<()> {
            self.handler
                .handle_batch_cancel_order_by_ids(req_id, res)
                .await
        }
    }

    struct PostOrderHandler {
        finish_tx: Sender<Result<()>>,
    }

    impl TestAsyncHandler for PostOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {}", order_id);
                self.finish_tx.send(Ok(())).await?;
            } else {
                self.finish_tx
                    .send(Err(qerror!("下单失败: {req_id} {:?}", result.result)))
                    .await?;
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_post_order() {
        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let exchange = config.exchange;
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceFutureWsApi::new(config.clone()).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await;
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");

        let order = Order::limit_open(symbol, price, amount, ps, tif, exchange);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await.unwrap();

        finish_rx.recv().await.unwrap().unwrap();
    }

    #[tokio::test]
    async fn test_post_post_only_order() {
        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let exchange = config.exchange;
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceFutureWsApi::new(config.clone()).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await;
        let ps = PosSide::Long;
        let tif = TimeInForce::PostOnly;
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");
        let order = Order::limit_open(symbol, price, amount, ps, tif, exchange);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await.unwrap();

        finish_rx.recv().await.unwrap().unwrap();
    }

    #[tokio::test]
    async fn test_open_market_order() {
        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let exchange = config.exchange;
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceFutureWsApi::new(config.clone()).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await;
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");
        let order = Order {
            price: Some(price),
            ..Order::market_open(symbol, amount, PosSide::Long, exchange)
        };
        let params = OrderParams::new(true, 1);
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await.unwrap();

        finish_rx.recv().await.unwrap().unwrap();
    }

    #[tokio::test]
    #[ignore = "需要先有仓位"]
    async fn test_close_market_order() {
        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let exchange = config.exchange;
        let (tx, rx) = unbounded();
        let (finish_tx, finish_rx) = unbounded();
        let ws_api = BinanceFutureWsApi::new(config.clone()).await;
        spawn(async move {
            ws_api
                .run(0, WrapHandler::from(PostOrderHandler { finish_tx }), rx)
                .await
                .unwrap();
        });

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await;
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");
        let order = Order::market_close(symbol, amount, PosSide::Long, exchange);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await.unwrap();

        finish_rx.recv().await.unwrap().unwrap();
    }

    struct CancelOrderHandler {
        tx: Sender<(u64, AsyncCmd)>,
        finish_tx: Sender<()>,
        symbol: Symbol,
    }

    impl TestAsyncHandler for CancelOrderHandler {
        async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
            if let Ok(order_id) = &result.result {
                info!("下单成功: {req_id} {}", order_id);
                self.tx
                    .send((
                        req_id + 1,
                        AsyncCmd::CancelOrder(CancelOrderCmd {
                            symbol: self.symbol.clone(),
                            order_id: OrderId::Id(order_id.clone()),
                        }),
                    ))
                    .await?;
            } else {
                info!("下单失败: {req_id} {:?}", result.result);
            }
            Ok(())
        }

        async fn handle_post_batch_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量下单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_amend_order(&self, req_id: u64, result: AmendOrderResult) -> Result<()> {
            info!("修改订单成功: {req_id} {:?}", result);
            Ok(())
        }

        async fn handle_cancel_order(&self, req_id: u64, result: CancelOrderResult) -> Result<()> {
            info!("撤单成功: {req_id} {:?}", result);
            self.finish_tx.send(()).await?;
            Ok(())
        }

        async fn handle_batch_cancel_order(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }

        async fn handle_batch_cancel_order_by_ids(
            &self,
            req_id: u64,
            orders: Result<BatchOrderRsp>,
        ) -> Result<()> {
            info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_cancel_order() {
        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let exchange = config.exchange;
        let ws_api = BinanceFutureWsApi::new(config.clone()).await;

        let (tx, rx) = bounded(10);

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await;
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");
        let order = Order::limit_open(symbol.clone(), price, amount, ps, tif, exchange);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await.unwrap();

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let handler = CancelOrderHandler {
                finish_tx,
                tx,
                symbol,
            };
            match ws_api.run(0, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();
    }

    #[tokio::test]
    async fn test_cancel_order_by_cid() {
        struct CancelOrderByCidHandler {
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for CancelOrderByCidHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                let order_id = &result.result.unwrap();
                info!("下单成功: {req_id} {:?}", order_id);
                let cancel_order = AsyncCmd::CancelOrder(CancelOrderCmd {
                    symbol: result.order.symbol.clone(),
                    order_id: OrderId::ClientOrderId(result.order.cid.clone().unwrap()),
                });
                self.tx.send((req_id + 1, cancel_order)).await?;
                Ok(())
            }

            async fn handle_post_batch_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量下单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_amend_order(
                &self,
                req_id: u64,
                result: AmendOrderResult,
            ) -> Result<()> {
                info!("修改订单成功: {req_id} {:?}", result);
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                self.finish_tx.send(()).await?;
                Ok(())
            }

            async fn handle_batch_cancel_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_batch_cancel_order_by_ids(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }
        }

        let _ = fmt().with_target(false).try_init();

        let config = test_config();
        let exchange = config.exchange;
        let ws_api = BinanceFutureWsApi::new(config.clone()).await;

        let (tx, rx) = bounded(10);

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await;
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");
        let order = Order::limit_open(symbol, price, amount, ps, tif, exchange);
        let params = OrderParams::default();
        let post_cmd = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_cmd)).await.unwrap();

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let handler = CancelOrderByCidHandler { finish_tx, tx };
            match ws_api.run(0, WrapHandler::from(handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();
    }

    #[tokio::test]
    async fn test_amend_order() {
        let _ = fmt().with_target(false).try_init();

        struct AmendOrderHandler {
            amend_order: Order,
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for AmendOrderHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                let order_id = &result.result.unwrap();
                info!("下单成功: {req_id} {:?}", order_id);
                let mut order_clone = self.amend_order.clone();
                order_clone.id = order_id.clone();
                let amend_cmd = AsyncCmd::AmendOrder(order_clone);
                self.tx.send((req_id + 1, amend_cmd)).await?;
                Ok(())
            }

            async fn handle_post_batch_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量下单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_amend_order(
                &self,
                req_id: u64,
                result: AmendOrderResult,
            ) -> Result<()> {
                info!("修改订单成功: {req_id} {:?}", result);
                self.finish_tx.send(()).await?;
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                Ok(())
            }

            async fn handle_batch_cancel_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_batch_cancel_order_by_ids(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }
        }

        let config = test_config();
        let exchange = config.exchange;
        let ws_api = BinanceFutureWsApi::new(config.clone()).await;

        let (tx, rx) = bounded(10);

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await;
        let ps = PosSide::Long;
        let tif = TimeInForce::GTC;
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");
        let order = Order::limit_open(symbol, price, amount, ps, tif, exchange);
        let amend_order = Order {
            price: Some(101.),
            ..order.clone()
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await.unwrap();

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let amend_handler = AmendOrderHandler {
                amend_order,
                finish_tx,
                tx,
            };
            match ws_api.run(0, WrapHandler::from(amend_handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();
    }

    #[tokio::test]
    async fn test_amend_post_only_order() {
        let _ = fmt().with_target(false).try_init();

        struct AmendOrderHandler {
            amend_order: Order,
            tx: Sender<(u64, AsyncCmd)>,
            finish_tx: Sender<()>,
        }

        impl TestAsyncHandler for AmendOrderHandler {
            async fn handle_post_order(&self, req_id: u64, result: PlaceOrderResult) -> Result<()> {
                if let Ok(order_id) = &result.result {
                    info!("下单成功: {req_id} {:?}", order_id);
                    let mut order_clone = self.amend_order.clone();
                    order_clone.id = order_id.clone();
                    self.tx
                        .send((req_id + 1, AsyncCmd::AmendOrder(order_clone)))
                        .await?;
                } else {
                    info!("下单失败: {req_id} {:?}", result.result);
                }
                Ok(())
            }

            async fn handle_post_batch_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量下单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_amend_order(
                &self,
                req_id: u64,
                result: AmendOrderResult,
            ) -> Result<()> {
                info!("修改订单成功: {req_id} {:?}", result);
                self.finish_tx.send(()).await?;
                Ok(())
            }

            async fn handle_cancel_order(
                &self,
                req_id: u64,
                result: CancelOrderResult,
            ) -> Result<()> {
                info!("撤单成功: {req_id} {:?}", result);
                Ok(())
            }

            async fn handle_batch_cancel_order(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }

            async fn handle_batch_cancel_order_by_ids(
                &self,
                req_id: u64,
                orders: Result<BatchOrderRsp>,
            ) -> Result<()> {
                info!("批量撤单成功: {req_id} {:?}", orders.unwrap());
                Ok(())
            }
        }

        let config = test_config();
        let exchange = config.exchange;
        let ws_api = BinanceFutureWsApi::new(config.clone()).await;

        let (tx, rx) = bounded(10);

        let rest = BinanceFuture::new(config).await;
        let symbol = btc_symbol(&rest).await;
        let ps = PosSide::Long;
        let tif = TimeInForce::PostOnly;
        let x = rest.get_instrument(symbol.clone()).await.unwrap();
        let bbo = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        let (price, amount) = min_limit_amount_price(&x, &bbo);
        info!("price: {price}, amount: {amount}");
        let order = Order::limit_open(symbol, price, amount, ps, tif, exchange);
        let amend_order = Order {
            price: Some(101.),
            ..order.clone()
        };
        let params = OrderParams::default();
        let post_order = AsyncCmd::PlaceOrder(PlaceOrderCmd { order, params });
        tx.send((1, post_order)).await.unwrap();

        let (finish_tx, finish_rx) = bounded(1);
        spawn(async move {
            let amend_handler = AmendOrderHandler {
                amend_order,
                finish_tx,
                tx,
            };
            match ws_api.run(0, WrapHandler::from(amend_handler), rx).await {
                Ok(()) => (),
                Err(e) => panic!("Error: {e}"),
            }
        });

        finish_rx.recv().await.unwrap();
    }
}

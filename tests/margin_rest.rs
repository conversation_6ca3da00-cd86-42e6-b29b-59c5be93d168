#[cfg(test)]
mod md {
    use log::info;
    use quant_common::base::traits::GetKlineParams;
    use tracing_subscriber::fmt;

    use quant_common::base::model::*;
    use quant_common::base::*;

    use binance::margin::rest::BinanceMargin;

    #[tokio::test]
    async fn test_get_ticker() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceMargin::new(config).await;
        let symbol = Symbol::new("LRC");
        let ticker = rest.get_ticker(symbol).await.unwrap();
        info!("{:#?}", ticker);
    }

    #[tokio::test]
    async fn test_get_tickers() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceMargin::new(config).await;
        let tickers = rest.get_tickers().await.unwrap();
        info!("tickers数量: {}", tickers.len());
        for ticker in &tickers[..10] {
            info!("{:?}", ticker);
        }
    }

    #[tokio::test]
    async fn test_get_bbo() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceMargin::new(config).await;
        let symbol = Symbol::new("CHEEMS");
        let ticker = rest.get_bbo_ticker(symbol).await.unwrap();
        info!("{:#?}", ticker);
    }

    #[tokio::test]
    async fn test_get_bbos() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceMargin::new(config).await;
        let bbos = rest.get_bbo_tickers().await.unwrap();
        info!("bbos数量: {}", bbos.len());
        for bbo in &bbos[..10] {
            info!("{:?}", bbo);
        }
    }

    #[tokio::test]
    async fn test_get_depth() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceMargin::new(config).await;
        let symbol = Symbol::new("BTC");
        let depth = rest.get_depth(symbol, Some(5)).await.unwrap();
        info!("{:#?}", depth);
    }

    #[tokio::test]
    async fn test_get_instrument() {
        let _ = fmt().pretty().try_init();

        let config = ExConfig::default();
        let rest = BinanceMargin::new(config).await;
        let instrument = rest.get_instrument(Symbol::new("CHEEMS")).await.unwrap();
        info!("{:#?}", instrument);
    }

    #[tokio::test]
    async fn test_get_instruments() {
        let _ = fmt().try_init();

        let config = ExConfig::default();
        let rest = BinanceMargin::new(config).await;
        let instruments = rest.get_instruments().await.unwrap();
        info!("数量: {}", instruments.len());
        for instrument in &instruments[..10] {
            info!("{:?}", instrument);
        }
    }

    #[tokio::test]
    async fn test_get_klines_ext() {
        let _ = fmt().try_init();

        let config = ExConfig::default();
        let rest = BinanceMargin::new(config).await;
        let symbol = Symbol::new("BTC");
        let params = GetKlineParams::new(symbol, KlineInterval::Min15).limit(5);
        let klines = rest.get_klines_ext(params).await.unwrap();
        info!("{} {:?}", klines.symbol, klines.interval);
        for kline in klines.candles {
            info!("{:?}", kline);
        }
    }

    #[tokio::test]
    #[ignore = "只能在正式环境中运行"]
    async fn test_get_fee_rate() {
        let _ = fmt().pretty().try_init();

        let rest = BinanceMargin::new(Default::default()).await;
        let symbol = Symbol::new("TRUMP");
        let fee_rate = rest.get_fee_rate(symbol).await.unwrap();
        info!("{:?}", fee_rate);
    }

    #[tokio::test]
    async fn test_is_dual_side() {
        let _ = fmt().pretty().try_init();

        let rest = BinanceMargin::new(Default::default()).await;
        let is_dual_side = rest.is_dual_side().await.unwrap();
        assert!(!is_dual_side);
    }
}

#[cfg(test)]
mod assets {
    use log::info;
    use std::fs::read_to_string;
    use std::time::Duration;
    use tokio::time::sleep;
    use tracing_subscriber::fmt;

    use quant_common::Result;
    use quant_common::base::model::order::*;
    use quant_common::base::model::*;
    use quant_common::base::*;
    use quant_common::time_ms;

    use binance::margin::rest::BinanceMargin;

    fn test_config() -> ExConfig {
        toml::from_str(&read_to_string("margin.toml").unwrap()).unwrap()
    }

    mod read {
        use super::*;

        #[tokio::test]
        async fn test_create_cid() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            for side in [OrderSide::Buy, OrderSide::Sell] {
                for pos_side in [PosSide::Long, PosSide::Short] {
                    let cid = config.exchange.create_cid(Some((side, pos_side)));
                    info!("cid: {cid}");
                }
            }
        }

        #[tokio::test]
        async fn test_get_balances() {
            let _ = fmt().try_init();

            let config = test_config();
            let rest = BinanceMargin::new(config).await;
            let balances = rest.get_balances().await.unwrap();
            for balance in balances.iter().filter(|b| b.balance > 0.) {
                info!("{:?}", balance);
            }
            let usdt = get_usdt_balance(balances);
            info!("usdt {:?}", usdt);
        }

        #[tokio::test]
        async fn test_get_balance() {
            let _ = fmt().try_init();

            let config = test_config();
            let rest = BinanceMargin::new(config).await;
            for asset in ["CHEEMS", "BABYDOGE", "USDT"] {
                let balance = rest.get_balance(asset).await.unwrap();
                info!("{asset} => {balance:?}");
            }
        }

        #[tokio::test]
        async fn test_get_orders() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let rest = BinanceMargin::new(config).await;
            for i in 0..3 {
                let end = time_ms() - 1000 * 3600 * 24 * i;
                let start = end - 1000 * 3600 * 24;
                let symbol = Symbol::new("BNB");
                let orders = rest.get_orders(symbol, start, end).await.unwrap();
                for order in orders {
                    info!("{:?}", order);
                }
            }
        }

        #[tokio::test]
        async fn test_get_open_orders() {
            let _ = fmt().pretty().try_init();

            let rest = BinanceMargin::new(test_config()).await;
            let symbol = Symbol::new("BNB");
            let orders = rest.get_open_orders(symbol).await.unwrap();
            info!("{:#?}", orders);
        }

        #[tokio::test]
        async fn test_get_all_open_orders() {
            let _ = fmt().pretty().try_init();

            let rest = BinanceMargin::new(test_config()).await;
            let orders = rest.get_all_open_orders().await.unwrap();
            for order in orders {
                info!("{:?}", order);
            }
        }

        #[tokio::test]
        async fn test_create_listen_key() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let rest = BinanceMargin::new(config).await;
            let listen_key = rest.create_listen_key().await.unwrap();
            info!("margin listen_key: {listen_key}");
        }

        #[tokio::test]
        async fn test_reflash_listen_key() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let rest = BinanceMargin::new(config).await;
            let listen_key = rest.create_listen_key().await.unwrap();
            info!("margin listen_key: {listen_key}");
            sleep(Duration::from_secs(5)).await;
            rest.refresh_listen_key(&listen_key).await.unwrap();
            info!("margin 刷新成功");
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行,日限20次(error 200000000)"]
        async fn test_bnb_burn() {
            let _ = fmt().try_init();

            let config = test_config();
            let rest = BinanceMargin::new(config).await;

            let discounted = rest.is_fee_discount_enabled().await.unwrap();
            info!("是否开启: {}", discounted);
            tokio::time::sleep(Duration::from_secs(1)).await;

            rest.set_fee_discount_enabled(!discounted).await.unwrap();
            info!("设置成功");
            tokio::time::sleep(Duration::from_secs(1)).await;

            rest.set_fee_discount_enabled(discounted).await.unwrap();
            info!("恢复成功");
            tokio::time::sleep(Duration::from_secs(1)).await;
        }

        #[tokio::test]
        async fn test_get_borrow_list() {
            let _ = fmt().try_init();

            let rest = BinanceMargin::new(test_config()).await;
            let borrow = rest.get_borrow(None).await.unwrap();
            for b in borrow {
                info!("borrow {b:?}");
            }
        }

        #[tokio::test]
        async fn test_get_borrow() {
            let _ = fmt().try_init();

            let rest = BinanceMargin::new(test_config()).await;
            let coin = Some("CHEEMS".to_string());
            let borrow = rest.get_borrow(coin).await.unwrap();
            info!("borrow {borrow:?}");
        }
    }

    mod write {
        use std::collections::HashSet;

        use super::*;

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_replay_all() {
            let _ = fmt().try_init();

            let rest = BinanceMargin::new(test_config()).await;
            let borrow = rest.get_borrow(None).await.unwrap();
            info!("借款数量: {}", borrow.len());
            for b in borrow {
                info!("还款 {b:?}");
                rest.repay_coin(b.coin, b.amount).await.unwrap();
            }
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_borrow_coin() -> Result<()> {
            let _ = fmt().pretty().try_init();

            let asset = "USDT".to_string();

            let config = test_config();
            let rest = BinanceMargin::new(config).await;

            let borrow = rest.get_borrow(Some(asset.clone())).await?;
            info!("借前: {:?}", borrow);

            rest.borrow_coin(asset.clone(), 0.01).await?;

            let borrow = rest.get_borrow(Some(asset.clone())).await?;
            info!("借后: {:?}", borrow);

            Ok(())
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_repay_coin() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let rest = BinanceMargin::new(config).await;
            rest.repay_coin("CHEEMS".to_string(), 0.01).await.unwrap();
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_buy() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let exchange = config.exchange;
            let rest = BinanceMargin::new(config).await;
            let symbol = Symbol::new("CHEEMS");
            let order = Order::limit_open(
                symbol,
                250.0,
                0.02,
                PosSide::Long,
                TimeInForce::GTC,
                exchange,
            );
            let oid = rest.post_order(order, Default::default()).await.unwrap();
            info!("{oid}");
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_market_with_price_order() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let exchange = config.exchange;

            let rest = BinanceMargin::new(config).await;

            let symbol = Symbol::new("CHEEMS");
            let order = Order {
                price: Some(1e-5),
                ..Order::market_open(symbol.clone(), 700., PosSide::Long, exchange)
            };
            let order_id = rest.post_order(order, Default::default()).await.unwrap();
            info!("{:#?}", order_id);
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_maker_with_gtc_order() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let exchange = config.exchange;

            let rest = BinanceMargin::new(config).await;
            let symbol = Symbol::new("DOGE");
            let ps = PosSide::Long;
            let tif = TimeInForce::PostOnly;
            let order = Order::limit_open(symbol.clone(), 0.2, 30., ps, tif, exchange);
            let order_id = rest.post_order(order, Default::default()).await.unwrap();
            info!("{order_id}");
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_sell() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let exchange = config.exchange;

            let rest = BinanceMargin::new(config).await;
            let symbol = Symbol::new("BNB");
            let order = Order::market_close(symbol, 0.008, PosSide::Long, exchange);
            let oid = rest.post_order(order, Default::default()).await.unwrap();
            info!("{oid}");
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_cancel_order() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let exchange = config.exchange;

            let rest = BinanceMargin::new(config).await;
            let symbol = Symbol::new("BNB");
            let order = Order::limit_close(
                symbol.clone(),
                800.0,
                0.008,
                PosSide::Long,
                TimeInForce::GTC,
                exchange,
            );
            let order_id = rest.post_order(order, Default::default()).await.unwrap();
            info!("挂单成功: {order_id}");
            let order_id = OrderId::Id(order_id);
            rest.cancel_order(symbol, order_id).await.unwrap();
            info!("取消成功");
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_batch_cancel_order() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let exchange = config.exchange;
            let symbol = Symbol::new("BNB");
            let tif = TimeInForce::GTC;
            let ps = PosSide::Long;
            let order = Order::limit_close(symbol.clone(), 800.0, 0.008, ps, tif, exchange);

            let rest = BinanceMargin::new(config).await;
            let order_id = rest.post_order(order, Default::default()).await.unwrap();
            info!("挂单成功: {order_id}");

            tokio::time::sleep(Duration::from_secs(5)).await;

            let order_ids = rest.batch_cancel_order(symbol).await.unwrap();
            info!("批量取消成功: {order_ids:#?}");
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_close_all_open_orders() {
            let _ = fmt().pretty().try_init();

            let rest = BinanceMargin::new(test_config()).await;
            let orders = rest.get_all_open_orders().await.unwrap();
            let symbols: HashSet<_> = orders.into_iter().map(|o| o.symbol).collect();
            info!("symbol数量: {}", symbols.len());
            for symbol in symbols {
                let order_ids = rest.batch_cancel_order(symbol).await.unwrap();
                info!("{order_ids:#?}");
            }
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_post_order() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let exchange = config.exchange;

            let rest = BinanceMargin::new(config).await;
            let symbol = Symbol::new("BNB");

            let mut order = Order::market_open(symbol.clone(), 0.008, PosSide::Long, exchange);
            let order_id = rest.post_order(order.clone(), Default::default()).await;
            let order_id = order_id.unwrap();
            info!("market open order_id: {order_id}");

            order.order_type = OrderType::Limit;
            order.price = Some(400.);
            order.amount = Some(1.);
            let order_id = rest.post_order(order.clone(), Default::default()).await;
            let order_id = order_id.unwrap();
            info!("limit order_id: {order_id}");

            order.time_in_force = TimeInForce::IOC;
            order.side = OrderSide::Buy;
            let order_id = rest.post_order(order.clone(), Default::default()).await;
            let order_id = order_id.unwrap();
            info!("ioc order_id: {order_id}");

            order.time_in_force = TimeInForce::FOK;
            let order_id = rest.post_order(order.clone(), Default::default()).await;
            assert!(order_id.is_err());

            order = Order::market_open(symbol.clone(), 0.1, PosSide::Short, exchange);
            let order_id = rest.post_order(order.clone(), Default::default()).await;
            let order_id = order_id.unwrap();
            info!("market short order_id: {order_id}");
        }

        #[tokio::test]
        #[ignore = "只能在正式环境中运行"]
        async fn test_get_order_by_id() {
            let _ = fmt().pretty().try_init();

            let config = test_config();
            let rest = BinanceMargin::new(config.clone()).await;
            let symbol = Symbol::new("SOL");

            // limit open and cancel
            let order = Order::limit_open(
                symbol.clone(),
                101.,
                0.05,
                PosSide::Long,
                TimeInForce::GTC,
                config.exchange,
            );
            let order_id = rest.post_order(order, Default::default()).await.unwrap();
            let _ = rest
                .cancel_order(symbol.clone(), OrderId::Id(order_id.clone()))
                .await;

            let order_id = OrderId::Id(order_id);
            let order = rest.get_order_by_id(symbol.clone(), order_id).await;
            let order = order.unwrap();
            info!("by id: {:?}", order);

            let order_id = OrderId::ClientOrderId(order.cid.unwrap());
            let order = rest.get_order_by_id(symbol, order_id).await.unwrap();
            info!("by cid: {:?}", order);
        }
    }
}

#[cfg(test)]
mod tests {
    use std::fs::read_to_string;
    use std::time::Duration;

    use gate::margin::rest::GateMargin;
    use gate::spot::model::{CurrencyPairReq, GateCandlestick, PATH_TICKER};
    use log::info;
    use quant_common::base::*;
    use quant_common::*;
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        toml::from_str(&read_to_string("margin.toml").unwrap()).unwrap()
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_request() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateMargin::new(config).await;
        let ser = CurrencyPairReq::new(Symbol::new("APE"));
        let params = sonic_rs::to_value(&ser).unwrap_or_default();

        let user_request = UserRequest {
            method: "GET".to_string(),
            path: PATH_TICKER.to_string(),
            auth: false,
            query: Some(params),
            body: None,
            url: None,
            headers: None,
        };

        let ticker = rest.request(user_request).await.unwrap();
        let rsp = sonic_rs::from_value::<Vec<GateCandlestick>>(&ticker).unwrap();
        info!("{rsp:#?}");
    }

    // 读接口
    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_fee_rate() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateMargin::new(config).await;
        let fee = rest.get_fee_rate(Symbol::new("BTC")).await.unwrap();
        info!("{fee:?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instrument() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new_with_quote("PENDLE", base::QuoteCcy::OTHER("ETH".to_string()));
        let instrument = rest.get_instrument(symbol).await.unwrap();
        info!("{instrument:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instruments() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateMargin::new(config).await;
        let instruments = rest.get_instruments().await.unwrap();
        info!("{:#?}", instruments.first());
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_ticker() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new("BTC");
        let ticker = rest.get_ticker(symbol.clone()).await.unwrap();
        info!("{ticker:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_depth() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new("BTC");
        let depth = rest.get_depth(symbol.clone(), Some(5)).await.unwrap();
        info!("{depth:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_bbo() {
        fmt().pretty().init();
        let config = ExConfig::default();
        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new("BTC");
        let ticker = rest.get_bbo_ticker(symbol.clone()).await.unwrap();
        info!("{ticker:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let end = time_ms();
        let start = end - 24 * 3600 * 1_000;
        let orders = rest.get_orders(symbol.clone(), start, end).await.unwrap();
        for order in orders {
            info!("{order:?}");
        }
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_all_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateMargin::new(config).await;
        let orders = rest.get_all_open_orders().await.unwrap();
        for order in orders {
            info!("{order:?}");
        }
    }

    #[tokio::test]
    #[ignore = "need proxy(export http_proxy=http://127.0.0.1:10809)"]
    async fn test_get_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let orders = rest.get_open_orders(symbol.clone()).await.unwrap();
        for order in orders {
            info!("{order:?}");
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateMargin::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");

        let mut order = Order::limit_open(
            symbol,
            150.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        order.order_type = OrderType::Limit;
        order.side = OrderSide::Buy;

        let params = OrderParams::new(false, 1);
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("{order_id:#?}");
        // let mut order = Order::market_open(Symbol::new("APE"), 3.0, PosSide::Long, exchange);

        // let params = OrderParams::new(false, 1);
        // order.price = Some(1.0);
        // order.amount = 3.09;
        // for _i in 1..=10 {
        //     // Market long open
        //     let order_id = rest.post_order(order.clone(), params.clone()).await;
        //     if let Ok(order_id) = order_id {
        //         info!("{:#?}", order_id);
        //         info!("market买入成功了");
        //         break;
        //     } else {
        //         qerror!("{:#?}", order_id);
        //         qerror!("market买入失败了捏");
        //     }
        // }
        // info!("等5s后再走");
        // tokio::time::sleep(std::time::Duration::from_secs(5)).await;

        // // Market long close
        // order.side = OrderSide::Sell;
        // order.amount = 0.00008;
        // for _i in 1..=10{
        //     // Market long open
        //     let order_id = rest
        //         .post_order(order.clone(), params.clone())
        //         .await;
        //     if let Ok(order_id) = order_id{
        //         info!("{:#?}", order_id);
        //         info!("market卖出成功了");
        //         break;
        //     }
        //     else{
        //         error!("{:#?}", order_id);
        //         error!("market卖出失败了捏");
        //     }
        // }

        //limit限定价格下单
        // let mut order = Order::limit_open(Symbol::new("BTC"), 67942.2, 0.00005,PosSide::Long,TimeInForce::IOC, exchange);//市场价买入0.01 USDT的BTC
        // order.cid = None;
        //
        // let params = OrderParams::new(false, 1);

        // for _i in 1..=10{
        //     // Market long open
        //     let order_id = rest
        //         .post_order(order.clone(), params.clone())
        //         .await;
        //     if let Ok(order_id) = order_id{
        //         info!("{:#?}", order_id);
        //         info!("下单成功了");
        //         break;
        //     }
        //     else{
        //         error!("{:#?}", order_id);
        //         error!("下单失败了捏");
        //     }
        // }
        // info!("等5s后再走");
        // tokio::time::sleep(std::time::Duration::from_secs(5)).await;

        // // Market long close
        // order.side = OrderSide::Sell;
        // order.price =Some(67854.0);
        // order.amount = 0.00005;
        // for _i in 1..=10{
        //     // Market long open
        //     let order_id = rest
        //         .post_order(order.clone(), params.clone())
        //         .await;
        //     if let Ok(order_id) = order_id{
        //         info!("{:#?}", order_id);
        //         info!("下单成功了");
        //         break;
        //     }
        //     else{
        //         error!("{:#?}", order_id);
        //         error!("下单失败了捏");
        //     }
        // }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_balances() {
        fmt().pretty().init();
        let config = test_config();
        info!("{config:#?}");
        let rest = GateMargin::new(config).await;
        let balances = rest.get_balances().await.unwrap();
        for balance in balances {
            info!("{balance:?}");
        }
    }

    // 写接口
    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_buy_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateMargin::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");
        let order = Order::market_open(symbol, 0.015, PosSide::Long, exchange);
        let params = OrderParams::new(false, 1);
        let order = Order {
            price: Some(200.0),
            ..order
        };

        let order_id = rest.post_order(order, params).await.unwrap();
        info!("{order_id:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_sell_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateMargin::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");
        let order = Order::market_close(symbol, 0.0129928, PosSide::Long, exchange);
        let params = OrderParams::new(false, 1);

        let order_id = rest.post_order(order, params).await.unwrap();
        info!("{order_id:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_oids() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateMargin::new(config).await;

        let symbol: Symbol = Symbol::new("SOL");

        let order = Order::limit_open(
            symbol.clone(),
            150.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );

        let params = OrderParams::new(false, 1);
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("{order_id:#?}");

        let ids = vec![order_id];

        let res = rest
            .batch_cancel_order_by_ids(Some(symbol), Some(ids), None)
            .await
            .unwrap();
        info!("{res:?}")
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_open_by_oids() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateMargin::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");
        let rsp = rest.get_open_orders(symbol.clone()).await.unwrap();
        if rsp.is_empty() {
            return;
        }
        let mut ids: Vec<String> = rsp.into_iter().map(|o| o.id).collect();
        info!("{ids:?}");
        ids.sort();

        let rsp = rest
            .batch_cancel_order_by_ids(Some(symbol), Some(ids.clone()), None)
            .await
            .unwrap();
        let mut cancel_ids: Vec<String> =
            rsp.success_list.into_iter().filter_map(|o| o.id).collect();
        info!("{cancel_ids:?}");
        cancel_ids.sort();
        assert_eq!(ids, cancel_ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_order() {
        fmt().pretty().init();
        let config = test_config();

        let exchange = config.exchange;

        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol.clone(),
            150.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );

        let params = OrderParams::new(false, 1);
        let mut order_ids: Vec<String> = vec![];
        for _ in 0..2 {
            let order_id = rest
                .post_order(order.clone(), params.clone())
                .await
                .unwrap();
            info!(" {order_id:#?}");
            order_ids.push(order_id);
        }
        order_ids.sort();
        info!("成功下单 {order_ids:?}");

        tokio::time::sleep(Duration::from_secs(1)).await;
        let rsp = rest.batch_cancel_order(symbol).await.unwrap();
        let mut ids: Vec<String> = rsp
            .success_list
            .into_iter()
            .map(|o| o.id.unwrap())
            .collect();
        ids.sort();
        info!("撤单 {ids:?}");
        assert_eq!(order_ids, ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new("BTC");
        let order = Order::limit_open(
            symbol.clone(),
            60000.0,
            0.00010,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let cid = order.cid.clone().unwrap();
        let is_dual_side = false;
        let params = OrderParams::new(is_dual_side, 1);

        let order_id = rest
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("下单成功:{order_id}");

        rest.cancel_order(symbol.clone(), OrderId::ClientOrderId(cid.clone()))
            .await
            .unwrap();
        info!("撤单成功");
    }

    #[tokio::test]
    #[ignore = "need proxy, 500 Internal Server Error"]
    async fn test_amend_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateMargin::new(config).await;
        let symbol = Symbol::new("SOL");

        let order = Order::limit_open(
            symbol.clone(),
            150.0,
            0.02,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let params = OrderParams::new(false, 1);

        let order_id = rest
            .post_order(order.clone(), params.clone())
            .await
            .unwrap();
        info!("买入成功了,订单id是:{order_id:#?}");

        tokio::time::sleep(Duration::from_secs(1)).await;

        let order = Order {
            id: order_id,
            price: Some(160.0),
            ..order
        };

        let order_id = rest.amend_order(order).await.unwrap();
        info!("修改成功了，订单id是: {order_id:#?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = GateMargin::new(config).await;
        let symbol: Symbol = Symbol::new("SOL");

        let orders: Vec<_> = (0..2)
            .map(|_| {
                Order::limit_open(
                    symbol.clone(),
                    150.0,
                    0.02,
                    PosSide::Long,
                    TimeInForce::GTC,
                    exchange,
                )
            })
            .collect();
        let cids: Vec<String> = orders.iter().filter_map(|o| o.cid.to_owned()).collect();
        info!("{cids:?}");

        let params = OrderParams::new(false, 1);
        let rsp = rest.post_batch_order(orders.clone(), params).await.unwrap();
        info!("{rsp:?}");
        let order_ids: Vec<String> = rsp.success_list.into_iter().filter_map(|o| o.cid).collect();
        info!("{order_ids:?}");
        assert_eq!(cids, order_ids);
    }

    // 杠杆接口
    // #[tokio::test]
    // #[ignore = "need proxy"]
    // async fn test_get_margin_ratio() {
    //     fmt().pretty().init();
    //     let config = test_config();
    //     let rest = GateMargin::new(config).await;
    //     let rsp = rest.get_margin_ratio().await.unwrap();
    //     info!("{:?}", rsp);
    // }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_borrow() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateMargin::new(config).await;

        let rsp = rest.get_borrow(None).await.unwrap();
        info!("全部借币: {rsp:?}");

        let rsp = rest.get_borrow(Some("BTC".to_string())).await.unwrap();
        info!("借BTC: {rsp:?}");

        let rsp = rest.get_borrow(Some("BTC".to_string())).await.unwrap();
        info!("借USDT: {rsp:?}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_borrow_coin() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateMargin::new(config).await;

        let coin = "USDT".to_string();
        let amount = 1.;

        rest.borrow_coin(coin, amount).await.unwrap();
        info!("借币成功");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_repay_coin() {
        fmt().pretty().init();
        let config = test_config();
        let rest = GateMargin::new(config).await;

        let coin = "USDT".to_string();
        let amount = 1.;

        rest.repay_coin(coin, amount).await.unwrap();
        info!("还币成功");
    }

    // #[tokio::test]
    // #[ignore = "need proxy"]
    // async fn test_get_uni_account_total() {
    //     fmt().pretty().init();
    //     let config = test_config();
    //     let rest = GateMargin::new(config).await;
    //     let rsp = rest.get_uni_account_total().await.unwrap();
    //     info!("{:?}", rsp);
    // }
}

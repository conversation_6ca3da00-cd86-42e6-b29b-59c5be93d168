#[cfg(test)]
mod tests {
    use okx::margin::rest::OkxMargin;
    use quant_common::{base::*, test_config};
    use quant_common::{time_ms, time_ns};
    use rustc_hash::FxHashMap;
    use tracing::info;
    use tracing_subscriber::fmt;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_ticker() {
        fmt().pretty().init();
        let rest = OkxMargin::default();
        let symbol = Symbol::new("BTC");
        let ticker = rest.get_ticker(symbol).await.unwrap();
        info!("{:#?}", ticker);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_tickers() {
        fmt().pretty().init();
        let rest = OkxMargin::default();
        let tickers = rest.get_tickers().await.unwrap();
        info!("len: {}", tickers.len());
        for ticker in tickers.into_iter().take(5) {
            info!("{:?}", ticker);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_mark_price() {
        fmt().pretty().init();
        let exchange = OkxMargin::default();
        let symbol = Symbol::new("BTC");
        let mark_price = exchange.get_mark_price(Some(symbol)).await.unwrap();
        info!("{:?}", mark_price);

        let mark_price_all = exchange.get_mark_price(None).await.unwrap();
        info!("{:?}", mark_price_all);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_bbo_ticker() {
        fmt().pretty().init();
        let rest = OkxMargin::default();
        let symbol = Symbol::new("SOL");
        let ticker = rest.get_bbo_ticker(symbol).await.unwrap();
        info!("{:?}", ticker);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_bbo_tickers() {
        fmt().pretty().init();
        let rest = OkxMargin::default();
        let tickers = rest.get_bbo_tickers().await.unwrap();
        for ticker in tickers.into_iter().take(5) {
            info!("{:?}", ticker);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_depth() {
        fmt().pretty().init();
        let rest = OkxMargin::default();
        let symbol = Symbol::new("BTC");
        let depth = rest.get_depth(symbol, Some(3)).await.unwrap();
        info!("{} bids: {:?}", depth.symbol, depth.bids);
        info!("{} asks: {:?}", depth.symbol, depth.asks);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instrument() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let instrument = rest.get_instrument(symbol).await.unwrap();
        info!("{:#?}", instrument);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_instruments() {
        fmt().pretty().init();
        let rest = OkxMargin::default();
        let instruments = rest.get_instruments().await.unwrap();
        info!("Len: {}", instruments.len());
        for instrument in instruments.into_iter().take(5) {
            info!("{:?}", instrument);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let orders = rest.get_open_orders(symbol).await.unwrap();
        for order in orders {
            info!("{:?}", order);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_all_open_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let orders = rest.get_all_open_orders().await.unwrap();
        for order in orders {
            info!("{:?}", order);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_orders() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let end = time_ms();
        let start = end - 1000 * 3600 * 24;
        let orders = rest.get_orders(symbol, start, end).await.unwrap();
        for order in orders {
            info!("{:?}", order);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_market_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = OkxMargin::new(config).await;
        let order = Order::market_open(Symbol::new("SOL"), 0.002, PosSide::Long, exchange);
        let params = OrderParams {
            is_dual_side: false,
            leverage: 1,
            margin_mode: Some(MarginMode::Cross),
            take_profit: None,
            stop_loss: None,
            ..Default::default()
        };
        let order = Order {
            price: Some(240.),
            ..order
        };
        let order_id = rest.post_order(order, params).await.unwrap();
        info!("order_id: {order_id}");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let params = OrderParams {
            is_dual_side: false,
            leverage: 2,
            margin_mode: Some(MarginMode::Cross),
            take_profit: None,
            stop_loss: None,
            ..Default::default()
        };
        let rest = OkxMargin::new(config).await;
        let order = Order::limit_open(
            Symbol::new("SOL"),
            160.0,
            0.001,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let order_id = rest.post_order(order, params).await.unwrap();
        info!("{}", order_id);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_post_batch_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = OkxMargin::new(config).await;
        let orders = vec![
            Order::market_open(Symbol::new("SOL"), 0.001, PosSide::Long, exchange),
            Order::market_open(Symbol::new("SOL"), 0.001, PosSide::Short, exchange),
        ];
        let params = OrderParams::new(true, 1);
        let order_ids = rest.post_batch_order(orders, params).await.unwrap();
        info!("{:?}", order_ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_batch_cancel_order_by_ids() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = OkxMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let orders = vec![
            Order::limit_open(
                symbol.clone(),
                1.0,
                0.001,
                PosSide::Long,
                TimeInForce::GTC,
                exchange,
            ),
            Order::limit_close(
                symbol.clone(),
                2.0,
                0.001,
                PosSide::Short,
                TimeInForce::GTC,
                exchange,
            ),
        ];
        let params = OrderParams {
            margin_mode: Some(MarginMode::Cross),
            ..Default::default()
        };
        let rsp = rest.post_batch_order(orders, params).await.unwrap();
        info!("{:?}", rsp);
        let ids = rsp
            .success_list
            .iter()
            .map(|r| r.id.clone().unwrap())
            .collect();
        let order_ids = rest
            .batch_cancel_order_by_ids(Some(symbol), Some(ids), None)
            .await
            .unwrap();
        info!("{:?}", order_ids);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_auto_cancel_open_order() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let orders = rest.get_all_open_orders().await.unwrap();
        let info: FxHashMap<Symbol, Vec<String>> =
            orders.into_iter().fold(FxHashMap::default(), |mut m, o| {
                m.entry(o.symbol).or_default().push(o.id);
                m
            });
        for (symbol, ids) in info {
            let rsp = rest
                .batch_cancel_order_by_ids(Some(symbol), Some(ids), None)
                .await
                .unwrap();
            for o in rsp.success_list {
                info!("{:?}", o);
            }
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_amend_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = OkxMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let mut order = Order::limit_open(
            symbol.clone(),
            100.0,
            0.001,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let params = OrderParams {
            margin_mode: Some(MarginMode::Cross),
            ..Default::default()
        };
        let order_id = rest.post_order(order.clone(), params).await.unwrap();
        info!("{:#?}", order_id);
        order.id = order_id.clone();
        order.price = Some(110.0);
        order.cid = Some(time_ns().to_string());
        let amend_order_id = rest.amend_order(order).await.unwrap();
        info!("{:#?}", amend_order_id);
        assert_eq!(order_id, amend_order_id);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_cancel_order() {
        fmt().pretty().init();
        let config = test_config();
        let exchange = config.exchange;
        let rest = OkxMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let order = Order::limit_open(
            symbol.clone(),
            100.0,
            0.001,
            PosSide::Long,
            TimeInForce::GTC,
            exchange,
        );
        let params = OrderParams {
            margin_mode: Some(MarginMode::Cross),
            ..Default::default()
        };
        let order_id = rest.post_order(order, params).await.unwrap();
        info!("{:?}", order_id);
        let id = OrderId::Id(order_id);
        rest.cancel_order(symbol, id).await.unwrap();
        info!("撤单成功");
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_fee_rate() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let symbol = Symbol::new("SOL");
        let fee_rate = rest.get_fee_rate(symbol).await.unwrap();
        info!("{:?}", fee_rate);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_balances() {
        fmt().pretty().with_env_filter("okx=debug,info").init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let balances = rest.get_balances().await.unwrap();
        for balance in balances {
            info!("{:?}", balance);
        }
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_leverage() {
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let symbol = Symbol::new("BTC");
        rest.set_leverage(symbol, 4).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_set_dual_side() {
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        rest.set_dual_side(true).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_is_dual_side() {
        let config: ExConfig = test_config();
        let rest = OkxMargin::new(config).await;
        let is_dual_side = rest.is_dual_side().await.unwrap();
        info!("{:?}", is_dual_side);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_user_id() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let user_id = rest.get_user_id().await.unwrap();
        info!("{:?}", user_id);
    }

    // #[tokio::test]
    // #[ignore = "need proxy"]
    // async fn test_get_margin_ratio() {
    //     fmt().pretty().with_env_filter("okx=debug,info").init();

    //     let config = test_config();
    //     let rest = OkxMargin::new(config).await;
    //     let rsp = rest.get_margin_ratio().await.unwrap();
    //     info!("{rsp}");
    // }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_account_info() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let account_info = rest.get_account_info().await.unwrap();
        info!("account info: {:?}", account_info);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_borrow_rate() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let coin = "BTC".to_string();
        let borrow_rate = rest.get_borrow_rate(Some(coin)).await.unwrap();
        info!("{:?}", borrow_rate);
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_borrow_limits() {
        fmt().pretty().init();
        let config = test_config();
        let rest = OkxMargin::new(config).await;
        let coin = "USDT".to_string();
        let borrow_limit = rest.get_borrow_limits(Some(false), coin).await.unwrap();
        info!("{:?}", borrow_limit);
    }
}

#[cfg(test)]
mod tests {
    use std::{str::FromStr, sync::Arc};

    use gate::spot::ws::GateSpotWs;
    use quant_common::base::*;
    use quant_common::*;
    use tokio::time::{Duration, timeout};
    use tracing_subscriber::fmt;
    // use tracing_subscriber::fmt;

    fn get_subscribe(symbols: Vec<Symbol>) -> Subscribes<DefaultWsHandler> {
        Subscribes::new(
            0,
            Default<PERSON>s<PERSON><PERSON><PERSON>,
            vec![
                // pub
                // SubscribeChannel::MarkPrice(symbols.clone()),
                SubscribeChannel::Bbo(Arc::new(symbols.clone())),
                // SubscribeChannel::Depth(DepthWsParams {
                //     symbols: Arc::new(symbols.clone()),
                //     levels: 5,
                // }),
                // SubscribeChannel::Trade(Arc::new(symbols.clone())),
                // SubscribeChannel::Funding(symbols.clone()),
                // // pri
                SubscribeChannel::Order(Arc::new(symbols.clone())),
                // SubscribeChannel::Position(symbols.clone()),
                // SubscribeChannel::Balance,

                // done
            ],
        )
    }

    #[tokio::test]
    #[ignore]
    async fn test_weburl() -> Result<()> {
        let _ = fmt().pretty().try_init();
        let config = ExConfig {
            host: Some("webws.gateio.live".to_string()),
            ..Default::default()
        };

        let mut ws = GateSpotWs::new(config).await;

        let symbols = vec![Symbol::new("COS"), Symbol::new("TON")];

        let subs = get_subscribe(symbols);

        // 使用 timeout 限制运行时间
        match timeout(Duration::from_secs(5), ws.run(subs)).await {
            Ok(Ok(_)) => {
                // ws.run 成功完成 (不太可能在 5 秒内发生)
                println!("WebSocket finished within 5 seconds.");
            }
            Ok(Err(e)) => {
                // ws.run 返回错误
                eprintln!("WebSocket returned an error: {e:?}");
                return Err(e);
            }
            Err(_) => {
                // 超时，符合预期
                println!("WebSocket ran for 5 seconds and timed out as expected.");
            }
        }

        Ok(())
    }

    #[tokio::test]
    async fn test_all() -> Result<()> {
        let _ = fmt().pretty().try_init();
        let config = test_config();

        let mut ws = GateSpotWs::new(config).await;

        let symbols = vec![Symbol::new("BTC"), Symbol::new("TON")];

        let subs = get_subscribe(symbols);

        // 使用 timeout 限制运行时间
        match timeout(Duration::from_secs(5), ws.run(subs)).await {
            Ok(Ok(_)) => {
                // ws.run 成功完成 (不太可能在 5 秒内发生)
                println!("WebSocket finished within 5 seconds.");
            }
            Ok(Err(e)) => {
                // ws.run 返回错误
                eprintln!("WebSocket returned an error: {e:?}");
                return Err(e);
            }
            Err(_) => {
                // 超时，符合预期
                println!("WebSocket ran for 5 seconds and timed out as expected.");
            }
        }

        Ok(())
    }

    #[tokio::test]
    #[ignore]
    async fn test_spot_ws_pri() -> Result<()> {
        let _ = fmt().pretty().try_init();
        let config = test_config();

        let mut ws = GateSpotWs::new(config).await;

        let symbols = vec![Symbol::new("BTC")];

        let subs = Subscribes::new(
            0,
            DefaultWsHandler,
            // vec![SubscribeChannel::Order(Arc::new(symbols.clone()))],
            vec![SubscribeChannel::Kline(KlineWsParams {
                symbols: Arc::new(symbols.clone()),
                interval: KlineInterval::from_str("1m").unwrap(),
            })],
        );
        // 使用 timeout 限制运行时间
        match timeout(Duration::from_secs(5), ws.run(subs)).await {
            Ok(Ok(_)) => {
                // ws.run 成功完成 (不太可能在 5 秒内发生)
                println!("WebSocket finished within 5 seconds.");
            }
            Ok(Err(e)) => {
                // ws.run 返回错误
                eprintln!("WebSocket returned an error: {e:?}");
                return Err(e);
            }
            Err(_) => {
                // 超时，符合预期
                println!("WebSocket ran for 5 seconds and timed out as expected.");
            }
        }

        Ok(())
    }
}

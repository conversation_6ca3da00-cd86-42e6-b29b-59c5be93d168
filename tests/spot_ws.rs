mod tests {
    use ethers::types::H160;
    use hyperliquid::util::generate_128bit_hex;
    use hyperliquid::{spot::ws::HyperLiquidSpotWs, util};
    use log::info;
    use quant_common::base::{Quote<PERSON>cy, WebSocket};
    use quant_common::{
        base::{
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DepthWsParams, ExConfig, Order, OrderId, OrderParams, OrderSide,
            OrderType, PosSide, Rest, SubscribeChannel, Subscribes, Symbol, TimeInForce, Transfer,
            WalletType,
        },
        time_ms, Result,
    };
    use serde::{Deserialize, Serialize};
    use std::sync::Arc;
    use std::time::Duration;
    use time::Time;
    use tokio::time::sleep;
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        let toml_str = r#"
        exchange = "HyperLiquidSpot"
        key = "******************************************"
        secret ="16914d71ba416fc9b75563c9b47f3a598b1a49740df821d9e5dbdd450f9fb808"
        passphrase = "passphrase"
        is_colo = false
        is_testnet = false
        is_unified = false
        params = { is_vip = "false", cookie = "some_cookie", code = "1234", org_id = "5678" }
    "#;
        toml::from_str(&toml_str).unwrap()
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_pub() -> Result<()> {
        fmt().pretty().init();
        let config = test_config();
        let mut ws = HyperLiquidSpotWs::new(config).await;
        let symbols = Arc::new(vec![
            Symbol {
                base: "PURR".to_string(),
                quote: QuoteCcy::USDC,
            },
            Symbol {
                base: "HYPE".to_string(),
                quote: QuoteCcy::USDC,
            },
        ]);
        let channels = vec![
            // SubscribeChannel::MarkPrice(symbols.clone()),
            // SubscribeChannel::Bbo(symbols.clone()),
            // SubscribeChannel::Depth(DepthWsParams::new(symbols.clone(), 5)),
            // SubscribeChannel::Trade(symbols.clone()),
            SubscribeChannel::Order(symbols.clone()),
            // SubscribeChannel::Balance,
        ];
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        // let ex = ws.clone();
        // tokio::spawn(async move {
        //     sleep(Duration::from_secs(5)).await;
        //     info!("run 5 s");
        //     let mut order = Order::market_open(Symbol{base:"BTC".to_string(),quote:quant_common::base::QuoteCcy::USDC}, 0.00014, PosSide::Short, ex.exchange());
        //     // Limit order
        //     order.order_type=OrderType::Limit;
        //     order.time_in_force=TimeInForce::GTC;
        //     // order.time_in_force=TimeInForce::IOC;
        //     // order.time_in_force=TimeInForce::FOK;
        //     order.pos_side=Some(PosSide::Short);
        //     order.side=OrderSide::Sell;
        //     order.price=Some(95763.);
        //     order.cid=Some(generate_128bit_hex());
        //     let params = OrderParams::new(false, 1);
        //     let order_id =ex.rest.post_order(order,params).await.expect("post order error");
        //     info!("rsp={:?}",order_id)
        // });
        ws.run(subs).await?;
        Ok(())
    }
}

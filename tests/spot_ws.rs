#[cfg(test)]
mod tests {
    use std::sync::Arc;
    use std::{fs::read_to_string, time::Duration};

    use binance::spot::rest::BinanceSpot;
    use binance::spot::ws::BinanceSpotWs;
    use quant_common::Result;
    use quant_common::base::{
        model::{ExConfig, Subscribes, Symbol},
        *,
    };
    use tokio::time::timeout;
    use tracing_subscriber::fmt;

    fn test_config() -> ExConfig {
        toml::from_str(&read_to_string("spot.toml").unwrap()).unwrap()
    }

    #[derive(Clone)]
    struct DepthWsHandler;

    impl WsHandler for DepthWsHandler {
        async fn on_depth(&self, depth: Depth, _: Exchange) -> Result<()> {
            tracing::info!("------------------- {} -------------------", depth.symbol);
            for ask in depth.asks.iter().rev() {
                tracing::info!("asks: {:.2} @ {}", ask.amount * ask.price, ask.price);
            }
            for bid in depth.bids {
                tracing::info!("bids: {:.2} @ {}", bid.amount * bid.price, bid.price);
            }
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_pub() {
        let _ = fmt().try_init();
        let config = ExConfig::default();
        let mut ws = BinanceSpotWs::new(config).await;
        let symbols = Arc::new(vec!["CAT"].into_iter().map(Symbol::new).collect::<Vec<_>>());
        let depth_params = DepthWsParams::new(symbols.clone(), 2);
        let channels = vec![SubscribeChannel::Depth(depth_params)];
        let subs = Subscribes::new(0, DepthWsHandler, channels);
        let _ = timeout(Duration::from_secs(15), ws.run(subs)).await;
    }

    #[tokio::test]
    #[ignore = "需要通过日志检查"]
    async fn test_pri() {
        let _ = fmt().try_init();
        let config = test_config();
        let symbols = Arc::new(
            vec!["BTC", "ETH", "CHEEMS", "CHEEMS", "ARKM"]
                .into_iter()
                .map(Symbol::new)
                .collect::<Vec<_>>(),
        );
        let channels = vec![
            SubscribeChannel::Order(symbols.clone()),
            // SubscribeChannel::Balance,
            // SubscribeChannel::Position(symbols.clone()),
        ];
        let mut ws = BinanceSpotWs::new(config).await;
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await.unwrap();
    }

    #[tokio::test]
    #[ignore = "需要通过日志检查"]
    async fn test_bbo() {
        let _ = fmt().try_init();
        let config = test_config();
        let symbols = Arc::new(
            vec!["SATS", "CAT", "CHEEMS", "PEPE"]
                .into_iter()
                .map(Symbol::new)
                .collect::<Vec<_>>(),
        );
        let channels = vec![SubscribeChannel::Bbo(symbols.clone())];
        let mut ws = BinanceSpotWs::new(config).await;
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        ws.run(subs).await.unwrap();
    }

    #[tokio::test]
    async fn test_all() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let symbols = Arc::new(vec![Symbol::new("BTC"), Symbol::new("ETH")]);
        let mut ws = BinanceSpotWs::new(config).await;
        let subs = Subscribes::default_spot(symbols);
        let ret = timeout(Duration::from_secs(10), ws.run(subs)).await;
        assert!(ret.is_err());
    }

    #[tokio::test]
    async fn test_ws_kline() {
        let _ = fmt().with_target(false).try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()
        };
        let mut ws = BinanceSpotWs::new(config.clone()).await;
        let symbols = Arc::new(
            vec!["ETH", "SATS"]
                .into_iter()
                .map(Symbol::new)
                .collect::<Vec<_>>(),
        );
        let channels = [KlineInterval::Min1, KlineInterval::Day1]
            .into_iter()
            .map(|interval| (symbols.clone(), interval))
            .map(|(symbols, interval)| SubscribeChannel::Kline(KlineWsParams { symbols, interval }))
            .collect();
        let subs = Subscribes::new(0, DefaultWsHandler, channels);
        let ret = timeout(Duration::from_secs(40), ws.run(subs)).await;
        assert!(ret.is_err());
    }

    #[tokio::test]
    #[ignore = "现货不支持TRADE_LITE"]
    async fn test_order_and_fill() {
        let _ = fmt().pretty().try_init();
        let config = test_config();
        let symbols = Arc::new(vec![Symbol::new("BTC"), Symbol::new("ETH")]);
        let mut ws = BinanceSpotWs::new(config).await;
        let subs = Subscribes::new(
            0,
            DefaultWsHandler,
            vec![SubscribeChannel::OrderAndFill(symbols)],
        );
        let ret = timeout(Duration::from_secs(10), ws.run(subs)).await;
        assert!(ret.is_err());
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn debug_symbols_ws_depth() -> Result<()> {
        let _ = fmt().try_init();
        let config = ExConfig {
            is_testnet: false,
            ..test_config()
        };
        let mut ws = BinanceSpotWs::new(config.clone()).await;
        let rest = BinanceSpot::new(config).await;
        let xs = rest.get_instruments().await?;
        let symbols = xs
            .into_iter()
            .map(|x| x.symbol)
            .take(20)
            .collect::<Vec<_>>();
        let symbols = Arc::new(symbols);

        #[derive(Clone)]
        struct DepthWsHandler;

        impl WsHandler for DepthWsHandler {
            async fn on_depth(&self, depth: Depth, exchange: Exchange) -> Result<()> {
                tracing::info!(
                    "------------------- {} @ {} -------------------",
                    depth.symbol,
                    exchange
                );
                for ask in depth.asks.iter().rev() {
                    tracing::info!("asks: {:.2} @ {:.2}", ask.amount * ask.price, ask.price);
                }
                for bid in depth.bids {
                    tracing::info!("bids: {:.2} @ {:.2}", bid.amount * bid.price, bid.price);
                }
                Ok(())
            }
        }

        let subs = Subscribes::new(
            0,
            DepthWsHandler,
            vec![SubscribeChannel::Depth(DepthWsParams::new(symbols, 1))],
        );
        ws.run(subs).await
    }

    #[tokio::test]
    #[ignore = "需要通过日志确认"]
    async fn test_ws_price() -> Result<()> {
        let _ = fmt().try_init();
        let config = Default::default();
        let mut ws = BinanceSpotWs::new(config).await;
        let symbols = Arc::new(vec![Symbol::new("PEPE")]);

        let subs = Subscribes::new(
            0,
            DefaultWsHandler,
            vec![SubscribeChannel::Trade(symbols.clone())],
        );
        ws.run(subs).await
    }
}

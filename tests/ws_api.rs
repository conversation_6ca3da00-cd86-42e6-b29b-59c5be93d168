use std::time::Duration;

use async_channel::bounded;
use quant_api::ws_api::{
    ExchangeWsAPI, create_private_ws_api, create_public_ws_api, is_ws_api_supported,
};
use quant_common::base::model::ws_api::{CancelOrderCmd, PlaceOrderCmd};
use quant_common::base::traits::ws_api::{AsyncResHandle, WebSocketAPI};
use quant_common::base::{AsyncCmd, Order, OrderParams, PosSide, Symbol, TimeInForce};
use quant_common::base::{Exchange, model::ExConfig};
use quant_common::{Result, test_config};
use tokio::task::yield_now;
use tracing_subscriber::fmt;

// 测试辅助结构体
struct TestHandler;

impl AsyncResHandle for TestHandler {
    async fn handle_post_order(
        &self,
        account_id: usize,
        req_id: u64,
        order: quant_common::base::PlaceOrderResult,
    ) -> Result<()> {
        tracing::info!(
            "Handle post order: account_id={}, req_id={}, order={:?}",
            account_id,
            req_id,
            order
        );
        Ok(())
    }

    async fn handle_post_batch_order(
        &self,
        account_id: usize,
        req_id: u64,
        orders: Result<quant_common::base::BatchOrderRsp>,
    ) -> Result<()> {
        tracing::info!(
            "Handle post batch order: account_id={}, req_id={}, orders={:?}",
            account_id,
            req_id,
            orders
        );
        Ok(())
    }

    async fn handle_amend_order(
        &self,
        account_id: usize,
        req_id: u64,
        order: quant_common::base::AmendOrderResult,
    ) -> Result<()> {
        tracing::info!(
            "Handle amend order: account_id={}, req_id={}, order={:?}",
            account_id,
            req_id,
            order
        );
        Ok(())
    }

    async fn handle_cancel_order(
        &self,
        account_id: usize,
        req_id: u64,
        order: quant_common::base::CancelOrderResult,
    ) -> Result<()> {
        tracing::info!(
            "Handle cancel order: account_id={}, req_id={}, order={:?}",
            account_id,
            req_id,
            order
        );
        Ok(())
    }

    async fn handle_batch_cancel_order(
        &self,
        account_id: usize,
        req_id: u64,
        orders: Result<quant_common::base::BatchOrderRsp>,
    ) -> Result<()> {
        tracing::info!(
            "Handle batch cancel order: account_id={}, req_id={}, orders={:?}",
            account_id,
            req_id,
            orders
        );
        Ok(())
    }

    async fn handle_batch_cancel_order_by_ids(
        &self,
        account_id: usize,
        req_id: u64,
        orders: Result<quant_common::base::BatchOrderRsp>,
    ) -> Result<()> {
        tracing::info!(
            "Handle batch cancel order by ids: account_id={}, req_id={}, orders={:?}",
            account_id,
            req_id,
            orders
        );
        Ok(())
    }
}

#[tokio::test]
async fn test_is_ws_api_supported() {
    // 测试支持的交易所
    // assert!(is_ws_api_supported(Exchange::BinanceSwap));
    assert!(is_ws_api_supported(Exchange::BinanceSpot));

    // 测试不支持的交易所
    assert!(!is_ws_api_supported(Exchange::BinanceMargin));
    assert!(!is_ws_api_supported(Exchange::HuobiSpot));
}

#[tokio::test]
#[ignore]
async fn test_create_public_ws_api() {
    // 测试创建支持的交易所
    let api = create_public_ws_api(Exchange::OkxSwap).await.unwrap();
    assert!(matches!(api, ExchangeWsAPI::OkxSwap(_)));
    assert_eq!(api.exchange(), Exchange::OkxSwap);

    // 测试创建不支持的交易所
    let api = create_public_ws_api(Exchange::OkxSwap).await.unwrap();

    assert!(matches!(api, ExchangeWsAPI::Unsupported(_)));
    assert_eq!(api.exchange(), Exchange::OkxSwap);
}

#[tokio::test]
#[ignore]
async fn test_create_private_ws_api() {
    // 创建配置
    let config = ExConfig {
        exchange: Exchange::BinanceSpot,
        // 其他配置字段根据实际需要填写
        key: "test_key".to_string(),
        secret: "test_secret".to_string(),
        ..Default::default()
    };

    // 测试创建支持的交易所
    let api = create_private_ws_api(config).await.unwrap();
    assert!(matches!(api, ExchangeWsAPI::BinanceSpot(_)));
    assert_eq!(api.exchange(), Exchange::BinanceSpot);

    // 测试创建不支持的交易所
    let config = ExConfig {
        exchange: Exchange::KucoinSpot,
        // 其他配置字段根据实际需要填写
        key: "test_key".to_string(),
        secret: "test_secret".to_string(),
        ..Default::default()
    };
    let api = create_private_ws_api(config).await.unwrap();
    assert!(matches!(api, ExchangeWsAPI::Unsupported(_)));
    assert_eq!(api.exchange(), Exchange::KucoinSpot);
}

#[tokio::test]
async fn test_run_unsupported_exchange() {
    // 创建不支持的交易所 API
    let api = create_public_ws_api(Exchange::HuobiSwap).await.unwrap();

    // 创建通道
    let (_tx, rx) = bounded::<(u64, AsyncCmd)>(100);

    // 尝试运行不支持的交易所
    let handler = TestHandler;
    let result = api.run(0, handler, rx).await;

    // 应该返回错误
    assert!(result.is_err());
    assert!(result.unwrap_err().to_string().contains("未实现"));
}

#[tokio::test]
#[ignore]
async fn test_run_supported_exchange() {
    fmt().pretty().init();
    let config = test_config();

    // 创建支持的交易所 API
    let api = create_private_ws_api(config).await.unwrap();

    let (tx, rx) = bounded::<(u64, AsyncCmd)>(100);

    // 运行 API
    let handler = TestHandler;
    tokio::spawn(async move {
        api.run(0, handler, rx).await.unwrap();
    });

    // 下限价单、修改订单、撤单
    let symbol = Symbol::new("BNB");
    let mut order = Order::limit_open(
        symbol.clone(),
        251.0,
        0.02,
        PosSide::Long,
        TimeInForce::GTC,
        Exchange::BinanceSwap,
    );
    let params = OrderParams::default();

    // 发送下单命令
    let place_order_cmd = PlaceOrderCmd {
        order: order.clone(),
        params,
    };
    let post_cmd = AsyncCmd::PlaceOrder(place_order_cmd);
    tx.send((1, post_cmd)).await.unwrap();
    yield_now().await;
    tokio::time::sleep(Duration::from_secs(1)).await;

    // 修改订单
    order.price = Some(252.0);
    let amend_cmd = AsyncCmd::AmendOrder(order.clone());
    tx.send((2, amend_cmd)).await.unwrap();
    yield_now().await;
    tokio::time::sleep(Duration::from_secs(1)).await;

    // 撤单
    let cancel_order_cmd = CancelOrderCmd {
        order_id: quant_common::base::OrderId::ClientOrderId(order.cid.unwrap()),
        symbol,
    };
    let cancel_cmd = AsyncCmd::CancelOrder(cancel_order_cmd);
    tx.send((3, cancel_cmd)).await.unwrap();

    tokio::time::sleep(Duration::from_secs(10)).await;
}

import copy
import time
import base_strategy
import trader # type: ignore

class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader_instance: trader.Trader):
        # 调用父类初始化
        super().__init__(cex_configs, dex_configs, config, trader_instance)

        self.interval = config.get('interval', 10000) / 1000
        self.symbol = config.get('symbol', 'ETH_USDT')

        self.first_instruments = None
        self.second_instruments = None

        self.cexs = cex_configs

        # 记录trader实例
        self.trader = trader_instance

        self.last_trigger_time = 0

        self.cid = None
        self.place_orderstart_time = 0
        self.cancel_ordering = False
        self.cancel_orderstart_time = 0

    def subscribes(self):
        """返回策略需要订阅的数据类型"""
        return [
            {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [
                        {
                            "Depth": {
                                "symbols": [self.symbol],
                                "levels": 2
                            },
                        },
                        {
                            "Bbo": [self.symbol]
                        }
                    ]
                }
            }
        ]

    def start(self):
        # 启动时先撤销所有订单
        self.trader.log("策略启动，准备撤销所有现有订单...", "INFO", web=True)

        self.clear_up()

        # 构建下单和撤单指令
        order = {
            "symbol": self.symbol,
            "order_type": "Limit",
            "side": "Buy",
            "time_in_force": "IOC",
            "price": 0,
            "amount": 0.01,
        }

        # 构建下单命令
        self.place_order_cmd = {
            "account_id": 0,
            "cmd": {
                "Async": {
                    "PlaceOrder": [order, {"is_dual_side": False}]
                }
            }
        }

        # 构建撤单命令
        self.cancel_order_cmd = {
            "account_id": 0,
            "cmd": {
                "Async": {
                    "CancelOrder": [{"ClientOrderId": ""}, self.symbol]
                }
            }
        }

        reutrn {
            "account_id": 0,
            "cmd": {
                "Sync": {"is_dual_side": True}
            }
        }

    def on_bbo(self, exchange, context, bbo):
        pass

    def on_depth(self, exchange, context, depth):
        if depth['bids'][0][0] >= depth['asks'][0][0]:
            self.trader.log(f"depth: {depth['bids'][0]} {depth['asks'][0]}", "ERROR", web=True)
        else:
            self.trader.log(f"depth: {depth['bids'][0]} {depth['asks'][0]}", "INFO", web=True)



    def trade(self, price):
        price = price - 200
        if time.time() - self.last_trigger_time > self.interval:
            bid_price = price
            cid = str(time.time_ns() // 1_000)
            cmd = copy.deepcopy(self.place_order_cmd)
            cmd['cmd']['Async']['PlaceOrder'][0]['price'] = bid_price
            cmd['cmd']['Async']['PlaceOrder'][0]['cid'] = cid + "-0"
            cmd['cmd']['Async']['PlaceOrder'][0]['pos_side'] = 'long'
            self.place_orderstart_time = time.time()
            self.last_trigger_time = time.time()
            return {
                'cmds': [cmd, cmd1],
            }
        else:
            return None

    def on_order(self, account_id, context, order):
        """
        订单状态更新时触发的方法。

        Args:
            account_id (str): 账户 ID。
            context: 上下文对象。
            order: 订单对象。
        """
        pass
        self.trader.log(f"订单状态更新: {order}", "INFO", web=True)

    def on_order_submitted(self, account_id, context, order_id_result, order):
        """
        订单提交成功时触发的方法。

        Args:
            account_id (str): 账户 ID。
            context (dict): 上下文对象。
            order_result (str): 包含订单 ID 的 Result 可能为 Err。
            order (dict): 订单信息。
        """
        if 'Ok' in order_id_result:
            latency = round((time.time() - self.place_orderstart_time) *1000, 1)
            if latency > 50:
                self.trader.log(f"订单提交成功 延迟: {latency}ms", "INFO", web=True)
            self.cid = order['cid']
        else:
            self.trader.log(f"订单提交失败: {order_id_result}", "ERROR", web=True)

    def on_order_canceled(self, account_id, context, result, id, symbol):
        """
        订单取消成功时触发的方法。

        Args:
            account_id (str): 账户 ID。
            context (dict): 上下文对象。
            order_id (str): 订单 ID。
            id (str): 撤单时传入的订单id/订单cid。
            symbol(str): 撤单时传入的symbol。
        """
        self.cancel_ordering = False
        self.cid = None
        if 'Ok' in result:
            latency = round((time.time() - self.cancel_orderstart_time) *1000, 1)
            if latency > 50:
                self.trader.log(f"订单取消成功 延迟: {latency}ms", "INFO", web=True)
            self.cid = None
        else:
            self.trader.log(f"订单取消失败: {result}", "ERROR", web=True)

    def on_stop(self):
        self.trader.log("策略停止", "INFO", web=True)
        self.clear_up()

    def clear_up(self):
        self.trader.log("开始清理", "INFO", web=True)

        # 清理仓位
        self.close_all_positions()

        self.trader.log("清理完成", "INFO", web=True)

    def get_position(self) -> dict:
        """
        获取当前持仓信息

        查询所有交易品种的持仓状态

        返回:
            dict: 持仓信息字典
        """
        cmd = {
            "account_id": 0,
            "cmd": {
                "Sync": {"Position": None}
            }
        }
        res = self.trader.publish(cmd)
        if res is None:
            self.trader.log("交易接口未就绪，无法获取持仓", level="WARN", color="yellow")
            return []
        elif 'Ok' in res:
            return res['Ok']
        else:
            self.trader.log(f"获取持仓失败: {res}", level="ERROR", color="red")
        return []

    def close_all_positions(self):
        """
        平掉所有持仓

        遍历所有持仓，提交市价单平仓
        """
        for position in self.get_position():
            symbol = position['symbol']
            amount = position['amount']
            if amount > 0:
                self.trader.log(f"平仓仓位: {position}", level="INFO", color="blue")
                pos_side = position['side']  # 持仓方向
                # 确定平仓方向：持多仓卖出，持空仓买入
                side = 'Sell' if pos_side == 'Long' else 'Buy'

                # 处理数量精度
                instrument = self.instruments.get(symbol, {})
                amount_precision = instrument.get('amount_precision', 3)
                amount_multiplier = instrument.get('amount_multiplier', 1)

                self.trader.log(f"平仓数量（单位币数量）: {amount}", level="INFO", color="blue")
                amount = amount * amount_multiplier  # 转换为交易所张数

                # 保留精度
                amount = round(amount, amount_precision)
                self.trader.log(f"平仓数量（合约张数）: {amount}", level="INFO", color="blue")

                # 准备市价平仓订单
                order = {
                    "symbol": symbol,
                    "order_type": "Market",  # 市价单，确保能立即成交
                    "side": side,
                    "pos_side": pos_side,
                    "time_in_force": "IOC",  # 立即成交否则取消
                    "price": None,  # 市价单无需指定价格
                    "amount": amount,
                }
                self.trader.log(f"平仓订单: {order}", level="INFO", color="blue")

                # 提交市价平仓订单
                cmd = {
                    "account_id": 0,
                    "cmd": {
                        "Sync": {
                            "PlaceOrder": [
                                order,
                                {
                                    "is_dual_side": False,
                                    "margin_mode": "Cross",  # 全仓模式
                                    "market_order_mode": "Normal"  # 普通市价单
                                }
                            ]
                        }
                    }
                }
                res = self.trader.publish(cmd)
                self.trader.log(f"平仓结果: {res}", level="INFO", color="blue")

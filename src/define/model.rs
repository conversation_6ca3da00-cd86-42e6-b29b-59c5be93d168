use std::collections::HashMap;

use ethers::{
    abi::{encode, ParamType, Tokenizable},
    types::{
        transaction::eip712::{self, encode_eip712_type, EIP712Domain, Eip712, Eip712Error},
        H160, U256,
    },
    utils::keccak256,
};
use quant_common::utils::de_from_str;
use quant_common::{
    base::{Depth, QuoteCcy, Symbol},
    qerror,
};
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};
use tracing::info;

use crate::spot::SpotToken;

use super::{action::get_order_type, web_data::WebData2Data};

pub(crate) const HYPERLIQUID_EIP_PREFIX: &str = "HyperliquidTransaction:";

fn eip_712_domain(chain_id: U256) -> EIP712Domain {
    EIP712Domain {
        name: Some("HyperliquidSignTransaction".to_string()),
        version: Some("1".to_string()),
        chain_id: Some(chain_id),
        verifying_contract: Some(
            "******************************************"
                .parse()
                .unwrap(),
        ),
        salt: None,
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct UsdSend {
    pub signature_chain_id: U256,
    pub hyperliquid_chain: String,
    pub destination: String,
    pub amount: String,
    pub time: u64,
}

impl Eip712 for UsdSend {
    type Error = Eip712Error;

    fn domain(&self) -> Result<EIP712Domain, Self::Error> {
        Ok(eip_712_domain(self.signature_chain_id))
    }

    fn type_hash() -> Result<[u8; 32], Self::Error> {
        Ok(eip712::make_type_hash(
            format!("{HYPERLIQUID_EIP_PREFIX}UsdSend"),
            &[
                ("hyperliquidChain".to_string(), ParamType::String),
                ("destination".to_string(), ParamType::String),
                ("amount".to_string(), ParamType::String),
                ("time".to_string(), ParamType::Uint(64)),
            ],
        ))
    }

    fn struct_hash(&self) -> Result<[u8; 32], Self::Error> {
        let Self {
            signature_chain_id: _,
            hyperliquid_chain,
            destination,
            amount,
            time,
        } = self;
        let items = vec![
            ethers::abi::Token::Uint(Self::type_hash()?.into()),
            encode_eip712_type(hyperliquid_chain.clone().into_token()),
            encode_eip712_type(destination.clone().into_token()),
            encode_eip712_type(amount.clone().into_token()),
            encode_eip712_type(time.into_token()),
        ];
        Ok(keccak256(encode(&items)))
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Withdraw3 {
    pub hyperliquid_chain: String,
    pub signature_chain_id: U256,
    pub amount: String,
    pub time: u64,
    pub destination: String,
}

impl Eip712 for Withdraw3 {
    type Error = Eip712Error;

    fn domain(&self) -> Result<EIP712Domain, Self::Error> {
        Ok(eip_712_domain(self.signature_chain_id))
    }

    fn type_hash() -> Result<[u8; 32], Self::Error> {
        Ok(eip712::make_type_hash(
            format!("{HYPERLIQUID_EIP_PREFIX}Withdraw"),
            &[
                ("hyperliquidChain".to_string(), ParamType::String),
                ("destination".to_string(), ParamType::String),
                ("amount".to_string(), ParamType::String),
                ("time".to_string(), ParamType::Uint(64)),
            ],
        ))
    }

    fn struct_hash(&self) -> Result<[u8; 32], Self::Error> {
        let Self {
            signature_chain_id: _,
            hyperliquid_chain,
            amount,
            time,
            destination,
        } = self;
        let items = vec![
            ethers::abi::Token::Uint(Self::type_hash()?.into()),
            encode_eip712_type(hyperliquid_chain.clone().into_token()),
            encode_eip712_type(destination.clone().into_token()),
            encode_eip712_type(amount.clone().into_token()),
            encode_eip712_type(time.into_token()),
        ];
        Ok(keccak256(encode(&items)))
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SpotSend {
    pub hyperliquid_chain: String,
    pub signature_chain_id: U256,
    pub destination: String,
    pub token: String,
    pub amount: String,
    pub time: u64,
}

impl Eip712 for SpotSend {
    type Error = Eip712Error;

    fn domain(&self) -> Result<EIP712Domain, Self::Error> {
        Ok(eip_712_domain(self.signature_chain_id))
    }

    fn type_hash() -> Result<[u8; 32], Self::Error> {
        Ok(eip712::make_type_hash(
            format!("{HYPERLIQUID_EIP_PREFIX}SpotSend"),
            &[
                ("hyperliquidChain".to_string(), ParamType::String),
                ("destination".to_string(), ParamType::String),
                ("token".to_string(), ParamType::String),
                ("amount".to_string(), ParamType::String),
                ("time".to_string(), ParamType::Uint(64)),
            ],
        ))
    }

    fn struct_hash(&self) -> Result<[u8; 32], Self::Error> {
        let Self {
            signature_chain_id: _,
            hyperliquid_chain,
            destination,
            token,
            amount,
            time,
        } = self;
        let items = vec![
            ethers::abi::Token::Uint(Self::type_hash()?.into()),
            encode_eip712_type(hyperliquid_chain.clone().into_token()),
            encode_eip712_type(destination.clone().into_token()),
            encode_eip712_type(token.clone().into_token()),
            encode_eip712_type(amount.clone().into_token()),
            encode_eip712_type(time.into_token()),
        ];
        Ok(keccak256(encode(&items)))
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct UsdClassTransfer {
    pub hyperliquid_chain: String,
    pub signature_chain_id: U256,
    pub amount: String,
    pub to_perp: bool,
    pub nonce: u64,
}
impl Eip712 for UsdClassTransfer {
    type Error = Eip712Error;

    fn domain(&self) -> Result<EIP712Domain, Self::Error> {
        Ok(eip_712_domain(self.signature_chain_id))
    }

    fn type_hash() -> Result<[u8; 32], Self::Error> {
        Ok(eip712::make_type_hash(
            format!("{HYPERLIQUID_EIP_PREFIX}SpotSend"),
            &[
                ("hyperliquidChain".to_string(), ParamType::String),
                ("to_perp".to_string(), ParamType::Bool),
                ("amount".to_string(), ParamType::String),
                ("nonce".to_string(), ParamType::Uint(64)),
            ],
        ))
    }

    fn struct_hash(&self) -> Result<[u8; 32], Self::Error> {
        let Self {
            signature_chain_id: _,
            hyperliquid_chain,
            to_perp,
            amount,
            nonce,
        } = self;
        let items = vec![
            ethers::abi::Token::Uint(Self::type_hash()?.into()),
            encode_eip712_type(hyperliquid_chain.clone().into_token()),
            encode_eip712_type(to_perp.into_token()),
            encode_eip712_type(amount.clone().into_token()),
            encode_eip712_type(nonce.into_token()),
        ];
        Ok(keccak256(encode(&items)))
    }
}

#[derive(Debug, Deserialize, Clone)]
pub struct OrderStatusResponse {
    pub status: String,
    /// `None` if the order is not found
    #[serde(default)]
    pub order: Option<OrderInfo>,
}
#[derive(Debug, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct OrderInfo {
    pub order: BasicOrderInfo,
    pub status: String,
    pub status_timestamp: u64,
}
#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BasicOrderInfo {
    pub coin: String,
    pub side: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub limit_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub sz: f64,
    pub oid: u64,
    pub timestamp: u64,
    pub trigger_condition: Option<String>,
    pub is_trigger: bool,
    #[serde(deserialize_with = "de_from_str")]
    pub trigger_px: f64,
    pub is_position_tpsl: bool,
    pub reduce_only: bool,
    pub order_type: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub orig_sz: f64,
    pub tif: Option<String>,
    #[serde(default)]
    pub cloid: Option<String>,
}
impl OrderInfo {
    pub fn to_order(&self, symbol: quant_common::base::Symbol) -> quant_common::base::Order {
        //B = Bid = Buy, A = Ask = Short. Side is aggressing side for trades.
        let (mut side, mut pos_side) = (
            quant_common::base::OrderSide::default(),
            quant_common::base::PosSide::default(),
        );
        if let Some(side_str) = &self.order.side {
            match side_str.as_str() {
                "B" => {
                    side = quant_common::base::OrderSide::Buy;
                    pos_side = quant_common::base::PosSide::Long;
                }
                "A" => {
                    side = quant_common::base::OrderSide::Sell;
                    pos_side = quant_common::base::PosSide::Short;
                }
                _ => {} //todo update unknown type
            };
        }
        let order_type = match self.order.order_type.as_deref() {
            Some("Market") => quant_common::base::OrderType::Market,
            Some("Limit") => quant_common::base::OrderType::Limit,
            Some("StopMarket") => quant_common::base::OrderType::Market,
            Some("StopLimit") => quant_common::base::OrderType::Limit,
            _ => quant_common::base::OrderType::default(), //todo update unknown type
        };
        let time_in_force = match self.order.tif.as_deref() {
            Some("Gtc") => quant_common::base::TimeInForce::GTC,
            Some("Ioc") => quant_common::base::TimeInForce::IOC,
            Some("Alo") => quant_common::base::TimeInForce::PostOnly,
            _ => quant_common::base::TimeInForce::default(), //todo update unknown type
        };
        //"filled" | "open" | "canceled" | "triggered" | "rejected" | "marginCanceled",
        let status = match self.status.as_str() {
            "open" => quant_common::base::OrderStatus::Open,
            "filled" => quant_common::base::OrderStatus::Filled,
            "canceled" => quant_common::base::OrderStatus::Canceled,
            "rejected" => quant_common::base::OrderStatus::Canceled,
            "triggered" => quant_common::base::OrderStatus::Filled,
            "marginCanceled" => quant_common::base::OrderStatus::Canceled,
            _ => quant_common::base::OrderStatus::default(), //todo update unknown type
        };

        quant_common::base::Order {
            id: self.order.oid.to_string(),
            cid: None,
            timestamp: self.order.timestamp as i64,
            status,
            symbol,
            order_type,
            side,
            pos_side: Some(pos_side),
            time_in_force,
            price: Some(self.order.limit_px),
            amount: Some(self.order.orig_sz),
            quote_amount: None,
            filled: self.order.sz,
            filled_avg_price: self.order.trigger_px,
        }
    }
}

#[derive(serde::Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct L2SnapshotResponse {
    pub coin: String,
    pub levels: Vec<Vec<Level>>,
    pub time: u64,
}
#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Level {
    pub n: u64,
    pub px: String,
    pub sz: String,
}
impl From<Level> for quant_common::base::DepthEntry {
    fn from(level: Level) -> Self {
        Self {
            price: level.px.parse::<f64>().unwrap_or(0.),
            amount: level.sz.parse::<f64>().unwrap_or(0.),
        }
    }
}

#[derive(serde::Deserialize, Debug)]
pub struct CandlesSnapshotResponse {
    #[serde(rename = "t")]
    pub time_open: u64,
    #[serde(rename = "T")]
    pub time_close: u64,
    #[serde(rename = "s")]
    pub coin: String,
    #[serde(rename = "i")]
    pub candle_interval: String,
    #[serde(rename = "o")]
    pub open: String,
    #[serde(rename = "c")]
    pub close: String,
    #[serde(rename = "h")]
    pub high: String,
    #[serde(rename = "l")]
    pub low: String,
    #[serde(rename = "v")]
    pub vlm: String,
    #[serde(rename = "n")]
    pub num_trades: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct LocalDepth {
    pub depth: Depth,
}

#[derive(Debug, Serialize, Deserialize)]
pub(crate) struct Subscribe<T> {
    pub method: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub subscription: Option<T>,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(tag = "type")]
#[serde(rename_all = "camelCase")]
pub enum Subscription {
    AllMids,
    Notification { user: H160 },
    WebData2 { user: H160 },
    Candle { coin: String, interval: String },
    L2Book { coin: String },
    Trades { coin: String },
    OrderUpdates { user: H160 },
    UserEvents { user: H160 },
    UserFills { user: H160 },
    UserFundings { user: H160 },
    UserNonFundingLedgerUpdates { user: H160 },
    ActiveAssetCtx { coin: String },
    ActiveAssetData { user: H160, coin: String }, //only supports Perps
    UserTwapSliceFills { user: H160 },
    UserTwapHistory { user: H160 },
}
#[derive(Deserialize, Debug)]
#[serde(tag = "channel")]
#[serde(rename_all = "camelCase")]
pub enum SubscriptionEvent {
    Pong,
    Error,
    SubscriptionResponse,
    Trades(Trades),
    L2Book(L2Book),
    OrderUpdates(OrderUpdates),
    UserFills(UserFills),
    UserFundings(UserFundings),
    ActiveSpotAssetCtx(ActiveAssetCtx),
    ActiveAssetCtx(ActiveAssetCtx),
    // ActiveAssetData(ActiveAssetCtx),
    WebData2(WebData2),
}
#[derive(Deserialize, Clone, Debug)]
pub struct WebData2 {
    pub data: WebData2Data,
}

#[derive(Deserialize, Clone, Debug)]
pub struct ActiveAssetCtx {
    pub data: ActiveAssetCtxData,
}

#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct ActiveAssetCtxData {
    pub coin: String,
    pub ctx: AssetCtx,
}
#[derive(Deserialize, Serialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
#[serde(untagged)]
pub enum AssetCtx {
    Perps(PerpsAssetCtx),
    Spot(SpotAssetCtx),
}

#[derive(Deserialize, Serialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct PerpsAssetCtx {
    #[serde(flatten)]
    pub shared: SharedAssetCtx,
    #[serde(deserialize_with = "de_from_str")]
    pub funding: f64,
    pub open_interest: String,
    pub oracle_px: String,
}
#[derive(Deserialize, Serialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct SpotAssetCtx {
    #[serde(flatten)]
    pub shared: SharedAssetCtx,
    pub circulating_supply: String,
    pub total_supply: String,
    pub day_base_vlm: String,
}
#[derive(Deserialize, Serialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct SharedAssetCtx {
    #[serde(deserialize_with = "de_from_str")]
    pub day_ntl_vlm: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub prev_day_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub mark_px: f64,
    pub mid_px: Option<String>,
}
#[derive(Deserialize, Clone, Debug)]
pub struct UserFundings {
    pub data: UserFundingsData,
}
#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct UserFundingsData {
    pub is_snapshot: Option<bool>,
    pub user: H160,
    pub fundings: Vec<UserFunding>,
}

#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct UserFunding {
    pub time: u64,
    pub coin: String,
    #[serde(deserialize_with = "de_from_str")]
    pub usdc: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub szi: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub funding_rate: f64,
}

// #[derive(Deserialize, Clone, Debug)]
// pub struct User {
//     pub data: UserData,
// }
// #[derive(Deserialize, Clone, Debug)]
// #[serde(rename_all = "camelCase")]
// pub enum UserData {
//     Fills(Vec<TradeInfo>),
//     Funding(UserFunding),
//     Liquidation(LiquidationInfo),
//     NonUserCancel(Vec<NonUserCancel>),
// }
// #[derive(Deserialize, Clone, Debug)]
// pub struct LiquidationInfo {
//     pub lid: u64,
//     pub liquidator: String,
//     pub liquidated_user: String,
//     pub liquidated_ntl_pos: String,
//     pub liquidated_account_value: String,
// }
// #[derive(Deserialize, Clone, Debug)]
// pub struct NonUserCancel {
//     pub coin: String,
//     pub oid: u64,
// }

#[derive(Deserialize, Clone, Debug)]
pub struct UserFills {
    pub data: UserFillsData,
}
#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct UserFillsData {
    pub is_snapshot: Option<bool>,
    pub user: H160,
    pub fills: Vec<TradeInfo>,
}
#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct TradeInfo {
    pub coin: String,
    pub side: String,
    #[serde(deserialize_with = "de_from_str")]
    pub px: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub sz: f64,
    pub time: u64,
    pub hash: String,
    pub start_position: String,
    pub dir: String,
    pub closed_pnl: String,
    pub oid: u64,
    pub cloid: Option<String>,
    pub crossed: bool,
    #[serde(deserialize_with = "de_from_str")]
    pub fee: f64,
    pub tid: u64,
}

#[derive(Deserialize, Clone, Debug)]
pub struct OrderUpdates {
    pub data: Vec<OrderUpdate>,
}

#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct OrderUpdate {
    pub order: BasicOrder,
    pub status: String,
    // pub status_timestamp: u64,
}

#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BasicOrder {
    pub coin: String,
    pub side: String,
    #[serde(deserialize_with = "de_from_str")]
    pub limit_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub sz: f64,
    pub oid: u64,
    pub timestamp: u64,
    #[serde(deserialize_with = "de_from_str")]
    pub orig_sz: f64,
    pub cloid: Option<String>,
}

impl OrderUpdate {
    pub fn to_order(&self, symbol: quant_common::base::Symbol) -> quant_common::base::Order {
        //B = Bid = Buy, A = Ask = Short. Side is aggressing side for trades.
        let (side, pos_side) = match self.order.side.as_str() {
            "B" => (
                quant_common::base::OrderSide::Buy,
                quant_common::base::PosSide::Long,
            ),
            "A" => (
                quant_common::base::OrderSide::Sell,
                quant_common::base::PosSide::Short,
            ),
            _ => (
                quant_common::base::OrderSide::default(),
                quant_common::base::PosSide::default(),
            ), //todo update unknown type
        };
        //"filled" | "open" | "canceled" | "triggered" | "rejected" | "marginCanceled",
        let status = match self.status.as_str() {
            "filled" => quant_common::base::OrderStatus::Filled,
            "open" => quant_common::base::OrderStatus::Open,
            "canceled" => quant_common::base::OrderStatus::Canceled,
            "triggered" => quant_common::base::OrderStatus::Filled,
            "rejected" => quant_common::base::OrderStatus::Canceled,
            "marginCanceled" => quant_common::base::OrderStatus::Canceled,
            _ => quant_common::base::OrderStatus::Open, //todo update
        };
        let mut time_in_force = quant_common::base::TimeInForce::FOK;
        if let Some(cid) = self.order.cloid.clone() {
            if let Ok(order_time_in_force) = get_order_type(cid.as_str()) {
                info!("order_time_in_force:{:?}", order_time_in_force);
                time_in_force = match order_time_in_force {
                    super::action::Order::Limit(limit) => match limit.tif.as_str() {
                        "Gtc" => quant_common::base::TimeInForce::GTC,
                        "Ioc" => quant_common::base::TimeInForce::IOC,
                        "Alo" => quant_common::base::TimeInForce::PostOnly,
                        _ => quant_common::base::TimeInForce::FOK,
                    },
                    super::action::Order::Trigger(trigger) => match trigger.tpsl.as_str() {
                        "tp" | "sl" => quant_common::base::TimeInForce::IOC,
                        _ => quant_common::base::TimeInForce::FOK,
                    },
                }
            };
        }
        let filled = 0.0;
        let filled_avg_price = 0.0;
        // if status == quant_common::base::OrderStatus::Filled {
        //     filled = self.order.orig_sz;
        //     filled_avg_price = self.order.limit_px;
        // }
        quant_common::base::Order {
            id: self.order.oid.to_string(),
            cid: self.order.cloid.clone(),
            timestamp: self.order.timestamp as i64,
            status,
            symbol,
            order_type: quant_common::base::OrderType::Limit,
            side,
            pos_side: Some(pos_side),
            time_in_force,
            price: Some(self.order.limit_px),
            amount: Some(self.order.orig_sz),
            filled,
            filled_avg_price,
            quote_amount: None,
        }
    }
}

#[derive(Deserialize, Clone, Debug)]
pub(crate) struct Trades {
    pub data: Vec<Trade>,
}
#[derive(Deserialize, Clone, Debug)]
pub(crate) struct Trade {
    pub coin: String,
    pub side: String,
    pub px: String,
    pub sz: String,
    pub time: u64,
    pub hash: String,
    pub tid: u64,
}

// impl From<Trade> for quant_common::base::Trade {
//     fn from(trade: Trade) -> Self {
//         Self {
//             symbol: trade.coin,
//             price: trade.px.parse::<f64>().unwrap_or(0.),
//             amount: trade.sz.parse::<f64>().unwrap_or(0.),
//             timestamp: trade.time,
//             id: trade.tid,
//             side: trade.side,
//         }
//     }
// }

#[derive(Deserialize, Clone, Debug)]
pub(crate) struct L2Book {
    pub data: L2BookData,
}
#[derive(Deserialize, Clone, Debug)]
pub(crate) struct L2BookData {
    pub coin: String,
    pub time: u64,
    pub levels: Vec<Vec<BookLevel>>,
}
#[derive(Deserialize, Clone, Debug)]
pub(crate) struct BookLevel {
    #[serde(deserialize_with = "de_from_str")]
    pub px: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub sz: f64,
    // pub n: u64,
}

pub(crate) trait AssetInfoFun {
    fn get_spot(&self, symbol: &Symbol) -> Option<String>;
    fn get_swap(&self, symbol: &Symbol) -> Option<String>;
    fn from_spot_to_symbol(&self, name: String) -> Option<Symbol>;
    fn from_swap_to_symbol(&self, name: String) -> Option<Symbol>;
    fn from_spot_index(&self, key: (usize, usize)) -> Option<Symbol>;
}
#[derive(Debug, Serialize, Deserialize, Default, Clone)]
pub(crate) struct AssetInfo {
    pub tokens: FxHashMap<String, SpotToken>, // BTC  SpotToken
    pub token_name: FxHashMap<(usize, usize), String>, // [1,0]  @142
    pub name_token: FxHashMap<String, (usize, usize)>, //@142 [1,0]
    pub ind_token: FxHashMap<usize, String>,  // 0  BTC
    pub token_ind: FxHashMap<String, usize>,  // BTC 0
    pub token_swap_symbol: FxHashMap<String, Symbol>, // BTC-USD  Symbol
    pub swap_symbol_token: FxHashMap<Symbol, String>, // Symbol  BTC-USD
}
impl AssetInfoFun for AssetInfo {
    /// 获取交易所需要的名称
    fn get_spot(&self, symbol: &Symbol) -> Option<String> {
        match self.tokens.get(symbol.base.as_str()) {
            Some(info) => {
                let base_index = info.index;
                match self.tokens.get(symbol.quote.to_string().as_str()) {
                    Some(info2) => {
                        let quote_index = info2.index;
                        let key = (base_index, quote_index);
                        if let Some(name) = self.token_name.get(&key) {
                            return Some(name.to_string());
                        }
                    }
                    None => return None,
                }
            }
            None => return None,
        }
        None
    }
    fn from_spot_to_symbol(&self, name: String) -> Option<Symbol> {
        match self.name_token.get(&name) {
            Some(key) => {
                let base_index = key.0;
                // let quote_index = key.1;
                let base_token = self.ind_token.get(&base_index)?;
                Some(Symbol::new_with_quote(base_token.as_str(), QuoteCcy::USDC))
            }
            None => None,
        }
    }
    fn from_spot_index(&self, key: (usize, usize)) -> Option<Symbol> {
        let base_index = key.0;
        let quote_index = key.1;
        let base_token = self.ind_token.get(&base_index)?;
        let _quote_token = self.ind_token.get(&quote_index)?;
        Some(Symbol::new_with_quote(base_token.as_str(), QuoteCcy::USDC))
    }
    fn from_swap_to_symbol(&self, name: String) -> Option<Symbol> {
        self.token_swap_symbol.get(&name).cloned()
    }
    fn get_swap(&self, symbol: &Symbol) -> Option<String> {
        self.swap_symbol_token.get(symbol).cloned()
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct WsRequest<T> {
    pub method: String, //"post",
    pub id: u64,        // <number>,
    pub request: Request<T>,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Request<T> {
    pub r#type: String, //"info" | "action",
    pub payload: T,     //{ ... }
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct WsResponse<T> {
    pub channel: String, //"post",
    pub data: Option<Data<T>>,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Data<T> {
    pub id: u64, // <number>,
    pub response: Response<T>,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Response<T> {
    pub r#type: String, //"info" | "action" | "error",
    pub payload: T,     //{ ... }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Payload {
    r#type: String, //"info" | "action" | "error",
    payload: Status,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Status {
    pub response: DataType,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct DataType {
    pub data: Statuses,
    pub r#type: String,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Statuses {
    pub statuses: Vec<Statuse>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) enum Statuse {
    Resting(Resting),
    Error(String),
    Success,
    Filled(Filled),
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Filled {
    pub oid: u64,
    pub cloid: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub total_sz: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub avg_px: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Resting {
    pub oid: u64,
    pub cloid: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct UniverseInfo2 {
    pub name: String,
    pub sz_decimals: u32,
    pub max_leverage: u32,
    #[serde(default)]
    pub only_isolated: bool,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MarketData2 {
    #[serde(deserialize_with = "de_from_str")]
    pub day_ntl_vlm: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub funding: f64,
    // pub impact_pxs: Vec<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub mark_px: f64,
    // pub mid_px: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub open_interest: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub oracle_px: f64,
    // pub premium: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub prev_day_px: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MarginTable {
    pub max_leverage: u32,
    pub description: String,
    pub margin_tiers: Vec<MarginTier>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MarginTier {
    #[serde(deserialize_with = "de_from_str")]
    pub lower_bound: f64,
    pub max_leverage: u32,
}

// impl MarketResponse {
//     pub fn from_json(json_str: &str) -> quant_common::Result<Self> {
//         let data: Vec<serde_json::Value> = serde_json::from_str(json_str)?;
//         if data.len() != 2 {
//             return Err(qerror!("invalid market response data len"));
//         }

//         let universe_response: UniverseResponse = serde_json::from_value(data[0].clone())?;
//         let market_data: Vec<MarketData> = serde_json::from_value(data[1].clone())?;

//         Ok(Self {
//             universe: universe_response.universe.get("universe"),
//             market_data,
//         })
//     }
// }

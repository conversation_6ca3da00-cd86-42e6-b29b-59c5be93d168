use serde::{Deserialize, Serialize};

#[derive(Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct WebData2Data {
    pub clearinghouse_state: ClearinghouseState,
    pub leading_vaults: Vec<LeadingVault>,
    pub total_vault_equity: String,
    pub open_orders: Vec<OpenOrder>,
    pub agent_address: String,
    pub agent_valid_until: u64,
    pub cum_ledger: String,
    pub meta: Meta,
    pub server_time: u64,
    pub is_vault: bool,
    pub user: String,
    pub twap_states: Vec<TwapState>,
    pub spot_state: SpotState,
    pub spot_asset_ctxs: Vec<SpotAssetCtx>,
    pub perps_at_open_interest_cap: Vec<String>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct ClearinghouseState {
    pub margin_summary: MarginSummary,
    pub cross_margin_summary: CrossMarginSummary,
    pub cross_maintenance_margin_used: String,
    pub withdrawable: String,
    pub asset_positions: Vec<AssetPosition>,
    pub time: u64,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct MarginSummary {
    pub account_value: String,
    pub total_ntl_pos: String,
    pub total_raw_usd: String,
    pub total_margin_used: String,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct CrossMarginSummary {
    pub account_value: String,
    pub total_ntl_pos: String,
    pub total_raw_usd: String,
    pub total_margin_used: String,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AssetPosition {
    pub r#type: String,
    pub position: Position,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Position {
    pub coin: String,
    pub szi: String,
    pub leverage: Leverage,
    pub entry_px: String,
    pub position_value: String,
    pub unrealized_pnl: String,
    pub return_on_equity: String,
    pub liquidation_px: Option<String>,
    pub margin_used: String,
    pub max_leverage: u64,
    pub cum_funding: CumFunding,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct Leverage {
    pub r#type: String,
    pub value: u64,
    pub raw_usd: Option<String>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct CumFunding {
    pub all_time: String,
    pub since_open: String,
    pub since_change: String,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LeadingVault {}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct OpenOrder {}
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct Meta {
    pub universe: Vec<UniverseEntry>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct UniverseEntry {
    pub sz_decimals: u8,
    pub name: String,
    pub max_leverage: u64,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct SpotState {
    pub balances: Vec<Balance>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct Balance {
    pub coin: String,
    pub token: u64,
    pub total: String,
    pub hold: String,
    pub entry_ntl: String,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct SpotAssetCtx {
    pub prev_day_px: String,
    pub day_ntl_vlm: String,
    pub mark_px: String,
    pub mid_px: Option<String>,
    pub circulating_supply: String,
    pub coin: String,
    pub total_supply: String,
    pub day_base_vlm: String,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct TwapState {}

use std::collections::HashMap;

use ethers::types::{Signature, H160, H256};
use log::info;
use quant_common::decimal_truncate;
use quant_common::utils::de_from_str;
use quant_common::{base::Symbol, qerror, time_ms, Error};
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};
use sonic_rs::Value;

use crate::util::{float_to_string_for_hashing, truncate_float, validate_price};

use super::model::{UsdClassTransfer, UsdSend};

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(tag = "type")]
#[serde(rename_all = "camelCase")]
pub enum Actions {
    UsdSend(UsdSend),
    Order(BulkOrder),
    Cancel(BulkCancel),
    CancelByCloid(BulkCancelCloid),
    BatchModify(BulkModify),
    UpdateLeverage(UpdateLeverage),
    UsdClassTransfer(UsdClassTransfer),
}

impl Actions {
    pub fn hash(&self, timestamp: u64, vault_address: Option<H160>) -> Result<H256, Error> {
        let mut bytes = rmp_serde::to_vec_named(self)
            .map_err(|e| qerror!("hyperliquid action hash error: {}", e))?;
        bytes.extend(timestamp.to_be_bytes());
        if let Some(vault_address) = vault_address {
            bytes.push(1);
            bytes.extend(vault_address.to_fixed_bytes());
        } else {
            bytes.push(0);
        }
        Ok(H256(ethers::utils::keccak256(bytes)))
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct ExchangePayload {
    pub action: sonic_rs::Value,
    pub nonce: u64,
    pub signature: Signature,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub is_frontend: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vault_address: Option<H160>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub expires_after: Option<u64>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(tag = "type")]
#[serde(rename_all = "camelCase")]
pub enum InfoRequest {
    #[serde(rename = "clearinghouseState")]
    UserState {
        user: H160,
    },
    #[serde(rename = "batchClearinghouseStates")]
    UserStates {
        users: Vec<H160>,
    },
    #[serde(rename = "spotClearinghouseState")]
    UserTokenBalances {
        user: H160,
    },
    #[serde(rename = "frontendOpenOrders")]
    FrontendOpenOrders {
        user: H160,
    },
    UserFees {
        user: H160,
    },
    OpenOrders {
        user: H160,
    },
    OrderStatus {
        user: H160,
        oid: u64,
    },
    Meta,
    SpotMeta,
    SpotMetaAndAssetCtxs,
    MetaAndAssetCtxs,
    AllMids,
    UserFills {
        user: H160,
    },
    #[serde(rename_all = "camelCase")]
    FundingHistory {
        coin: String,
        start_time: u64,
        end_time: Option<u64>,
    },
    #[serde(rename_all = "camelCase")]
    UserFunding {
        user: H160,
        start_time: u64,
        end_time: Option<u64>,
    },
    L2Book {
        coin: String,
    },
    RecentTrades {
        coin: String,
    },
    #[serde(rename_all = "camelCase")]
    CandleSnapshot {
        req: CandleSnapshotRequest,
    },
    Referral {
        user: H160,
    },
    HistoricalOrders {
        user: H160,
    },
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct CandleSnapshotRequest {
    pub coin: String,
    pub interval: String,
    pub start_time: u64,
    pub end_time: u64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct UserBalances {
    pub balances: Vec<TokenBalance>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct TokenBalance {
    pub coin: String,
    pub token: i64,
    #[serde(deserialize_with = "de_from_str")]
    pub hold: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub total: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub entry_ntl: f64,
}

impl From<TokenBalance> for quant_common::base::Balance {
    fn from(value: TokenBalance) -> Self {
        Self {
            asset: value.coin,
            balance: value.total,
            available_balance: value.total - value.hold,
            unrealized_pnl: value.entry_ntl,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct FrontendOpenOrdersResp {
    // B = Bid = Buy, A = Ask = Short. Side is aggressing side for trades.
    pub coin: String,
    pub is_position_tpsl: bool,
    pub is_trigger: bool,
    #[serde(deserialize_with = "de_from_str")]
    pub limit_px: f64,
    pub oid: u64,
    pub cloid: Option<String>,
    pub order_type: String,
    #[serde(deserialize_with = "de_from_str")]
    pub orig_sz: f64,
    pub reduce_only: bool,
    pub side: String,
    #[serde(deserialize_with = "de_from_str")]
    pub sz: f64,
    pub timestamp: u64,
    pub trigger_condition: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub trigger_px: f64,
    pub children: Vec<Option<FrontendOpenOrdersResp>>,
    pub tif: Option<String>,
}
impl FrontendOpenOrdersResp {
    pub fn to_order(&self, symbol: quant_common::base::Symbol) -> quant_common::base::Order {
        //B = Bid = Buy, A = Ask = Short. Side is aggressing side for trades.
        let (side, pos_side) = match self.side.as_str() {
            "B" => (
                quant_common::base::OrderSide::Buy,
                quant_common::base::PosSide::Long,
            ),
            "A" => (
                quant_common::base::OrderSide::Sell,
                quant_common::base::PosSide::Short,
            ),
            _ => (
                quant_common::base::OrderSide::default(),
                quant_common::base::PosSide::default(),
            ), //todo update unknown type
        };
        let order_type = match self.order_type.as_str() {
            "Market" => quant_common::base::OrderType::Market,
            "Limit" => quant_common::base::OrderType::Limit,
            "StopMarket" => quant_common::base::OrderType::Market,
            "StopLimit" => quant_common::base::OrderType::Limit,
            _ => quant_common::base::OrderType::default(), //todo update unknown type
        };
        let time_in_force = match self.tif.as_deref() {
            Some("Gtc") => quant_common::base::TimeInForce::GTC,
            Some("Ioc") => quant_common::base::TimeInForce::IOC,
            Some("Alo") => quant_common::base::TimeInForce::PostOnly,
            _ => quant_common::base::TimeInForce::default(), //todo update unknown type
        };

        quant_common::base::Order {
            id: self.oid.to_string(),
            cid: self.cloid.clone(),
            timestamp: self.timestamp as i64,
            status: quant_common::base::OrderStatus::Open,
            symbol,
            order_type,
            side,
            pos_side: Some(pos_side),
            time_in_force,
            price: Some(self.limit_px),
            amount: Some(self.orig_sz),
            quote_amount: None,
            filled: 0.,
            filled_avg_price: 0.,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename = "CamelCase")]
pub(crate) enum OrderType {
    Market,
    Limit,
    StopMarket,
    StopLimit,
    Scale,
    Twap,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BulkOrder {
    pub orders: Vec<OrderRequest>,
    pub grouping: String,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub builder: Option<BuilderInfo>,
}
#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct OrderRequest {
    #[serde(rename = "a", alias = "asset")]
    pub asset: usize,
    #[serde(rename = "b", alias = "isBuy")]
    pub is_buy: bool,
    #[serde(rename = "p", alias = "limitPx")]
    pub limit_px: String,
    #[serde(rename = "s", alias = "sz")]
    pub sz: String,
    #[serde(rename = "r", alias = "reduceOnly", default)]
    pub reduce_only: bool,
    #[serde(rename = "t", alias = "orderType")]
    pub order_type: Order,
    #[serde(rename = "c", alias = "cloid", skip_serializing_if = "Option::is_none")]
    pub cloid: Option<String>,
}
#[derive(Default, Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BuilderInfo {
    #[serde(rename = "b")]
    pub builder: String,
    #[serde(rename = "f")]
    pub fee: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ClientOrder {
    Limit(ClientLimit),
    Trigger(ClientTrigger),
}
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientLimit {
    pub tif: String,
}
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientTrigger {
    pub is_market: bool,
    pub trigger_px: f64,
    pub tpsl: String,
}
#[derive(Debug, Serialize, Deserialize)]
pub struct ClientOrderRequest {
    pub asset: String,
    pub is_buy: bool,
    pub reduce_only: bool,
    pub limit_px: f64,
    pub sz: f64,
    pub cloid: Option<String>,
    pub order_type: ClientOrder,
}
#[derive(Deserialize, Serialize, Clone, Debug)]
pub struct Limit {
    pub tif: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Trigger {
    pub is_market: bool,
    pub trigger_px: String,
    pub tpsl: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub enum Order {
    Limit(Limit),
    Trigger(Trigger),
}

// use std::sync::LazyLock;
// pub(crate) static CLIENT_ORDER_REQUEST_TYPE: LazyLock<ClientOrderResquestTypeCache> = LazyLock::new(|| {
//    ClientOrderResquestTypeCache{cache:HashMap::new()}
// });
pub(crate) struct ClientOrderRequestTypeCache {
    pub cache: HashMap<String, Order>,
}
use once_cell::sync::OnceCell;
use std::sync::RwLock;
pub static CLIENT_ORDER_REQUEST_TYPE: OnceCell<RwLock<ClientOrderRequestTypeCache>> =
    OnceCell::new();
// 读取函数
pub(crate) fn get_order_type(cloid: &str) -> quant_common::Result<Order> {
    // 获取 OnceCell 的内容
    let cache = match CLIENT_ORDER_REQUEST_TYPE.get() {
        Some(cache) => cache,                                           // 已初始化
        None => return Err(qerror!("client order type cache is none")), // 未初始化，返回 None
    };
    // 获取读锁
    let locked_cache = cache
        .read()
        .map_err(|_| qerror!("Failed to acquire read lock"))?;

    // 从 HashMap 中查找值
    let order_type = locked_cache.cache.get(cloid).cloned(); // cloned() 返回值的副本
    if let Some(order_type) = order_type {
        Ok(order_type)
    } else {
        Err(qerror!("{}not find order type in cache", cloid))
    }
}

impl ClientOrderRequest {
    ///参数 market_type对应合约和现货的值:
    ///
    /// Prices can have up to 5 significant figures, but no more than MAX_DECIMALS - szDecimals decimal places where MAX_DECIMALS is 6 for perps and 8 for spot. Integer prices are always allowed, regardless of the number of significant figures. E.g. 123456.0 is a valid price even though 12345.6 is not.
    /// 参考:`https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/tick-and-lot-size`
    pub(crate) fn convert(
        self,
        coin_to_asset: &FxHashMap<String, usize>,
        sz_decimals: u32,
        market_type: u32,
    ) -> quant_common::Result<OrderRequest> {
        let order_type = match self.order_type {
            ClientOrder::Limit(limit) => Order::Limit(Limit { tif: limit.tif }),
            ClientOrder::Trigger(trigger) => Order::Trigger(Trigger {
                trigger_px: float_to_string_for_hashing(
                    trigger.trigger_px,
                    (market_type - sz_decimals) as u8,
                ),
                is_market: trigger.is_market,
                tpsl: trigger.tpsl,
            }),
        };
        let &asset = coin_to_asset
            .get(&self.asset)
            .ok_or(qerror!("AssetNotFound"))?;

        let cloid = self.cloid;
        if let Some(cloid) = cloid.clone() {
            // 初始化或获取缓存
            let cache = CLIENT_ORDER_REQUEST_TYPE.get_or_init(|| {
                RwLock::new(ClientOrderRequestTypeCache {
                    cache: HashMap::new(),
                })
            });
            // 写入缓存
            let mut locked_cache = cache
                .write()
                .map_err(|_| qerror!("Failed to acquire write lock"))?;
            locked_cache.cache.insert(cloid, order_type.clone());
        }

        let limit_px =
            match validate_price(self.limit_px, sz_decimals as usize, market_type as usize, 5) {
                Ok(px) => px,
                Err(e) => return Err(qerror!("{}", e)),
            };
        let limit_px = float_to_string_for_hashing(limit_px, (market_type - sz_decimals) as u8);
        let sz = decimal_truncate(self.sz, sz_decimals as i32).to_string();
        Ok(OrderRequest {
            asset,
            is_buy: self.is_buy,
            reduce_only: self.reduce_only,
            limit_px,
            sz,
            order_type,
            cloid,
        })
    }
}

impl ClientOrderRequest {
    pub fn from_order(
        order: quant_common::base::Order,
        params: quant_common::base::OrderParams,
    ) -> Vec<Self> {
        let mut orders = Vec::new();
        let is_buy = match order.side {
            quant_common::base::OrderSide::Buy => true,
            quant_common::base::OrderSide::Sell => false,
        };

        let mut order_type = match order.order_type {
            quant_common::base::OrderType::Limit => match order.time_in_force {
                quant_common::base::TimeInForce::GTC => ClientOrder::Limit(ClientLimit {
                    tif: "Gtc".to_string(),
                }),
                quant_common::base::TimeInForce::IOC => ClientOrder::Limit(ClientLimit {
                    tif: "Ioc".to_string(),
                }),
                quant_common::base::TimeInForce::FOK => ClientOrder::Limit(ClientLimit {
                    tif: "Ioc".to_string(),
                }),
                quant_common::base::TimeInForce::PostOnly => ClientOrder::Limit(ClientLimit {
                    tif: "Alo".to_string(),
                }),
            },
            // quant_common::base::OrderType::Market => ClientOrder::Trigger(ClientTrigger{
            //     is_market:true,
            //     trigger_px: order.price.unwrap_or(0.),
            //     tpsl: "sl".to_string(),
            // }), //现货不支持这个订单类型
            quant_common::base::OrderType::Market => ClientOrder::Limit(ClientLimit {
                tif: "FrontendMarket".to_string(), //FrontendMarket
            }),
            //真的没有市价单
            // _ => unimplemented!("not support order type: {:?}",order.order_type),
        };
        orders.push(Self {
            asset: order.symbol.base.clone(),
            is_buy,
            reduce_only: order.reduce_only(),
            limit_px: order.price.unwrap_or(0.),
            sz: order.amount.unwrap_or(0.0),
            cloid: order.cid.clone(),
            order_type: order_type.clone(),
        });
        if let Some(stop_loss) = params.stop_loss {
            let mut float_price = order.price.unwrap_or(0.);
            match stop_loss.trigger_price {
                quant_common::base::TriggerPrice::MarkPrice(price) => {
                    float_price = price;
                    order_type = ClientOrder::Trigger(ClientTrigger {
                        is_market: true,
                        trigger_px: price,
                        tpsl: "sl".to_string(),
                    })
                }
                quant_common::base::TriggerPrice::ContractPrice(price) => {
                    float_price = price;
                    order_type = ClientOrder::Trigger(ClientTrigger {
                        is_market: false,
                        trigger_px: price,
                        tpsl: "sl".to_string(),
                    })
                }
            }
            let mut item = Self {
                asset: order.symbol.base.clone(),
                is_buy: !is_buy,
                reduce_only: true,
                limit_px: order.price.unwrap_or(0.),
                sz: order.amount.unwrap_or(0.0),
                cloid: order.cid.clone(),
                order_type: order_type.clone(),
            };
            if !item.is_buy {
                item.limit_px = float_price * 0.92; //开空仓的时候是0.92，空仓的时候是1.08
            } else {
                item.limit_px = float_price * 1.08; //开多仓的时候是0.92，空仓的时候是1.08
            }
            orders.push(item);
        }
        if let Some(take_profit) = params.take_profit {
            let mut float_price = order.price.unwrap_or(0.);
            match take_profit.trigger_price {
                quant_common::base::TriggerPrice::MarkPrice(price) => {
                    float_price = price;
                    order_type = ClientOrder::Trigger(ClientTrigger {
                        is_market: true,
                        trigger_px: price,
                        tpsl: "tp".to_string(),
                    })
                }
                quant_common::base::TriggerPrice::ContractPrice(price) => {
                    float_price = price;
                    order_type = ClientOrder::Trigger(ClientTrigger {
                        is_market: false,
                        trigger_px: price,
                        tpsl: "tp".to_string(),
                    })
                }
            }
            let mut item = Self {
                asset: order.symbol.base.clone(),
                is_buy: !is_buy,
                reduce_only: true,
                limit_px: order.price.unwrap_or(0.),
                sz: order.amount.unwrap_or(0.0),
                cloid: order.cid.clone(),
                order_type: order_type.clone(),
            };
            if !item.is_buy {
                item.limit_px = float_price * 0.92; //开空仓的时候是0.92，空仓的时候是1.08
            } else {
                item.limit_px = float_price * 1.08; //开多仓的时候是0.92，空仓的时候是1.08
            }
            orders.push(item);
        }
        orders
    }
}

impl From<quant_common::base::Order> for ClientOrderRequest {
    fn from(order: quant_common::base::Order) -> Self {
        let is_buy = match order.side {
            quant_common::base::OrderSide::Buy => true,
            quant_common::base::OrderSide::Sell => false,
        };
        let order_type = match order.order_type {
            quant_common::base::OrderType::Limit => match order.time_in_force {
                quant_common::base::TimeInForce::GTC => ClientOrder::Limit(ClientLimit {
                    tif: "Gtc".to_string(),
                }),
                quant_common::base::TimeInForce::IOC => ClientOrder::Limit(ClientLimit {
                    tif: "Ioc".to_string(),
                }),
                quant_common::base::TimeInForce::FOK => ClientOrder::Limit(ClientLimit {
                    tif: "Ioc".to_string(),
                }),
                quant_common::base::TimeInForce::PostOnly => ClientOrder::Limit(ClientLimit {
                    tif: "Alo".to_string(),
                }),
            },
            // quant_common::base::OrderType::Market => ClientOrder::Trigger(ClientTrigger{
            //     is_market:true,
            //     trigger_px: order.price.unwrap_or(0.),
            //     tpsl: "sl".to_string(),
            // }), //现货不支持这个订单类型
            quant_common::base::OrderType::Market => ClientOrder::Limit(ClientLimit {
                tif: "FrontendMarket".to_string(), //FrontendMarket
            }),
            //真的没有市价单
            // _ => unimplemented!("not support order type: {:?}",order.order_type),
        };
        Self {
            asset: order.symbol.base.clone(),
            is_buy,
            reduce_only: order.reduce_only(),
            limit_px: order.price.unwrap_or(0.),
            sz: order.amount.unwrap_or(0.0),
            cloid: order.cid,
            order_type,
        }
    }
}

#[derive(Deserialize, Debug, Clone)]
pub struct RestingOrder {
    pub oid: u64,
    pub cloid: Option<String>,
}

#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct FilledOrder {
    pub total_sz: String,
    pub avg_px: String,
    pub oid: u64,
    pub cloid: Option<String>,
}

#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub enum ExchangeDataStatus {
    Success,
    WaitingForFill,
    WaitingForTrigger,
    Error(String),
    Resting(RestingOrder),
    Filled(FilledOrder),
}

#[derive(Deserialize, Debug, Clone)]
pub struct ExchangeDataStatuses {
    pub statuses: Vec<ExchangeDataStatus>,
}

#[derive(Deserialize, Debug, Clone)]
pub struct ExchangeResponse {
    #[serde(rename = "type")]
    pub r#type: String,
    pub data: Option<ExchangeDataStatuses>,
}

#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
#[serde(tag = "status", content = "response")]
pub enum ExchangeResponseStatus {
    Ok(ExchangeResponse),
    Err(String),
}

// #[derive(Debug)]
// pub struct ClientCancelRequestCloid {
//     pub asset: String,
//     pub cloid: String,
// }

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BulkCancelCloid {
    pub cancels: Vec<CancelRequestCloid>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CancelRequestCloid {
    pub asset: u32,
    pub cloid: String,
}
// #[derive(Debug)]
// pub struct ClientCancelRequest {
//     pub asset: String,
//     pub oid: u64,
// }

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CancelRequest {
    #[serde(rename = "a", alias = "asset")]
    pub asset: u32,
    #[serde(rename = "o", alias = "oid")]
    pub oid: u64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BulkCancel {
    pub cancels: Vec<CancelRequest>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BulkModify {
    pub modifies: Vec<ModifyRequest>,
}

// #[derive(Debug)]
// pub struct ClientModifyRequest {
//     pub oid: u64,
//     pub order: ClientOrderRequest,
// }

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ModifyRequest {
    pub oid: u64,
    pub order: OrderRequest,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct UpdateLeverage {
    pub asset: u32,
    pub is_cross: bool,
    pub leverage: u32,
}

#[derive(serde::Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct UserFillsResponse {
    pub closed_pnl: String,
    pub coin: String,
    pub crossed: bool,
    pub dir: String,
    pub hash: String,
    pub oid: u64,
    pub px: String,
    pub side: String,
    pub start_position: String,
    pub sz: String,
    pub time: u64,
    pub fee: String,
    pub fee_token: String,
    pub builder_fee: Option<String>,
    pub tid: u64,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct UserStateResponse {
    pub asset_positions: Vec<AssetPosition>,
    // pub cross_maintenance_margin_used: String,
    // pub cross_margin_summary: MarginSummary,
    pub margin_summary: MarginSummary,
    // pub withdrawable: String,
}
#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct PositionData {
    pub coin: String,
    pub entry_px: Option<String>,
    pub leverage: Leverage,
    pub liquidation_px: Option<String>,
    pub margin_used: String,
    pub position_value: String,
    pub return_on_equity: String,
    pub szi: String,
    pub unrealized_pnl: String,
    pub max_leverage: u32,
    pub cum_funding: CumulativeFunding,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct Leverage {
    #[serde(rename = "type")]
    pub type_string: String,
    pub value: u32,
    pub raw_usd: Option<String>,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct CumulativeFunding {
    pub all_time: String,
    pub since_open: String,
    pub since_change: String,
}

#[derive(Deserialize, Debug)]
pub struct AssetPosition {
    pub position: PositionData,
    #[serde(rename = "type")]
    pub type_string: String,
}
impl From<AssetPosition> for quant_common::base::Position {
    fn from(value: AssetPosition) -> Self {
        let side = if value.position.szi.parse::<f64>().unwrap() > 0. {
            quant_common::base::PosSide::Long
        } else {
            quant_common::base::PosSide::Short
        };
        let margin_mode = if value.position.leverage.type_string == "isolated" {
            quant_common::base::MarginMode::Isolated
        } else {
            quant_common::base::MarginMode::Cross
        };
        let mut quote = quant_common::base::QuoteCcy::USDT;
        // the only USDC-denominated perpetual contracts are PURR-USD and HYPE-USD
        if value.position.coin == "PURR" || value.position.coin == "HYPE" {
            quote = quant_common::base::QuoteCcy::USDC;
        }
        Self {
            symbol: Symbol {
                base: value.position.coin,
                quote,
            },
            timestamp: time_ms(),
            margin_mode,
            side,
            leverage: value.position.leverage.value as u8,
            amount: value.position.szi.parse::<f64>().unwrap().abs(),
            entry_price: value
                .position
                .entry_px
                .unwrap_or("0".to_string())
                .parse::<f64>()
                .unwrap(),
            unrealized_pnl: value.position.unrealized_pnl.parse::<f64>().unwrap_or(0.),
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct MarginSummary {
    pub account_value: String,
    // pub total_margin_used: String,
    pub total_ntl_pos: String,
    pub total_raw_usd: String,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Token {
    pub name: String,
    pub sz_decimals: u8,
    pub wei_decimals: u8,
    pub index: u8,
    pub token_id: String,
    pub is_canonical: bool,
    pub evm_contract: Option<String>,
    pub full_name: Option<String>,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Universe {
    pub name: String,
    pub tokens: Vec<u8>,
    pub index: u8,
    pub is_canonical: bool,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct MarketData {
    pub day_ntl_vlm: String,
    pub mark_px: String,
    pub mid_px: Option<String>,
    pub prev_day_px: String,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct MarketInfo {
    pub tokens: Vec<Token>,
    pub universe: Vec<Universe>,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct MarketDataWrapper {
    pub market_info: MarketInfo,
    pub market_data: Vec<MarketData>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Delta {
    pub coin: String,
    #[serde(deserialize_with = "de_from_str")]
    pub funding_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub szi: f64,
    pub type_field: String,
    #[serde(deserialize_with = "de_from_str")]
    pub usdc: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct FundingEntry {
    pub delta: Delta,
    pub hash: String,
    pub time: u64,
}

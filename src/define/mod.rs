pub(crate) mod action;
pub(crate) mod model;
pub(crate) mod web_data;

pub(crate) const MAX_RETRY: u8 = 60;

pub(crate) const EXCHANGESPOT: &str = "HyperLiquidSpot";
pub(crate) const EXCHANGESWAP: &str = "HyperLiquidSwap";

pub(crate) const API_HYPERLIQUID: &str = "https://api.hyperliquid.xyz";
pub(crate) const WS_API_HYPERLIQUID: &str = "wss://api.hyperliquid.xyz/ws";
pub(crate) const TEST_API_HYPERLIQUID: &str = "https://api.hyperliquid-testnet.xyz";
pub(crate) const TEST_WS_API_HYPERLIQUID: &str = "wss://api.hyperliquid-testnet.xyz/ws";

pub(crate) const PATH_INFO: &str = "/info"; //See a user's open orders
pub(crate) const PAYH_EXCHANGE: &str = "/exchange";

use once_cell::sync::Lazy;
// 取消注释，使用papaya的HashMap
use papaya::HashMap as PapayaMap;
use quant_common::Result;
use quant_common::base::model::Exchange;
use quant_common::base::model::Instrument;
use quant_common::base::model::Symbol;
use quant_common::qerror;
use std::collections::HashSet;
use std::sync::Arc;

// 使用全局静态变量存储所有交易所的缓存
pub static EXCHANGE_CACHES: Lazy<PapayaMap<Exchange, Arc<InstrumentCache>>> =
    Lazy::new(|| PapayaMap::with_capacity(1024));

/// 表示instrument更新的结果分类
#[derive(Debug, Default)]
pub struct InstrumentUpdateResult {
    /// 新增的instruments
    pub added: Vec<Instrument>,
    /// 更新的instruments
    pub updated: Vec<Instrument>,
    /// 删除的symbols
    pub removed: Vec<Symbol>,
}

/// 每个交易所的instrument缓存
pub struct InstrumentCache {
    /// 使用papaya的无锁HashMap存储instruments
    pub instruments: PapayaMap<Symbol, Instrument>,
}

/// 初始化交易所的instrument缓存
pub async fn init_exchange_cache(exchange: Exchange, instruments: Vec<Instrument>) -> Result<()> {
    let cache = InstrumentCache {
        instruments: PapayaMap::with_capacity(1024),
    };
    let instruments_cache: papaya::HashMapRef<
        '_,
        Symbol,
        Instrument,
        std::hash::RandomState,
        papaya::LocalGuard<'_>,
    > = cache.instruments.pin();
    for instrument in instruments {
        instruments_cache.insert(instrument.symbol.clone(), instrument);
    }
    drop(instruments_cache);

    let exchange_cache = EXCHANGE_CACHES.pin();
    exchange_cache.insert(exchange, Arc::new(cache));
    Ok(())
}

/// 获取指定交易所的instrument缓存
pub async fn get_exchange_cache(exchange: Exchange) -> Result<Arc<InstrumentCache>> {
    // 获取全局缓存的读锁
    let cache_map = EXCHANGE_CACHES.pin();

    // 检查缓存是否已存在
    match cache_map.get(&exchange) {
        Some(cache) => Ok(cache.clone()),
        None => Err(qerror!(
            "Exchange instrument cache not found for {}",
            exchange
        )),
    }
}

/// 更新指定交易所的全部instruments
///
/// 将提供的instruments与现有缓存对比，返回新增、更新和删除的instrument
/// 同时更新缓存中的数据
///
/// # 参数
/// * `exchange` - 要更新的交易所
/// * `instruments` - 最新的全量instruments数据
///
/// # 返回值
/// 返回一个InstrumentUpdateResult，包含新增、更新和删除的instruments详情
pub async fn update_exchange_instruments(
    exchange: Exchange,
    instruments: Vec<Instrument>,
) -> Result<InstrumentUpdateResult> {
    // 获取缓存
    let cache = get_exchange_cache(exchange).await?;

    // 获取当前缓存的instruments
    let guard = cache.instruments.pin();

    // 构建现有symbols集合，用于后续比较
    let mut existing_symbols = HashSet::new();
    for (symbol, _) in guard.iter() {
        existing_symbols.insert(symbol.clone());
    }

    // 构建新symbols集合，用于后续比较
    let mut new_symbols = HashSet::new();
    for instrument in &instruments {
        new_symbols.insert(instrument.symbol.clone());
    }

    // 构建更新结果
    let mut result = InstrumentUpdateResult::default();

    // 找出已删除的symbols
    for existing_symbol in existing_symbols.iter() {
        if !new_symbols.contains(existing_symbol) {
            result.removed.push(existing_symbol.clone());
        }
    }

    // 更新缓存并分类新增/更新的instruments
    for instrument in instruments {
        let symbol = instrument.symbol.clone();

        // 检查是新增还是更新
        if existing_symbols.contains(&symbol) {
            // 如果已存在，检查是否有实际更新
            if let Some(old_instrument) = guard.get(&symbol)
                && old_instrument != &instrument
            {
                result.updated.push(instrument.clone());
            }
        } else {
            // 新增
            result.added.push(instrument.clone());
        }

        // 无论是新增还是更新都插入缓存
        guard.insert(symbol, instrument);
    }

    // 从缓存中删除已移除的symbols
    for symbol in &result.removed {
        guard.remove(symbol);
    }

    Ok(result)
}

/// 获取指定交易所特定交易对的instrument
///
/// # 参数
/// * `exchange` - 交易所
/// * `symbol` - 要获取的交易对symbol
///
/// # 返回值
/// 如果找到对应的instrument，返回其克隆；否则返回None
pub async fn get_instrument(exchange: Exchange, symbol: &Symbol) -> Result<Option<Instrument>> {
    // 获取缓存的读锁
    let cache_map = EXCHANGE_CACHES.pin();

    // 尝试获取交易所缓存
    if let Some(cache) = cache_map.get(&exchange) {
        // 创建guard访问papaya的HashMap
        let guard = cache.instruments.pin();

        // 获取并克隆instrument
        return Ok(guard.get(symbol).cloned());
    }

    // 缓存不存在
    Ok(None)
}

#[cfg(test)]
mod tests {
    use super::*;
    use quant_common::base::model::InsState;
    use quant_common::base::model::{Exchange, Instrument, Symbol};

    // 辅助函数，创建测试用的instrument
    fn create_test_instrument(symbol: &str) -> Instrument {
        Instrument {
            symbol: Symbol::new(symbol),
            state: InsState::Normal,
            price_tick: 0.01,
            amount_tick: 0.001,
            price_precision: 2,
            amount_precision: 6,
            min_qty: 0.001,
            min_notional: 5.0,
            price_multiplier: 1.0,
            amount_multiplier: 1.0,
        }
    }

    #[tokio::test]
    async fn test_init_and_get_exchange_cache() {
        // 创建测试数据
        let exchange = Exchange::BinanceSwap;
        let instruments = vec![create_test_instrument("BTC"), create_test_instrument("ETH")];

        // 初始化缓存
        let init_result = init_exchange_cache(exchange, instruments.clone()).await;
        assert!(init_result.is_ok());

        // 获取并验证缓存
        let cache_result = get_exchange_cache(exchange).await;
        assert!(cache_result.is_ok());

        let cache = cache_result.unwrap();
        let guard = cache.instruments.pin();
        assert_eq!(guard.len(), 2);

        // 验证具体内容
        let btc = Symbol::new("BTC");
        let eth = Symbol::new("ETH");

        assert!(guard.contains_key(&btc));
        assert!(guard.contains_key(&eth));
    }

    #[tokio::test]
    async fn test_get_exchange_cache_not_found() {
        // 尝试获取不存在的交易所缓存
        let result = get_exchange_cache(Exchange::OkxSwap).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_get_instrument() {
        // 初始化测试数据
        let exchange = Exchange::BinanceSwap;
        let instruments = vec![create_test_instrument("BTC"), create_test_instrument("ETH")];

        let _ = init_exchange_cache(exchange, instruments.clone()).await;

        // 测试获取存在的instrument
        let btc_sym = Symbol::new("BTC");
        let btc_result = get_instrument(exchange, &btc_sym).await;
        assert!(btc_result.is_ok());
        let btc_opt = btc_result.unwrap();
        assert!(btc_opt.is_some());
        let btc = btc_opt.unwrap();
        assert_eq!(btc.symbol, btc_sym);

        // 测试获取不存在的instrument
        let ltc_sym = Symbol::new("LTC");
        let ltc_result = get_instrument(exchange, &ltc_sym).await;
        assert!(ltc_result.is_ok());
        assert!(ltc_result.unwrap().is_none());
    }

    #[tokio::test]
    async fn test_update_exchange_instruments() {
        // 初始化测试数据
        let exchange = Exchange::BinanceSwap;
        let initial_instruments =
            vec![create_test_instrument("BTC"), create_test_instrument("ETH")];

        let _ = init_exchange_cache(exchange, initial_instruments).await;

        // 创建更新数据：保留ETH，移除BTC，添加LTC
        let mut updated_eth = create_test_instrument("ETH");
        updated_eth.price_precision = 3; // 修改某个属性，模拟更新
        let update_instruments = vec![updated_eth, create_test_instrument("LTC")];

        // 执行更新
        let update_result = update_exchange_instruments(exchange, update_instruments).await;
        assert!(update_result.is_ok());

        let result = update_result.unwrap();

        // 验证结果
        assert_eq!(result.added.len(), 1);
        assert_eq!(result.added[0].symbol, Symbol::new("LTC"));

        assert_eq!(result.updated.len(), 1);
        assert_eq!(result.updated[0].symbol, Symbol::new("ETH"));
        assert_eq!(result.updated[0].price_precision, 3);

        assert_eq!(result.removed.len(), 1);
        assert_eq!(result.removed[0], Symbol::new("BTC"));

        // 验证缓存状态
        let cache = get_exchange_cache(exchange).await.unwrap();
        let guard = cache.instruments.pin();
        assert_eq!(guard.len(), 2);
        assert!(guard.contains_key(&Symbol::new("ETH")));
        assert!(guard.contains_key(&Symbol::new("LTC")));
        assert!(!guard.contains_key(&Symbol::new("BTC")));
    }
}

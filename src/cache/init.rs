use std::collections::HashSet;

use crate::cache::instruments::init_exchange_cache;
use crate::cache::instruments::update_exchange_instruments;
use crate::create_public_rest;
use quant_common::Result;
use quant_common::base::ExConfig;
use quant_common::base::Rest;
use quant_common::base::traits::instrument::InstrumentHandler;
use tracing::error;

pub async fn init_instruments<H: InstrumentHandler + Send + Sync + 'static>(
    exs: Vec<ExConfig>,
    interval: u64,
    strategy: H,
) -> Result<()> {
    if exs.is_empty() {
        return Ok(());
    }
    // 根据exchange 去重
    let exs = exs
        .into_iter()
        .map(|ex| ex.exchange)
        .collect::<HashSet<_>>();

    let mut rests = Vec::new();

    for ex in exs.clone() {
        let rest = create_public_rest(ex).await;
        let instruments = rest.get_instruments().await?;
        init_exchange_cache(ex, instruments).await?;
        rests.push(rest);
    }

    // from env
    let interval = tokio::time::Duration::from_secs(interval);

    let mut interval = tokio::time::interval(interval);

    tokio::spawn(async move {
        let exs = exs.into_iter().collect::<Vec<_>>();
        loop {
            interval.tick().await;

            for i in 0..rests.len() {
                if let Ok(instruments) = rests[i].get_instruments().await {
                    match update_exchange_instruments(exs[i].to_owned(), instruments).await {
                        Ok(res) => {
                            if !res.updated.is_empty()
                                && let Err(e) =
                                    strategy.on_instrument_updated(exs[i], res.updated).await
                            {
                                error!("on_instrument_update error: {:?}", e);
                            }
                            if !res.removed.is_empty()
                                && let Err(e) =
                                    strategy.on_instrument_removed(exs[i], res.removed).await
                            {
                                error!("on_instrument_delete error: {:?}", e);
                            }
                            if !res.added.is_empty()
                                && let Err(e) =
                                    strategy.on_instrument_added(exs[i], res.added).await
                            {
                                error!("on_instrument_add error: {:?}", e);
                            }
                        }
                        Err(e) => {
                            error!("update_exchange_instruments error: {:?}", e);
                        }
                    }
                }
            }
        }
    });

    Ok(())
}

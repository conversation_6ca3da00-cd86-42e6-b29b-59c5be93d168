use base64::Engine;
use ed25519_dalek::pkcs8::DecodePrivateKey;
use ed25519_dalek::{Signature, Signer, SigningKey};

use hmac::Mac;
use quant_common::base::{BboTicker, FailOrder, Instrument, SuccessOrder};
use quant_common::{Result, qerror, separate_prefix_numbers};

pub(crate) enum BnOrderItemRsp {
    Succ(SuccessOrder),
    Fail(FailOrder),
}

pub fn is_ed25519_secret(secret: &str) -> bool {
    secret.starts_with("-----")
}

pub(crate) fn gen_sign(query: &str, api_secret: &str, timestamp: i64) -> Result<String> {
    if is_ed25519_secret(api_secret) {
        return gen_ed25519_sign(query, api_secret, timestamp);
    }

    use hmac::Mac;
    let timestamp = match timestamp {
        0 => quant_common::time_ms(),
        _ => timestamp,
    };
    let query = match query.is_empty() {
        true => format!("timestamp={timestamp}"),
        false => format!("{query}&timestamp={timestamp}"),
    };
    let mut mac = hmac::Hmac::<sha2::Sha256>::new_from_slice(api_secret.as_bytes())?;
    mac.update(query.as_bytes());
    let bytes = mac.finalize().into_bytes();
    let signature = format!("{bytes:02x}");

    Ok(format!("{query}&signature={signature}"))
}

pub(crate) fn gen_ed25519_sign(query: &str, api_secret: &str, timestamp: i64) -> Result<String> {
    let timestamp = match timestamp {
        0 => quant_common::time_ms(),
        _ => timestamp,
    };
    let query = match query.is_empty() {
        true => format!("timestamp={timestamp}"),
        false => format!("{query}&timestamp={timestamp}"),
    };
    let signing_key = SigningKey::from_pkcs8_pem(api_secret)?;
    let signature: Signature = signing_key.sign(query.as_bytes());
    let signature = base64::prelude::BASE64_STANDARD.encode(signature.to_bytes());
    let encoded_signature = urlencoding::encode(&signature);
    Ok(format!("{query}&signature={encoded_signature}"))
}

pub fn sign_ws_api_params(secret: &str, params: &serde_json::Value) -> Result<String> {
    let params = match params.as_object() {
        Some(map) => map,
        None => return Err(qerror!("params is not an object")),
    };
    let mut sorted_params = vec![];
    for (k, v) in params {
        if k == "signature" {
            continue;
        }
        sorted_params.push((k.to_string(), v));
    }
    sorted_params.sort_by(|a, b| a.0.cmp(&b.0));
    let input = sorted_params
        .iter()
        .map(|(k, v)| format!("{k}={}", value_to_string(v)))
        .collect::<Vec<_>>()
        .join("&");
    debug!("input: {}", input);
    sign_input(secret, &input)
}

fn sign_input(secret: &str, input: &str) -> Result<String> {
    match is_ed25519_secret(secret) {
        true => ed25519_sign(secret, input),
        false => hmac_sign(secret, input),
    }
}

fn ed25519_sign(secret: &str, input: &str) -> Result<String> {
    let key = SigningKey::from_pkcs8_pem(secret)?;
    let signature: Signature = key.sign(input.as_bytes());
    let signature = base64::prelude::BASE64_STANDARD.encode(signature.to_bytes());
    Ok(signature)
}

fn hmac_sign(secret: &str, input: &str) -> Result<String> {
    let mut mac = hmac::Hmac::<sha2::Sha256>::new_from_slice(secret.as_bytes())?;
    mac.update(input.as_bytes());
    let bytes = mac.finalize().into_bytes();
    Ok(format!("{bytes:02x}"))
}

fn value_to_string(value: &serde_json::Value) -> String {
    match value {
        serde_json::Value::Null => "null".to_string(),
        serde_json::Value::Bool(b) => b.to_string(),
        serde_json::Value::Number(number) => number.to_string(),
        serde_json::Value::String(s) => s.clone(),
        serde_json::Value::Array(vec) => vec
            .iter()
            .map(value_to_string)
            .collect::<Vec<_>>()
            .join(", "),
        serde_json::Value::Object(map) => map
            .iter()
            .map(|(key, value)| format!("{}: {}", key, value_to_string(value)))
            .collect::<Vec<_>>()
            .join(", "),
    }
}

#[derive(Clone, PartialEq, Eq, Hash, Copy, Debug)]
pub enum PrefixFormat {
    // 使用原始数字格式，如"1000000MOGUSDT"
    Numeric(u32),
    // 使用简写格式，如"1MDOGEBABYUSDT"
    MillionPrefix,
}

impl Default for PrefixFormat {
    fn default() -> Self {
        Self::Numeric(1)
    }
}

impl PrefixFormat {
    pub fn prefix(&self) -> u32 {
        match self {
            Self::Numeric(x) => *x,
            Self::MillionPrefix => 1_000_000,
        }
    }

    pub fn to_string(&self, name: &str) -> String {
        match self {
            Self::Numeric(1) => name.to_string(),
            Self::Numeric(prefix) => format!("{prefix}{name}"),
            Self::MillionPrefix => format!("1M{name}"),
        }
    }
}

pub fn parse_prefix(s: String) -> Result<(PrefixFormat, String)> {
    match s.starts_with("1M") {
        true => Ok((PrefixFormat::MillionPrefix, s[2..].to_string())),
        false => {
            let (prefix_mul, try_symbol) = separate_prefix_numbers(&s);
            match prefix_mul.as_str() {
                "1" | "" => Ok((PrefixFormat::Numeric(1), s)),
                _ => {
                    let prefix_mul = prefix_mul.parse::<u32>()?;
                    Ok((PrefixFormat::Numeric(prefix_mul), try_symbol))
                }
            }
        }
    }
}

// 小于1的 且 10的整指数倍的tick
pub fn ceil(tick: f64, v: f64) -> f64 {
    let tick = (1.0 / tick).ceil();
    (v * tick).ceil() / tick
}

pub fn round(tick: f64, v: f64) -> f64 {
    let tick = (1.0 / tick).round();
    (v * tick).round() / tick
}

pub fn min_limit_amount_price(x: &Instrument, bbo: &BboTicker) -> (f64, f64) {
    let price = ceil(x.price_tick / x.price_multiplier, bbo.bid_price * 0.96);
    let amount = ceil(x.amount_tick, x.min_notional / price * x.amount_multiplier);
    let price = ceil(x.price_tick, price * x.price_multiplier);
    (price, amount)
}

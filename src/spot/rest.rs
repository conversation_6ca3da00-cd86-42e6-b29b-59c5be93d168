use std::sync::Arc;

use quant_common::base::traits::{
    GetDepositAddressParams, GetKlineParams, GetUserIdParams, SubTransferParams, TransferParams,
    WithdrawalParams,
};
use quant_common::base::{model::*, traits::rest::Rest};
use quant_common::client_pool::{ClientPool, new_reqwest_client};
use quant_common::{Error, QuantError, Result, qerror};
use reqwest::Method;
use serde::{Serialize, de::DeserializeOwned};
use sonic_rs::Value;
use url::Url;

use crate::spot::model::*;
use crate::util::{BnOrderItemRsp, gen_ed25519_sign, gen_sign};

#[derive(Clone)]
pub struct BinanceSpot {
    url: Url,
    client: Arc<ClientPool>,
    client_gzip: Arc<ClientPool>,
    config: ExConfig,
    discount: Discount,
}

impl BinanceSpot {
    pub async fn new(config: ExConfig) -> Self {
        let client = Arc::new(new_reqwest_client(config.multi_ip, false).await.unwrap());
        let client_gzip = Arc::new(new_reqwest_client(config.multi_ip, true).await.unwrap());

        let url = match &config.host {
            Some(host) => host.as_str(),
            None => match config.is_testnet {
                true => REST_TEST_BASE_URL,
                false => REST_SPOT_BASE_URL,
            },
        };
        let url = format!("https://{url}");
        let url = Url::parse(&url).unwrap();
        let discount = config
            .params
            .get("discount")
            .map(|v| sonic_rs::from_str(v).unwrap())
            .unwrap_or_else(|| Discount::new("BNB".to_string(), 0.75));
        let rest = Self {
            url,
            client,
            client_gzip,
            config,
            discount,
        };

        init_special_symbols(&rest)
            .await
            .expect("init special symbols failed");

        rest
    }

    #[inline(always)]
    pub(crate) async fn get<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::GET.to_string(),
            path: path.to_string(),
            auth: false,
            query: Some(query),
            ..Default::default()
        };
        let value = self.request(user_req).await?;
        let rsp: T = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }

    #[inline(always)]
    pub(crate) async fn get_gzip<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::GET.to_string(),
            path: path.to_string(),
            auth: false,
            query: Some(query),
            ..Default::default()
        };
        let value = self.req(user_req, 0, true).await?;
        let rsp: T = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }

    #[inline(always)]
    pub(crate) async fn get_gzip_no_params<T: DeserializeOwned>(
        &self,
        path: &'static str,
    ) -> Result<T> {
        let user_req = UserRequest {
            method: Method::GET.to_string(),
            path: path.to_string(),
            auth: false,
            ..Default::default()
        };
        let value = self.req(user_req, 0, true).await?;
        let rsp: T = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }

    #[inline(always)]
    pub(crate) async fn get_signed<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::GET.to_string(),
            path: path.to_string(),
            auth: true,
            query: Some(query),
            ..Default::default()
        };
        let value = self.request(user_req).await?;
        let rsp: T = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }

    #[inline(always)]
    pub(crate) async fn post<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::POST.to_string(),
            path: path.to_string(),
            auth: true,
            query: Some(query),
            ..Default::default()
        };
        let value = self.request(user_req).await?;

        match sonic_rs::from_value(&value) {
            Ok(rsp) => Ok(rsp),
            Err(err) => Err(qerror!("{err}, value: {value:?}")),
        }
    }

    #[inline(always)]
    pub(crate) async fn delete<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::DELETE.to_string(),
            path: path.to_string(),
            auth: true,
            query: Some(query),
            ..Default::default()
        };
        let value = self.request(user_req).await?;
        let rsp: T = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }

    // gzip:
    //   false 小数据时延时低
    //   true  大数据时延时低
    pub(crate) async fn req(
        &self,
        user_req: UserRequest,
        timestamp: i64,
        gzip: bool,
    ) -> Result<Value> {
        let mut url = match &user_req.url {
            Some(url) => url.parse::<Url>()?.join(&user_req.path)?,
            None => self.url.join(&user_req.path)?,
        };
        let params = user_req.query.unwrap_or_default();
        let query = serde_urlencoded::to_string(params)?;
        let query = if user_req.auth {
            let secret = &self.config.secret;
            if secret.starts_with("-----") {
                gen_ed25519_sign(&query, secret, timestamp)?
            } else {
                gen_sign(&query, secret, timestamp)?
            }
        } else {
            query
        };
        if !query.is_empty() {
            url.set_query(Some(&query));
        }
        let method: Method = Method::from_bytes(user_req.method.as_bytes())?;
        let mut req = match gzip {
            true => self.client_gzip.request(method, url.clone()),
            false => self.client.request(method, url.clone()),
        };
        if user_req.auth {
            req = req.header(HEADER_KEY, &self.config.key);
        }
        let rsp = req.send().await.map_err(QuantError::network_error)?;
        let status = rsp.status();

        let content_length = rsp.content_length();
        // let content_encoding = rsp.headers().get("content-encoding");
        // info!("content_length: {content_length:?}, content_encoding: {content_encoding:?}");
        let rsp = rsp.bytes().await;
        if let Err(err) = rsp {
            let err = format!(
                "url: {url} status: {status}, content-length: {content_length:?} err: {err:?}"
            );
            return Err(QuantError::network_error(err));
        }
        let rsp = rsp?;

        let path = user_req.path.clone();
        if !status.is_success() {
            let err: BinanceErr = sonic_rs::from_slice(&rsp).map_err(|err| {
                let rsp = std::str::from_utf8(&rsp[..rsp.len().min(1024)]).unwrap();
                qerror!("{url} {err} {rsp}")
            })?;
            let msg = format!("{} {url}\n => {} {}", user_req.method, err.code, err.msg);
            let error = match err.code {
                -5021 => Error::fok_rejected(msg),
                -5022 => Error::maker_only_rejected(msg),
                -2019 | -5013 => Error::insufficient_balance(msg),
                -2011 | -2013 => Error::order_not_found(msg),
                -1111 => Error::parameter_error(msg),
                -2010 => Error::order_rejected(msg),
                _ => Error::exchange_error(msg),
            };
            Err(error)
        } else {
            sonic_rs::from_slice(&rsp).map_err(|err| {
                let rsp = std::str::from_utf8(rsp.get(0..1024).unwrap_or(&[])).unwrap();
                qerror!("{path}?{query} {err} {rsp}")
            })
        }
    }
}

impl BinanceSpot {
    pub(crate) async fn listen_key_req(
        &self,
        method: Method,
        path: &str,
        query: Option<&str>,
    ) -> Result<String> {
        let mut url = self.url.join(path)?;
        if let Some(query) = query {
            url.set_query(Some(&format!("listenKey={query}")));
        }
        let r = self
            .client
            .request(method, url)
            .header(HEADER_KEY, &self.config.key)
            .send()
            .await?;
        let status = r.status();
        let body = r.text().await?;

        match status.is_success() {
            true => Ok(body),
            false => Err(qerror!("{}", body)),
        }
    }

    #[inline]
    pub async fn create_listen_key(&self) -> Result<String> {
        let path = PATH_LISTEN_KEY;
        let r = self.listen_key_req(Method::POST, path, None).await?;
        let r: RspListenKey = sonic_rs::from_str(&r)?;
        Ok(r.listen_key)
    }

    #[inline]
    pub async fn refresh_listen_key(&self, listen_key: &str) -> Result<()> {
        let listen_key = Some(listen_key);
        let path = PATH_LISTEN_KEY;
        let _ = self.listen_key_req(Method::PUT, path, listen_key).await?;
        Ok(())
    }

    #[inline]
    pub(crate) async fn depth(&self, symbol: BnSymbol, limit: Option<u32>) -> Result<BnRestDepth> {
        let params = DepthReq::new(symbol, limit);
        self.get(PATH_DEPTH, &params).await
    }

    pub async fn capitals(&self) -> Result<CapitalsRsp> {
        self.get_signed(PATH_CAPITALS, &()).await
    }

    pub async fn symbol_infos(&self) -> Result<Vec<SymbolInfo>> {
        let exchange_info: ExchangeInfo = self.get_gzip_no_params(PATH_EXCHANGE_INFO).await?;
        Ok(exchange_info
            .symbols
            .into_iter()
            .filter(|s| s.is_open())
            .collect())
    }
}

impl Rest for BinanceSpot {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        let params: SymbolArg = symbol.into();
        let ticker: BnTicker = self.get_gzip(PATH_TICKER_24H, &params).await?;
        Ok(ticker.into())
    }

    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        let tickers_response: Vec<BnTicker> = self.get_gzip_no_params(PATH_TICKER_24H).await?;
        Ok(tickers_response.into_iter().map(Into::into).collect())
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        let params: SymbolArg = symbol.into();
        let resp: RestBookTicker = self.get(PATH_BOOKTICKER, &params).await?;
        Ok(resp.into())
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        let resp: Vec<RestBookTicker> = self.get(PATH_BOOKTICKER, &()).await?;
        Ok(resp.into_iter().map(Into::into).collect())
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        let resp = self.depth(symbol.clone().into(), limit).await?;
        Ok(resp.into_depth(symbol))
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        let arg: SymbolArg = symbol.clone().into();
        let exchange_info: ExchangeInfo = self.get(PATH_EXCHANGE_INFO, &arg).await?;

        exchange_info
            .symbols
            .into_iter()
            .map(Instrument::from)
            .filter(|x| x.is_open() && !x.symbol.quote.is_other())
            .find(|x| x.symbol == symbol)
            .ok_or_else(|| qerror!("get_instrument {} not found", symbol))
    }

    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        let symbol_info = self.symbol_infos().await?;

        Ok(symbol_info
            .into_iter()
            .map(Instrument::from)
            .filter(|x| x.is_open() && BnQuote::is_other(&x.symbol.quote))
            .collect())
    }

    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        Err(QuantError::api_not_implemented(format!(
            "{} 没有提供 get_funding_rates 接口",
            self.name()
        )))
    }

    async fn get_klines_ext(&self, params: GetKlineParams) -> Result<Kline> {
        let symbol = params.symbol.clone();
        let interval = params.interval.clone();
        let params: KlineReq = params.into();
        let prefix = params.symbol.prefix_f64();
        let candles: Vec<BnCandle> = self.get_gzip(PATH_KLINE, &params).await?;
        let candles = candles
            .into_iter()
            .map(|v| v.into_base(prefix))
            .collect::<Vec<Candle>>();
        Ok(Kline::new(symbol, interval, candles))
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        let order_request = NewOrderRequest::new(order, &params)?;
        let rsp: OrderResponse = self.post(PATH_ORDER, &order_request).await?;
        Ok(rsp.order_id.to_string())
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let _ = (orders, params);
        let err = format!("{} 没有原生批量下单接口", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let req = GetOrderReq::new(symbol, Some(order_id))?;
        let rsp: BnOrder = self.get_signed(PATH_ORDER, &req).await?;
        Ok(rsp.into())
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        let req = OpenOrdersReq::new(Some(symbol), None, None);
        let rsp: Vec<BnOrder> = self.get_signed(PATH_OPEN_ORDERS, &req).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        let rsp: Vec<BnOrder> = self.get_signed(PATH_OPEN_ORDERS, &()).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        let req = OpenOrdersReq::new(Some(symbol), Some(start_time), Some(end_time));
        let rsp: Vec<BnOrder> = self.get_signed(PATH_ALL_ORDERS, &req).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        let amend_request: CancelReplaceOrderRequest = order.into();
        let rsp: CancelReplaceOrderResponse =
            self.post(PATH_CANCEL_REPLACE_ORDER, &amend_request).await?;
        Ok(rsp.new_order_response.order_id.to_string())
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        let cancel_request = SymbolOrderId::new(symbol, order_id);
        let _: TrivialResponse = self.delete(PATH_ORDER, &cancel_request).await?;
        Ok(())
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        let params: SymbolArg = symbol.into();
        let resp: TradeFee = self.get_signed(PATH_COMMISSION_RATE, &params).await?;
        Ok(resp.into())
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        let account: Account = self.get_signed(PATH_ACCOUNT, &()).await?;
        Ok(account.balances.into_iter().map(Into::into).collect())
    }

    async fn get_position(&self, _symbol: Symbol) -> Result<Vec<Position>> {
        Err(QuantError::api_not_implemented(format!(
            "{} 没有提供 get_position 接口",
            self.name()
        )))
    }

    async fn get_positions(&self) -> Result<Vec<Position>> {
        Err(QuantError::api_not_implemented(format!(
            "{} 没有提供 get_positions 接口",
            self.name()
        )))
    }

    async fn set_leverage(&self, _symbol: Symbol, _leverage: u8) -> Result<()> {
        Err(QuantError::api_not_implemented(format!(
            "{} 没有提供 set_leverage 接口",
            self.name()
        )))
    }

    async fn is_dual_side(&self) -> Result<bool> {
        Ok(false)
    }

    async fn set_dual_side(&self, dual_side: bool) -> Result<()> {
        match dual_side {
            false => Ok(()),
            true => Err(qerror!("{} 没有双向持仓", self.name())),
        }
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
        let params: SymbolArg = symbol.into();
        let orders: Vec<CancelOpenOrder> = self.delete(PATH_OPEN_ORDERS, &params).await?;
        let rsp = orders
            .into_iter()
            .fold(BatchOrderRsp::default(), |mut r, item| {
                match item.into() {
                    BnOrderItemRsp::Succ(succ) => r.success_list.push(succ),
                    BnOrderItemRsp::Fail(fail) => r.failure_list.push(fail),
                }

                r
            });
        Ok(rsp)
    }

    async fn batch_cancel_order_by_ids(
        &self,
        _symbol: Option<Symbol>,
        _ids: Option<Vec<String>>,
        _cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        Err(QuantError::api_not_implemented(format!(
            "{} 未提供 batch_cancel_order_by_ids 接口",
            self.name()
        )))
    }

    async fn get_deposit_address(
        &self,
        ccy: String,
        chain: Option<Chain>,
        amount: Option<f64>,
    ) -> Result<Vec<DepositAddress>> {
        let req = DepositAddressReq::new(ccy, chain.clone(), amount);
        let rsp: DepositAddressRsp = self.get_signed(PATH_DEPOSIT, &req).await?;
        Ok(rsp.into_base(chain))
    }

    async fn get_deposit_address_ext(
        &self,
        params: GetDepositAddressParams,
    ) -> Result<Vec<DepositAddress>> {
        let req = DepositAddressReq::new(params.ccy, params.chain.clone(), params.amount);
        let rsp: DepositAddressRsp = self.get_signed(PATH_DEPOSIT, &req).await?;
        Ok(rsp.into_base(params.chain))
    }

    async fn withdrawal(&self, withdrawal: WithDrawlParams) -> Result<()> {
        let req = WithdrawalReq::try_from(withdrawal)?;
        let _rsp: WithdrawRsp = self.post(PATH_WITHDRAW, &req).await?;
        Ok(())
    }

    async fn withdrawal_ext(&self, params: WithdrawalParams) -> Result<()> {
        let req = WithdrawalReq::try_from(params.params)?;
        let _rsp: WithdrawRsp = self.post(PATH_WITHDRAW, &req).await?;
        Ok(())
    }

    async fn get_user_id(&self) -> Result<String> {
        Err(QuantError::api_not_implemented(format!(
            "{} 查询的uid不能用于子母划转, 子母划转需要的mail目前也不支持通过api + uid查询, 目前需要在 https://www.binance.com/zh-CN/my/sub-account/account-management 中手动查询",
            self.name()
        )))
    }

    async fn get_user_id_ext(&self, params: GetUserIdParams) -> Result<String> {
        let _ = params;
        Err(QuantError::api_not_implemented(format!(
            "{} 查询的uid不能用于子母划转, 子母划转需要的mail目前也不支持通过api + uid查询, 目前需要在 https://www.binance.com/zh-CN/my/sub-account/account-management 中手动查询",
            self.name()
        )))
    }

    async fn transfer(&self, transfer: Transfer) -> Result<()> {
        let transfer_request = TransferReq::from(transfer);
        let _r: TransferRsp = self.post(PATH_TRANSFER, &transfer_request).await?;
        Ok(())
    }

    async fn transfer_ext(&self, params: TransferParams) -> Result<()> {
        let transfer_request = TransferReq::from(params.transfer);
        let _r: TransferRsp = self.post(PATH_TRANSFER, &transfer_request).await?;
        Ok(())
    }

    async fn sub_transfer(&self, transfer: SubTransfer) -> Result<()> {
        let req: SubTransferReq = transfer.into();
        let _rsp: SubTransferRsp = self.post(PATH_SUB_TRANSFER, &req).await?;
        Ok(())
    }

    async fn sub_transfer_ext(&self, params: SubTransferParams) -> Result<()> {
        let req: SubTransferReq = params.transfer.into();
        let _rsp: SubTransferRsp = self.post(PATH_SUB_TRANSFER, &req).await?;
        Ok(())
    }

    async fn request(&self, user_req: UserRequest) -> Result<Value> {
        self.req(user_req, 0, false).await
    }

    async fn get_fee_discount_info(&self) -> Result<Option<Discount>> {
        Ok(Some(self.discount.clone()))
    }

    async fn is_fee_discount_enabled(&self) -> Result<bool> {
        let resp: BnbBurnInfo = self.get_signed(PATH_BNB_BURN, &()).await?;
        Ok(resp.into())
    }

    async fn set_fee_discount_enabled(&self, enable: bool) -> Result<()> {
        let req = BnbBurnInfo::new(enable);
        let _rsp: BnbBurnRsp = self.post(PATH_BNB_BURN, &req).await?;
        Ok(())
    }
}

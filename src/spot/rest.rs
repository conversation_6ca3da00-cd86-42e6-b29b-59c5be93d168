use crate::spot::model::*;
use crate::swap::model::{AccountStatus, BbAccountBalance};
use quant_common::base::model::*;
use quant_common::base::*;
use quant_common::{Result, new_reqwest_client, qerror, separate_prefix_numbers, time_ms};
use reqwest::Method;
use reqwest::header::{CONTENT_TYPE, HeaderMap, HeaderValue};
use rustc_hash::FxHashMap;
use serde::de::DeserializeOwned;
use serde::{Deserialize, Serialize};
use sonic_rs::{JsonValueTrait, Value};
use std::collections::HashMap;
use std::str::FromStr;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::join;
use tracing::debug;
use url::Url;

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
struct BbAvailableWithdrawal {
    available_withdrawal: String,
    available_withdrawal_map: Option<HashMap<String, String>>,
}

#[derive(Debug)]
pub struct BybitSpot {
    pub url: Url,
    pub client: reqwest::Client,
    pub config: ExConfig,
    pub is_dual_side: AtomicBool,
    pub symbol_map: FxHashMap<String, BbSymbol>,
}

impl Default for BybitSpot {
    fn default() -> Self {
        let config = ExConfig::default();
        let client = new_reqwest_client();
        let url = "".parse().expect("parse rest base url err.");
        Self {
            url,
            client,
            config,
            is_dual_side: AtomicBool::new(false),
            symbol_map: FxHashMap::default(),
        }
    }
}

impl BybitSpot {
    async fn new_uninitialized(config: ExConfig) -> Self {
        let client = new_reqwest_client();
        let host: String =
            config
                .host
                .as_ref()
                .map(|h| h.to_string())
                .unwrap_or(if config.is_testnet {
                    REST_TEST_BASE_HOST.to_string()
                } else if config.is_colo {
                    REST_COLONY_BASE_HOST.to_string()
                } else {
                    REST_BASE_HOST.to_string()
                });
        let url = Url::parse(&format!("https://{host}")).expect("parse url err.");
        Self {
            url,
            client,
            config,
            is_dual_side: AtomicBool::new(false),
            symbol_map: FxHashMap::default(),
        }
    }

    pub async fn new(config: ExConfig) -> Self {
        let mut rest = Self::new_uninitialized(config).await;
        rest.init_symbol_map().await.expect("初始化symbol map失败");
        rest
    }

    async fn get_available_withdrawal(&self, asset: &str) -> Result<f64> {
        let account_type = if self.config.is_unified {
            "UNIFIED".to_string()
        } else {
            "CONTRACT".to_string()
        };
        let map = HashMap::from([("accountType", account_type), ("coin", asset.to_string())]);
        let resp = self
            .req::<ApiResponse<Value>, _>(
                Method::GET,
                "/v5/asset/transfer/query-account-coin-balance",
                &map,
                true,
            )
            .await?;
        let result = self.check_response_result(resp)?;

        if let Some(avail_with) = result.get("availableWithdrawal") {
            return avail_with
                .as_str()
                .unwrap_or("0")
                .parse()
                .map_err(|e| qerror!("{}", e));
        }

        if let Some(balance) = result.get("balance")
            && let Some(transfer_balance) = balance.get("transferBalance")
        {
            return transfer_balance
                .as_str()
                .unwrap_or("0")
                .parse()
                .map_err(|e| qerror!("{}", e));
        }

        Err(qerror!("get available withdrawal err"))
    }

    #[inline(always)]
    pub async fn req<T: DeserializeOwned, P: Serialize>(
        &self,
        method: Method,
        path: &str,
        query: &P,
        with_sign: bool,
    ) -> Result<T> {
        // 定义请求窗口时间
        let recv_window = 5000;

        let host = self.url.to_string();

        // 根据请求方法来确定请求参数的拼接模式
        let request_param_str = if Method::GET == method {
            serde_urlencoded::to_string(query).expect("url encode err")
        } else {
            sonic_rs::to_string(query).expect("json encode err")
        };

        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        headers.insert("X-BAPI-API-KEY", HeaderValue::from_str(&self.config.key)?);
        headers.insert(
            "X-BAPI-RECV-WINDOW",
            HeaderValue::from_str(&recv_window.to_string())?,
        );

        let timestamp = time_ms();
        if with_sign {
            // 构造待签名的字符串
            let params = format!(
                "{}{}{}{}",
                timestamp, &self.config.key, recv_window, request_param_str
            );

            // 生成 HMAC-SHA256 签名
            let sign = quant_common::hmac_sha256_hex(self.config.secret.as_ref(), &params)?;
            headers.insert(
                "X-BAPI-TIMESTAMP",
                HeaderValue::from_str(&timestamp.to_string())?,
            );
            headers.insert("X-BAPI-SIGN", HeaderValue::from_str(&sign)?);
        }

        // 构建请求
        let request_builder = if method == Method::GET {
            self.client
                .request(method, format!("{host}{path}"))
                .query(query)
        } else {
            debug!("{}", request_param_str);
            self.client
                .request(method, format!("{host}{path}"))
                .body(request_param_str)
        }
        .headers(headers);

        let response = request_builder.send().await?;

        // 检查响应状态并打印响应体
        if !response.status().is_success() {
            let status = response.status().to_string();
            let body = response.text().await.expect("get text err");
            return Err(qerror!("status:{status},body:{body}"));
        }
        let body = response.bytes().await?;
        debug!("Response body: {:?}", body);

        sonic_rs::from_slice(&body)
            .map_err(|err| qerror!("{err} {}", std::str::from_utf8(&body).unwrap()))
    }

    pub fn transfer_bb_symbol(&self, system_symbol: Symbol) -> Result<BbSymbol> {
        self.symbol_map
            .get(&system_symbol.to_string())
            .cloned()
            .ok_or(qerror!("symbol not found"))
    }

    pub async fn init_symbol_map(&mut self) -> Result<()> {
        let map = HashMap::from([("category", "spot".to_owned())]);
        let resp = self
            .req::<ApiResponse<BbInstrument>, _>(
                Method::GET,
                "/v5/market/instruments-info",
                &map,
                true,
            )
            .await?;
        debug!("resp={:?}", resp);
        let bb_instruments = resp.result.ok_or(qerror!("get result err"))?.list;
        for bb_instrument_inner in bb_instruments {
            let (numbers, letters) = separate_prefix_numbers(&bb_instrument_inner.base_coin);
            let (base, base_unit) = if numbers.len() > 1 {
                // 有倍数
                (letters, numbers)
            } else {
                (format!("{numbers}{letters}"), "".to_string())
            };
            let bb_symbol = BbSymbol {
                symbol: bb_instrument_inner.symbol,
                base,
                quote: bb_instrument_inner.quote_coin,
                base_unit,
            };
            let system_symbol = format!("{}_{}", bb_symbol.base, bb_symbol.quote);
            self.symbol_map.insert(system_symbol, bb_symbol);
        }
        Ok(())
    }

    async fn get_common_tickers(&self, symbol: Option<Symbol>) -> Result<ApiResponse<BbTicker>> {
        let mut map = HashMap::from([("category", "spot".to_owned())]);
        if let Some(s) = symbol {
            map.insert("symbol", self.transfer_bb_symbol(s)?.to_string());
        }
        let response = self
            .req::<ApiResponse<BbTicker>, _>(Method::GET, "/v5/market/tickers", &map, false)
            .await?;
        Ok(response)
    }

    fn check_response_result<T>(&self, response: ApiResponse<T>) -> Result<T> {
        if response.ret_code == 0 {
            response.result.ok_or(qerror!("get result err"))
        } else {
            Err(qerror!("请求错误：{}", response.ret_msg))
        }
    }
}

impl Clone for BybitSpot {
    fn clone(&self) -> Self {
        Self {
            url: self.url.clone(),
            client: self.client.clone(),
            config: self.config.clone(),
            is_dual_side: AtomicBool::new(self.is_dual_side.load(Ordering::Relaxed)),
            symbol_map: self.symbol_map.clone(),
        }
    }
}

impl Rest for BybitSpot {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, req: UserRequest) -> Result<Value> {
        let method = Method::from_str(req.method.as_str())?;
        let p = if method == Method::GET {
            req.query
        } else {
            req.body
        };
        self.req(method, &req.path, &p, req.auth).await
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        let resp = self.get_common_tickers(Some(symbol)).await?;
        let ts = resp.time;
        let result = self.check_response_result(resp)?;
        let ticker = result
            .list
            .into_iter()
            .next()
            .map(|t| {
                let mut tk = Ticker::from(t);
                tk.timestamp = ts;
                tk
            })
            .ok_or(qerror!("BbTickerInner List 不能为空"))?;
        Ok(ticker)
    }

    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        let resp = self.get_common_tickers(None).await?;
        let ts = resp.time;
        let result = self.check_response_result(resp)?;
        let tickers = result
            .list
            .into_iter()
            .filter_map(|t| {
                let perp = Symbol::split_no_separator_symbol::<BybitQuote>(&t.symbol);
                if perp.is_ok() {
                    let mut tk = Ticker::from(t);
                    tk.timestamp = ts;
                    Some(tk)
                } else {
                    None
                }
            })
            .collect::<Vec<Ticker>>();
        Ok(tickers)
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        let resp = self.get_common_tickers(Some(symbol)).await?;
        let ts = resp.time;
        let result = self.check_response_result(resp)?;
        let ticker = result
            .list
            .into_iter()
            .next()
            .map(|t| {
                let mut tk = BboTicker::from(t);
                tk.timestamp = ts;
                tk
            })
            .ok_or(qerror!("BbTickerInner List 不能为空"))?;
        Ok(ticker)
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        let resp = self.get_common_tickers(None).await?;
        let ts = resp.time;
        let result = self.check_response_result(resp)?;
        let tickers = result
            .list
            .into_iter()
            .filter_map(|t| {
                let perp = Symbol::split_no_separator_symbol::<BybitQuote>(&t.symbol);
                if perp.is_ok() {
                    let mut tk = BboTicker::from(t);
                    tk.timestamp = ts;
                    Some(tk)
                } else {
                    None
                }
            })
            .collect::<Vec<BboTicker>>();
        Ok(tickers)
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        let map = HashMap::from([
            ("category", "spot".to_owned()),
            ("symbol", self.transfer_bb_symbol(symbol)?.to_string()),
            ("limit", limit.unwrap_or(10).to_string()),
        ]);
        let resp = self
            .req::<ApiResponse<BbDepth>, _>(Method::GET, "/v5/market/orderbook", &map, true)
            .await?;
        let result = self.check_response_result(resp)?;
        Ok(result.into())
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        let map = HashMap::from([
            ("category", "spot".to_owned()),
            ("symbol", self.transfer_bb_symbol(symbol)?.to_string()),
        ]);

        let resp = self
            .req::<ApiResponse<BbInstrument>, _>(
                Method::GET,
                "/v5/market/instruments-info",
                &map,
                true,
            )
            .await?;
        let result = self.check_response_result(resp)?;
        let mut vec: Vec<_> = result.list.into_iter().map(Instrument::from).collect();
        vec.pop()
            .ok_or(qerror!("Vec is empty, cannot remove an element."))
    }

    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        let map = HashMap::from([("category", "spot".to_owned())]);
        let resp = self
            .req::<ApiResponse<BbInstrument>, _>(
                Method::GET,
                "/v5/market/instruments-info",
                &map,
                true,
            )
            .await?;
        let result = self.check_response_result(resp)?;
        let vec = result
            .list
            .into_iter()
            .filter_map(|i| {
                let perp = Symbol::split_no_separator_symbol::<BybitQuote>(&i.symbol);
                if perp.is_ok() && i.innovation == "0" {
                    let ins = Instrument::from(i);
                    Some(ins)
                } else {
                    None
                }
            })
            .collect();
        Ok(vec)
    }

    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        unimplemented!("现货没有资金费率接口");
    }

    async fn set_leverage(&self, _symbol: Symbol, _leverage: u8) -> Result<()> {
        unimplemented!("现货没有设置杠杆接口");
    }

    async fn is_dual_side(&self) -> Result<bool> {
        unimplemented!("{} 现货没有双向持仓相关接口", self.name());
    }

    async fn set_dual_side(&self, _is_dual_side: bool) -> Result<()> {
        unimplemented!("{} 现货没有双向持仓相关接口", self.name());
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        let map = HashMap::from([
            ("category", "spot".to_owned()),
            ("symbol", self.transfer_bb_symbol(symbol)?.to_string()),
        ]);
        let resp = self
            .req::<ApiResponse<BbList<BbFeeRate>>, _>(
                Method::GET,
                "/v5/account/fee-rate",
                &map,
                true,
            )
            .await?;

        let result = self.check_response_result(resp)?;
        let fee: Option<FeeRate> = result.list.into_iter().map(FeeRate::from).next();
        fee.ok_or(qerror!("get fee err"))
    }

    async fn get_usdt_balance(&self) -> Result<Balance> {
        self.get_balance("USDT").await
    }

    async fn get_balance(&self, asset: &str) -> Result<Balance> {
        let account_type = if self.config.is_unified {
            "UNIFIED".to_string()
        } else {
            "CONTRACT".to_string()
        };
        let map = HashMap::from([("accountType", account_type), ("coin", asset.to_string())]);
        let result = if asset == "USDT" {
            let (usdt, all_balances) = tokio::join!(
                self.req::<ApiResponse<BbAccountBalance>, _>(
                    Method::GET,
                    "/v5/asset/transfer/query-account-coin-balance",
                    &map,
                    true,
                ),
                self.get_balances()
            );
            let all_balances = all_balances?
                .into_iter()
                .find(|b| b.asset == asset)
                .ok_or(qerror!("get balance err"))?;
            (usdt?, Some(all_balances))
        } else {
            (
                self.req::<ApiResponse<BbAccountBalance>, _>(
                    Method::GET,
                    "/v5/asset/transfer/query-account-coin-balance",
                    &map,
                    true,
                )
                .await?,
                None,
            )
        };

        let balance: Balance = result
            .0
            .result
            .ok_or(qerror!("get balance err"))?
            .balance
            .into();
        if let Some(mut all_balances) = result.1 {
            all_balances.available_balance = balance.available_balance;
            Ok(all_balances)
        } else {
            Ok(balance)
        }
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        let map = HashMap::from([("accountType", "UNIFIED".to_owned())]);
        let (wallet_balance_resp, usdt_avail_balance_resp) = join!(
            self.req::<ApiResponse<BbList<BbBalance>>, _>(
                Method::GET,
                "/v5/account/wallet-balance",
                &map,
                true,
            ),
            self.get_available_withdrawal("USDT")
        );

        let wallet_balance_resp = wallet_balance_resp?;
        let usdt_avail_balance = usdt_avail_balance_resp.unwrap_or_default();

        let mut result = self.check_response_result(wallet_balance_resp)?;
        let account = result.list.pop().ok_or(qerror!("get balance list err"))?;
        let mut balance_list: Vec<Balance> = account.coin.into_iter().map(Balance::from).collect();

        if let Some(usdt_balance) = balance_list
            .iter_mut()
            .find(|b| b.asset.to_uppercase() == "USDT")
        {
            usdt_balance.available_balance = usdt_avail_balance;
        }

        Ok(balance_list)
    }

    async fn get_fee_discount_info(&self) -> Result<Option<Discount>> {
        unimplemented!("bybit 不支持 get_fee_discount_info 接口")
    }

    async fn is_fee_discount_enabled(&self) -> Result<bool> {
        unimplemented!("bybit 不支持 is_fee_discount_enabled 接口")
    }

    async fn set_fee_discount_enabled(&self, _enable: bool) -> Result<()> {
        unimplemented!("bybit 不支持 set_fee_discount_enabled 接口")
    }

    async fn get_max_leverage(&self, _symbol: Symbol) -> Result<u8> {
        unimplemented!("bybit 现货不支持 get_max_leverage 接口")
    }

    async fn get_deposit_address(
        &self,
        _ccy: String,
        _chain: Option<Chain>,
        _amount: Option<f64>,
    ) -> Result<Vec<DepositAddress>> {
        todo!()
    }

    async fn withdrawal(&self, _withdrwl_params: WithDrawlParams) -> Result<()> {
        todo!()
    }

    async fn get_user_id(&self) -> Result<String> {
        let map: HashMap<String, String> = HashMap::new();
        let resp = self
            .req::<ApiResponse<Value>, _>(Method::GET, "/v5/user/query-api", &map, true)
            .await?;

        let result = self.check_response_result(resp)?;
        let user_id = result.get("userID").unwrap().as_i64().unwrap();
        Ok(user_id.to_string())
    }

    async fn transfer(&self, transfer: Transfer) -> Result<()> {
        let wallet_fn = |t| match t {
            WalletType::Spot => "SPOT".to_string(),
            WalletType::CoinFuture | WalletType::UsdtFuture => "CONTRACT".to_string(),
            _ => "".to_string(),
        };
        let to = wallet_fn(transfer.to);
        let from = wallet_fn(transfer.from);
        let transfer_id = uuid::Uuid::new_v4().to_string();
        let map = HashMap::from([
            ("transferId", transfer_id),
            ("coin", transfer.asset.to_uppercase()),
            ("amount", transfer.amount.to_string()),
            ("fromAccountType", from),
            ("toAccountType", to),
        ]);
        let resp = self
            .req::<ApiResponse<Value>, _>(
                Method::POST,
                "/v5/asset/transfer/inter-transfer",
                &map,
                true,
            )
            .await?;
        let result = self.check_response_result(resp)?;
        let status = result
            .get("status")
            .ok_or(qerror!("get status err"))?
            .as_str()
            .unwrap();
        if status == "FAILED" {
            Err(qerror!("{}", status))
        } else {
            Ok(())
        }
    }

    async fn sub_transfer(&self, transfer: SubTransfer) -> Result<()> {
        if transfer.from_account == transfer.to_account {
            return Err(qerror!("不支持同一个 uid 进行转账，请使用 transfer"));
        }
        let wallet_fn = |t| match t {
            WalletType::Spot => "SPOT".to_string(),
            WalletType::CoinFuture | WalletType::UsdtFuture => "CONTRACT".to_string(),
            _ => "".to_string(),
        };
        let to = wallet_fn(transfer.to);
        let from = wallet_fn(transfer.from);
        let transfer_id = uuid::Uuid::new_v4().to_string();
        let map = HashMap::from([
            ("transferId", transfer_id),
            ("coin", transfer.asset.to_uppercase()),
            ("amount", transfer.amount.to_string()),
            (
                "fromMemberId",
                transfer
                    .from_account
                    .ok_or(qerror!("from_account 不能为空"))?,
            ),
            (
                "toMemberId",
                transfer.to_account.ok_or(qerror!("to_account 不能为空"))?,
            ),
            ("fromAccountType", from),
            ("toAccountType", to),
        ]);
        let resp = self
            .req::<ApiResponse<Value>, _>(
                Method::POST,
                "/v5/asset/transfer/universal-transfer",
                &map,
                true,
            )
            .await?;

        let result = self.check_response_result(resp)?;
        let status = result
            .get("status")
            .ok_or(qerror!("{}", "get status err"))?
            .as_str()
            .unwrap();
        if status == "FAILED" {
            Err(qerror!("{}", status))
        } else {
            Ok(())
        }
    }

    async fn get_margin_mode(&self, _symbol: Symbol, _margin_coin: String) -> Result<MarginMode> {
        unimplemented!("bybit 现货不支持 get_margin_mode 接口")
    }

    async fn set_margin_mode(
        &self,
        _symbol: Symbol,
        _margin_coin: String,
        _margin_mode: MarginMode,
    ) -> Result<()> {
        unimplemented!("bybit 现货不支持 set_margin_mode 接口")
    }

    async fn get_account_info(&self) -> Result<AccountInfo> {
        todo!()
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        let position_idx = if params.is_dual_side {
            match order.pos_side {
                Some(PosSide::Long) => Some(2),
                Some(PosSide::Short) => Some(1),
                _ => None,
            }
        } else {
            None
        };
        let time_in_force = match order.time_in_force {
            TimeInForce::IOC => "IOC",
            TimeInForce::FOK => "FOK",
            TimeInForce::GTC => "GTC",
            TimeInForce::PostOnly => "IOC",
        };
        let side = if let OrderSide::Buy = order.side {
            "Buy"
        } else {
            "Sell"
        };
        let o_type = if let OrderType::Market = order.order_type {
            "Market"
        } else {
            "Limit"
        };
        let request = NewOrderRequest {
            category: "spot".to_string(),
            symbol: self.transfer_bb_symbol(order.symbol)?.to_string(),
            side: side.to_string(),
            order_type: o_type.to_string(),
            qty: order.amount.unwrap_or_default(),
            price: order.price,
            market_unit: Some("baseCoin".to_string()),
            time_in_force: Some(time_in_force.to_string()),
            position_idx,
            order_link_id: order.cid,
        };
        debug!("&request:{:?}", &request);
        let resp = self
            .req::<ApiResponse<BbOrderResult>, _>(Method::POST, "/v5/order/create", &request, true)
            .await?;

        let result = self.check_response_result(resp)?;
        Ok(result.order_id)
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let order_list: Vec<_> = orders
            .into_iter()
            .map(|order| {
                let position_idx = if params.is_dual_side {
                    match order.pos_side {
                        Some(PosSide::Long) => Some(2),
                        Some(PosSide::Short) => Some(1),
                        _ => None,
                    }
                } else {
                    None
                };
                let time_in_force = match order.time_in_force {
                    TimeInForce::IOC => "IOC",
                    TimeInForce::FOK => "FOK",
                    TimeInForce::GTC => "GTC",
                    TimeInForce::PostOnly => "IOC",
                };
                let side = if let OrderSide::Buy = order.side {
                    "Buy"
                } else {
                    "Sell"
                };
                let o_type = if let OrderType::Market = order.order_type {
                    "Market"
                } else {
                    "Limit"
                };
                let symbol = self.transfer_bb_symbol(order.symbol).unwrap().to_string();
                NewOrderRequest {
                    category: "spot".to_string(),
                    symbol,
                    side: side.to_string(),
                    order_type: o_type.to_string(),
                    qty: order.amount.unwrap_or_default(),
                    price: order.price,
                    market_unit: Some("baseCoin".to_string()),
                    time_in_force: Some(time_in_force.to_string()),
                    position_idx,
                    order_link_id: order.cid,
                }
            })
            .collect();
        let order_list_request = NewOrderListRequest {
            category: "spot".to_string(),
            request: order_list,
        };
        let result = self
            .req::<ApiResponse<BbList<BbOrderListResultInner>>, _>(
                Method::POST,
                "/v5/order/create-batch",
                &order_list_request,
                true,
            )
            .await?;

        let mut data = BatchOrderRsp {
            success_list: vec![],
            failure_list: vec![],
        };
        let mut msglist: BbList<BbMsg> =
            sonic_rs::from_str(result.ret_ext_info.unwrap().0.as_raw_str())?;
        let mut orders = result.result.ok_or(qerror!("get result err"))?.list;
        for _ in 0..orders.len() {
            let order = orders.pop().unwrap();
            let msg = msglist.list.pop().unwrap();
            if msg.code == 0 {
                data.success_list.push(SuccessOrder {
                    id: Some(order.order_id),
                    cid: Some(order.order_link_id),
                })
            } else {
                data.failure_list.push(FailOrder {
                    id: Some(order.order_id),
                    cid: Some(order.order_link_id),
                    error_code: msg.code,
                    error: msg.msg,
                })
            }
        }
        Ok(data)
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let mut map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", self.transfer_bb_symbol(symbol)?.to_string()),
        ]);
        match order_id {
            OrderId::Id(id) => {
                map.insert("orderId", id);
            }
            OrderId::ClientOrderId(cid) => {
                map.insert("orderLinkId", cid);
            }
        };
        let resp = self
            .req::<ApiResponse<BbList<BbOrderData>>, _>(
                Method::GET,
                "/v5/order/realtime",
                &map,
                true,
            )
            .await?;

        let mut result = self.check_response_result(resp)?;
        let order: Option<Order> = result.list.pop().map(Order::from);
        order.ok_or(qerror!("get fee err"))
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        let map = HashMap::from([
            ("category", "spot".to_owned()),
            ("symbol", self.transfer_bb_symbol(symbol)?.to_string()),
        ]);
        let resp = self
            .req::<ApiResponse<BbList<BbOrderData>>, _>(
                Method::GET,
                "/v5/order/realtime",
                &map,
                true,
            )
            .await?;
        let result = self.check_response_result(resp)?;
        let orders: Vec<Order> = result
            .list
            .into_iter()
            .map(|o| {
                let sys_symbol = Symbol::split_no_separator_symbol::<BybitQuote>(&o.symbol);
                let mut order1 = Order::from(o);
                order1.symbol = sys_symbol.unwrap();
                order1
            })
            .collect();
        Ok(orders)
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        unimplemented!("bybit 不支持 get_all_open_orders 接口, 请使用 get_open_orders 接口")
    }

    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        let mut map = HashMap::from([
            ("category", "spot".to_string()),
            (
                "symbol",
                self.transfer_bb_symbol(symbol.clone())?.to_string(),
            ),
        ]);

        // 添加可选的时间参数
        if start_time > 0 {
            map.insert("startTime", start_time.to_string());
        }
        if end_time > 0 {
            map.insert("endTime", end_time.to_string());
        }

        let resp = self
            .req::<ApiResponse<BbList<BbOrderData>>, _>(
                Method::GET,
                "/v5/order/history",
                &map,
                true,
            )
            .await?;

        let result = self.check_response_result(resp)?;
        let orders: Vec<Order> = result
            .list
            .into_iter()
            .map(|o| {
                let mut order = Order::from(o);
                order.symbol = symbol.clone();
                order
            })
            .collect();
        Ok(orders)
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        let mut map = HashMap::from([("category", "spot".to_owned())]);
        let qty = order.amount.unwrap_or_default().to_string();
        map.insert("symbol", self.transfer_bb_symbol(order.symbol)?.to_string());
        map.insert("qty", qty);
        if order.price.is_some() {
            map.insert("price", order.price.unwrap().to_string());
        }
        if order.cid.is_some() {
            map.insert("orderLinkId", order.cid.unwrap());
        } else {
            map.insert("orderId", order.id);
        }
        let resp = self
            .req::<ApiResponse<BbOrderResult>, _>(Method::POST, "/v5/order/amend", &map, true)
            .await?;

        let result = self.check_response_result(resp)?;
        Ok(result.order_id)
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        let mut map = HashMap::from([("category", "spot".to_owned())]);
        map.insert("symbol", self.transfer_bb_symbol(symbol)?.to_string());
        match order_id {
            OrderId::Id(id) => map.insert("orderId", id),
            OrderId::ClientOrderId(cid) => map.insert("orderLinkId", cid),
        };

        let resp = self
            .req::<ApiResponse<BbOrderResult>, _>(Method::POST, "/v5/order/cancel", &map, true)
            .await?;
        let _result = self.check_response_result(resp)?;
        Ok(())
    }

    async fn batch_cancel_order(&self, _symbol: Symbol) -> Result<BatchOrderRsp> {
        unimplemented!("batch_cancel_order not supported")
    }

    async fn batch_cancel_order_by_ids(
        &self,
        _symbol: Option<Symbol>,
        _ids: Option<Vec<String>>,
        _cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        todo!()
    }

    async fn get_position(&self, _symbol: Symbol) -> Result<Vec<Position>> {
        unimplemented!("现货没有position,请考虑balance");
    }

    async fn get_positions(&self) -> Result<Vec<Position>> {
        unimplemented!("现货没有position,请考虑balance");
    }

    async fn get_max_position(&self, _symbol: Symbol, _leverage: u8) -> Result<MaxPosition> {
        unimplemented!("现货没有position,请考虑balance");
    }

    async fn borrow_coin(&self, _coin: String, _amount: f64) -> Result<()> {
        unimplemented!("现货没有借币接口,请考虑 margin");
    }

    async fn get_borrow(&self, _coin: Option<String>) -> Result<Vec<Borrow>> {
        unimplemented!("现货没有借币接口,请考虑 margin");
    }

    async fn repay_coin(&self, _coin: String, _amount: f64) -> Result<()> {
        unimplemented!("现货没有借币接口,请考虑 margin");
    }

    async fn get_account_mode(&self) -> Result<AccountMode> {
        let map: HashMap<&str, &str> = HashMap::new();
        let resp = self
            .req::<ApiResponse<AccountStatus>, _>(Method::GET, "/v5/account/info", &map, true)
            .await?;

        let result = self.check_response_result(resp)?;
        Ok(AccountMode::from(result))
    }

    async fn set_account_mode(&self, _mode: AccountMode) -> Result<()> {
        unimplemented!("不支持这个 api")
    }
}

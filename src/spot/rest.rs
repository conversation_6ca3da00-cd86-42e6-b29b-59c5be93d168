use ethers::signers::{LocalWallet, Signer};
use hex::ToHex;
use log::info;
use quant_common::{
    base::{ExConfig, FailOrder, InsState, OrderId, QuoteCcy, Rest, Symbol, UserRequest},
    new_reqwest_client, qerror, time_ms,
};
use rand::thread_rng;
use reqwest::{
    header::{HeaderMap, HeaderValue, CONTENT_TYPE},
    Method,
};
use serde::{de::DeserializeOwned, Serialize};
use std::{collections::HashMap, vec};
use tracing::warn;
use url::Url;

use crate::{
    define::{
        self,
        action::{
            Actions, BulkCancel, BulkCancelCloid, BulkModify, BulkOrder, CancelRequest,
            CancelRequestCloid, CandleSnapshotRequest, ClientOrderRequest, ExchangePayload,
            ExchangeResponseStatus, FrontendOpenOrdersResp, InfoRequest, ModifyRequest,
            UserBalances, UserFillsResponse,
        },
        model::{
            AssetInfo, AssetInfoFun, CandlesSnapshotResponse, L2SnapshotResponse, OrderInfo,
            OrderStatusResponse,
        },
    },
    spot::SpotMeta,
    util::{
        self,
        signature::{agent::l1, create_signature::sign_typed_data},
    },
};

#[derive(Debug, Clone)]
pub struct HyperLiquidSpot {
    pub url: Url,
    pub(crate) client: reqwest::Client,
    pub(crate) config: ExConfig,
    pub(crate) coin_to_asset: AssetInfo,
    pub(crate) coin_meta: HashMap<String, u32>,
}

impl HyperLiquidSpot {
    pub async fn new(config: ExConfig) -> Self {
        let mut url_str = if config.is_testnet {
            define::TEST_API_HYPERLIQUID
        } else {
            define::API_HYPERLIQUID
        }
        .to_string();
        if let Some(host) = config.host.clone() {
            url_str = format!("https://{}", host);
        }
        let url: Url = url_str.parse().unwrap();
        let rest = new_reqwest_client();
        //let vault_address = wallet.address();
        let input = InfoRequest::SpotMeta;
        let params = sonic_rs::to_string(&input).unwrap();
        let response = rest
            .request(Method::POST, format!("{}{}", url_str, define::PATH_INFO))
            .body(params)
            .header(CONTENT_TYPE, HeaderValue::from_static("application/json"))
            .send()
            .await
            .unwrap();
        let bytes = response.bytes().await.unwrap();
        let info: SpotMeta = sonic_rs::from_slice(&bytes).unwrap();
        let mut coin_to_asset = AssetInfo::default();
        let mut coin_meta: HashMap<String, u32> = Default::default();
        info.add_pair_and_name_to_index_map(&mut coin_to_asset);
        for symbol in info.universe {
            let base_info = &info.tokens[symbol.tokens[0]];
            let mut base = base_info.name.clone();
            if base == "UBTC" {
                base = "BTC".to_string();
            }
            coin_meta.insert(base, base_info.sz_decimals);
        }
        Self {
            url,
            client: rest,
            config,
            coin_to_asset,
            coin_meta,
        }
    }
    pub fn new_wallect() -> quant_common::Result<String> {
        // 生成一个新的随机私钥
        let wallet = LocalWallet::new(&mut thread_rng());
        let private_key = wallet.signer().to_bytes().encode_hex();
        Ok(private_key)
    }

    #[inline(always)]
    async fn post<P: Serialize, R: DeserializeOwned>(
        &self,
        path: &str,
        params: &P,
        with_auth: bool,
    ) -> quant_common::Result<R> {
        let body = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::POST.to_string(),
            path: path.to_string(),
            auth: with_auth,
            body: Some(body),
            ..Default::default()
        };
        let value = self.request(user_req).await?;
        // info!("value={}",value);
        let rsp: R = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }
}

impl Rest for HyperLiquidSpot {
    fn name(&self) -> &'static str {
        define::EXCHANGESPOT
    }

    async fn request(
        &self,
        req: quant_common::base::UserRequest,
    ) -> quant_common::Result<sonic_rs::Value> {
        let mut url = match req.url {
            Some(url) => url.parse::<Url>()?.join(&req.path).unwrap(),
            None => self.url.join(&req.path)?,
        };
        #[allow(unused_mut)]
        let mut req_builder;
        let mut headers = HeaderMap::new();
        if req.auth {
            //需要认证，参数必须在body中
            //数据反序列化回去，然后签名
            let wallet: LocalWallet = self.config.secret.parse()?;
            if let Some(body) = req.body.clone() {
                let agrs: Actions = sonic_rs::from_value(&body)?;
                let nonce = util::next_nonce();
                let (data, signature) = match agrs.clone() {
                    Actions::UsdClassTransfer(action) => {
                        let mut data = action;
                        data.nonce = nonce;

                        let signature = sign_typed_data(&data, &wallet)?;
                        (Actions::UsdClassTransfer(data), signature)
                    }
                    _ => {
                        let connection_id = agrs.hash(nonce, None)?; //todo update  vault_address
                        let signature = sign_typed_data(
                            &l1::Agent {
                                source: if self.config.is_testnet {
                                    String::from("b")
                                } else {
                                    String::from("a")
                                }, //"a"主网,"b"测试网
                                connection_id,
                            },
                            &wallet,
                        )?;
                        (agrs, signature)
                    }
                };
                let payload = ExchangePayload {
                    action: sonic_rs::to_value(&data)?,
                    signature,
                    nonce,
                    is_frontend: None,
                    vault_address: None,
                    expires_after: None,
                };
                let params = sonic_rs::to_string(&payload)?;
                if req.query.is_some() {
                    url.set_query(Some(params.as_str()));
                }
                let method = Method::from_bytes(req.method.as_bytes())?;
                // info!("url={}||method={}||params={}",url.to_string(),method,params);
                req_builder = self
                    .client
                    .request(method, url.clone().to_string())
                    .body(params)
            } else {
                return Err(qerror!("{} auth request body is none", self.name()));
            }
        } else {
            let query = match req.query {
                Some(query) => serde_urlencoded::to_string(query)?,
                None => "".to_string(),
            };
            let body = match req.body {
                Some(body) => sonic_rs::to_string(&body)?,
                None => "".to_string(),
            };
            if !query.is_empty() {
                url.set_query(Some(query.as_str()));
            }
            let method = Method::from_bytes(req.method.as_bytes())?;
            // info!("url={}||method={}||body={}||query={}",url.to_string(),method,body,query);
            req_builder = if method == Method::GET {
                self.client.request(method, url.clone().to_string())
            } else {
                self.client
                    .request(method, url.clone().to_string())
                    .body(body)
            };
        }
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        let response = req_builder.headers(headers).send().await?;
        if !response.status().is_success() {
            let status = response.status().to_string();
            let body = response.text().await.expect("get text err");
            return Err(qerror!(
                "{}{} status:{status},body:{body}",
                url.host_str().unwrap(),
                url.path()
            ));
        }
        let bytes = response.bytes().await?;
        sonic_rs::from_slice(&bytes).map_err(|err| {
            qerror!(
                "{}?{} {err} {}",
                url.path(),
                url.query().unwrap(),
                std::str::from_utf8(&bytes).unwrap()
            )
        })
    }

    async fn get_instrument(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<quant_common::base::Instrument> {
        let params = InfoRequest::SpotMeta;
        let spot_meta: SpotMeta = self.post(define::PATH_INFO, &params, false).await?;
        for item in spot_meta.universe {
            let base_info = &spot_meta.tokens[item.tokens[0]];
            let quote_info = &spot_meta.tokens[item.tokens[1]];
            let mut base = base_info.name.clone();
            if base == "UBTC" {
                base = "BTC".to_string();
            }
            if quote_info.name == symbol.quote.to_string() && base == symbol.base {
                //目前所有数据（quote）都是USDC的
                let price_tick = (base_info.wei_decimals - base_info.sz_decimals) as f64;
                let result = quant_common::base::Instrument {
                    symbol: symbol.clone(),
                    state: InsState::Normal,
                    price_tick: 10f64.powf(-price_tick),
                    amount_tick: 10f64.powf(-(base_info.sz_decimals as f64)),
                    price_precision: price_tick as i32,
                    amount_precision: base_info.sz_decimals as i32,
                    min_qty: 0.,
                    min_notional: 10., //Order must have minimum value of $10
                    price_multiplier: 1.,
                    amount_multiplier: 1.,
                };
                return Ok(result);
            }
        }
        Err(qerror!("not found instrument||symbol={}", symbol))
    }

    async fn get_instruments(&self) -> quant_common::Result<Vec<quant_common::base::Instrument>> {
        //获取币种报价的数量单位
        /*
        For perpetuals coin is the name returned in the meta response.

        For Spot, coin should be PURR/USDC for PURR, and @{index}
        e.g. @1 for all other spot tokens where index is the index in the universe field
        of the spotMeta response.
        */
        let params = InfoRequest::SpotMeta;
        let spot_meta: SpotMeta = self.post(define::PATH_INFO, &params, false).await?;
        let mut result = Vec::new();
        for symbol in spot_meta.universe {
            let base_info = &spot_meta.tokens[symbol.tokens[0]];
            let quote_info = &spot_meta.tokens[symbol.tokens[1]];
            if quote_info.name != "USDC" {
                //目前所有数据（quote）都是USDC的
                warn!("quote is not USDC||info=={:?}", quote_info);
                continue;
            }
            //Prices can have up to 5 significant figures,
            // but no more than MAX_DECIMALS - szDecimals decimal places where MAX_DECIMALS is 6 for perps and 8 for spot.
            // For example,
            //
            // for perps, 1234.5 is valid but 1234.56 is not (too many significant figures). 0.001234 is valid,
            // but 0.0012345 is not (more than 6 decimal places).
            //
            // For spot, 0.0001234 is valid if szDecimals is 0 or 1,
            // but not if szDecimals is greater than 2 (more than 8-2 decimal places).
            // Integer prices are always allowed, regardless of the number of significant figures.
            // E.g. 123456.0 is a valid price even though 12345.6 is not.

            //Sizes are rounded to the szDecimals of that asset. For example, if szDecimals = 3 then 1.001 is a valid size but 1.0001 is not.
            /*
            weiDecimals：从代币的最小整数单位到人类可解释的浮点数的转换率。
            例如，EVM 网络上的 ETH 有 weiDecimals = 18，比特币网络上的 BTC 有 weiDecimals = 8。
            szDecimals：现货订单簿上的最小可交易小数位数。
            换句话说，所有现货订单簿上代币的手数都将是 10 ** (weiDecimals - szDecimals) 。
            要求 szDecimals + 5 <= weiDecimals。
            */
            let price_tick = (base_info.wei_decimals - base_info.sz_decimals) as f64;
            let item = quant_common::base::Instrument {
                symbol: Symbol::new_with_quote(&base_info.name, QuoteCcy::USDC),
                state: InsState::Normal,
                price_tick: 10f64.powf(-price_tick),
                amount_tick: 10f64.powf(-(base_info.sz_decimals as f64)),
                price_precision: price_tick as i32,
                amount_precision: base_info.sz_decimals as i32,
                min_qty: 0.,
                min_notional: 10.,
                price_multiplier: 1.,
                amount_multiplier: 1.,
            };
            result.push(item);
        }
        Ok(result)
    }
    async fn get_fee_rate(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<quant_common::base::FeeRate> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let params = InfoRequest::UserFills {
            user: wallet.address(),
        };
        let rsp: Vec<UserFillsResponse> = self.post(define::PATH_INFO, &params, false).await?;
        for item in rsp {
            if item.coin == symbol.base && item.crossed && item.fee != "0.0" {
                let fee = item.fee.parse::<f64>()?;
                let px = item.px.parse::<f64>()?;
                let sz = item.sz.parse::<f64>()?;
                let builder_fee = item.builder_fee.unwrap_or("0".to_string()).parse::<f64>()?;
                let taker = (fee + builder_fee) / (px * sz);
                return Ok(quant_common::base::FeeRate {
                    taker,
                    maker: taker,
                    buyer: taker,
                    seller: taker,
                });
            }
        }
        Err(qerror!("{}:{}get fee rate err", self.name(), symbol))
    }

    async fn get_balances(&self) -> quant_common::Result<Vec<quant_common::base::Balance>> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let params = InfoRequest::UserTokenBalances {
            user: wallet.address(),
        };
        let rsp: UserBalances = self.post(define::PATH_INFO, &params, false).await?;
        Ok(rsp.balances.into_iter().map(|item| item.into()).collect())
    }

    async fn post_order(
        &self,
        order: quant_common::base::Order,
        _params: quant_common::base::OrderParams,
    ) -> quant_common::Result<String> {
        if !self
            .coin_to_asset
            .token_ind
            .contains_key(&order.symbol.base)
        {
            return Err(qerror!(
                "{} not support symbol {}",
                self.name(),
                order.symbol
            ));
        }
        let item: ClientOrderRequest = order.clone().into();

        let action = Actions::Order(BulkOrder {
            orders: vec![item.convert(
                &self.coin_to_asset.token_ind,
                self.coin_meta[order.symbol.base.as_str()],
                8,
            )?],
            grouping: "na".to_string(),
            builder: None,
        });
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        match info {
            ExchangeResponseStatus::Ok(exchange_response) => match exchange_response.data {
                Some(data) => match data.statuses.first() {
                    Some(status) => match status {
                        define::action::ExchangeDataStatus::Resting(resting_order) => {
                            Ok(resting_order.oid.to_string())
                        }
                        define::action::ExchangeDataStatus::Filled(filled_order) => {
                            Ok(filled_order.oid.to_string())
                        }
                        _ => Err(qerror!("{:?}", status)),
                    },
                    None => Err(qerror!("{:?}", data.statuses)),
                },
                None => Err(qerror!("{:?}", exchange_response)),
            },
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    async fn post_batch_order(
        &self,
        orders: Vec<quant_common::base::Order>,
        _params: quant_common::base::OrderParams,
    ) -> quant_common::Result<quant_common::base::BatchOrderRsp> {
        let mut transformed_orders = Vec::new();
        for order in orders.clone() {
            let item: ClientOrderRequest = order.clone().into();
            transformed_orders.push(item.convert(
                &self.coin_to_asset.token_ind,
                self.coin_meta[order.symbol.base.as_str()],
                8,
            )?);
        }
        let action = Actions::Order(BulkOrder {
            orders: transformed_orders,
            grouping: "na".to_string(),
            builder: None,
        });
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        let mut result = quant_common::base::BatchOrderRsp::default();
        match info {
            ExchangeResponseStatus::Ok(exchange_response) => match exchange_response.data {
                Some(data) => {
                    for status in data.statuses {
                        match status {
                            define::action::ExchangeDataStatus::Resting(resting_order) => {
                                result.success_list.push(quant_common::base::SuccessOrder {
                                    id: Some(resting_order.oid.to_string()),
                                    cid: resting_order.cloid,
                                })
                            }
                            define::action::ExchangeDataStatus::Filled(filled_order) => {
                                result.success_list.push(quant_common::base::SuccessOrder {
                                    id: Some(filled_order.oid.to_string()),
                                    cid: filled_order.cloid,
                                })
                            }
                            define::action::ExchangeDataStatus::Error(e) => {
                                result.failure_list.push(FailOrder {
                                    id: None,
                                    cid: None,
                                    error_code: 0,
                                    error: e,
                                });
                            }
                            _ => continue,
                        };
                    }
                    //进一步处理失败的订单
                    orders.into_iter().for_each(|v| {
                        if !result.success_list.iter().any(|item| item.cid == v.cid) {
                            result.failure_list.push(FailOrder {
                                id: Some(v.id),
                                cid: v.cid,
                                error_code: 0,
                                error: "order failed".to_string(),
                            });
                        }
                    });
                    Ok(result)
                }
                None => Err(qerror!("{:?}", exchange_response)),
            },
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    async fn get_open_orders(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<Vec<quant_common::base::Order>> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let params = InfoRequest::FrontendOpenOrders {
            user: wallet.address(),
        };
        let rsp: Vec<FrontendOpenOrdersResp> = self.post(define::PATH_INFO, &params, false).await?;
        let mut result = Vec::new();
        rsp.into_iter().for_each(|item| {
            if let Some(msymbol) = self.coin_to_asset.from_spot_to_symbol(item.coin.clone()) {
                if symbol == msymbol {
                    result.push(item.to_order(msymbol.clone()));
                }
            };
        });
        Ok(result)
    }

    async fn get_all_open_orders(&self) -> quant_common::Result<Vec<quant_common::base::Order>> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let params = InfoRequest::FrontendOpenOrders {
            user: wallet.address(),
        };
        let rsp: Vec<FrontendOpenOrdersResp> = self.post(define::PATH_INFO, &params, false).await?;
        Ok(rsp
            .into_iter()
            .filter_map(|item| {
                if let Some(msymbol) = self.coin_to_asset.from_spot_to_symbol(item.coin.clone()) {
                    return Some(item.to_order(msymbol.clone()));
                };
                None
            })
            .collect())
    }
    async fn get_order_by_id(
        &self,
        symbol: Symbol,
        order_id: OrderId,
    ) -> quant_common::Result<quant_common::base::Order> {
        let oid = match order_id {
            OrderId::Id(id) => id.parse()?,
            OrderId::ClientOrderId(cid) => cid.parse()?,
        };
        let wallet: LocalWallet = self.config.secret.parse()?;
        let input = InfoRequest::OrderStatus {
            user: wallet.address(),
            oid,
        };
        let rsp: OrderStatusResponse = self.post(define::PATH_INFO, &input, false).await?;
        if let Some(info) = rsp.order {
            return Ok(info.to_order(symbol.clone()));
        }
        Err(qerror!(
            "order not found||symbol={},order_id={}",
            symbol,
            oid
        ))
    }
    async fn get_orders(
        &self,
        symbol: quant_common::base::Symbol,
        start_time: i64,
        end_time: i64,
    ) -> quant_common::Result<Vec<quant_common::base::Order>> {
        if let Some(index) = self.coin_to_asset.get_spot(&symbol) {
            let wallet: LocalWallet = self.config.secret.parse()?;
            let input = InfoRequest::HistoricalOrders {
                user: wallet.address(),
            };
            let rsp: Vec<OrderInfo> = self.post(define::PATH_INFO, &input, false).await?;
            let mut result = Vec::new();
            for info in rsp {
                if info.order.coin != index {
                    continue;
                }
                if let Some(msymbol) = self
                    .coin_to_asset
                    .from_spot_to_symbol(info.order.coin.clone())
                {
                    if symbol != msymbol {
                        continue;
                    }
                    if (info.order.timestamp as i64) >= start_time
                        && (info.order.timestamp as i64) <= end_time
                    {
                        result.push(info.to_order(symbol.clone()));
                    }
                };
            }
            return Ok(result);
        }
        Err(qerror!("order not  support|symbol={}", symbol))
    }

    async fn amend_order(&self, order: quant_common::base::Order) -> quant_common::Result<String> {
        let modify: ClientOrderRequest = order.clone().into();
        let action = Actions::BatchModify(BulkModify {
            modifies: vec![ModifyRequest {
                oid: order.id.parse()?,
                order: modify.convert(
                    &self.coin_to_asset.token_ind,
                    self.coin_meta[order.symbol.base.as_str()],
                    8,
                )?,
            }],
        });
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        match info {
            ExchangeResponseStatus::Ok(exchange_response) => match exchange_response.data {
                Some(data) => match data.statuses.first() {
                    Some(status) => match status {
                        define::action::ExchangeDataStatus::Resting(resting_order) => {
                            Ok(resting_order.oid.to_string())
                        }
                        define::action::ExchangeDataStatus::Filled(filled_order) => {
                            Ok(filled_order.oid.to_string())
                        }
                        _ => Err(qerror!("{:?}", status)),
                    },
                    None => Err(qerror!("{:?}", data.statuses)),
                },
                None => Err(qerror!("{:?}", exchange_response)),
            },
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    async fn cancel_order(
        &self,
        symbol: quant_common::base::Symbol,
        order_id: quant_common::base::OrderId,
    ) -> quant_common::Result<()> {
        let asset = self
            .coin_to_asset
            .token_ind
            .get(&symbol.base)
            .ok_or(qerror!("{} AssetNotFound", self.name()))?;
        let action = match order_id {
            OrderId::Id(id) => Actions::Cancel(BulkCancel {
                cancels: vec![CancelRequest {
                    asset: *asset as u32,
                    oid: id.parse()?,
                }],
            }),
            OrderId::ClientOrderId(cid) => Actions::CancelByCloid(BulkCancelCloid {
                cancels: vec![CancelRequestCloid {
                    asset: *asset as u32,
                    cloid: cid,
                }],
            }),
        };
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        match info {
            ExchangeResponseStatus::Ok(_exchange_response) => Ok(()),
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    async fn batch_cancel_order(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<quant_common::base::BatchOrderRsp> {
        let orders = self.get_open_orders(symbol.clone()).await?;
        let mut transformed_cancels = Vec::new();
        for order in orders.clone() {
            if order.symbol.base == symbol.base {
                let asset = self
                    .coin_to_asset
                    .token_ind
                    .get(&symbol.base)
                    .ok_or(qerror!("{} AssetNotFound", self.name()))?;
                transformed_cancels.push(CancelRequest {
                    asset: *asset as u32,
                    oid: order.id.parse()?,
                });
            }
        }
        let action = Actions::Cancel(BulkCancel {
            cancels: transformed_cancels,
        });
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        let mut result = quant_common::base::BatchOrderRsp::default();
        match info {
            ExchangeResponseStatus::Ok(exchange_response) => match exchange_response.data {
                Some(data) => {
                    for status in data.statuses {
                        match status {
                            define::action::ExchangeDataStatus::Resting(resting_order) => {
                                result.success_list.push(quant_common::base::SuccessOrder {
                                    id: Some(resting_order.oid.to_string()),
                                    cid: resting_order.cloid,
                                })
                            }
                            define::action::ExchangeDataStatus::Filled(filled_order) => {
                                result.success_list.push(quant_common::base::SuccessOrder {
                                    id: Some(filled_order.oid.to_string()),
                                    cid: filled_order.cloid,
                                })
                            }
                            _ => continue,
                        };
                    }
                    //进一步处理失败的订单
                    let orders_after = self.get_open_orders(symbol.clone()).await?;
                    if !orders_after.is_empty() {
                        orders_after.into_iter().for_each(|v| {
                            orders.iter().for_each(|item| {
                                if item.cid == v.cid {
                                    result.failure_list.push(FailOrder {
                                        id: Some(v.id.clone()),
                                        cid: v.cid.clone(),
                                        error_code: 0,
                                        error: "order failed".to_string(),
                                    });
                                }
                            })
                        });
                    } else {
                        orders.iter().for_each(|item| {
                            result.success_list.push(quant_common::base::SuccessOrder {
                                id: Some(item.id.clone()),
                                cid: item.cid.clone(),
                            })
                        })
                    }

                    Ok(result)
                }
                None => Err(qerror!("{:?}", exchange_response)),
            },
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    // 未知的错误
    // async fn transfer(&self, transfer: quant_common::base::Transfer) -> quant_common::Result<()> {
    //     if transfer.asset!="USDC"{
    //         return  Err(qerror!("{} not support transfer",transfer.asset))
    //     }
    //     let usdc = (transfer.amount).round() as u64;
    //     let to_perp = match transfer.from {
    //         quant_common::base::WalletType::Spot => match transfer.to {
    //             quant_common::base::WalletType::Spot =>return Ok(()),
    //             quant_common::base::WalletType::UsdtFuture => true,
    //             _ =>{
    //                 return  Err(qerror!("not support transfer from {:?} to {:?}",transfer.from,transfer.to));
    //             }
    //         },
    //         quant_common::base::WalletType::UsdtFuture => match transfer.to {
    //             quant_common::base::WalletType::Spot =>false,
    //             quant_common::base::WalletType::UsdtFuture => return Ok(()),
    //             _ =>{
    //                 return  Err(qerror!("not support transfer from {:?} to {:?}",transfer.from,transfer.to));
    //             }
    //         },
    //         _ =>{
    //             return  Err(qerror!("not support transfer from {:?} to {:?}",transfer.from,transfer.to));
    //         },
    //     };
    //     let action = Actions::UsdClassTransfer(UsdClassTransfer {
    //         hyperliquid_chain: if self.config.is_testnet{"Testnet"}else{"Mainnet"}.to_string(),
    //         amount: usdc.to_string(),
    //         to_perp,
    //         nonce: 0,
    //         signature_chain_id: U256::from(1337),//
    //     });
    //     let info:ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
    //     match info {
    //         ExchangeResponseStatus::Ok(_exchange_response) => Ok(()),
    //         ExchangeResponseStatus::Err(_) => Err(qerror!("{:?}",info)),
    //     }
    // }

    async fn get_bbo_ticker(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<quant_common::base::BboTicker> {
        let info = self.get_depth(symbol.clone(), None).await?;
        if info.bids.is_empty() || info.asks.is_empty() {
            return Err(qerror!("{}get bbo ticker err:{:?}", self.name(), info));
        }
        let item = quant_common::base::BboTicker {
            symbol,
            bid_price: info.bids[0].price,
            bid_qty: info.bids[0].amount,
            ask_price: info.asks[0].price,
            ask_qty: info.asks[0].amount,
            timestamp: info.timestamp,
        };
        Ok(item)
    }
    async fn get_depth(
        &self,
        symbol: quant_common::base::Symbol,
        limit: Option<u32>,
    ) -> quant_common::Result<quant_common::base::Depth> {
        if let Some(index) = self.coin_to_asset.get_spot(&symbol) {
            let input = if symbol.base == "PURR" {
                InfoRequest::L2Book {
                    coin: "PURR".to_string(),
                }
            } else {
                InfoRequest::L2Book { coin: index }
            };
            let rsp: L2SnapshotResponse = self.post(define::PATH_INFO, &input, false).await?;
            let mut result = quant_common::base::Depth {
                symbol,
                bids: vec![],
                asks: vec![],
                timestamp: rsp.time as i64,
            };
            if let Some(bids) = rsp.levels.first() {
                result.bids = bids.iter().map(|item| item.clone().into()).collect();
            }
            if let Some(asks) = rsp.levels.get(1) {
                result.asks = asks.iter().map(|item| item.clone().into()).collect();
            }
            return Ok(result.clone_limited(limit.unwrap_or(100) as usize));
        }
        Err(qerror!(
            "{} not support get symbol:{} depth",
            self.name(),
            symbol
        ))
    }

    async fn get_ticker(&self, symbol: Symbol) -> quant_common::Result<quant_common::base::Ticker> {
        let now_time = time_ms() as u64;
        if let Some(index) = self.coin_to_asset.get_spot(&symbol) {
            let input = if symbol.base == "PURR" {
                InfoRequest::CandleSnapshot {
                    req: CandleSnapshotRequest {
                        coin: "PURR/USDC".to_string(),
                        interval: "1d".to_string(),
                        start_time: now_time - 86400000,
                        end_time: now_time,
                    },
                }
            } else {
                InfoRequest::CandleSnapshot {
                    req: CandleSnapshotRequest {
                        coin: index,
                        interval: "1d".to_string(),
                        start_time: now_time - 86400000,
                        end_time: now_time,
                    },
                }
            };
            let rsp: Vec<CandlesSnapshotResponse> =
                self.post(define::PATH_INFO, &input, false).await?;
            if let Some(item) = rsp.first() {
                let mut item = quant_common::base::Ticker {
                    symbol,
                    timestamp: item.time_close as i64,
                    high: item.high.parse()?,
                    low: item.low.parse()?,
                    open: item.open.parse()?,
                    close: item.close.parse()?,
                    volume: item.vlm.parse()?,
                    quote_volume: item.vlm.parse()?,
                    change: 0.,
                    change_percent: 0.,
                };
                item.change = item.close - item.open;
                item.change_percent = item.change / item.open;
                return Ok(item);
            }
        }
        Err(qerror!(
            "{} get_ticker error||symbol={}",
            self.name(),
            symbol
        ))
    }

    async fn get_bbo_tickers(&self) -> quant_common::Result<Vec<quant_common::base::BboTicker>> {
        Err(qerror!("{} not support get_bbo_tickers", self.name()))
    }
    async fn get_position(
        &self,
        _symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<Vec<quant_common::base::Position>> {
        Err(qerror!("{} not support get_position", self.name()))
    }

    async fn get_positions(&self) -> quant_common::Result<Vec<quant_common::base::Position>> {
        Err(qerror!("{} not support get_positions", self.name()))
    }

    async fn get_tickers(&self) -> quant_common::Result<Vec<quant_common::base::Ticker>> {
        Err(qerror!("{} not support get_tickers", self.name()))
    }

    async fn get_funding_rates(&self) -> quant_common::Result<Vec<quant_common::base::Funding>> {
        Err(qerror!("{} 现货没有funding_rates", self.name()))
    }

    async fn set_leverage(
        &self,
        _symbol: quant_common::base::Symbol,
        _leverage: u8,
    ) -> quant_common::Result<()> {
        Err(qerror!("{} 现货没有设置杠杆接口", self.name()))
    }

    async fn is_dual_side(&self) -> quant_common::Result<bool> {
        Ok(false)
    }

    async fn set_dual_side(&self, is_dual_side: bool) -> quant_common::Result<()> {
        match is_dual_side {
            true => Err(qerror!("{} 现货没有双向持仓", self.name())),
            false => Ok(()),
        }
    }
}

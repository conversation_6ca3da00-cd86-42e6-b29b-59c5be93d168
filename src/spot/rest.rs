use quant_common::base::*;
use quant_common::*;

use bytes::Bytes;
use reqwest::Method;
use serde::de::DeserializeOwned;
use serde_json::Value as serdeValue;
use sha2::Digest;
use sha2::Sha512;
use sonic_rs::Value;
use sonic_rs::json;
use url::Url;

use crate::spot::model::*;

#[derive(Debug, Clone)]
pub struct GateSpot {
    url: Url,
    client: reqwest::Client,
    pub config: ExConfig,
}

impl Default for GateSpot {
    fn default() -> Self {
        let config = ExConfig::default();
        let client = new_reqwest_client();

        let host = config
            .host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(if config.is_testnet {
                REST_TEST_BASE_HOST.to_string()
            } else if config.is_colo {
                REST_COLO_BASE_HOST.to_string()
            } else {
                REST_BASE_HOST.to_string()
            });
        let mut url = format!("https://{host}");
        if host == "webws.gateio.live"
            || host == "ws.gate.io"
            || host == "ws.gateio.io"
            || host == "fx-webws.gateio.live"
        {
            url = format!("https://{REST_BASE_HOST}");
        }
        Self {
            url: url.parse().unwrap(),
            client,
            config,
        }
    }
}

impl GateSpot {
    pub async fn new(config: base::model::ExConfig) -> Self {
        let client = new_reqwest_client();

        let host = config
            .host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(if config.is_testnet {
                REST_TEST_BASE_HOST.to_string()
            } else if config.is_colo {
                REST_COLO_BASE_HOST.to_string()
            } else {
                REST_BASE_HOST.to_string()
            });
        let mut url = format!("https://{host}");
        if host == "webws.gateio.live"
            || host == "ws.gate.io"
            || host == "ws.gateio.io"
            || host == "fx-webws.gateio.live"
        {
            url = format!("https://{REST_BASE_HOST}");
        }
        Self {
            url: url.parse().unwrap(),
            client,
            config,
        }
    }
}

impl Rest for GateSpot {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, user_request: UserRequest) -> Result<Value> {
        // 解析请求方法
        let method = Method::from_bytes(user_request.method.as_bytes())?;
        // 解析请求路径
        let path = user_request.path.as_str();

        let params = user_request.query;
        let body = match user_request.body {
            Some(body) => body.to_string(),
            None => "".to_string(),
        };
        let query = match params {
            Some(params) => params.to_string(),
            None => "".to_string(),
        };

        let result = self
            .req::<Value>(method, path, &query, &body, user_request.auth)
            .await?;
        Ok(result)
    }

    async fn get_ticker(&self, symbol: base::model::Symbol) -> Result<base::model::Ticker> {
        let ser = serde_urlencoded::to_string(CurrencyPairReq::new(symbol.clone()))?;
        let tickers = self.get::<Vec<GateCandlestick>>(PATH_TICKER, &ser).await?;
        let ticker = tickers
            .first()
            .ok_or_else(|| qerror!("No ticker data received"))?;

        let change = ticker.close - ticker.open;
        let change_percent = (change / ticker.open) * 100.0;
        let ticker = Ticker {
            symbol,
            timestamp: ticker.timestamp * 1000,
            high: ticker.high,
            low: ticker.low,
            open: ticker.open,
            close: ticker.close,
            volume: ticker.volume,
            quote_volume: ticker.quote_volume,
            change: ticker.close - ticker.open,
            change_percent,
        };
        Ok(ticker)
    }

    async fn get_tickers(&self) -> Result<Vec<base::model::Ticker>> {
        let tickers = self.get::<Vec<GateTicker>>(PATH_TICKERS, "").await?;
        let mut tickers_ret: Vec<Ticker> = Vec::new();
        for ticker in tickers {
            let change = ticker.last / ticker.change_percentage;
            let open = ticker.last - change;
            let change_percent = ticker.change_percentage;
            let ticker = Ticker {
                symbol: ticker.currency_pair.into(),
                timestamp: time_ms(),
                high: ticker.high_24h,
                low: ticker.low_24h,
                open,
                close: ticker.last,
                volume: ticker.base_volume,
                quote_volume: ticker.quote_volume,
                change,
                change_percent,
            };
            tickers_ret.push(ticker);
        }
        Ok(tickers_ret)
    }

    async fn get_bbo_ticker(&self, symbol: base::model::Symbol) -> Result<base::model::BboTicker> {
        let gate_spot_symbol = GateSpotSymbol::from(symbol);
        let depth = self.depth(gate_spot_symbol.clone(), Some(1)).await?;
        Ok(depth.into_bbo_ticker(gate_spot_symbol))
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<base::model::BboTicker>> {
        unimplemented!("GateSpot does not support getting all BBO tickers")
    }

    async fn get_depth(
        &self,
        symbol: base::model::Symbol,
        limit: Option<u32>,
    ) -> Result<base::model::Depth> {
        let gate_symbol = GateSpotSymbol::from(symbol);
        let depth = self.depth(gate_symbol.clone(), limit).await?;
        Ok(depth.convert(gate_symbol, limit.unwrap_or(u32::MAX) as usize))
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<base::model::Instrument> {
        let currency_pair = self.currency_pair(symbol.into()).await?;
        Ok(currency_pair.into())
    }

    async fn get_instruments(&self) -> Result<Vec<base::model::Instrument>> {
        let currency_pairs = self.currency_pairs().await?;
        Ok(currency_pairs.into_iter().map(|x| x.into()).collect())
    }

    async fn get_funding_rates(&self) -> Result<Vec<base::model::Funding>> {
        unimplemented!()
    }

    //amount: 交易数量
    // type为limit时，指交易货币，即需要交易的货币，如BTC_USDT中指BTC。
    // type为market时，根据买卖不同指代不同
    // side : buy 指代计价货币，BTC_USDT中指USDT
    // side : sell 指代交易货币，BTC_USDT中指BTC
    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        if params.take_profit.is_some() || params.stop_loss.is_some() {
            warn!("GateSpot does not support take_profit and stop_loss");
        }
        let gate_order = GateSpotOrderReq::new(
            order,
            GateAccount::new(self.config.is_unified, &params.margin_mode),
        )?;
        let body = sonic_rs::to_string(&gate_order)?;
        let rsp: OrderRsp = self.post(PATH_ORDER, &body).await?;
        Ok(rsp.id)
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let gate_orders: Vec<GateSpotOrderReq> = orders
            .into_iter()
            .map(|o| {
                GateSpotOrderReq::new(
                    o,
                    GateAccount::new(self.config.is_unified, &params.margin_mode),
                )
            })
            .collect::<Result<Vec<_>>>()?;
        let body = sonic_rs::to_string(&gate_orders)?;

        let rsp: Vec<serde_json::Value> = self.post(PATH_BATCH_ORDER, &body).await?;

        let mut success_list = Vec::new();
        let mut failure_list = Vec::new();

        for order in rsp {
            let succeeded = order["succeeded"].as_bool().unwrap_or(false);
            let id = order["id"].as_str().unwrap_or_default().to_string();
            let text = order["text"].as_str().unwrap_or_default().to_string();

            if succeeded {
                success_list.push(SuccessOrder {
                    id: Some(id),
                    cid: Some(text),
                });
            } else {
                failure_list.push(FailOrder {
                    id: Some(id),
                    cid: Some(text),
                    error_code: -1,
                    error: "Order failed".to_string(),
                });
            }
        }

        Ok(BatchOrderRsp {
            success_list,
            failure_list,
        })
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        let oid = order.id.clone();
        let account = GateAccount::new(self.config.is_unified, &None);
        let req = GateSpotOrderReq::new(order, account)?;
        let rsp = self.amend_gate_order(oid, req).await?;
        Ok(rsp.id.to_string())
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        self.open_orders(GateAccount::new(self.config.is_unified, &None))
            .await
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        self.orders(
            PATH_ORDERS_HISTORY,
            symbol.into(),
            "open".to_string(),
            None,
            None,
            GateAccount::new(self.config.is_unified, &None),
        )
        .await
    }
    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        self.orders(
            PATH_ORDERS_HISTORY,
            symbol.into(),
            "finished".to_string(),
            Some(start_time / 1_000),
            Some(end_time / 1_000),
            GateAccount::new(self.config.is_unified, &None),
        )
        .await
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        let order_id = match order_id {
            OrderId::Id(id) => id,
            OrderId::ClientOrderId(cid) => cid,
        };
        let query_string = serde_urlencoded::to_string(CurrencyPairReq::new(symbol))?;
        let _rsp = self
            .req::<OrderRsp>(
                Method::DELETE,
                &format!("{PATH_ORDER}/{order_id}"),
                &query_string,
                "",
                true,
            )
            .await?;
        Ok(())
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
        let query_string = serde_urlencoded::to_string(CurrencyPairReq::new(symbol))?;
        let rsp: Vec<OrderRsp> = self
            .req(Method::DELETE, PATH_ORDER, &query_string, "", true)
            .await?;

        // 由于这个接口似乎只返回成功的订单，我们将所有返回的订单ID都放入 success_list
        let success_list = rsp
            .into_iter()
            .map(|r| SuccessOrder {
                id: Some(r.id.to_string()),
                cid: None,
            })
            .collect();

        Ok(BatchOrderRsp {
            success_list,
            failure_list: vec![], // 因为这个接口没有返回失败信息，所以failure_list为空
        })
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        let req = serde_urlencoded::to_string(CurrencyReq::new(symbol))?;
        let fee: AccountFee = self.get_signed(PATH_FEE, &req).await?;
        Ok(fee.into())
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        match self.config.is_unified {
            true => self.unified_balances().await,
            false => {
                let accounts: Vec<GateSpotBalance> = self.get_signed(PATH_ACCOUNT, "").await?;
                Ok(accounts.into_iter().map(|account| account.into()).collect())
            }
        }
    }

    async fn get_position(
        &self,
        _symbol: base::model::Symbol,
    ) -> Result<Vec<base::model::Position>> {
        unimplemented!("GateSpot does not support get_position")
    }

    async fn get_positions(&self) -> Result<Vec<base::model::Position>> {
        unimplemented!("GateSpot does not support get_positions")
    }

    async fn set_leverage(&self, _symbol: base::model::Symbol, _leverage: u8) -> Result<()> {
        unimplemented!("GateSpot does not support set_leverage")
    }

    async fn is_dual_side(&self) -> Result<bool> {
        unimplemented!("GateSpot does not support dual_side")
    }

    async fn set_dual_side(&self, _is_dual_side: bool) -> Result<()> {
        unimplemented!("GateSpot does not support set_dual_side")
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let oid_req = match order_id {
            OrderId::Id(id) => id,
            OrderId::ClientOrderId(cid) => cid,
        };
        let req_tail = format!("currency_pair={}_{}", symbol.base, symbol.quote);
        let path_get_order: String = format!("/api/v4/spot/orders/{oid_req}");
        let _resp: GateOrderRSP = self
            .req(Method::GET, &path_get_order, &req_tail, "", true)
            .await?;
        Ok(_resp.into())
    }

    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        // 检查 cids
        if cids.is_some() {
            return Err(qerror!("不支持通过 cid 取消订单"));
        }

        // 获取 symbol，如果为 None 则返回错误
        let symbol = symbol.ok_or_else(|| qerror!("symbol 是必填项"))?;

        // 获取 ids，如果为 None 则返回错误
        let ids = ids.ok_or_else(|| qerror!("ids 是必填项"))?;

        // 如果 ids 为空，直接返回空结果
        if ids.is_empty() {
            return Ok(BatchOrderRsp {
                success_list: vec![],
                failure_list: vec![],
            });
        }

        // 构建请求数据
        let req_vec: Vec<Value> = ids
            .into_iter()
            .map(|id| {
                json!({
                    "currency_pair": format!("{}_{}", symbol.base, symbol.quote),
                    "id": id,
                })
            })
            .collect();
        let req_string =
            sonic_rs::to_string(&req_vec).map_err(|e| qerror!("JSON序列化失败: {}", e))?;
        let res: serdeValue = self.post(PATH_BATCH_CANCEL_OIDS, &req_string).await?;

        let mut success_list = Vec::new();
        let mut failure_list = Vec::new();

        if let Some(array) = res.as_array() {
            for item in array {
                let id = item["id"].as_str().unwrap_or_default().to_string();
                let succeeded = item["succeeded"].as_bool().unwrap_or(false);

                if succeeded {
                    success_list.push(SuccessOrder {
                        id: Some(id),
                        cid: None,
                    });
                } else {
                    let error = item["message"]
                        .as_str()
                        .unwrap_or("Unknown error")
                        .to_string();
                    let error_code = match item["label"].as_str() {
                        Some("ORDER_NOT_FOUND") => 404,
                        _ => -1,
                    };

                    failure_list.push(FailOrder {
                        id: Some(id),
                        cid: None,
                        error_code,
                        error,
                    });
                }
            }
        }

        Ok(BatchOrderRsp {
            success_list,
            failure_list,
        })
    }

    async fn transfer_ext(&self, params: traits::TransferParams) -> Result<()> {
        self.transfer(params.transfer).await
    }

    async fn transfer(&self, _transfer: Transfer) -> Result<()> {
        let params = json!({
            "currency":_transfer.asset,
            "amount": _transfer.amount,
            "from": match _transfer.from {
                WalletType::UsdtFuture => "futures",
                WalletType::Spot => "spot",
                WalletType::Margin => "margin",
                _ => panic!("暂不支持从{:?}划转", _transfer.from)

            },
            "to": match _transfer.to {
                WalletType::UsdtFuture => "futures",
                WalletType::Spot => "spot",
                WalletType::Margin => "margin",
                _ => panic!("暂不支持划转到{:?}", _transfer.from)
            },
            "settle":_transfer.asset
        });
        let req_string =
            sonic_rs::to_string(&params).map_err(|e| qerror!("JSON序列化失败: {}", e))?;
        let _res: serdeValue = self.post(PATH_TRANSFER, &req_string).await?;
        Ok(())
    }

    async fn get_account_mode(&self) -> Result<AccountMode> {
        let account_detail: AccountModeRsp = self.get_signed(ACCOUNT_MODE, "").await?;
        Ok(account_detail.mode.into())
    }

    async fn set_account_mode(&self, mode: AccountMode) -> Result<()> {
        let params = match mode {
            AccountMode::Classic => json!({
                "mode": "classic"
            }),
            AccountMode::SpotAndSwap => json!({
                "mode": "spot_and_swap"
            }),
            AccountMode::MultiCurrency => json!({
                "mode": "multi_currency",
                "settings": {
                    "usdt_futures": true
                }
            }),
            AccountMode::Portfolio => json!({
                "mode": "portfolio",
                "settings": {
                    "spot_hedge": true
                }
            }),
        };
        let req_string =
            sonic_rs::to_string(&params).map_err(|e| qerror!("JSON序列化失败: {}", e))?;
        let _res: serdeValue = self
            .req(Method::PUT, ACCOUNT_MODE, "", &req_string, true)
            .await?;
        Ok(())
    }

    async fn get_account_info(&self) -> Result<AccountInfo> {
        // total_available_margin unified_account_total_equity unified_account_total
        let r: GateAccountInfo = self.get_signed(PATH_UNI_ACCOUNT, "").await?;
        Ok(r.to_acct_info())
        // unimplemented!("")
    }
}

impl GateSpot {
    pub(crate) async fn req_bytes(
        &self,
        method: &Method,
        path: &str,
        query: &str,
        body: &str,
        required_auth: bool,
    ) -> Result<Bytes> {
        let mut url = self.url.join(path).unwrap();
        if !query.is_empty() {
            url.set_query(Some(query));
        }
        let mut req = self.client.request(method.clone(), url.clone());
        if required_auth {
            let time = time().to_string();
            let sign = rest_gen_sign(
                method.as_str(),
                path,
                query,
                body,
                &time,
                &self.config.secret,
            );
            req = req
                .header("Accept", "application/json")
                .header("Content-Type", "application/json")
                .header("Timestamp", &time)
                .header("SIGN", &sign)
                .header("KEY", &self.config.key);
        };
        if !body.is_empty() {
            let body = body.to_owned();
            req = req.body(body);
        }

        let rsp = req.send().await?;
        let status = rsp.status();

        if !status.is_success() {
            let rsp = rsp.text().await?;
            let error = format!(
                "{url}{method} {path} {query} {body} {status} {}",
                &rsp[0..rsp.len().min(1024)]
            );
            let error = match sonic_rs::from_str::<GateErr>(&rsp) {
                Ok(r) => match r.label.as_str() {
                    "INSUFFICIENT_AVAILABLE" | "BALANCE_NOT_ENOUGH" => {
                        QuantError::insufficient_balance(error)
                    }
                    "ORDER_NOT_FOUND" => QuantError::order_not_found(error),
                    "ORDER_POC_IMMEDIATE" => QuantError::maker_only_rejected(error),
                    _ => qerror!("{error}"),
                },
                Err(e) => {
                    if status == reqwest::StatusCode::TOO_MANY_REQUESTS {
                        QuantError::network_error(error)
                    } else {
                        qerror!("{e}, {error} {rsp}")
                    }
                }
            };

            return Err(error);
        }

        let rsp = rsp.bytes().await?;
        Ok(rsp)
    }
    pub(crate) async fn req<RSP: DeserializeOwned>(
        &self,
        method: Method,
        path: &str,
        query: &str,
        body: &str,
        required_auth: bool,
    ) -> Result<RSP> {
        let rsp = self
            .req_bytes(&method, path, query, body, required_auth)
            .await?;
        sonic_rs::from_slice::<RSP>(&rsp).map_err(|err| {
            let rsp = std::str::from_utf8(&rsp[..rsp.len().min(1024)]).unwrap_or_default();
            qerror!("{method} {path} {query} {err} {rsp}")
        })
    }

    #[inline]
    async fn get<RSP: DeserializeOwned>(&self, path: &str, query: &str) -> Result<RSP> {
        self.req(Method::GET, path, query, "", false).await
    }

    pub(crate) async fn depth(
        &self,
        symbol: GateSpotSymbol,
        limit: Option<u32>,
    ) -> Result<DepthRest> {
        let query_string = serde_urlencoded::to_string(OrderBookReq {
            currency_pair: symbol,
            with_id: true,
            limit,
        })?;
        self.get::<DepthRest>(PATH_DEPTH, &query_string).await
    }
    #[inline]
    pub async fn post<RSP: DeserializeOwned>(&self, path: &str, body: &str) -> Result<RSP> {
        self.req(Method::POST, path, "", body, true).await
    }
    #[inline]
    pub async fn get_signed<RSP: DeserializeOwned>(&self, path: &str, query: &str) -> Result<RSP> {
        self.req(Method::GET, path, query, "", true).await
    }
}

impl GateSpot {
    // 读接口
    pub async fn unified_balances(&self) -> Result<Vec<Balance>> {
        let r: GateAccountInfo = self.get_signed(PATH_UNI_ACCOUNT, "").await?;
        // println!("-->{:?}\n", r);
        Ok(r.get_balances())
    }

    pub async fn get_server_time(&self) -> i64 {
        let url = self.url.join(PATH_CURRENCY_PAIRS).unwrap();
        let mut req = self.client.request(Method::GET, url);
        req = req
            .header("Accept", "application/json")
            .header("Content-Type", "application/json");
        let res = req.send().await;
        let res = match res {
            Ok(res) => res,
            Err(_) => return time_ms(),
        };
        let header = res.headers().get("X-Out-Time").unwrap().to_str().unwrap();
        header.parse::<i64>().unwrap() / 1000
    }

    pub async fn account_detail(&self) -> Result<AccountDetail> {
        let r: AccountDetail = self.get_signed(PATH_ACCOUNT_DETAIL, "").await?;
        Ok(r)
    }
    pub async fn open_orders(&self, account: GateAccount) -> Result<Vec<Order>> {
        let req = OpenOrdersReq { account };
        let req = serde_urlencoded::to_string(req)?;
        let rsp: Vec<OpenOrdersRsp> = self.get_signed(PATH_ORDERS_ALL_OPEN, &req).await?;
        let orders: Vec<Order> = rsp
            .into_iter()
            .flat_map(|os| os.orders.into_iter().map(|o| o.into()))
            .collect();
        Ok(orders)
    }

    pub async fn orders(
        &self,
        path: &str,
        currency_pair: GateSpotSymbol,
        status: String,
        from: Option<i64>,
        to: Option<i64>,
        account: GateAccount,
    ) -> Result<Vec<Order>> {
        let req = serde_urlencoded::to_string(GateSpotOrdersReq {
            status,
            currency_pair,
            from,
            to,
            account: Some(account),
        })?;

        let result: Vec<GateOrderRSP> = self.get_signed(path, &req).await?;
        Ok(result.into_iter().map(|item| item.into()).collect())
    }

    pub async fn usdt_account(&self) -> Result<GateSpotBalance> {
        self.get_signed(PATH_ACCOUNT, "").await
    }

    pub async fn currency_pair(&self, symbol: GateSpotSymbol) -> Result<CurrencyPair> {
        self.get(&format!("{PATH_CURRENCY_PAIRS}/{symbol}"), "")
            .await
    }

    pub async fn currency_pairs(&self) -> Result<Vec<CurrencyPair>> {
        let r: Vec<CurrencyPair> = self.get(PATH_CURRENCY_PAIRS, "").await?;
        Ok(r)
    }

    // 写接口
    pub async fn amend_gate_order(&self, oid: String, req: GateSpotOrderReq) -> Result<OrderRsp> {
        let req = GateSpotAmendOrderReq {
            price: req.price,
            amount: None,
            account: match req.account {
                GateAccount::CrossMargin | GateAccount::Unified => Some(GateAccount::CrossMargin),
                account => Some(account),
            },
        };
        let body = sonic_rs::to_string(&req)?;
        self.req(
            Method::PATCH,
            &format!("{PATH_ORDER}/{oid}"),
            "",
            &body,
            true,
        )
        .await
    }
}

fn rest_gen_sign(
    method: &str,
    path: &str,
    query_string: &str,
    body: &str,
    time: &str,
    secret: &str,
) -> String {
    use hmac::Mac;

    let mut hasher = Sha512::new();
    hasher.update(body.as_bytes());
    let digest = hasher.finalize();
    let body_hexdigest = format!("{digest:02x}");

    let s = format!("{method}\n{path}\n{query_string}\n{body_hexdigest}\n{time}");
    let mut mac = hmac::Hmac::<Sha512>::new_from_slice(secret.as_bytes()).unwrap();
    mac.update(s.as_bytes());
    let digest = mac.finalize().into_bytes();
    format!("{digest:02x}")
}

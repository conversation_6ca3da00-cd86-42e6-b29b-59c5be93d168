use quant_common::base::model::*;
use quant_common::base::traits::rest::Rest;
use quant_common::*;

use reqwest::{Client, Method, Response};
use serde::Serialize;
use sonic_rs::Value;
use url::Url;

use crate::spot::model::*;

#[derive(Debug, Clone)]
pub struct OkxSpot {
    url: Url,
    client: Client,
    config: ExConfig,
}

impl Default for OkxSpot {
    fn default() -> Self {
        let url = format!("{}{}{}", REST_BASE_URL, API_PREFIX, VERSION);
        OkxSpot {
            url: url
                .parse()
                .unwrap_or_else(|_| panic!("Failed to parse URL: {}", url)),
            client: new_reqwest_client(),
            config: ExConfig::default(),
        }
    }
}

impl OkxSpot {
    pub async fn new(config: ExConfig) -> Self {
        let default_host = if config
            .params
            .get("aws")
            .map(|v| v == "true")
            .unwrap_or(false)
        {
            REST_AWS_BASE_URL
        } else {
            REST_BASE_URL
        }
        .to_string();
        let host = config
            .host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(default_host);
        let url = format!("{}{}{}", host, API_PREFIX, VERSION);

        OkxSpot {
            url: url
                .parse()
                .unwrap_or_else(|_| panic!("Failed to parse URL: {}", url)),
            client: new_reqwest_client(),
            config,
        }
    }

    pub async fn get<T: Serialize>(
        &self,
        path: &str,
        is_auth: bool,
        params: Option<T>,
    ) -> Result<Value> {
        let params = params.map_or(Ok(None), |p| serialized_to_value(p).map(Some))?;
        self.request(UserRequest {
            method: Method::GET.to_string(),
            path: path.to_owned(),
            auth: is_auth,
            query: params,
            url: None,
            headers: None,
            body: None,
        })
        .await
    }

    pub async fn post<T: Serialize>(
        &self,
        path: &str,
        is_auth: bool,
        params: Option<T>,
    ) -> Result<Value> {
        let params = params.map_or(Ok(None), |p| serialized_to_value(p).map(Some))?;
        self.request(UserRequest {
            method: Method::POST.to_string(),
            path: path.to_owned(),
            auth: is_auth,
            query: None,
            url: None,
            headers: None,
            body: params,
        })
        .await
    }

    pub async fn instruments(&self, symbol: Option<Symbol>) -> Result<Vec<Instrument>> {
        let mut req = OkxInstrumentReq::default();
        if let Some(symbol) = symbol {
            req.set_symbol(symbol);
        }
        let result = self.get(PATH_INSTRUMENTS, false, req.into()).await?;
        let instruments = sonic_rs::from_value::<Vec<OkxInstrumentResp>>(&result)?;
        Ok(instruments.into_iter().map(|i| i.into()).collect())
    }

    pub async fn orders(&self, req: OkxOrdersReq) -> Result<Vec<Order>> {
        let path = PATH_ORDERS;
        let result = self.get(path, true, req.into()).await?;
        let orders = sonic_rs::from_value::<Vec<OkxOrderResp>>(&result)?;
        Ok(orders.into_iter().map(|o| o.into()).collect())
    }

    pub async fn history_orders(&self, req: OkxOrdersReq) -> Result<Vec<Order>> {
        let path = PATH_ORDERS_HISTORY;
        let result = self.get(path, true, req.into()).await?;
        let orders = sonic_rs::from_value::<Vec<OkxOrderResp>>(&result).map_err(|err| {
            let result = sonic_rs::to_string(&result).unwrap();
            qerror!("err: {err}, result: {result}",)
        })?;
        Ok(orders.into_iter().map(|o| o.into()).collect())
    }

    pub async fn account_balance(&self) -> Result<Vec<OkxBalancesResp>> {
        let value = self.get::<()>(PATH_ACCOUNT_BALANCE, true, None).await?;
        sonic_rs::from_value(&value).map_err(|err| {
            let rsp = sonic_rs::to_string(&value).unwrap();
            qerror!("转码失败: {err}, value: {rsp}")
        })
    }

    pub async fn post_order_org(&self, req: OkxPostOrderReq) -> Result<String> {
        let result = self.post(PATH_POST_ORDER, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxPostOrderResp>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No order id"))?;
        match resp.s_code {
            0 => Ok(resp.ord_id),
            _ => Err(qerror!("{:?}", resp)),
        }
    }

    pub async fn post_batch_order_org(&self, req: Vec<OkxPostOrderReq>) -> Result<BatchOrderRsp> {
        let result = self.post(PATH_POST_ORDER_BATCH, true, req.into()).await?;
        let resp: BatchOrderRespWrapper = sonic_rs::from_value(&result)?;
        Ok(resp.into())
    }
}

impl Rest for OkxSpot {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, user_request: UserRequest) -> Result<Value> {
        let req_url = self.url.join(&user_request.path)?;
        let method = Method::from_bytes(user_request.method.as_bytes())?;
        let resp = request_inner(
            &self.config,
            &self.client,
            req_url,
            method,
            user_request.query,
            user_request.body,
            user_request.auth,
            self.config.is_testnet,
        )
        .await?;
        let status = resp.status();
        if !status.is_success() {
            let err = format!("{:?}", resp.text().await?);
            return Err(qerror!("{err}"));
        }
        let rsp = resp.bytes().await?;
        let result = sonic_rs::from_slice::<OkxResp>(&rsp)?;
        match result.code {
            0 => (), // 全部成功
            1 => (), // 全部失败, 但可能内部有具体错误
            2 => (), // 部分成功, 但可能内部有具体错误
            _ => {
                let err = std::str::from_utf8(&rsp[..rsp.len().min(1024)])?;
                if matches!(result.code, ERROR_CODE_TOO_MANY_REQUESTS) {
                    return Err(Error::exchange_rate_limit(err));
                }
                return Err(qerror!("{err}"));
            }
        }
        Ok(result.data)
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        self.get_tickers()
            .await?
            .into_iter()
            .find(|t| t.symbol == symbol)
            .ok_or_else(|| qerror!("Okx spot/margin 不存在该交易对"))
    }

    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        let req = OkxTickersReq::default();
        let result = self.get(PATH_TICKERS, false, Some(req)).await?;
        let tickers = sonic_rs::from_value::<Vec<OkxTickerResp>>(&result)?;
        Ok(tickers.into_iter().map(|t| t.into()).collect())
    }

    async fn get_mark_price(&self, symbol: Option<Symbol>) -> Result<Vec<MarkPrice>> {
        let req = OkxMarkPriceReq::new(symbol);
        let result = self.get(PATH_MARK_PRICE, false, req.into()).await?;
        let mark_prices = sonic_rs::from_value::<Vec<OkxMarkPriceResp>>(&result)?;
        Ok(mark_prices.into_iter().map(|m| m.into()).collect())
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        self.get_bbo_tickers()
            .await?
            .into_iter()
            .find(|t| t.symbol == symbol)
            .ok_or_else(|| qerror!("Okx spot/margin 不存在该交易对"))
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        let req = OkxTickersReq::default();
        let result = self.get(PATH_TICKERS, false, req.into()).await?;
        let tickers = sonic_rs::from_value::<Vec<OkxTickerResp>>(&result)?;
        Ok(tickers.into_iter().map(|t| t.into()).collect())
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        let req = OkxDepthReq::new(symbol.clone(), limit);
        let result = self.get(PATH_DEPTH, false, req.into()).await?;
        let depths = sonic_rs::from_value::<Vec<OkxDepthResp>>(&result)?;
        let mut depth_resp = depths
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No depth"))?;
        depth_resp.set_symbol(symbol);
        let depth = depth_resp.into();
        Ok(depth)
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        let result = self.instruments(Some(symbol)).await?;
        let instrument = result
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No instrument"))?;
        Ok(instrument)
    }

    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        self.instruments(None).await
    }

    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        Err(qerror!("Okx Spot funding rates is not supported"))
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let req = OkxGetOrderByIdReq::new(symbol, order_id);
        let result = self.get(PATH_POST_ORDER, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxOrderResp>>(&result)?;
        let resp = resp.into_iter().next().ok_or_else(|| qerror!("No order"))?;
        Ok(resp.into())
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        let req = OkxOrdersReq::open_orders(OkxInstType::Spot, symbol);
        self.orders(req).await
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        let req = OkxOrdersReq::all_open_orders(OkxInstType::Spot);
        self.orders(req).await
    }

    async fn get_orders(&self, symbol: Symbol, start: i64, end: i64) -> Result<Vec<Order>> {
        let req = OkxOrdersReq::history_orders(OkxInstType::Spot, symbol, start, end);
        self.history_orders(req).await
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        if order.amount.is_none() {
            return Err(qerror!("Amount is required"));
        }
        let req = OkxPostOrderReq::new(order, params, false)?;
        self.post_order_org(req).await
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let mut req = vec![];
        for order in orders {
            let order = OkxPostOrderReq::new(order, params.clone(), false)?;
            req.push(order);
        }

        self.post_batch_order_org(req).await
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        if order.amount.is_none() {
            return Err(qerror!("Amount is required"));
        }
        let req = OkxPostAmendOrderReq::new(order);
        let result = self.post(PATH_POST_ORDER_AMEND, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxPostOrderResp>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No order id"))?;
        match resp.s_code {
            0 => Ok(resp.ord_id),
            _ => Err(qerror!("{:?}", resp)),
        }
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        let (order_id, client_id) = match order_id {
            OrderId::Id(id) => (Some(id), None),
            OrderId::ClientOrderId(cid) => (None, Some(cid)),
        };
        let req = OkxPostCancelOrderReq::new(symbol, order_id, client_id);
        let _ = self.post(PATH_POST_ORDER_CANCEL, true, req.into()).await?;
        Ok(())
    }

    async fn batch_cancel_order(&self, _symbol: Symbol) -> Result<BatchOrderRsp> {
        unimplemented!("Okx batch cancel order is not supported.Because it needs OrderId which is not supplied by trait")
    }

    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        if symbol.is_none() || (ids.is_none() && cids.is_none()) {
            // symbol is required, and at least one of ids or cids is required
            return Err(qerror!("symbol or ids and cids are required"));
        }
        let req = OkxBatchCancelOrderReq::new(symbol.unwrap(), ids, cids);
        let result = self.post(PATH_CANCEL_BATCH_ORDER, true, req.into()).await?;
        let resp = sonic_rs::from_value::<BatchOrderRespWrapper>(&result)?;
        Ok(resp.into())
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        let req = OkxTradeFeeReq::new(symbol);
        let result = self.get(PATH_TRADE_FEE, true, req.into()).await?;
        let fees = sonic_rs::from_value::<Vec<OkxTradeFeeResp>>(&result)?;
        let fee = fees
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No fee rate"))?;
        Ok(fee.into())
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        let balances: Vec<OkxBalancesResp> = self.account_balance().await?;
        let mut account_balance = vec![];
        if let Some(account) = balances.into_iter().next() {
            for balance in account.details {
                account_balance.push(balance.into());
            }
        }
        Ok(account_balance)
    }

    async fn get_position(&self, _symbol: Symbol) -> Result<Vec<Position>> {
        unimplemented!("Okx Spot position is not supported");
    }

    async fn get_positions(&self) -> Result<Vec<Position>> {
        unimplemented!("Okx Spot positions is not supported");
    }

    async fn set_leverage(&self, symbol: Symbol, leverage: u8) -> Result<()> {
        let req = OkxSetLeverageReq::new(symbol, leverage);
        let _ = self.post(PATH_SET_LEVERAGE, true, req.into()).await?;
        Ok(())
    }

    async fn is_dual_side(&self) -> Result<bool> {
        let result = self.get::<()>(PATH_ACCOUNT_CONFIG, true, None).await?;
        let config = sonic_rs::from_value::<Vec<OkxAccountConfig>>(&result)?;
        let config = config
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No account config"))?;
        Ok(config.is_dual_side())
    }

    async fn set_dual_side(&self, is_dual_side: bool) -> Result<()> {
        let mut req = OkxAccountConfig::default();
        req.set_position_mode(is_dual_side);
        let _ = self.post(PATH_SET_POSITION_MODE, true, req.into()).await?;
        Ok(())
    }

    /// 获取用户id
    async fn get_user_id(&self) -> Result<String> {
        let result = self.get::<()>(PATH_ACCOUNT_CONFIG, true, None).await?;
        let resp = sonic_rs::from_value::<Vec<OkxAccountConfig>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No user id"))?;
        Ok(resp.get_uid())
    }

    /// 获取充值地址
    async fn get_deposit_address(
        &self,
        ccy: String,
        chain: Option<Chain>,
        _amount: Option<f64>,
    ) -> Result<Vec<DepositAddress>> {
        let req = OkxDepositAddressReq::new(ccy);
        let result = self.get(PATH_DEPOSIT_ADDRESS, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxDepositAddressResp>>(&result)?;
        let mut deposit_addresses: Vec<DepositAddress> =
            resp.into_iter().map(|r| r.into()).collect();
        if let Some(chain) = chain {
            deposit_addresses.retain(|d| d.chain == Some(chain.clone()));
        }
        Ok(deposit_addresses)
    }

    /// 提币
    async fn withdrawal(&self, withdrwl_params: WithDrawlParams) -> Result<()> {
        let req = OkxWithdrawalReq::new(withdrwl_params);
        let _ = self.post(PATH_WITHDRAWAL, true, req.into()).await?;
        Ok(())
    }

    async fn get_account_info(&self) -> Result<AccountInfo> {
        let rsp = self.account_balance().await?;
        let account_info = rsp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No account info"))?;
        Ok(account_info.into())
    }
    // 现货开统一账户开关，被实现为 组合保证金模式
    // async fn get_margin_ratio(&self) -> Result<f64> {
    //     let rsp = self.account_balance().await?;
    //     info!("{rsp:#?}");
    //     match rsp.into_iter().next() {
    //         Some(account) => Ok(account.get_margin_ratio()),
    //         None => Err(qerror!("{} 返回了空数组", self.name())),
    //     }
    // }
}

#[allow(clippy::too_many_arguments)]
async fn request_inner(
    config: &ExConfig,
    client: &Client,
    url: Url,
    method: Method,
    req_query: Option<Value>,
    req_body: Option<Value>,
    auth: bool,
    is_testnet: bool,
) -> Result<Response> {
    let url_path = url.path().to_string();
    let mut req = client.request(method.clone(), url);

    let (mut body, mut query) = (None, None);
    if let Some(params) = req_query {
        req = req.query(&params);
        query = Some(params);
    }

    if let Some(params) = req_body {
        req = req.body(params.to_string());
        body = Some(params);
    }

    if is_testnet {
        req = req.header(HEADER_TESTNET, "1");
    }

    if auth {
        req = sign(config, method, &url_path, query, body, req)?;
    }
    req.send().await.map_err(|e| Error::network_error(e))
}

fn sign(
    config: &ExConfig,
    method: Method,
    req_path: &str,
    query: Option<Value>,
    body: Option<Value>,
    mut req: reqwest::RequestBuilder,
) -> Result<reqwest::RequestBuilder, Error> {
    let timestamp = quant_common::time_iso();
    let mut sign_msg = format!("{}{}{}", timestamp, method, req_path);
    if let Some(query) = query {
        sign_msg.push('?');
        let query = serde_urlencoded::to_string(&query)?;
        sign_msg.push_str(&query);
    }
    if let Some(body) = body {
        sign_msg.push_str(&body.to_string());
    }

    // info!("sign_msg: {sign_msg}");

    req = req.header(HEADER_CONTENT_TYPE, HEADER_JSON);
    req = req.header(HEADER_KEY, &config.key);
    req = req.header(HEADER_SIGN, crypto(&config.secret, sign_msg));
    req = req.header(HEADER_TIMESTAMP, timestamp);
    req = req.header(HEADER_PASSPHRASE, &config.passphrase);

    Ok(req)
}

#[inline]
pub(crate) fn crypto(secret: &str, msg: String) -> String {
    use hmac::Mac;
    let mut mac = hmac::Hmac::<sha2::Sha256>::new_from_slice(secret.as_bytes()).unwrap();
    mac.update(msg.as_bytes());
    let bytes = mac.finalize().into_bytes();
    base64::Engine::encode(&base64::prelude::BASE64_STANDARD, bytes)
}

#[inline]
fn serialized_to_value<T: Serialize>(t: T) -> Result<Value> {
    sonic_rs::to_value(&t).map_err(|e| qerror!("{:?}", e))
}

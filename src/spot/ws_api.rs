use super::model::*;
use super::rest::GateSpot;
use async_channel::Receiver;
use core::panic;
use futures_util::{SinkExt, StreamExt};
use hex;
use hmac::{Hmac, Mac};
use quant_common::base::model::ws_api::AsyncCmd;
use quant_common::*;
use quant_common::{base::*, connect_ws_with_proxy};
use rustc_hash::FxHashMap;
use serde_json::json;
use serde_json::{self, Value};
use sha2::Sha512;
use std::collections::VecDeque;
use std::time::{Duration, Instant};
use tokio::select;
use tokio_tungstenite::tungstenite::Message;

type HmacSha512 = Hmac<Sha512>;

#[allow(dead_code)]
#[derive(Debug, Default)]
pub struct GateSpotWsApi {
    url: String,
    config: ExConfig,
    rest: GateSpot,
    req_cache: FxHashMap<u64, CachedRequest>,
    req_queue: VecDeque<u64>, // 按时间顺序存储请求ID，用于超时检查
}

impl Clone for GateSpotWsApi {
    fn clone(&self) -> Self {
        Self {
            url: self.url.clone(),
            config: self.config.clone(),
            ..Default::default()
        }
    }
}

#[derive(Debug)]
struct CachedRequest {
    cmd: AsyncCmd,
    timestamp: Instant,
}

impl WebSocketAPI for GateSpotWsApi {
    fn exchange(&self) -> Exchange {
        model::Exchange::GateSpot
    }

    async fn run<H: AsyncResHandle>(
        mut self,
        account_id: usize,
        handler: H,
        mut rx: Receiver<(u64, AsyncCmd)>,
    ) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(account_id, &handler, &mut rx).await {
                Ok(_) => {
                    warn!("websocket断开连接, 尝试重连");
                }
                Err(e) => {
                    error!("websocket连接错误: {e:?}");
                    if time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            let err = qerror!("Gate现货websocket重试失败次数过多: {e}");
                            return Err(err);
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl GateSpotWsApi {
    pub async fn new(config: ExConfig) -> Self {
        let host = config
            .host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(if config.is_colo {
                WS_COLO_HOST.to_string()
            } else {
                WS_HOST.to_string()
            });
        let mut url = if config.is_testnet {
            format!("wss://{WS_TEST_HOST}/v4/ws/spot")
        } else {
            format!("wss://{host}/ws/v4/")
        };
        if (host == "webws.gateio.live"
            || host == "ws.gate.io"
            || host == "ws.gateio.io"
            || host == "fx-webws.gateio.live")
            && !config.is_testnet
        {
            url = format!("wss://{WS_HOST}/ws/v4/");
        }
        let rest = GateSpot::default();
        Self {
            config,
            url,
            rest,
            req_cache: FxHashMap::default(),
            req_queue: VecDeque::with_capacity(1000),
        }
    }

    fn cache_request_if_needed(&mut self, req_id: u64, cmd: &AsyncCmd) {
        match cmd {
            AsyncCmd::PlaceOrder(_) | AsyncCmd::AmendOrder(_) | AsyncCmd::CancelOrder(_) => {
                // 清理缓存，防止无限增长
                self.cleanup_cache();

                // 添加新请求到缓存
                self.req_cache.insert(
                    req_id,
                    CachedRequest {
                        cmd: cmd.clone(),
                        timestamp: Instant::now(),
                    },
                );
                self.req_queue.push_back(req_id);

                debug!(
                    "已缓存请求: req_id={}, cmd_type={}",
                    req_id,
                    self.get_cmd_type_str(cmd)
                );
            }
            _ => {}
        }
    }

    // 删除get_cmd_type方法，保留get_cmd_type_str用于日志记录
    fn get_cmd_type_str(&self, cmd: &AsyncCmd) -> &'static str {
        match cmd {
            AsyncCmd::PlaceOrder(_) => "PlaceOrder",
            AsyncCmd::CancelOrder(_) => "CancelOrder",
            AsyncCmd::AmendOrder(_) => "AmendOrder",
            AsyncCmd::BatchPlaceOrder(_) => "BatchPlaceOrder",
            AsyncCmd::BatchCancelOrder(_) => "BatchCancelOrder",
            AsyncCmd::BatchCancelOrderByIds(_) => "BatchCancelOrderByIds",
        }
    }

    // 清理超时和过多的缓存条目
    fn cleanup_cache(&mut self) {
        // 检查并移除超时请求
        let now = Instant::now();
        let timeout = Duration::from_secs(180);

        // 从队列前端检查，移除超时的请求
        while let Some(&req_id) = self.req_queue.front() {
            if let Some(cached_req) = self.req_cache.get(&req_id) {
                if now.duration_since(cached_req.timestamp) > timeout {
                    // 该请求已超时，移除
                    self.req_queue.pop_front();
                    self.req_cache.remove(&req_id);
                    warn!("请求超时已自动清理: req_id={req_id}");
                } else {
                    // 如果最早的请求没有超时，后面的更不会超时
                    break;
                }
            } else {
                // 队列中的ID不在缓存中，清理队列
                self.req_queue.pop_front();
            }
        }

        // 如果缓存大小超过上限，移除最早的请求
        while self.req_cache.len() > 5000 {
            if let Some(req_id) = self.req_queue.pop_front() {
                self.req_cache.remove(&req_id);
                warn!("缓存已达到最大大小，移除最早的请求: req_id={req_id}");
            } else {
                break;
            }
        }
    }
}

impl GateSpotWsApi {
    #[allow(unused_variables)]
    async fn run_inner<H: AsyncResHandle>(
        &mut self,
        account_id: usize,
        handler: &H,
        rx: &mut Receiver<(u64, AsyncCmd)>,
    ) -> Result<()> {
        let ws = connect_ws_with_proxy(&self.url).await?;
        let (mut writer, mut reader) = ws.split();
        let server_time = self.rest.get_server_time().await;
        let login_msg = ws_api_sign_server(
            self.config.clone(),
            "spot.login".to_string(),
            json!({}),
            server_time,
        );

        // 使用 Payload::Owned
        let login_json =
            serde_json::to_string(&login_msg).expect("Failed to serialize login message to JSON");
        let login_msg = Message::Text(login_json);
        writer.send(login_msg).await?;
        tokio::time::sleep(Duration::from_secs(1)).await; //wait for login
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(10));
        let mut login_interval = tokio::time::interval(tokio::time::Duration::from_secs(360));
        let cleanup_interval = Duration::from_secs(60 / 2);
        let mut last_cleanup = Instant::now();

        loop {
            if last_cleanup.elapsed() > cleanup_interval {
                self.cleanup_cache();
                last_cleanup = Instant::now();
            }
            select! {
                cmd = rx.recv() => {
                    debug!("收到请求");
                    let (req_id, cmd) = match cmd {
                        Ok((req_id, cmd)) => (req_id, cmd),
                        Err(e) => {
                            warn!("异步通道已关闭: {e:?}");
                            break;
                        }
                    };

                    self.cache_request_if_needed(req_id, &cmd);

                    match cmd.clone() {
                        AsyncCmd::PlaceOrder(place_cmd) =>{
                            let order_msg = self.get_send_order_msg(req_id, place_cmd);
                            let order_msg = Message::Text(serde_json::to_string(&order_msg).map_err(|e| qerror!("parse str_order_msg error: {:?}, json: {}", e, order_msg))?);
                            writer.send(order_msg).await?;
                        }

                        AsyncCmd::CancelOrder(cancel_cmd) =>{
                            let cancel_msg = self.get_cancel_order_msg(req_id, cancel_cmd);
                            let cancel_msg = Message::Text(serde_json::to_string(&cancel_msg).map_err(|e| qerror!("parse str_order_msg error: {:?}, json: {}", e, cancel_msg))?);
                            writer.send(cancel_msg).await?;
                        }

                        AsyncCmd::AmendOrder(amend_cmd) =>{
                            let amd_msg = self.get_amend_msg(req_id, amend_cmd);
                            let str_amand_msg = serde_json::to_string(&amd_msg).map_err(|e| qerror!("parse str_order_msg error: {:?}, json: {}", e, amd_msg))?;
                            let amend_msg = Message::Text(serde_json::to_string(&amd_msg).map_err(|e| qerror!("parse str_order_msg error: {:?}, json: {}", e, amd_msg))?);
                            writer.send(amend_msg).await?;
                        }

                        AsyncCmd::BatchCancelOrder(batch_cancel_cmd) =>{
                            error!("ws_api不支持批量撤单: {batch_cancel_cmd:?}");
                            handler.handle_batch_cancel_order(account_id, req_id, Err(qerror!("ws_api不支持批量撤单: {:?}", batch_cancel_cmd))).await?;
                        }

                        AsyncCmd::BatchPlaceOrder(batch_place_cmd) =>{
                            error!("ws_api不支持批量下单: {batch_place_cmd:?}");
                            handler.handle_post_batch_order(account_id, req_id, Err(qerror!("ws_api不支持批量下单: {:?}", batch_place_cmd))).await?;
                        }

                        AsyncCmd::BatchCancelOrderByIds(batch_cancel_order_by_ids_cmd) =>{
                            error!("ws_api不支持批量撤单: {batch_cancel_order_by_ids_cmd:?}");
                            handler.handle_batch_cancel_order(account_id, req_id, Err(qerror!("ws_api不支持批量撤单: {:?}", batch_cancel_order_by_ids_cmd))).await?;
                        }
                    }
                }

                rsp = reader.next() => {
                    let rsp = match rsp {
                        Some(Ok(x)) => x,
                        Some(Err(e)) => {
                            error!("receive err: {e:?}");
                            break;
                        }
                        None => {
                            error!("receive None");
                            break;
                        }
                    };
                    match &rsp {
                        Message::Text(text) => {
                            let json_val: Value = serde_json::from_str(text).map_err(|e| qerror!("parse ex_income_msg error: {:?}, json: {}", e, text))?;
                            let pretty_json = serde_json::to_string_pretty(&json_val).unwrap();
                            let channel = json_val.get("channel")
                                .and_then(|v| v.as_str())
                                .unwrap_or("");
                            if channel == "spot.pong" {
                                continue;
                            }

                            let req_id = json_val.get("request_id").and_then(|v| v.as_str()).unwrap_or("");
                            if !req_id.is_empty(){
                                let header = json_val.get("header").ok_or_else(||{qerror!("数据没有header:{}",json_val)})?;
                                let data = json_val.get("data").ok_or_else(||{qerror!("数据没有data:{}",json_val)})?;
                                let real_channel = header.get("channel").and_then(|v| v.as_str()).unwrap_or("");

                                if real_channel == "spot.order_place"{
                                    self.handle_order_resp(json_val.clone(), handler, account_id).await?;
                                }

                                if real_channel == "spot.order_cancel"{
                                    self.handle_order_cancel(json_val.clone(), handler, account_id).await?;
                                }

                                if real_channel == "spot.order_amend"{
                                    self.handle_order_amend(json_val.clone(), handler, account_id).await?;
                                }

                                if real_channel == "spot.login"{
                                    info!("login: {:?}", json_val.get("data"));
                                }
                            } else{
                                debug!("收到无法处理的信息: channel: {channel}, data: {json_val}");
                            }
                        },
                        Message::Binary(bin) => {
                            let string_data = String::from_utf8_lossy(bin);
                            let json_val: Value = serde_json::from_str(&string_data).map_err(|e| qerror!("parse ex_income_msg error: {:?}, json: {}", e, string_data))?;
                            debug!("收到二进制数据: {json_val}");
                        },
                        Message::Ping(_) => {
                            let pong = Message::Pong(vec![]);
                            writer.send(pong).await?;
                        },
                        Message::Close(_) => break,
                        _ => {},
                    }
                }

                _ = ping_interval.tick() => {
                    let ping_msg = Message::Text(serde_json::to_string(&json!({
                        "channel":"spot.ping",
                        "time":time_ms()
                    })).unwrap());
                    writer.send(ping_msg).await?;
                }

                _ = login_interval.tick() => {
                    let server_time = self.rest.get_server_time().await;
                    let login_msg = ws_api_sign_server(
                        self.config.clone(),
                        "futures.login".to_string(),
                        json!({}),
                        server_time,
                    );

                    // 使用 Payload::Owned
                    let login_json =
                        serde_json::to_string(&login_msg).expect("Failed to serialize login message to JSON");
                    let login_msg = Message::Text(login_json);
                    writer.send(login_msg).await?;
                }
            }
        }
        Ok(())
    }

    async fn handle_order_resp<H: AsyncResHandle>(
        &mut self,
        ex_income_data: Value,
        handler: &H,
        account_id: usize,
    ) -> Result<()> {
        let ack_val = ex_income_data
            .get("ack")
            .and_then(|v| v.as_bool())
            .unwrap_or(false);
        if ack_val {
            return Ok(());
        }

        let req_id = ex_income_data
            .get("request_id")
            .and_then(|v| v.as_str())
            .ok_or_else(|| qerror!("订单信息没有ID:{}", ex_income_data))?;
        let req_num: u64 = req_id
            .parse()
            .map_err(|e| qerror!("转换任务Id失败{},{}", e, ex_income_data))?;

        if let Some(cached_req) = self.req_cache.remove(&req_num) {
            let index = self.req_queue.iter().position(|&id| id == req_num);
            if let Some(index) = index {
                self.req_queue.remove(index);
            }
            match cached_req.cmd {
                AsyncCmd::PlaceOrder(place_order_cmd) => {
                    let status = ex_income_data
                        .get("header")
                        .and_then(|v| v.get("status"))
                        .and_then(|v| v.as_str())
                        .unwrap_or("400");
                    let mut order = place_order_cmd.order;
                    order.id = ex_income_data
                        .get("data")
                        .and_then(|v| v.get("result"))
                        .and_then(|v| v.get("id"))
                        .and_then(|v| v.as_str())
                        .unwrap_or("")
                        .to_string();
                    let order_resp = if status == "200" {
                        Ok(order.id.clone())
                    } else {
                        Err(qerror!(
                            "下单失败: {}",
                            ex_income_data
                                .get("data")
                                .unwrap_or(&Value::Null)
                                .get("errs")
                                .unwrap_or(&Value::Null)
                                .get("message")
                                .unwrap_or(&Value::Null)
                        ))
                    };
                    let result = quant_common::base::PlaceOrderResult {
                        result: order_resp,
                        order,
                    };
                    handler
                        .handle_post_order(account_id, req_num, result)
                        .await?;
                }
                _cmd => {}
            }
        }
        Ok(())
    }

    async fn handle_order_cancel<H: AsyncResHandle>(
        &mut self,
        ex_income_data: Value,
        handler: &H,
        account_id: usize,
    ) -> Result<()> {
        let req_id = ex_income_data
            .get("request_id")
            .and_then(|v| v.as_str())
            .ok_or_else(|| qerror!("订单信息没有ID:{}", ex_income_data))?;
        let req_num: u64 = req_id
            .parse()
            .map_err(|e| qerror!("转换Id失败{},{}", e, ex_income_data))?;

        if let Some(cached_req) = self.req_cache.remove(&req_num) {
            let index = self.req_queue.iter().position(|&id| id == req_num);
            if let Some(index) = index {
                self.req_queue.remove(index);
            }
            match cached_req.cmd {
                AsyncCmd::CancelOrder(cancel_cmd) => {
                    let status = ex_income_data
                        .get("header")
                        .and_then(|v| v.get("status"))
                        .and_then(|v| v.as_str())
                        .unwrap_or("400");
                    let order_resp = if status == "200" {
                        Ok(())
                    } else {
                        let err = ex_income_data
                            .get("data")
                            .unwrap_or(&Value::Null)
                            .get("errs")
                            .unwrap_or(&Value::Null)
                            .get("message")
                            .unwrap_or(&Value::Null);
                        let label = ex_income_data
                            .get("data")
                            .unwrap_or(&Value::Null)
                            .get("errs")
                            .unwrap_or(&Value::Null)
                            .get("label")
                            .unwrap_or(&Value::Null);
                        if label == "ORDER_NOT_FOUND" {
                            return Err(QuantError::order_not_found(err));
                        }
                        Err(qerror!("取消失败: {}", err))
                    };
                    let order_id = OrderId::Id(
                        ex_income_data
                            .get("data")
                            .and_then(|v| v.get("result"))
                            .and_then(|v| v.get("id"))
                            .and_then(|v| v.as_str())
                            .unwrap_or("")
                            .to_string(),
                    );
                    let result = quant_common::base::CancelOrderResult {
                        result: order_resp,
                        order_id,
                        symbol: cancel_cmd.symbol,
                    };
                    handler
                        .handle_cancel_order(account_id, req_num, result)
                        .await?;
                }
                _cmd => {}
            }
        }
        Ok(())
    }

    async fn handle_order_amend<H: AsyncResHandle>(
        &mut self,
        ex_income_data: Value,
        handler: &H,
        account_id: usize,
    ) -> Result<()> {
        let req_id = ex_income_data
            .get("request_id")
            .and_then(|v| v.as_str())
            .ok_or_else(|| qerror!("订单信息没有ID:{}", ex_income_data))?;
        let req_num: u64 = req_id
            .parse()
            .map_err(|e| qerror!("转换Id失败{},{}", e, ex_income_data))?;

        if let Some(cached_req) = self.req_cache.remove(&req_num) {
            let index = self.req_queue.iter().position(|&id| id == req_num);
            if let Some(index) = index {
                self.req_queue.remove(index);
            }
            match cached_req.cmd {
                AsyncCmd::AmendOrder(cancel_cmd) => {
                    let status = ex_income_data
                        .get("header")
                        .and_then(|v| v.get("status"))
                        .and_then(|v| v.as_str())
                        .unwrap_or("400");
                    let order_resp = if status == "200" {
                        Ok(cancel_cmd.id.clone())
                    } else {
                        Err(qerror!(
                            "修改失败: {}",
                            ex_income_data
                                .get("data")
                                .unwrap_or(&Value::Null)
                                .get("errs")
                                .unwrap_or(&Value::Null)
                                .get("message")
                                .unwrap_or(&Value::Null)
                        ))
                    };
                    let result = quant_common::base::AmendOrderResult {
                        order: cancel_cmd,
                        result: order_resp,
                    };
                    handler
                        .handle_amend_order(account_id, req_num, result)
                        .await?;
                }
                _cmd => {}
            }
        }
        Ok(())
    }

    fn get_send_order_msg(&self, req_id: u64, place_cmd: PlaceOrderCmd) -> serde_json::Value {
        // 现货默认使用现货账户
        let account = GateAccount::Spot;

        let req = GateSpotOrderReq::new(place_cmd.clone().order, account).unwrap();
        let mut params_json = json!(req);

        // 处理价格字段
        params_json["price"] = json!(match place_cmd.order.price {
            Some(price) => price.to_string(),
            None => "".to_string(),
        });

        if place_cmd.order.order_type == OrderType::Market {
            // remove time_in_force
            params_json["time_in_force"] = json!("ioc");
        }

        let req_id2: i64 = req_id as i64;
        ws_api_sign(
            self.config.clone(),
            "spot.order_place".to_string(),
            params_json,
            req_id2,
        )
    }

    fn get_cancel_order_msg(&self, req_id: u64, cancel_cmd: CancelOrderCmd) -> serde_json::Value {
        let symbol = GateSpotSymbol::from(cancel_cmd.clone().symbol);

        let mut req_params = json!({
            "currency_pair": symbol
        });

        match cancel_cmd.order_id {
            OrderId::ClientOrderId(cid) => {
                req_params["order_id"] = json!(cid);
            }
            OrderId::Id(id) => {
                req_params["order_id"] = json!(id);
            }
        }
        let req_id2: i64 = req_id as i64;
        ws_api_sign(
            self.config.clone(),
            "spot.order_cancel".to_string(),
            req_params,
            req_id2,
        )
    }

    fn get_amend_msg(&self, req_id: u64, order: Order) -> serde_json::Value {
        let symbol = GateSpotSymbol::from(order.symbol.clone());
        let order_id = if order.cid.is_some() {
            order.cid.unwrap()
        } else {
            order.id.clone()
        };
        let mut req_params = json!({
            "currency_pair": symbol,
            "order_id": order_id,
        });

        // 只能改价格或数量
        if order.amount.is_some() && order.price.is_some() {
            warn!("Amend order only can chose one of amount or price, use price default");
        }

        // 修改优先修改价格
        if let Some(price) = order.price {
            req_params["price"] = json!(price.to_string());
        } else if let Some(amount) = order.amount {
            req_params["amount"] = json!(amount.to_string());
        }

        let req_id2: i64 = req_id as i64;
        ws_api_sign(
            self.config.clone(),
            "spot.order_amend".to_string(),
            req_params,
            req_id2,
        )
    }
}

fn ws_api_sign(
    config: ExConfig,
    channel: String,
    payload: serde_json::Value,
    req_id: i64,
) -> serde_json::Value {
    let param_json = serde_json::to_string(&payload).unwrap();
    let ts_now = (time_ms() / 1000) + 1;
    let message = format!("{}\n{}\n{}\n{}", "api", channel, param_json, ts_now);
    let mut mac = HmacSha512::new_from_slice(config.secret.as_bytes())
        .expect("HMAC can take key of any size");
    mac.update(message.as_bytes());
    let signature = hex::encode(mac.finalize().into_bytes());

    let data_param = serde_json::json!({
        "time": ts_now,
        "channel": channel,
        "event": "api",
        "payload": {
            "req_id": req_id.to_string(),
            "api_key": config.key,
            "req_header": {"X-Gate-Channel-Id": "xxx"},
            "signature": signature,
            "timestamp": ts_now.to_string(),
            "req_param": payload,
        },
    });
    data_param
}

fn ws_api_sign_server(
    config: ExConfig,
    channel: String,
    payload: serde_json::Value,
    server_time: i64,
) -> serde_json::Value {
    let param_json = serde_json::to_string(&payload).unwrap();
    let ts_now = server_time / 1000;
    let message = format!("{}\n{}\n{}\n{}", "api", channel, param_json, ts_now);
    let mut mac = HmacSha512::new_from_slice(config.secret.as_bytes())
        .expect("HMAC can take key of any size");
    mac.update(message.as_bytes());
    let signature = hex::encode(mac.finalize().into_bytes());

    let data_param = serde_json::json!({
        "time": ts_now,
        "channel": channel,
        "event": "api",
        "payload": {
            "req_id": server_time.to_string(),
            "api_key": config.key,
            "req_header": {"X-Gate-Channel-Id": "xxx"},
            "signature": signature,
            "timestamp": ts_now.to_string(),
            "req_param": payload,
        },
    });
    data_param
}

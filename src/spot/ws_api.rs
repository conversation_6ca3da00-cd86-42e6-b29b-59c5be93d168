use async_channel::Receiver;
use fastwebsockets::{Frame, OpCode};
use quant_common::base::traits::ws_api::{AsyncResHandle, WebSocketAPI};
use quant_common::base::{AsyncCmd, ExConfig, Exchange};
use quant_common::fast_proxy::connect_ws_with_proxy;
use quant_common::{QuantError, Result, qerror, time};
use rustc_hash::FxHashMap;
use std::collections::VecDeque;
use std::time::{Duration, Instant};
use tokio::select;

use crate::spot::model::{WsApiReq, WsApiRsp};
use crate::util::is_ed25519_secret;

use super::model::{WS_API_TEST_URL, WS_API_URL};
use super::rest::BinanceSpot;

const MAX_RETRY: usize = 10;
const REQUEST_TIMEOUT_SECS: u64 = 60; // 请求超时时间（秒）
const MAX_CACHE_SIZE: usize = 1000; // 最大缓存条目数

#[derive(Debug)]
struct CachedRequest {
    cmd: AsyncCmd,
    timestamp: Instant,
}

#[derive(Default, Debug)]
pub struct BinanceSpotWsApi {
    url: String,
    config: ExConfig,
    req_cache: FxHashMap<u64, CachedRequest>,
    req_queue: VecDeque<u64>, // 按时间顺序存储请求ID，用于超时检查
}

impl Clone for BinanceSpotWsApi {
    fn clone(&self) -> Self {
        Self {
            url: self.url.clone(),
            config: self.config.clone(),
            ..Default::default()
        }
    }
}
impl BinanceSpotWsApi {
    pub async fn new(config: ExConfig) -> Self {
        let host = match &config.ws_api_host {
            Some(host) => host.as_str(),
            None => match config.is_testnet {
                true => WS_API_TEST_URL,
                false => WS_API_URL,
            },
        };
        let url = format!("wss://{host}/ws-api/v3");
        let _rest = BinanceSpot::new(config.clone()).await; // 隐式更新 SPECIAL_SYMBOLS, 即 price_multiplier

        Self {
            url,
            config,
            req_cache: FxHashMap::default(),
            req_queue: VecDeque::with_capacity(MAX_CACHE_SIZE),
        }
    }

    fn cache_request_if_needed(&mut self, req_id: u64, cmd: &AsyncCmd) {
        match cmd {
            AsyncCmd::PlaceOrder(_) | AsyncCmd::AmendOrder(_) | AsyncCmd::CancelOrder(_) => {
                // 清理缓存，防止无限增长
                self.cleanup_cache();

                // 添加新请求到缓存
                self.req_cache.insert(
                    req_id,
                    CachedRequest {
                        cmd: cmd.clone(),
                        timestamp: Instant::now(),
                    },
                );
                self.req_queue.push_back(req_id);

                debug!(
                    "已缓存请求: req_id={}, cmd_type={}",
                    req_id,
                    self.get_cmd_type_str(cmd)
                );
            }
            _ => {}
        }
    }

    // 清理超时和过多的缓存条目
    fn cleanup_cache(&mut self) {
        // 检查并移除超时请求
        let now = Instant::now();
        let timeout = Duration::from_secs(REQUEST_TIMEOUT_SECS);

        // 从队列前端检查，移除超时的请求
        while let Some(&req_id) = self.req_queue.front() {
            if let Some(cached_req) = self.req_cache.get(&req_id) {
                if now.duration_since(cached_req.timestamp) > timeout {
                    // 该请求已超时，移除
                    self.req_queue.pop_front();
                    self.req_cache.remove(&req_id);
                    warn!("请求超时已自动清理: req_id={}", req_id);
                } else {
                    // 如果最早的请求没有超时，后面的更不会超时
                    break;
                }
            } else {
                // 队列中的ID不在缓存中，清理队列
                self.req_queue.pop_front();
            }
        }

        // 如果缓存大小超过上限，移除最早的请求
        while self.req_cache.len() > MAX_CACHE_SIZE {
            if let Some(req_id) = self.req_queue.pop_front() {
                self.req_cache.remove(&req_id);
                warn!("缓存已达到最大大小，移除最早的请求: req_id={}", req_id);
            } else {
                break;
            }
        }
    }

    // 获取命令类型的字符串表示，用于日志记录
    fn get_cmd_type_str(&self, cmd: &AsyncCmd) -> &'static str {
        match cmd {
            AsyncCmd::PlaceOrder(_) => "PlaceOrder",
            AsyncCmd::CancelOrder(_) => "CancelOrder",
            AsyncCmd::AmendOrder(_) => "AmendOrder",
            AsyncCmd::BatchPlaceOrder(_) => "BatchPlaceOrder",
            AsyncCmd::BatchCancelOrder(_) => "BatchCancelOrder",
            AsyncCmd::BatchCancelOrderByIds(_) => "BatchCancelOrderByIds",
        }
    }

    async fn handle_error<T>(
        &self,
        handler: &T,
        req_id: u64,
        err: QuantError,
        account_id: usize,
        cmd: AsyncCmd,
    ) -> Result<()>
    where
        T: AsyncResHandle,
    {
        match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                let result = quant_common::base::PlaceOrderResult {
                    result: Err(err),
                    order: place_cmd.order,
                };
                handler
                    .handle_post_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let result = quant_common::base::CancelOrderResult {
                    result: Err(err),
                    order_id: cancel_cmd.order_id,
                    symbol: cancel_cmd.symbol,
                };
                handler
                    .handle_cancel_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchPlaceOrder(_) => {
                handler
                    .handle_post_batch_order(account_id, req_id, Err(err))
                    .await?;
            }
            AsyncCmd::AmendOrder(order) => {
                let result = quant_common::base::AmendOrderResult {
                    result: Err(err),
                    order,
                };
                handler
                    .handle_amend_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchCancelOrder(_) => {
                handler
                    .handle_batch_cancel_order(account_id, req_id, Err(err))
                    .await?;
            }
            AsyncCmd::BatchCancelOrderByIds(_) => {
                handler
                    .handle_batch_cancel_order(account_id, req_id, Err(err))
                    .await?;
            }
        }

        Ok(())
    }

    // 处理响应
    async fn handle_response<T>(
        &self,
        handler: &T,
        account_id: usize,
        req_id: u64,
        rsp: WsApiRsp,
        cmd: AsyncCmd,
    ) -> Result<()>
    where
        T: AsyncResHandle,
    {
        match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                let order_rsp = rsp.post_order_rsp();
                let result = quant_common::base::PlaceOrderResult {
                    result: order_rsp,
                    order: place_cmd.order,
                };
                handler
                    .handle_post_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let cancel_rsp = rsp.cancel_order_rsp();
                let result = quant_common::base::CancelOrderResult {
                    result: cancel_rsp.map(|_| ()),
                    order_id: cancel_cmd.order_id,
                    symbol: cancel_cmd.symbol,
                };
                handler
                    .handle_cancel_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::AmendOrder(order) => {
                let amend_rsp = rsp.cancel_replace_order_rsp();
                let result = quant_common::base::AmendOrderResult {
                    result: amend_rsp,
                    order,
                };
                handler
                    .handle_amend_order(account_id, req_id, result)
                    .await?;
            }
            cmd => {
                warn!(
                    "收到不支持的命令类型响应: req_id={}, cmd_type={:?}",
                    req_id,
                    self.get_cmd_type_str(&cmd)
                );
            }
        }

        Ok(())
    }
}

impl WebSocketAPI for BinanceSpotWsApi {
    fn exchange(&self) -> Exchange {
        Exchange::BinanceSpot
    }

    async fn run<H: AsyncResHandle>(
        mut self,
        account_id: usize,
        handler: H,
        mut rx: Receiver<(u64, AsyncCmd)>,
    ) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(&handler, &mut rx, account_id).await {
                Ok(_) => {}
                Err(e) => {
                    error!("websocket错误: {e:?}");
                    if time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            let err = qerror!("币安现货websocket重试失败次数过多: {e}");
                            return Err(err);
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl BinanceSpotWsApi {
    async fn run_inner<H: AsyncResHandle>(
        &mut self,
        handler: &H,
        rx: &mut Receiver<(u64, AsyncCmd)>,
        account_id: usize,
    ) -> Result<()> {
        let mut ws = connect_ws_with_proxy(&self.url).await?;
        if is_ed25519_secret(&self.config.secret) {
            let logon = WsApiReq::logon(0, self.config.key.clone(), &self.config.secret)?;
            let payload = serde_json::to_vec(&logon)?;
            ws.write_frame(Frame::text(payload.into())).await?;

            let rsp = ws.read_frame().await?;
            let rsp: WsApiRsp = match rsp.opcode {
                OpCode::Text => {
                    debug!("收到响应: {}", String::from_utf8_lossy(&rsp.payload));
                    sonic_rs::from_slice(&rsp.payload)?
                }
                _ => return Err(qerror!("收到意外的响应")),
            };

            rsp.into_logon_rsp()?;
        }

        // 定期清理缓存的时间间隔（秒）
        let cleanup_interval = Duration::from_secs(REQUEST_TIMEOUT_SECS / 2);
        let mut last_cleanup = Instant::now();

        loop {
            // 定期检查和清理过期请求
            if last_cleanup.elapsed() > cleanup_interval {
                self.cleanup_cache();
                last_cleanup = Instant::now();
            }

            select! {
                cmd = rx.recv() => {
                    debug!("收到请求");
                    let (req_id, cmd) = match cmd {
                        Ok((req_id, cmd)) => (req_id, cmd),
                        Err(_) => {
                            break;
                        }
                    };

                    // 缓存需要的请求类型（下单、修改订单、撤单）
                    self.cache_request_if_needed(req_id, &cmd);

                    let message = match self.req_to_message(req_id, cmd.clone()).await {
                        Ok(message) => message,
                        Err(e) => {
                            error!("发送订单请求错误: {:?}", e);
                            // 出错时移除并获取缓存的命令
                            if let Some(cached_req) = self.req_cache.remove(&req_id) {
                                // 从队列中也移除该请求ID
                                let index = self.req_queue.iter().position(|&id| id == req_id);
                                if let Some(index) = index {
                                    self.req_queue.remove(index);
                                }
                                self.handle_error(handler, req_id, e, account_id, cached_req.cmd).await?;
                            }
                            continue;
                        }
                    };

                    match ws.write_frame(message).await {
                        Ok(()) => {}
                        Err(e) => {
                            error!("发送订单请求错误: {:?}", e);
                            let err = qerror!("发送订单请求错误: {e}");
                            // 出错时移除并获取缓存的命令
                            if let Some(cached_req) = self.req_cache.remove(&req_id) {
                                // 从队列中也移除该请求ID
                                let index = self.req_queue.iter().position(|&id| id == req_id);
                                if let Some(index) = index {
                                    self.req_queue.remove(index);
                                }
                                self.handle_error(handler, req_id, err, account_id, cached_req.cmd).await?;
                            }
                            return Err(e.into());
                        }
                    }
                }
                rsp = ws.read_frame() => {
                    let rsp = rsp?;
                    let rsp: WsApiRsp = match rsp.opcode {
                        OpCode::Text => {
                            debug!("收到响应: {}", String::from_utf8_lossy(&rsp.payload));
                            match sonic_rs::from_slice(&rsp.payload) {
                                Ok(r) => r,
                                Err(e) => {
                                    error!("解析响应JSON失败: {:?}, 原始数据: {:?}", e, String::from_utf8_lossy(&rsp.payload));
                                    continue;
                                }
                            }
                        }
                        OpCode::Binary => {
                            debug!("收到二进制响应: {:?}", rsp.payload);
                            match sonic_rs::from_slice(&rsp.payload) {
                                Ok(r) => r,
                                Err(e) => {
                                    error!("解析二进制响应失败: {:?}", e);
                                    continue;
                                }
                            }
                        }
                        OpCode::Ping => {
                            ws.write_frame(Frame::pong(rsp.payload)).await?;
                            continue;
                        }
                        OpCode::Pong => continue,
                        OpCode::Close => {
                            return Err(qerror!("websocket断开连接"));
                        }
                        _ => continue,
                    };

                    let req_id = rsp.id();

                    // 直接从缓存中移除并获取对应的请求，一步到位
                    if let Some(cached_req) = self.req_cache.remove(&req_id) {
                        // 从队列中也移除该请求ID
                        let index = self.req_queue.iter().position(|&id| id == req_id);
                        if let Some(index) = index {
                            self.req_queue.remove(index);
                        }

                        let cmd_type = self.get_cmd_type_str(&cached_req.cmd);
                        debug!("处理响应: req_id={}, cmd_type={}", req_id, cmd_type);

                        // 使用辅助方法处理响应
                        self.handle_response(handler, account_id, req_id, rsp, cached_req.cmd).await?;
                    } else {
                        warn!("收到未知请求ID的响应: {}", req_id);
                    }
                }
            }
        }
        Ok(())
    }

    async fn req_to_message(&self, req_id: u64, cmd: AsyncCmd) -> Result<Frame> {
        let api_key = self.config.key.clone();
        let secret = &self.config.secret;
        let req = match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                WsApiReq::post_order(req_id, api_key, secret, place_cmd.order)?
            }
            AsyncCmd::CancelOrder(cancel_cmd) => WsApiReq::cancel_order(
                req_id,
                api_key,
                secret,
                cancel_cmd.symbol,
                cancel_cmd.order_id,
            )?,
            AsyncCmd::AmendOrder(order) => {
                WsApiReq::cancel_replace_order(req_id, api_key, secret, order)?
            }
            AsyncCmd::BatchPlaceOrder(_) => return Err(qerror!("交易所未支持批量下单")),
            AsyncCmd::BatchCancelOrder(_) => {
                return Err(qerror!("交易所未支持批量取消订单"));
            }
            AsyncCmd::BatchCancelOrderByIds(_) => {
                return Err(qerror!("交易所未支持通过ID批量取消订单"));
            }
        };

        debug!("发送请求: {}", serde_json::to_string(&req)?);
        Ok(Frame::text(serde_json::to_vec(&req)?.into()))
    }
}

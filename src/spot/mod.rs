use std::{collections::HashMap, str};

use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};

use crate::define::model::AssetInfo;

pub mod rest;
pub mod ws;
pub mod ws_api;

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct SpotMeta {
    pub tokens: Vec<SpotToken>,
    pub universe: Vec<OtherSpotToken>,
}

impl SpotMeta {
    pub fn add_pair_and_name_to_index_map(&self, coin_to_asset: &mut AssetInfo) {
        self.tokens.iter().for_each(|info| {
            let base = if info.name == "UBTC" {
                "BTC".to_string()
            } else {
                info.name.clone()
            };
            coin_to_asset.tokens.insert(base.clone(), info.clone());
            coin_to_asset.ind_token.insert(info.index, base);
        });

        for asset in self.universe.iter() {
            let Some(base) = coin_to_asset.ind_token.get(&asset.tokens[0]) else {
                continue;
            };
            // let Some(quote) = index_to_name.get(&asset.tokens[1]) else {
            //     continue;
            // };
            let base = if *base == "UBTC" { "BTC" } else { base };
            let spot_ind = 10000 + asset.index;
            coin_to_asset.token_ind.insert(base.to_string(), spot_ind);
            coin_to_asset.ind_token.insert(spot_ind, base.to_string());
            coin_to_asset
                .token_name
                .insert((asset.tokens[0], asset.tokens[1]), asset.name.clone());
            coin_to_asset
                .name_token
                .insert(asset.name.clone(), (asset.tokens[0], asset.tokens[1]));
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct SpotToken {
    pub name: String,
    pub sz_decimals: u32,
    pub wei_decimals: u32,
    pub index: usize,
    pub token_id: String,
    pub is_canonical: bool,
    pub evm_contract: Option<EvmContract>,
    pub full_name: Option<String>,
    pub deployer_trading_fee_share: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct EvmContract {
    pub address: String,
    #[serde(rename = "evm_extra_wei_decimals")]
    pub evm_extra_wei_decimals: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct OtherSpotToken {
    pub name: String,
    pub tokens: [usize; 2],
    pub index: usize,
    pub is_canonical: bool,
}

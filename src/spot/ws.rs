use std::{collections::HashSet, time::Duration};

use futures_util::{SinkExt, StreamExt};
use quant_common::{
    QuantError,
    base::{
        DepthWsParams, Exchange, KlineWsParams, SubscribeChannel, WsHandler,
        model::{ExConfig, MAX_RETRY, Reader, Subscribes, Writer},
        traits::ws::WebSocket,
    },
};
use quant_common::{Result, connect_ws_with_proxy, qerror, time};
use rustc_hash::FxHashMap;
use tokio::{task::Join<PERSON><PERSON><PERSON>, time::Instant};
use tokio_tungstenite::tungstenite::protocol::Message;

use crate::margin::rest::BinanceMargin;
use crate::spot::model::*;
use crate::spot::rest::BinanceSpot;

pub struct BinanceSpotWs {
    url: String,
    exchange: Exchange,
    rest: BinanceSpot,
    listen_key: String,
    local_depths: FxHashMap<BnSymbol, LocalDepthState>,
    config: ExConfig,
    delay: Instant,
    depth_limit: usize,
    margin: Option<BinanceMargin>,
}

impl BinanceSpotWs {
    pub async fn new(config: ExConfig) -> Self {
        let exchange = config.exchange;
        let host = match &config.ws_host {
            Some(host) => host.as_str(),
            None => match config.is_testnet {
                true => WS_TEST_URL,
                false => WS_URL,
            },
        };

        let url = format!("wss://{host}/stream");
        let rest = BinanceSpot::new(config.clone()).await;

        BinanceSpotWs {
            url,
            exchange,
            rest,
            listen_key: Default::default(),
            local_depths: Default::default(),
            depth_limit: 1,
            margin: None,
            config,
            delay: Instant::now(),
        }
    }

    pub async fn new_with_margin(config: ExConfig, margin: BinanceMargin) -> Self {
        let mut ws = Self::new(config).await;
        ws.margin = Some(margin);
        ws
    }
}

impl WebSocket for BinanceSpotWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(&subs).await {
                Ok(_) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    warn!("websocket disconnect, try to reconnect");
                }
                Err(e) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    error!("websocket connect error: {:?}", e);
                    if time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            let err = qerror!("BinanceSpot websocket retry failed too many times");
                            return Err(err);
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl BinanceSpotWs {
    async fn run_inner<H: WsHandler>(&mut self, subs: &Subscribes<H>) -> Result<()> {
        let handler = &subs.handler;
        let (mut writer, mut reader) = self.connect().await?;
        self.sub(&mut writer, &subs.channels).await?;
        handler.on_connected(self.exchange, subs.id).await?;
        let pong = Message::Pong(vec![]);
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(10));
        let mut refresh_interval = tokio::time::interval(tokio::time::Duration::from_secs(30 * 60));
        loop {
            tokio::select! {
                msg = reader.next() => {
                    let msg = match msg {
                        Some(Ok(x)) => x,
                        Some(Err(err)) => {
                            error!("receive err: {:?}", err);
                            break;
                        }
                        None => {
                            error!("receive None");
                            break;
                        }
                    };
                    match msg {
                        Message::Text(_) | Message::Binary(_) => self.handle_msg(msg, subs).await,
                        Message::Ping(p) => {
                            let message = Message::Pong(p);
                            writer.send(message).await?;
                        },
                        Message::Close(_) => break,
                        _ => {},
                    }
                }
                _ = ping_interval.tick() => {
                    writer.send(pong.clone()).await?;
                }
                _ = refresh_interval.tick() => {
                    if !self.listen_key.is_empty() {
                        // listen_key有效期延长至本次调用后60分钟
                        match &self.margin {
                            Some(margin) => margin.refresh_listen_key(&self.listen_key).await?,
                            None => self.rest.refresh_listen_key(&self.listen_key).await?,
                        }
                    }
                }
            }
        }
        Ok(())
    }

    async fn connect(&mut self) -> Result<(Writer, Reader), QuantError> {
        let ws_stream = connect_ws_with_proxy(&self.url).await?;
        let (writer, reader) = ws_stream.split();
        Ok((writer, reader))
    }

    async fn handle_msg_inner<H: WsHandler>(
        &mut self,
        msg: StreamData,
        subs: &Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        if msg.stream == self.listen_key {
            let msg = msg.data.get();
            let payload: AccountPayload = sonic_rs::from_str(msg)
                .map_err(|e| qerror!("parse msg payload error: {msg} {:?}", e))?;

            match payload {
                AccountPayload::OutboundAccountPosition(update) => {
                    for balance in update.balances {
                        handler.on_balance(balance.into(), subs.id).await?;
                    }
                }
                AccountPayload::ExecutionReport(report) => {
                    handler.on_order(report.into_order(), subs.id).await?;
                }
                AccountPayload::BalanceUpdate => (),
            }
        } else {
            let stream = msg.stream;
            let json = msg.data.get();

            if stream.ends_with(BOOK_TICKER_CHANNEL) {
                if subs.has_bbo() {
                    let bbo = sonic_rs::from_str::<BookTickerPayload>(json)?;
                    handler.on_bbo(bbo.into(), self.exchange).await?;
                }
            } else if stream.ends_with(DEPTH_CHANNEL) {
                let update: BnDepthUpdate = sonic_rs::from_str(json)?;
                self.handle_depth_update(update, subs).await?;
            } else if stream.ends_with(TRADE_CHANNEL) && subs.has_trade() {
                let trade = sonic_rs::from_str::<BnTrade>(json)?;
                handler.on_trade(trade.into(), self.exchange).await?;
            } else {
                match stream.split_once("@") {
                    Some((_symbol, channel)) if channel.starts_with(KLINE_CHANNEL) => {
                        let kline = sonic_rs::from_str::<WsKline>(json)?;
                        handler.on_kline(kline.into(), self.exchange).await?;
                    }
                    _ => warn!("未知的stream消息: {stream}"),
                }
            }
        }
        Ok(())
    }

    async fn handle_depth_update<H: WsHandler>(
        &mut self,
        update: BnDepthUpdate,
        subs: &Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;

        let local_depth_state = self
            .local_depths
            .entry(update.symbol.clone())
            .or_insert(LocalDepthState::Init);

        match local_depth_state {
            LocalDepthState::Init => {
                let symbol = update.symbol.clone();
                *local_depth_state = LocalDepthState::Pending {
                    pending: vec![update],
                    handle: depth_snapshot_task(&self.config, &mut self.delay, symbol).await,
                };
            }
            LocalDepthState::Working(local) => {
                match local.update(update) {
                    Ok(Some(min_level)) => {
                        if min_level < self.depth_limit {
                            let depth = local.depth(self.depth_limit);
                            handler.on_depth(depth, self.exchange).await?;
                        }
                    }
                    Ok(None) => (),
                    Err(err) => {
                        warn!("增量包失序: {err}");
                        *local_depth_state = LocalDepthState::Init;
                    }
                };
            }
            LocalDepthState::Pending { pending, handle } => {
                let symbol = update.symbol.clone();
                pending.push(update);

                if handle.is_finished() {
                    match create_local_depth(handle, &symbol, pending).await {
                        Ok(local) => {
                            let depth = local.depth(self.depth_limit);
                            *local_depth_state = LocalDepthState::Working(local);
                            handler.on_depth(depth, self.exchange).await?;
                        }
                        Err(err) => {
                            warn!("err: {err}");
                            let config = &self.config;
                            let delay = &mut self.delay;
                            *handle = depth_snapshot_task(config, delay, symbol).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    async fn handle_msg<H: WsHandler>(&mut self, data: Message, handler: &Subscribes<H>) {
        let msg = match &data {
            Message::Text(t) => sonic_rs::from_str::<StreamData>(t),
            Message::Binary(b) => sonic_rs::from_slice::<StreamData>(b),
            _ => unreachable!(),
        };
        match msg {
            Ok(msg) => {
                if let Err(e) = self.handle_msg_inner(msg, handler).await {
                    error!("handle message error: {:?}", e);
                }
            }
            Err(e) => {
                if sonic_rs::from_str::<WsId>(&data.to_string()).is_err() {
                    error!("parse message error: {:?}", e);
                }
            }
        }
    }

    pub(crate) async fn sub(
        &mut self,
        w: &mut Writer,
        channels: &[SubscribeChannel],
    ) -> Result<()> {
        let mut params = HashSet::new();

        for channel in channels {
            match channel {
                SubscribeChannel::Bbo(symbols) => {
                    for symbol in symbols.iter() {
                        let symbol: BnSymbol = symbol.clone().into();
                        let symbol = symbol.to_lowercase();
                        params.insert(format!("{symbol}{BOOK_TICKER_CHANNEL}"));
                    }
                }
                SubscribeChannel::Depth(DepthWsParams { symbols, levels }) => {
                    for symbol in symbols.iter() {
                        let symbol: BnSymbol = symbol.clone().into();
                        let symbol = symbol.to_lowercase();
                        params.insert(format!("{symbol}{DEPTH_CHANNEL}"));
                    }
                    self.depth_limit = *levels as usize;
                }
                SubscribeChannel::Trade(symbols) => {
                    for symbol in symbols.iter() {
                        let symbol: BnSymbol = symbol.clone().into();
                        let symbol = symbol.to_lowercase();
                        params.insert(format!("{symbol}{TRADE_CHANNEL}"));
                    }
                }
                SubscribeChannel::Order(_symbols) => {
                    if self.listen_key.is_empty() {
                        self.listen_key = match &self.margin {
                            Some(margin) => margin.create_listen_key().await?,
                            None => self.rest.create_listen_key().await?,
                        };
                    }

                    params.insert(self.listen_key.clone());
                }
                SubscribeChannel::Balance => {
                    if self.listen_key.is_empty() {
                        self.listen_key = match &self.margin {
                            Some(margin) => margin.create_listen_key().await?,
                            None => self.rest.create_listen_key().await?,
                        };
                    }
                    params.insert(self.listen_key.clone());
                }
                SubscribeChannel::Kline(KlineWsParams { symbols, interval }) => {
                    for symbol in symbols.iter() {
                        let symbol: BnSymbol = symbol.clone().into();
                        let symbol = symbol.to_lowercase();
                        params.insert(format!("{symbol}@{KLINE_CHANNEL}_{interval}"));
                    }
                }
                _ => {
                    return Err(qerror!(
                        "{} channel not support: {channel:?}",
                        self.exchange
                    ));
                }
            }
        }

        if params.is_empty() {
            warn!("sub params is empty");
            return Ok(());
        }
        let req = SubscriptionRequest {
            id: time(),
            method: SubReqMethod::Subscribe,
            params: params.into_iter().collect(),
        };
        let req_str = sonic_rs::to_string(&req)?;
        let message = Message::Text(req_str);
        w.send(message).await?;
        Ok(())
    }
}

#[derive(Default)]
enum LocalDepthState {
    #[default]
    Init,
    Pending {
        pending: Vec<BnDepthUpdate>,
        handle: JoinHandle<Result<BnRestDepth>>,
    },
    Working(LocalDepth),
}

async fn depth_snapshot_task(
    config: &ExConfig,
    delay: &mut Instant,
    symbol: BnSymbol,
) -> JoinHandle<Result<BnRestDepth>> {
    let config = config.clone();
    *delay = (*delay).max(Instant::now()) + Duration::from_secs_f32(2400. / 20. / 60. + 0.1); // 一次调用20，一分钟不超过2400权重
    let wait = *delay;

    tokio::spawn(async move {
        tokio::time::sleep_until(wait).await;
        let rest = BinanceSpot::new(config).await;
        rest.depth(symbol, Some(5000)).await // 按文档要求
    })
}

async fn create_local_depth(
    handle: &mut JoinHandle<Result<BnRestDepth>>,
    symbol: &BnSymbol,
    pending: &[BnDepthUpdate],
) -> Result<LocalDepth> {
    let depth = handle.await??;
    LocalDepth::new(symbol.clone().into(), depth, pending)
}

use std::collections::HashSet;

use ethers::signers::{LocalWallet, Signer};
use futures_util::{SinkExt, StreamExt};
use log::info;
use quant_common::{
    base::{
        balance, BboTicker, Depth, DepthEntry, ExConfig, Exchange, MarkPrice, OrderSide, QuoteCcy,
        SubscribeChannel, Subscribes, Symbol, WebSocket, WsHandler,
    },
    connect_ws_with_proxy, qerror, time, Result,
};
use rustc_hash::FxHashMap;
use tokio_tungstenite::tungstenite::Message;
use tracing::{error, warn};

use crate::define::{
    self,
    model::{AssetInfoFun, L2Book, Subscribe, Subscription, SubscriptionEvent},
};

use super::rest::HyperLiquidSpot;

#[derive(Debug, Clone)]
pub struct HyperLiquidSpotWs {
    exchange: Exchange,
    url: String,
    config: ExConfig,
    depth_levels: FxHashMap<Symbol, usize>,
    pub rest: HyperLiquidSpot,
    // local_depths:FxHashMap<String,LocalDepth>,
}
impl HyperLiquidSpotWs {
    pub async fn new(config: ExConfig) -> Self {
        let mut url = if config.is_testnet {
            define::TEST_WS_API_HYPERLIQUID
        } else {
            define::WS_API_HYPERLIQUID
        }
        .to_string();
        if let Some(host) = config.ws_host.clone() {
            url = format!("wss://{}/ws", host);
        }
        let rest = HyperLiquidSpot::new(config.clone()).await;
        Self {
            exchange: Exchange::HyperLiquidSpot,
            url,
            config,
            depth_levels: FxHashMap::default(),
            rest,
            // local_depths:FxHashMap::default(),
        }
    }
}
impl WebSocket for HyperLiquidSpotWs {
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: quant_common::base::WsHandler>(
        &mut self,
        subs: quant_common::base::Subscribes<H>,
    ) -> quant_common::Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(&subs).await {
                Ok(_) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    warn!("{} websocket disconnect, try to reconnect", self.exchange);
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
                Err(e) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    error!("{} websocket connect error: {:?}", self.exchange, e);
                    if time() - start < 60 {
                        retry += 1;
                        if retry > define::MAX_RETRY {
                            let err =
                                qerror!("{} websocket retry failed too many times", self.exchange);
                            return Err(err);
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl HyperLiquidSpotWs {
    async fn run_inner<H: WsHandler>(&mut self, subs: &Subscribes<H>) -> Result<()> {
        let ws = connect_ws_with_proxy(&self.url).await?;
        let (mut writer, mut reader) = ws.split();
        let handler = &subs.handler;
        handler.on_connected(self.exchange, subs.id).await?;
        let mut filter_set: HashSet<Symbol> = HashSet::new();
        for sub in &subs.channels {
            match sub {
                SubscribeChannel::MarkPrice(symbols) => {
                    for symbol in symbols.iter() {
                        if let Some(index) = self.rest.coin_to_asset.get_spot(symbol) {
                            let args = Subscription::ActiveAssetCtx { coin: index };
                            let sub_args = Subscribe::<Subscription> {
                                method: "subscribe".to_string(),
                                subscription: Some(args),
                            };
                            let msg: String = serde_json::to_string(&sub_args)?;
                            let sub_req = Message::text(msg);
                            writer.send(sub_req).await?;
                        }
                    }
                }
                SubscribeChannel::Bbo(symbols) => {
                    for symbol in symbols.iter() {
                        filter_set.insert(symbol.clone());
                    }
                }
                SubscribeChannel::Depth(depth_ws_params) => {
                    for symbol in depth_ws_params.symbols.iter() {
                        filter_set.insert(symbol.clone());
                        self.depth_levels
                            .insert(symbol.clone(), depth_ws_params.levels as usize);
                    }
                }
                SubscribeChannel::Trade(symbols) => {
                    for symbol in symbols.iter() {
                        if let Some(index) = self.rest.coin_to_asset.get_spot(symbol) {
                            let args = Subscription::Trades { coin: index };
                            let sub_args = Subscribe::<Subscription> {
                                method: "subscribe".to_string(),
                                subscription: Some(args),
                            };
                            let msg: String = serde_json::to_string(&sub_args)?;
                            let sub_req = Message::text(msg);
                            writer.send(sub_req).await?;
                        }
                    }
                }
                SubscribeChannel::Order(_symbols) => {
                    let wallet: LocalWallet = self.config.secret.parse()?;
                    let args = Subscription::OrderUpdates {
                        user: wallet.address(),
                    };
                    let sub_args = Subscribe::<Subscription> {
                        method: "subscribe".to_string(),
                        subscription: Some(args),
                    };
                    let msg: String = serde_json::to_string(&sub_args)?;
                    let sub_req = Message::text(msg);
                    writer.send(sub_req).await?;
                }
                SubscribeChannel::Balance => {
                    let wallet: LocalWallet = self.config.secret.parse()?;
                    let args = Subscription::WebData2 {
                        user: wallet.address(),
                    };
                    let sub_args = Subscribe::<Subscription> {
                        method: "subscribe".to_string(),
                        subscription: Some(args),
                    };
                    let msg: String = serde_json::to_string(&sub_args)?;
                    let sub_req = Message::text(msg);
                    writer.send(sub_req).await?;
                }
                _ => return Err(qerror!("{} is not supported sub:{:?}", self.exchange, sub)),
            }
        }
        if !filter_set.is_empty() {
            let symbols: Vec<Symbol> = filter_set.into_iter().collect();
            for symbol in symbols {
                if let Some(index) = self.rest.coin_to_asset.get_spot(&symbol) {
                    let args = Subscription::L2Book { coin: index };
                    let sub_args = Subscribe::<Subscription> {
                        method: "subscribe".to_string(),
                        subscription: Some(args),
                    };
                    let msg: String = serde_json::to_string(&sub_args)?;
                    let sub_req = Message::text(msg);
                    writer.send(sub_req).await?;
                }
            }
        }
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(25));
        loop {
            tokio::select! {
                _ =ping_interval.tick()=>{
                    let ping = Subscribe::<Subscription>{
                        method: "ping".to_string(),
                        subscription:None,
                     };
                    let sub_req= Message::text(sonic_rs::to_string(&ping)?);
                    writer.send(sub_req).await?;
                },
                rsp = reader.next() =>{
                    let msg = match rsp{
                        Some(data) => data?,
                        None => continue,
                    };
                    match msg {
                        Message::Text(_)|Message::Binary(_)=>{
                            self.handle_msg(msg,subs).await?;
                        }
                        Message::Close(close_frame) => {
                            warn!("websocket closed: {:?}", close_frame);
                            break
                        }
                        _=>(),
                    }
                }
            }
        }
        Ok(())
    }

    #[allow(clippy::extra_unused_lifetimes)]
    async fn handle_msg<'a, H: WsHandler>(
        &mut self,
        data: Message,
        subs: &Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        let event: SubscriptionEvent = match &data {
            Message::Text(t) => sonic_rs::from_str(t)?,
            Message::Binary(b) => sonic_rs::from_slice(b)?,
            _ => return Err(qerror!("invalid message type:{}", data)),
        };
        match event {
            SubscriptionEvent::Error => {
                //{"channel":"error","data":"Already subscribed: {\"type\":\"activeAssetCtx\",\"coin\":\"BTC\"}"}
                //如果错误中包含已以订阅，则该错误不算错误
                if data.clone().into_text()?.contains("Already subscribed") {
                    return Ok(());
                } else {
                    return Err(qerror!("{}:{}", self.exchange, data));
                }
            }
            SubscriptionEvent::Trades(trades) => {
                if subs.has_trade() {
                    for trade in trades.data {
                        if let Some(symbol) =
                            self.rest.coin_to_asset.from_spot_to_symbol(trade.coin)
                        {
                            let side = if trade.side == "B" {
                                OrderSide::Buy
                            } else {
                                OrderSide::Sell
                            };
                            let trade = quant_common::base::Trade {
                                id: trade.tid.to_string(),
                                symbol,
                                timestamp: trade.time as i64,
                                price: trade.px.parse()?,
                                amount: trade.sz.parse()?,
                                side,
                            };
                            handler.on_trade(trade, self.exchange).await?;
                        }
                    }
                }
            }
            SubscriptionEvent::L2Book(l2_book) => {
                if subs.has_depth() {
                    if let Ok(depth) = self.depth_handler(&l2_book).await {
                        let limit = self.depth_levels.get(&depth.symbol).unwrap_or(&100);
                        handler
                            .on_depth(depth.clone_limited(*limit), self.exchange)
                            .await?;
                    };
                }
                if subs.has_bbo() {
                    if let Ok(ticker) = self.bbo_handler(&l2_book).await {
                        handler.on_bbo(ticker, self.exchange).await?;
                    };
                }
            }
            SubscriptionEvent::OrderUpdates(order_updates) => {
                if subs.has_order() {
                    for order in order_updates.data {
                        if let Some(symbol) = self
                            .rest
                            .coin_to_asset
                            .from_spot_to_symbol(order.order.coin.clone())
                        {
                            handler.on_order(order.to_order(symbol), subs.id).await?;
                        };
                    }
                }
            }
            SubscriptionEvent::WebData2(web_data) => {
                if subs.has_balance() {
                    for asset_info in web_data.data.spot_state.balances {
                        let balance = asset_info.total.parse().unwrap_or(0.0);
                        let hlod = asset_info.hold.parse().unwrap_or(0.0);
                        if balance == 0.0 && hlod == 0.0 {
                            continue;
                        }
                        let balance = quant_common::base::Balance {
                            asset: asset_info.coin,
                            balance,
                            available_balance: 0.,
                            unrealized_pnl: asset_info.entry_ntl.parse().unwrap_or(0.0),
                        };
                        handler.on_balance(balance, subs.id).await?;
                    }
                }
            }
            SubscriptionEvent::ActiveSpotAssetCtx(active_asset_ctx) => {
                if subs.has_mark_price() {
                    match active_asset_ctx.data.ctx {
                        define::model::AssetCtx::Perps(_perps_asset_ctx) => return Ok(()),
                        define::model::AssetCtx::Spot(spot_asset_ctx) => {
                            if let Some(symbol) = self.rest.coin_to_asset.from_spot_to_symbol(
                                active_asset_ctx.data.coin.as_str().to_string(),
                            ) {
                                let mark_price = MarkPrice {
                                    symbol,
                                    price: spot_asset_ctx.shared.mark_px,
                                };
                                handler.on_mark_price(mark_price, self.exchange).await?;
                            };
                        }
                    }
                }
            }
            _ => return Ok(()),
        }
        Ok(())
    }
    async fn depth_handler(&mut self, book: &L2Book) -> Result<Depth> {
        if book.data.levels.len() < 2 {
            return Err(qerror!(
                "{} depth data not enough levels:{:?}",
                self.exchange,
                book.data.levels
            ));
        }
        if book.data.levels[0].is_empty() || book.data.levels[1].is_empty() {
            return Err(qerror!("invalid book:{:?}", book.data));
        }
        if let Some(symbol) = self
            .rest
            .coin_to_asset
            .from_spot_to_symbol(book.data.coin.clone())
        {
            let mut depth = Depth {
                symbol,
                timestamp: book.data.time as i64,
                asks: Vec::new(),
                bids: Vec::new(),
            };
            //每次都是全量数据
            let (mut asks, mut bids) = (Vec::new(), Vec::new());
            for bid in &book.data.levels[0] {
                bids.push(DepthEntry {
                    price: bid.px,
                    amount: bid.sz,
                });
            }
            for ask in &book.data.levels[1] {
                asks.push(DepthEntry {
                    price: ask.px,
                    amount: ask.sz,
                });
            }
            depth.asks = asks;
            depth.bids = bids;
            return Ok(depth);
        }
        Err(qerror!("not finger coin {}", book.data.coin))
    }
    async fn bbo_handler(&mut self, book: &L2Book) -> Result<BboTicker> {
        if book.data.levels.len() < 2 {
            return Err(qerror!("{} invalid book:{:?}", self.exchange, book.data));
        }
        if book.data.levels[0].is_empty() || book.data.levels[1].is_empty() {
            return Err(qerror!("{} invalid book:{:?}", self.exchange, book.data));
        }
        if let Some(symbol) = self
            .rest
            .coin_to_asset
            .from_spot_to_symbol(book.data.coin.clone())
        {
            let ticker = BboTicker {
                symbol,
                bid_price: book.data.levels[0][0].px,
                bid_qty: book.data.levels[0][0].sz,
                ask_price: book.data.levels[1][0].px,
                ask_qty: book.data.levels[1][0].sz,
                timestamp: book.data.time as i64,
            };
            return Ok(ticker);
        }
        Err(qerror!("not finger coin {}", book.data.coin))
    }
}

use crate::spot::model::*;
use crate::spot::rest::BybitSpot;
use futures_util::{SinkExt, StreamExt};
use hmac::{Hmac, Mac};
use quant_common::base::WebSocket;
use quant_common::base::model::*;
use quant_common::{Result, connect_ws_with_proxy, qerror, time_ms};
use rustc_hash::FxHashMap;
use sha2::Sha256;
use sonic_rs::{JsonValueTrait, Value};
use std::collections::HashMap;
use tokio_tungstenite::tungstenite::protocol::Message;
use tracing::{debug, error, info};

#[derive(Debug)]
pub struct BybitSpotWs {
    rest: BybitSpot,
    exchange: Exchange,
    _temp_ticker: HashMap<String, BbTickerSnapshot>,
    local_depth: FxHashMap<String, Depth>,
    topics: HashMap<String, bool>,
    depth_limit: usize,
}

impl BybitSpotWs {
    pub async fn new(config: ExConfig) -> Self {
        let rest = BybitSpot::new(config).await;

        BybitSpotWs {
            rest,
            _temp_ticker: HashMap::new(),
            local_depth: FxHashMap::default(),
            topics: HashMap::new(),
            exchange: Exchange::BybitSpot,
            depth_limit: 100,
        }
    }
}

impl WebSocket for BybitSpotWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = quant_common::time();
            match self.run_inner(&subs).await {
                Ok(_) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    info!("BybitSpot websocket disconnect");
                }
                Err(e) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    error!("BybitSpot websocket connect error: {:?}", e);
                    if quant_common::time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            return Err(qerror!("BybitSpot websocket retry failed too many times"));
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl BybitSpotWs {
    async fn run_inner<H: WsHandler>(&mut self, subs: &Subscribes<H>) -> Result<()> {
        let (mut writer, mut reader) = self.connect(subs.has_private()).await?;
        subs.handler.on_connected(self.exchange, subs.id).await?;
        self.sub(&mut writer, &subs.channels).await?;

        let ping = Message::Text(r###"{"op": "ping"}"###.to_string());
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(20));
        loop {
            tokio::select! {
                msg = reader.next() => {
                    let msg = match msg {
                        Some(Ok(x)) => x,
                        Some(Err(err)) => {
                            error!("receive err: {:?}", err);
                            break;
                        }
                        None => {
                            error!("receive None");
                            break;
                        }
                    };
                    match &msg {
                        Message::Text(body) =>  {
                            debug!("message body:{}", body);
                            let parsed : sonic_rs::Result<Value> = sonic_rs::from_str(body);
                            match parsed {
                                Ok(json) => {
                                    // 检查 JSON 是否包含 op: ping or subscribe
                                    if let Some(op) = json.get("op")
                                        && (op == "ping" || op == "pong" || op == "subscribe" || op == "auth") {
                                            // 如果是 ping or subscribe 响应，跳过
                                            continue;
                                        }
                                    // 检查 JSON 是否错误
                                    if let Some(success) = json.get("success")
                                        && success == false {
                                            return Err(qerror!("websocket command err:{}", json.get("ret_msg").unwrap()));
                                        }

                                    match self.handle_msg(msg, subs).await {
                                        Ok(()) => (),
                                        Err(e) => error!("bybit handle_msg err: {e}"),
                                    };
                                },
                                Err(e) => {
                                    // 如果解析失败，可能是非法 JSON，继续处理其他消息
                                    error!("Failed to parse message body as JSON: {},because:{}", body,e);
                                }
                            }
                        },
                        Message::Close(_) => break,
                        _ => {},
                    }
                }
                _ = ping_interval.tick() => {
                    writer.send(ping.clone()).await?;
                }
            }
        }
        Ok(())
    }

    async fn connect(
        &mut self,
        private: bool,
    ) -> Result<(Writer, Reader), quant_common::QuantError> {
        let config = self.rest.config.clone();
        let host: String =
            config
                .host
                .as_ref()
                .map(|h| h.to_string())
                .unwrap_or(if config.is_testnet {
                    WS_TEST_HOST.to_string()
                } else if config.is_colo {
                    WS_COLO_HOST.to_string()
                } else {
                    WS_HOST.to_string()
                });
        let url = if private {
            format!("wss://{host}/v5/private").to_string()
        } else {
            format!("wss://{host}/v5/public/linear").to_string()
        };

        debug!("websocker url:{}", url);
        let ws_stream = connect_ws_with_proxy(&url).await?;
        let (mut writer, reader) = ws_stream.split();
        // auth
        if private {
            let api_key = self.rest.config.key.clone();
            let timestamp = time_ms() + (24 * 60 * 60 * 1000);

            let message = format!("GET/realtime{timestamp}");

            let mut mac = Hmac::<Sha256>::new_from_slice(self.rest.config.secret.as_bytes())
                .expect("Invalid API secret length");

            mac.update(message.as_bytes());

            let result = mac.finalize();
            let signature = hex::encode(result.into_bytes());
            let json_data =
                format!(r#"{{"op":"auth","args":["{api_key}",{timestamp},"{signature}"]}}"#);
            debug!("send auth message {}", json_data);
            writer.send(Message::text(json_data)).await?;
        }
        Ok((writer, reader))
    }

    async fn handle_msg<H: WsHandler>(
        &mut self,
        data: Message,
        subs: &Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        match &data {
            Message::Text(t) => {
                debug!("real message body:{}", t);
                let result: sonic_rs::Result<Value> = sonic_rs::from_str(t);
                match result?.get("topic") {
                    None => {
                        debug!("topic 不能为空:{}", t);
                        return Err(qerror!("topic 不能为空"));
                    }
                    Some(topic) => {
                        let topic = topic.to_string().trim_matches('"').to_string();
                        match topic {
                            topic if topic.starts_with("orderbook.") => {
                                let new: BbOrderBookSnapshot =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let d = if new.r#type == "snapshot" {
                                    let init_depth: Depth = new.into();
                                    let depth = init_depth.clone_limited(self.depth_limit);
                                    self.local_depth.insert(topic, init_depth);
                                    depth
                                } else {
                                    let local_depth = self
                                        .local_depth
                                        .get_mut(&topic)
                                        .ok_or(qerror!("get BbOrderBookSnapshot err"))?;
                                    local_depth.update_depth(new.into());
                                    local_depth.clone_limited(self.depth_limit)
                                };

                                handler.on_depth(d, self.exchange).await?
                            }

                            topic if topic.starts_with("publicTrade.") => {
                                let snapshot: PublicTradeSnapshot =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let trades: Vec<_> =
                                    snapshot.data.into_iter().map(Trade::from).collect();
                                // TODO 待上流优化 api
                                for x in trades {
                                    handler.on_trade(x, self.exchange).await?
                                }
                            }

                            topic if topic.to_string().starts_with("order.spot") => {
                                debug!("order");
                                let snapshot: BbPriRsp<Vec<BbOrderData>> =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let orders: Vec<_> =
                                    snapshot.data.into_iter().map(Order::from).collect();
                                for x in orders {
                                    handler.on_order(x, subs.id).await?
                                }
                            }
                            topic if topic.to_string().starts_with("wallet") => {
                                debug!("wallet");
                                let mut snapshot: BbPriRsp<Vec<BbBalance>> =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let bb_balances =
                                    snapshot.data.pop().ok_or(qerror!("empty json"))?;
                                let balances: Vec<_> =
                                    bb_balances.coin.into_iter().map(Balance::from).collect();
                                for balance in balances {
                                    handler.on_balance(balance, subs.id).await?
                                }
                            }
                            _ => {}
                        };
                    }
                }
            }
            _ => unreachable!(),
        };
        Ok(())
    }
    pub(crate) async fn sub(
        &mut self,
        w: &mut Writer,
        channels: &[SubscribeChannel],
    ) -> Result<()> {
        for channel in channels {
            match channel {
                SubscribeChannel::Depth(params) => {
                    self.depth_limit = params.levels as usize;
                    for symbol in params.symbols.iter() {
                        let symbol = self
                            .rest
                            .symbol_map
                            .get(&symbol.to_string())
                            .unwrap()
                            .to_string();
                        let levels = match params.levels {
                            2..=50 => 50,
                            51..=200 => 200,
                            201..=500 => 500,
                            _ => 1,
                        };
                        self.topics
                            .insert(format!("orderbook.{levels}.{symbol}"), false);
                    }
                }
                SubscribeChannel::Trade(symbols) => {
                    for symbol in symbols.iter() {
                        let symbol = self
                            .rest
                            .symbol_map
                            .get(&symbol.to_string())
                            .unwrap()
                            .to_string();
                        self.topics.insert(format!("publicTrade.{symbol}"), false);
                    }
                }
                // private
                SubscribeChannel::Order(symbols) => {
                    let symbols = symbols
                        .iter()
                        .map(|symbol| {
                            self.rest
                                .symbol_map
                                .get(&symbol.to_string())
                                .unwrap()
                                .to_string()
                        })
                        .collect::<Vec<String>>()
                        .join(",")
                        .to_string();
                    let topic = format!("order.spot-{symbols}");
                    self.topics.insert(topic, true);
                }
                SubscribeChannel::Balance => {
                    self.topics.insert("wallet".to_string(), true);
                }
                _ => {
                    unimplemented!("bybit spot channel not support:{:?}", channel)
                }
            }
        }

        if self.topics.is_empty() {
            return Ok(());
        }

        let toplist = self
            .topics
            .keys()
            .map(|s| s.replace('_', ""))
            .map(|t| {
                if let Some((before_dash, _)) = t.split_once('-') {
                    return before_dash.to_string();
                }
                t
            })
            .collect::<Vec<_>>()
            .join(r##"", ""##);

        debug!("topics{:?}", self.topics);
        let msg = format!(r##"{{"op": "subscribe", "args": ["{toplist}"]}}"##);
        debug!("send subscribe message: {}", msg);

        w.send(Message::Text(msg)).await?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {}

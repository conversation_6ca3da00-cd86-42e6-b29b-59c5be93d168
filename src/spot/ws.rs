use crate::spot::model::*;
use quant_common::base::*;
use quant_common::order_utils::OrderTradeCache;
use quant_common::*;
// use common::{connect_ws_with_proxy, qerror, time, Result};
use futures_util::{SinkExt, StreamExt};
use rustc_hash::FxHashMap;
use sonic_rs::from_str;
use tokio_tungstenite::tungstenite::protocol::Message;

#[allow(unused_imports)]
use crate::{
    spot::model::{
        Channel, GateBookTiker, GateEvent, GateOrder, GateSpotSymbol, GateSpotWsBalance,
        OrderBooks, WS_HOST, new_ws_subscribe_req,
    },
    spot::rest::GateSpot,
};
#[derive(Debug)]
pub struct GateSpotWs {
    exchange: Exchange,
    url: String,
    is_weburl: bool,
    rest: GateSpot,
    limit: usize,
    ticker_cache: FxHashMap<Symbol, BboTicker>,
    order_cache: OrderTradeCache,
}

impl GateSpotWs {
    pub async fn new(config: ExConfig) -> Self {
        let host = config
            .host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(if config.is_colo {
                WS_COLO_HOST.to_string()
            } else {
                WS_HOST.to_string()
            });
        let mut url = if config.is_testnet {
            format!("wss://{WS_TEST_HOST}/v4/ws/spot")
        } else {
            format!("wss://{host}/ws/v4/")
        };
        let mut is_weburl = false;
        let mut config = config.clone();
        // v3
        if host == "webws.gateio.live"
            || host == "ws.gate.io"
            || host == "ws.gateio.io"
            || host == "fx-webws.gateio.live"
        {
            is_weburl = true;
            config.host = Some(WS_HOST.to_string());
            url = format!("wss://{host}/v3/");
        }
        let rest = GateSpot::new(config.clone()).await;

        GateSpotWs {
            exchange: config.exchange,
            is_weburl,
            url,
            rest,
            limit: usize::MAX,
            ticker_cache: FxHashMap::default(),
            order_cache: OrderTradeCache::default(),
        }
    }
}
impl WebSocket for GateSpotWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: base::Subscribes<H>) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(&subs).await {
                Ok(_) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    warn!("websocket disconnect");
                }
                Err(e) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    error!("websocket connect error: {e:?}");
                    if time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            return Err(qerror!(
                                "GateSwapWs websocket retry failed too many times"
                            ));
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl GateSpotWs {
    async fn run_inner<H: WsHandler>(&mut self, subs: &base::Subscribes<H>) -> Result<()> {
        let handler = &subs.handler;
        handler.on_connected(self.exchange, subs.id).await?;

        // 根据需要建立连接
        let (mut writer_v3, mut reader_v3) = if self.is_weburl && subs.has_public() {
            let (mut writer, reader) = self.connect().await?;
            self.sub(&mut writer, &subs.channels, false).await?; // v3
            (Some(writer), Some(reader))
        } else {
            (None, None)
        };

        let (mut writer_v4, mut reader_v4) =
            if (!self.is_weburl && subs.has_public()) || subs.has_private() {
                let (mut writer, reader) = self.connectv4().await?;

                if !self.is_weburl && subs.has_public() {
                    self.sub(&mut writer, &subs.channels, false).await?;
                }

                if !self.is_weburl && subs.has_private() {
                    self.sub(&mut writer, &subs.channels, true).await?;
                }

                if self.is_weburl && subs.has_private() {
                    self.sub(&mut writer, &subs.channels, true).await?;
                }

                (Some(writer), Some(reader))
            } else {
                (None, None)
            };

        let ping = Message::Ping(vec![]);
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(10));

        loop {
            tokio::select! {
                // 处理 v3 读取
                v3_msg = async {
                    if let Some(ref mut reader) = reader_v3 {
                        reader.next().await
                    } else {
                        std::future::pending::<Option<Result<Message, tokio_tungstenite::tungstenite::Error>>>().await
                    }
                }, if reader_v3.is_some() => {
                    match v3_msg {
                        Some(Ok(msg)) => {
                            match &msg {
                                Message::Text(_) | Message::Binary(_) => {
                                    self.handle_msg_v3(msg, subs).await;
                                }
                                Message::Ping(_) => {
                                    if let Some(ref mut writer) = writer_v3 {
                                        writer.send(Message::Pong(vec![])).await?;
                                    }
                                }
                                Message::Close(_) => break,
                                _ => {}
                            }
                        }
                        Some(Err(err)) => {
                            error!("v3 receive err: {err:?}");
                            break;
                        }
                        None => {
                            error!("v3 receive None");
                            break;
                        }
                    }
                }

                // 处理 v4 读取
                v4_msg = async {
                    if let Some(ref mut reader) = reader_v4 {
                        reader.next().await
                    } else {
                        std::future::pending::<Option<Result<Message, tokio_tungstenite::tungstenite::Error>>>().await
                    }
                }, if reader_v4.is_some() => {
                    match v4_msg {
                        Some(Ok(msg)) => {
                            match &msg {
                                Message::Text(_) | Message::Binary(_) => {
                                    self.handle_msg_v4(msg, subs).await;
                                }
                                Message::Ping(_) => {
                                    if let Some(ref mut writer) = writer_v4 {
                                        writer.send(Message::Pong(vec![])).await?;
                                    }
                                }
                                Message::Close(_) => break,
                                _ => {}
                            }
                        }
                        Some(Err(err)) => {
                            error!("v4 receive err: {err:?}");
                            break;
                        }
                        None => {
                            error!("v4 receive None");
                            break;
                        }
                    }
                }

                // 发送心跳
                _ = ping_interval.tick() => {
                    // 只给活跃的连接发送心跳
                    if let Some(ref mut writer) = writer_v3 {
                        writer.send(ping.clone()).await?;
                    }
                    if let Some(ref mut writer) = writer_v4 {
                        writer.send(ping.clone()).await?;
                    }
                }
            }
        }
        Ok(())
    }

    async fn connect(&mut self) -> Result<(Writer, Reader), QuantError> {
        // let (ws_stream, _) = connect_async(self.url).await?;
        let ws_stream = connect_ws_with_proxy(&self.url).await?;
        let (writer, reader) = ws_stream.split();
        Ok((writer, reader))
    }

    async fn connectv4(&mut self) -> Result<(Writer, Reader), QuantError> {
        let url = if self.is_weburl {
            format!("wss://{WS_HOST}/ws/v4/")
        } else {
            self.url.clone()
        };
        let ws_stream = connect_ws_with_proxy(&url).await?;
        let (writer, reader) = ws_stream.split();
        Ok((writer, reader))
    }

    fn change_ticker(&mut self, order_book: Depth) -> bool {
        let symbol = order_book.symbol.clone();
        let ticker = self.ticker_cache.get(&symbol);
        let new_ticker = BboTicker {
            symbol: order_book.symbol.clone(),
            bid_price: order_book
                .bids
                .first()
                .unwrap_or(&DepthEntry::default())
                .price,
            bid_qty: order_book
                .bids
                .first()
                .unwrap_or(&DepthEntry::default())
                .amount,
            ask_price: order_book
                .asks
                .first()
                .unwrap_or(&DepthEntry::default())
                .price,
            ask_qty: order_book
                .asks
                .first()
                .unwrap_or(&DepthEntry::default())
                .amount,
            timestamp: order_book.timestamp,
        };

        match ticker {
            Some(old_ticker) => {
                if old_ticker.bid_price != new_ticker.bid_price
                    || old_ticker.ask_price != new_ticker.ask_price
                    || old_ticker.ask_qty != new_ticker.ask_qty
                    || old_ticker.bid_qty != new_ticker.bid_qty
                {
                    self.ticker_cache.insert(symbol, new_ticker);
                    return true;
                }
            }
            None => {
                self.ticker_cache.insert(symbol, new_ticker);
                return true;
            }
        }
        false
    }

    async fn handle_msg_inner_v3<H: WsHandler>(
        &mut self,
        channel: ChannelV3Update,
        params: &[RawValue],
        subs: &base::Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        match channel {
            ChannelV3Update::OrderBookUpdate => {
                let raw_value = params.get(1).ok_or_else(|| {
                    qerror!("params数组越界: 需要至少2个元素，但只有 {}", params.len())
                })?; // bid ask ts {"id":366517800,"bids":[["0.002972","704177.5"]],"asks":[["0.002981","464489.3"]],"current":1744618221.64,"update":1744618221.51}
                let order_book = from_str::<OrderBooksV3>(raw_value.get()).map_err(|e| {
                    qerror!("parse OrderBook error: {:?}, json: {}", e, raw_value.get())
                })?;
                let symbol = params.get(2).ok_or_else(|| {
                    qerror!("params数组越界: 需要至少3个元素，但只有 {}", params.len())
                })?;
                let gate_symbol = from_str::<GateSpotSymbol>(symbol.get()).map_err(|e| {
                    qerror!(
                        "parse GateSpotSymbol error: {:?}, json: {}",
                        e,
                        symbol.get()
                    )
                })?;
                let order_book: Depth = order_book.into_depth(gate_symbol.clone());
                if subs.has_bbo() && self.change_ticker(order_book.clone()) {
                    handler
                        .on_bbo(
                            BboTicker {
                                symbol: order_book.symbol.clone(),
                                bid_price: order_book
                                    .asks
                                    .first()
                                    .unwrap_or(&DepthEntry::default())
                                    .price,
                                bid_qty: order_book
                                    .asks
                                    .first()
                                    .unwrap_or(&DepthEntry::default())
                                    .amount,
                                ask_price: order_book
                                    .bids
                                    .first()
                                    .unwrap_or(&DepthEntry::default())
                                    .price,
                                ask_qty: order_book
                                    .bids
                                    .first()
                                    .unwrap_or(&DepthEntry::default())
                                    .amount,
                                timestamp: order_book.timestamp,
                            },
                            self.exchange,
                        )
                        .await?;
                }
                if subs.has_depth() {
                    handler
                        .on_depth(order_book.truncate_limited(self.limit), self.exchange)
                        .await?;
                }
            }
            ChannelV3Update::TradesUpdate => {
                if !subs.has_trade() {
                    return Ok(());
                }
                let raw_value = params.get(1).ok_or_else(|| {
                    qerror!("params数组越界: 需要至少2个元素，但只有 {}", params.len())
                })?;
                let symbol = params.first().ok_or_else(|| {
                    qerror!("params数组越界: 需要至少2个元素，但只有 {}", params.len())
                })?;
                let trade = from_str::<Vec<TradeV3>>(raw_value.get()).map_err(|e| {
                    qerror!("parse Trades error: {:?}, json: {}", e, raw_value.get())
                })?;
                let gate_symbol = from_str::<GateSpotSymbol>(symbol.get()).map_err(|e| {
                    qerror!(
                        "parse GateSpotSymbol error: {:?}, json: {}",
                        e,
                        symbol.get()
                    )
                })?;
                let ts_now = time_ms();
                for t in trade {
                    if (ts_now - ((t.time * 1000.0) as i64)).abs() < 10_000
                    // 10s
                    {
                        handler
                            .on_trade(t.into_trade(gate_symbol.clone()), self.exchange)
                            .await?;
                    }
                }
            }
            _ => {}
        }
        Ok(())
    }
    async fn handle_msg_inner_v4<H: WsHandler>(
        &mut self,
        channel: Channel,
        result: &RawValue,
        subs: &base::Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        let json = result.get();
        match channel {
            Channel::Balance => {
                if subs.has_balance() {
                    let balances = from_str::<Vec<GateSpotWsBalance>>(json)
                        .map_err(|e| qerror!("parse Balance error: {:?}, json: {}", e, json))?;
                    for balance in balances {
                        let balance = Balance::from(balance);
                        handler
                            .on_balance(stable_balance_non_zero(balance), subs.id)
                            .await?;
                    }
                }
            }
            Channel::BookTicker => {
                if subs.has_bbo() {
                    let book_ticker =
                        BboTicker::from(from_str::<GateBookTiker>(json).map_err(|e| {
                            qerror!("parse BookTicker error: {:?}, json: {}", e, json)
                        })?);
                    handler.on_bbo(book_ticker, self.exchange).await?;
                }
            }
            Channel::Orders => {
                if subs.has_order() || subs.has_order_and_fill() {
                    for order in from_str::<Vec<GateOrder>>(json)
                        .map_err(|e| qerror!("parse Orders error: {:?}, json: {}", e, json))?
                    {
                        let order: Order = order.to_local_order();
                        if subs.has_order_and_fill()
                            && let Some(order) = self.order_cache.process_order(order.clone())
                        {
                            handler.on_order_and_fill(order, subs.id).await?;
                        }
                        if subs.has_order() {
                            handler.on_order(order, subs.id).await?;
                        }
                    }
                }
            }
            Channel::Ping | Channel::Empty => {}
            Channel::OrderBook => {
                if subs.has_depth() {
                    let depth = from_str::<OrderBooks>(json).map_err(|e| {
                        qerror!("parse OrderBookUpdate error: {:?}, json: {}", e, json)
                    })?;
                    let depth: Depth = depth.into();
                    handler
                        .on_depth(depth.truncate_limited(self.limit), self.exchange)
                        .await?;
                }
            }
            Channel::UserTrades => {
                if subs.has_order_and_fill() {
                    let fill = from_str::<GateFill>(json)
                        .map_err(|e| qerror!("parse UserTrades error: {:?}, json: {}", e, json))?;
                    let order: Order = fill.into();
                    if let Some(order) = self.order_cache.process_order(order) {
                        handler.on_order_and_fill(order, subs.id).await?;
                    }
                }
            }
            Channel::Trades => {
                if subs.has_trade() {
                    let trade = from_str::<GateTrade>(json)
                        .map_err(|e| qerror!("parse Trades error: {:?}, json: {}", e, json))?;

                    handler.on_trade(trade.into(), self.exchange).await?;
                }
            }
            Channel::Candlesticks => {
                if subs.has_kline() {
                    let kline = from_str::<GateKline>(json).map_err(|e| {
                        qerror!("parse Candlesticks error: {:?}, json: {}", e, json)
                    })?;
                    handler.on_kline(kline.into(), self.exchange).await?;
                }
            }
            chan => {
                panic!("unhandle {chan:?}");
            }
        }
        Ok(())
    }

    async fn handle_msg_v3<H: WsHandler>(&mut self, data: Message, handler: &base::Subscribes<H>) {
        let msg = match &data {
            Message::Text(t) => sonic_rs::from_str::<GateEventV3>(t),
            Message::Binary(b) => sonic_rs::from_slice::<GateEventV3>(b),
            _ => unreachable!(),
        };
        match msg {
            Ok(msg) => {
                if msg.params.is_none() {
                    return;
                }
                if let Err(e) = self
                    .handle_msg_inner_v3(msg.method, &msg.params.unwrap(), handler)
                    .await
                    && msg.error.is_some()
                {
                    error!("handle message error: {e:?}");
                }
            }
            Err(e) => {
                error!("parse message error: {e:?}");
            }
        }
    }

    async fn handle_msg_v4<H: WsHandler>(&mut self, data: Message, handler: &base::Subscribes<H>) {
        let msg = match &data {
            Message::Text(t) => sonic_rs::from_str::<GateEvent>(t),
            Message::Binary(b) => sonic_rs::from_slice::<GateEvent>(b),
            _ => unreachable!(),
        };
        match msg {
            Ok(msg) => {
                if let Err(e) = self
                    .handle_msg_inner_v4(msg.channel, &msg.result, handler)
                    .await
                {
                    if msg.event != "subscribe" {
                        error!("handle message error: {e:?}");
                    } else {
                        let json = msg.result.get();
                        if let Ok(value) = sonic_rs::from_str::<SubscribeStatus>(json)
                            && value.status != "success"
                        {
                            error!("handle message error: {:?}", msg.error);
                        }
                    }
                }
            }
            Err(e) => {
                error!("parse message error: {e:?}");
            }
        }
    }

    pub(crate) async fn sub(
        &mut self,
        w: &mut Writer,
        channels: &[SubscribeChannel],
        is_private: bool,
    ) -> Result<()> {
        let mut params = rustc_hash::FxHashSet::default();
        let instrument = self.rest.get_instruments().await?;
        let instrument: FxHashMap<String, Instrument> = instrument
            .into_iter()
            .map(|x| (x.symbol.to_string(), x))
            .collect();
        for sub in channels {
            if is_private
                && !matches!(
                    sub,
                    SubscribeChannel::Balance
                        | SubscribeChannel::Order(_)
                        | SubscribeChannel::Position(_)
                )
            {
                continue;
            }
            if !is_private
                && matches!(
                    sub,
                    SubscribeChannel::Balance
                        | SubscribeChannel::Order(_)
                        | SubscribeChannel::Position(_)
                )
            {
                continue;
            }
            match sub {
                SubscribeChannel::Bbo(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    let limit = 5;
                    if self.is_weburl {
                        for symbol in symbols {
                            let precision = Self::get_precision(&instrument, symbol.as_str());
                            params.insert((
                                Channel::OrderBook,
                                Some(vec![symbol, limit.to_string(), precision]),
                            ));
                        }
                    } else {
                        params.insert((Channel::BookTicker, Some(symbols))); // V4
                    }
                }
                SubscribeChannel::Depth(depth_params) => {
                    let symbols = Self::symbols_cvt(depth_params.symbols.as_ref().clone());
                    self.limit = depth_params.levels as usize;
                    if depth_params.levels <= 0 {
                        warn!("depth limit is less than 0");
                    }
                    let mut level = if depth_params.levels <= 5 {
                        "5"
                    } else if depth_params.levels <= 10 {
                        "10"
                    } else if depth_params.levels <= 20 {
                        "20"
                    } else if depth_params.levels <= 30 {
                        "30"
                    } else if depth_params.levels <= 50 {
                        "50"
                    } else {
                        "100"
                    };
                    if self.is_weburl && depth_params.levels > 30 {
                        warn!("gate spot weburl depth limit is greater than 30");
                        level = "30"
                    }
                    for symbol in symbols {
                        let precision = if self.is_weburl {
                            Self::get_precision(&instrument, symbol.as_str())
                        } else {
                            "100ms".to_string()
                        };
                        params.insert((
                            Channel::OrderBook,
                            Some(vec![symbol, level.to_owned(), precision]),
                        ));
                    }
                }
                SubscribeChannel::Trade(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    params.insert((Channel::Trades, Some(symbols)));
                }
                SubscribeChannel::Order(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    for symbol in symbols {
                        params.insert((Channel::Orders, Some(vec![symbol])));
                    }
                }
                SubscribeChannel::Balance => {
                    params.insert((Channel::Balance, None));
                }
                SubscribeChannel::OrderAndFill(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    for symbol in symbols {
                        params.insert((Channel::UserTrades, Some(vec![symbol.clone()])));
                        params.insert((Channel::Orders, Some(vec![symbol])));
                    }
                }
                SubscribeChannel::Kline(kline_params) => {
                    let symbols = Self::symbols_cvt(kline_params.symbols.as_ref().clone());
                    for symbol in symbols {
                        params.insert((
                            Channel::Candlesticks,
                            Some(vec![kline_params.interval.to_string(), symbol]),
                        ));
                    }
                }
                _ => {
                    warn!("gate spot unhandle subscribe channel: {sub:?}");
                }
            }
        }

        if params.is_empty() {
            warn!("sub params is empty");
            return Ok(());
        }
        let time = time();
        for (channel, payload) in params {
            let req = if !self.is_weburl {
                let req = new_ws_subscribe_req(
                    channel,
                    "subscribe",
                    payload,
                    time,
                    &self.rest.config.key,
                    &self.rest.config.secret,
                );
                sonic_rs::to_string(&req)?
            } else {
                let req = new_v3_req(channel, payload, time);
                sonic_rs::to_string(&req)?
            };
            let message = Message::Text(req);
            w.send(message).await?;
        }

        debug!("send sub message success");
        Ok(())
    }

    fn symbols_cvt(symbols: Vec<Symbol>) -> Vec<String> {
        symbols
            .into_iter()
            .map(|x| GateSpotSymbol::from(x).to_string())
            .collect()
    }

    fn get_precision(instrument: &FxHashMap<String, Instrument>, symbol: &str) -> String {
        instrument
            .get(symbol)
            .map(|x| x.price_tick.to_string())
            .unwrap_or("0.1".to_string())
    }
}

// #[cfg(test)]
// mod tests {
//     use super::*;
//     use base::model::{Subscribes, Symbol};
//     use common::Result;
//     use tracing_subscriber::fmt;

//     #[tokio::test]
//     #[ignore = "need proxy"]
//     async fn test_all() -> Result<()> {
//         fmt().pretty().init();
//         let config = ExConfig::default();

//         let mut ws = GateSpotWs::new(config).await;

//         let symbols = vec![Symbol::new("BTC")];

//         let subs = Subscribes::default_spot(symbols);
//         ws.run(subs).await?;
//         Ok(())
//     }
// }

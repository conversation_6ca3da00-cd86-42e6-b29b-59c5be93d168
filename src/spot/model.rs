use quant_common::base::{
    <PERSON><PERSON>, BboTicker, De<PERSON>h, <PERSON><PERSON><PERSON><PERSON><PERSON>ry, FeeRate, InsState, Instrument, MarginMode, Order,
    OrderSide, OrderSource, OrderStatus, OrderType, PosSide, Position, QuoteCcy, RawValue, Symbol,
    Ticker, TimeInForce, Trade,
};
use quant_common::deserialize_f64_pairs;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt::{Display, Formatter};
use tracing::debug;

pub const EXCHANGE: &str = "BybitSpot";

pub const REST_BASE_HOST: &str = "api.bybit.com";
pub const REST_COLONY_BASE_HOST: &str = "api-colo.bybit.com"; // 假的
pub const REST_TEST_BASE_HOST: &str = "api-demo.bybit.com";

// pub const REST_BASE_URL: &str = "https://api.bybit.com";
// pub const REST_TEST_BASE_URL: &str = "https://api-demo.bybit.com";

pub const WS_HOST: &str = "stream.bybit.com";
pub const WS_COLO_HOST: &str = "stream-colo.bybit.com"; // 假的
pub const WS_TEST_HOST: &str = "stream-testnet.bybit.com";

// pub const WS_URL: &str = "wss://stream.bybit.com/v5/public/spot";
// pub const WS_PRIVATE_URL: &str = "wss://stream.bybit.com/v5/private";
// pub const WS_TEST_URL: &str = "wss://stream-testnet.bybit.com/v5/public/spot";
// pub const WS_PRIVATE_TEST_URL: &str = "wss://stream-testnet.bybit.com/v5/private/spot";

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ApiResponse<T> {
    pub ret_code: i32,
    pub ret_msg: String,
    pub result: Option<T>,
    pub ret_ext_info: Option<RawValue>,
    pub time: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbResult {}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BbSymbol {
    pub symbol: String,
    pub base: String,
    pub quote: String,
    pub base_unit: String,
}

impl Display for BbSymbol {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.symbol)
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbTicker {
    pub category: String,
    pub list: Vec<BbTickerInner>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbOrderData {
    pub category: String,                      // 产品类型
    pub order_id: String,                      // 订单ID
    pub order_link_id: String,                 // 用户自定义ID
    pub is_leverage: Option<String>,           // 是否借贷 (仅适用于 spot 类型的统一账户)
    pub block_trade_id: String,                // 大宗交易订单ID
    pub symbol: String,                        // 合约名称
    pub price: String,                         // 订单价格
    pub qty: String,                           // 订单数量
    pub side: String,                          // 方向: Buy 或 Sell
    pub position_idx: i32,                     // 仓位标识
    pub order_status: String,                  // 订单状态
    pub create_type: Option<String>,           // 订单创建类型 (仅适用于 linear 或 inverse)
    pub cancel_type: Option<String>,           // 订单被取消类型
    pub reject_reason: Option<String>,         // 拒绝原因
    pub avg_price: Option<String>,             // 平均成交价格
    pub leaves_qty: Option<String>,            // 剩余未成交数量
    pub leaves_value: Option<String>,          // 剩余未成交价值
    pub cum_exec_qty: String,                  // 累计成交数量
    pub cum_exec_value: String,                // 累计成交价值
    pub cum_exec_fee: String,                  // 累计成交手续费
    pub closed_pnl: String,                    // 平仓盈亏
    pub fee_currency: Option<String>,          // 手续费币种
    pub time_in_force: String,                 // 执行策略
    pub order_type: String,                    // 订单类型: Market, Limit
    pub stop_order_type: Option<String>,       // 条件单类型
    pub oco_trigger_by: Option<String>,        // OCO订单的触发类型
    pub order_iv: Option<String>,              // 隐含波动率
    pub market_unit: Option<String>,           // qty的单位选择
    pub trigger_price: Option<String>,         // 触发价格
    pub take_profit: Option<String>,           // 止盈价格
    pub stop_loss: Option<String>,             // 止损价格
    pub tpsl_mode: Option<String>,             // 止盈止损模式
    pub tp_limit_price: Option<String>,        // 止盈转限价单价格
    pub sl_limit_price: Option<String>,        // 止损转限价单价格
    pub tp_trigger_by: Option<String>,         // 止盈的触发价格类型
    pub sl_trigger_by: Option<String>,         // 止损的触发价格类型
    pub trigger_direction: i32,                // 触发方向
    pub trigger_by: Option<String>,            // 触发价格类型
    pub last_price_on_created: Option<String>, // 下单时市场价格
    pub reduce_only: bool,                     // 是否仅减仓
    pub close_on_trigger: bool,                // 是否触发后平仓委托
    pub place_type: Option<String>,            // 下单方式 (期权)
    pub smp_type: Option<String>,              // SMP执行类型
    pub smp_group: i32,                        // SMP组ID
    pub smp_order_id: Option<String>,          // 触发SMP的交易对手订单ID
    pub created_time: String,                  // 创建时间戳 (毫秒)
    pub updated_time: String,                  // 更新时间戳 (毫秒)
}

#[derive(Deserialize)]
#[allow(clippy::upper_case_acronyms)]
pub enum BybitQuote {
    USDT,
    USDC,
    USD,
}

impl From<BybitQuote> for QuoteCcy {
    fn from(value: BybitQuote) -> Self {
        match value {
            BybitQuote::USDT => QuoteCcy::USDT,
            BybitQuote::USDC => QuoteCcy::USDC,
            BybitQuote::USD => QuoteCcy::USD,
        }
    }
}

impl From<BbOrderData> for Order {
    fn from(bb_order: BbOrderData) -> Self {
        let id = bb_order.order_id;
        // 客户自定义ID（此处可能为空）
        let cid = if bb_order.order_link_id.is_empty() {
            None
        } else {
            Some(bb_order.order_link_id)
        };
        let timestamp = bb_order.created_time.parse::<i64>().unwrap_or_default();

        let status = match bb_order.order_status.as_str() {
            "New" => OrderStatus::Open,
            "PartiallyFilled" => OrderStatus::PartiallyFilled,
            "Filled" => OrderStatus::Filled,
            "Rejected" => OrderStatus::Canceled,
            "Canceled" => OrderStatus::Canceled,
            _ => OrderStatus::Canceled, // 默认
        };

        // 交易对符号
        let symbol = Symbol::split_no_separator_symbol::<BybitQuote>(&bb_order.symbol).unwrap();

        // 订单类型
        let order_type = match bb_order.order_type.as_str() {
            "Market" => OrderType::Market,
            "Limit" => OrderType::Limit,
            _ => OrderType::Limit, // 默认
        };

        // 订单方向
        let side = match bb_order.side.as_str() {
            "Buy" => OrderSide::Buy,
            "Sell" => OrderSide::Sell,
            _ => OrderSide::Buy, // 默认
        };

        // 仓位方向
        let pos_side = None;

        // 时间有效期
        let time_in_force = match bb_order.time_in_force.as_str() {
            "IOC" => TimeInForce::IOC,
            "FOK" => TimeInForce::FOK,
            "GTC" => TimeInForce::GTC,
            _ => TimeInForce::GTC, // 默认
        };

        // 订单价格
        let price = if !bb_order.price.is_empty() {
            Some(bb_order.price.parse::<f64>().unwrap_or_default())
        } else {
            None
        };

        // 基准货币数量
        let amount = bb_order.qty.parse::<f64>().unwrap_or_default();

        // 已成交数量
        let filled = bb_order.cum_exec_qty.parse::<f64>().unwrap_or_default();

        // 已成交均价
        let filled_avg_price = bb_order
            .avg_price
            .unwrap_or_default()
            .parse::<f64>()
            .unwrap_or_default();

        // 创建 Order 实例
        Order {
            id,
            cid,
            timestamp,
            status,
            symbol,
            order_type,
            side,
            pos_side,
            time_in_force,
            price,
            filled,
            filled_avg_price,
            amount: Some(amount),
            quote_amount: None,
            source: OrderSource::Order,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbTickerInner {
    pub symbol: String,
    pub last_price: String,
    pub prev_price_24h: String,
    pub price_24h_pcnt: String,
    pub high_price_24h: String,
    pub low_price_24h: String,
    pub turnover_24h: String,
    pub volume_24h: String,
    pub ask_1_size: String,
    pub bid_1_price: String,
    pub ask_1_price: String,
    pub bid_1_size: String,
    pub basis: Option<String>,
}

impl From<BbTickerInner> for Ticker {
    fn from(bb: BbTickerInner) -> Self {
        let last_price: f64 = bb.last_price.parse().unwrap_or(0.0);
        let high_price: f64 = bb.high_price_24h.parse().unwrap_or(0.0);
        let low_price: f64 = bb.low_price_24h.parse().unwrap_or(0.0);
        let volume: f64 = bb.volume_24h.parse().unwrap_or(0.0);
        let quote_volume: f64 = bb.turnover_24h.parse().unwrap_or(0.0);
        let change: f64 = last_price - bb.prev_price_24h.parse().unwrap_or(0.0);
        let change_percent: f64 = (change / bb.prev_price_24h.parse().unwrap_or(1.0)) * 100.0;

        Ticker {
            symbol: Symbol::split_no_separator_symbol::<BybitQuote>(&bb.symbol).unwrap(),
            timestamp: 0, // 根据需要设置时间戳
            high: high_price,
            low: low_price,
            open: bb.prev_price_24h.parse().unwrap_or(0.0), // 根据需要设置开盘价
            close: last_price,
            volume,
            quote_volume,
            change,
            change_percent,
        }
    }
}

impl From<BbTickerInner> for BboTicker {
    fn from(bbticker: BbTickerInner) -> Self {
        let symbol = Symbol::split_no_separator_symbol::<BybitQuote>(&bbticker.symbol).unwrap();

        let bid_price = bbticker.bid_1_price.parse().unwrap_or(0.0);
        let bid_qty = bbticker.bid_1_size.parse().unwrap_or(0.0);

        let ask_price = bbticker.ask_1_price.parse().unwrap_or(0.0);
        let ask_qty = bbticker.ask_1_size.parse().unwrap_or(0.0);

        BboTicker {
            symbol,
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
            timestamp: 0,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbDepth {
    pub s: String,
    pub a: Vec<Vec<String>>, // Ask
    pub b: Vec<Vec<String>>, // Bid
    pub ts: u64,             // Timestamp
    pub u: u64,              // Update
    pub seq: u64,            // Sequence
    pub cts: u64,            // Client Timestamp
}

impl From<BbDepth> for Depth {
    fn from(bb_depth: BbDepth) -> Self {
        let bids = bb_depth
            .b
            .iter()
            .filter_map(|entry| {
                if entry.len() == 2 {
                    Some(DepthEntry {
                        price: entry[0].parse().unwrap_or(0.0),
                        amount: entry[1].parse().unwrap_or(0.0),
                    })
                } else {
                    None
                }
            })
            .collect();

        let asks = bb_depth
            .a
            .iter()
            .filter_map(|entry| {
                if entry.len() == 2 {
                    Some(DepthEntry {
                        price: entry[0].parse().unwrap_or(0.0),
                        amount: entry[1].parse().unwrap_or(0.0),
                    })
                } else {
                    None
                }
            })
            .collect();

        Depth {
            symbol: Symbol::split_no_separator_symbol::<BybitQuote>(&bb_depth.s).unwrap(),
            bids,
            asks,
            timestamp: bb_depth.ts as i64,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PriceFilter {
    pub tick_size: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct LotSizeFilter {
    pub base_precision: String,
    pub quote_precision: String,
    pub min_order_qty: String,
    pub max_order_qty: String,
    pub min_order_amt: String,
    pub max_order_amt: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbInstrumentInner {
    pub symbol: String,
    pub status: String,
    pub base_coin: String,
    pub quote_coin: String,
    // 是否属于创新区交易对. `0`: 否, `1`: 是
    pub innovation: String,
    // 是否支持杠杆交易
    pub margin_trading: String,
    pub st_tag: String,
    pub price_filter: PriceFilter,
    pub lot_size_filter: LotSizeFilter,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbInstrument {
    pub category: String,
    pub list: Vec<BbInstrumentInner>,
}

impl From<BbInstrumentInner> for Instrument {
    fn from(inner: BbInstrumentInner) -> Self {
        Instrument {
            symbol: Symbol::split_no_separator_symbol::<BybitQuote>(&inner.symbol).unwrap(),
            state: match inner.status.as_str() {
                "PreLaunch" => InsState::Close, // 根据需要调整
                "Trading" => InsState::Normal,
                "Delivering" => InsState::Maintenance,
                "Closed" => InsState::Close,
                _ => InsState::Close, // 默认值
            },
            price_tick: inner.price_filter.tick_size.parse().unwrap_or(0.1), // 解析价格步长
            amount_tick: inner
                .lot_size_filter
                .quote_precision
                .parse()
                .unwrap_or(0.001), // 使用 qty_step 或提供默认值
            price_precision: -(inner.price_filter.tick_size.parse::<f64>().unwrap().log10() as i32),
            amount_precision: -(inner
                .lot_size_filter
                .quote_precision
                .parse::<f64>()
                .unwrap()
                .log10() as i32),
            min_qty: inner.lot_size_filter.min_order_qty.parse().unwrap_or(1.0),
            min_notional: inner.lot_size_filter.min_order_amt.parse().unwrap_or(0.0),
            price_multiplier: 1.0,
            amount_multiplier: 1.0, // 根据你的逻辑设置
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbOrderResult {
    pub order_id: String,
    pub order_link_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbOrderListResultInner {
    pub category: String,
    pub symbol: String,
    pub order_id: String,
    pub order_link_id: String,
    pub create_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbPosition {
    pub position_idx: u32,                 // 持仓索引
    pub risk_id: u32,                      // 风险ID
    pub risk_limit_value: String,          // 风险限额值
    pub symbol: String,                    // 交易对符号
    pub side: String,                      // 买卖方向 (Buy/Sell)
    pub size: String,                      // 持仓数量
    pub avg_price: Option<String>,         // 平均价格
    pub position_value: String,            // 持仓价值
    pub trade_mode: u32,                   // 交易模式
    pub position_status: String,           // 持仓状态
    pub auto_add_margin: u32,              // 是否自动追加保证金 (0/1)
    pub adl_rank_indicator: u32,           // ADL 排名指示器
    pub leverage: String,                  // 杠杆倍数
    pub position_balance: String,          // 持仓余额
    pub mark_price: String,                // 标记价格
    pub liq_price: String,                 // 清算价格
    pub bust_price: String,                // 爆仓价格
    pub position_mm: String,               // 维持保证金
    pub position_im: String,               // 起始保证金
    pub tpsl_mode: String,                 // 止盈止损模式
    pub take_profit: String,               // 止盈
    pub stop_loss: String,                 // 止损
    pub trailing_stop: String,             // 移动止损
    pub unrealised_pnl: String,            // 未实现盈亏
    pub session_avg_price: String,         // 会话平均价格
    pub cur_realised_pnl: String,          // 当前已实现盈亏
    pub cum_realised_pnl: String,          // 累计已实现盈亏
    pub seq: u64,                          // 序列号
    pub is_reduce_only: bool,              // 是否仅减少
    pub mmr_sys_update_time: String,       // 系统维持保证金更新时间
    pub leverage_sys_updated_time: String, // 系统杠杆更新时间
    pub created_time: String,              // 创建时间
    pub updated_time: String,              // 更新时间
    pub entry_price: Option<String>,
    pub category: Option<String>,
}

impl From<BbPosition> for Position {
    fn from(bb_pos: BbPosition) -> Self {
        Position {
            symbol: Symbol::split_no_separator_symbol::<BybitQuote>(&bb_pos.symbol).unwrap(),
            timestamp: bb_pos.created_time.parse().unwrap_or_default(),
            margin_mode: if bb_pos.position_idx == 0 {
                MarginMode::Isolated
            } else {
                MarginMode::Cross
            }, // 假设 `MarginMode` 有 `from_str` 方法
            side: if bb_pos.side == "Buy" {
                PosSide::Long
            } else {
                PosSide::Short
            },
            leverage: bb_pos.leverage.parse().unwrap_or(1),
            amount: bb_pos.size.parse().unwrap_or_default(),
            entry_price: bb_pos
                .avg_price
                .unwrap_or("0.0".to_string())
                .parse()
                .unwrap(),
            unrealized_pnl: bb_pos.unrealised_pnl.parse().unwrap_or_default(),
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
pub struct BbMsg {
    pub code: i32,
    pub msg: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct BbList<T> {
    pub list: Vec<T>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbBalance {
    // 账户余额相关信息
    pub total_equity: String, // 总资产
    #[serde(rename = "accountIMRate")]
    pub account_im_rate: String, // 账户IM利率
    pub total_margin_balance: String, // 总保证金余额
    pub total_initial_margin: String, // 总初始保证金
    pub account_type: String, // 账户类型
    pub total_available_balance: String, // 可用余额
    #[serde(rename = "accountMMRate")]
    pub account_mm_rate: String, // 账户MM利率
    #[serde(rename = "totalPerpUPL")]
    pub total_perp_upl: String, // 总永续盈亏
    pub total_wallet_balance: String, // 总钱包余额
    #[serde(rename = "accountLTV")]
    pub account_ltv: String, // 账户贷款价值比
    pub total_maintenance_margin: String, // 总维持保证金
    pub coin: Vec<CoinBalance>, // 资产信息
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CoinBalance {
    pub available_to_borrow: String,           // 可借资产
    pub bonus: String,                         // 奖励
    pub accrued_interest: String,              // 应计利息
    pub available_to_withdraw: Option<String>, // 可提取资产
    #[serde(rename = "totalOrderIM")]
    pub total_order_im: String, // 总订单IM
    pub equity: String,                        // 资产
    #[serde(rename = "totalPositionMM")]
    pub total_position_mm: String, // 总仓位MM
    pub usd_value: String,                     // USD价值
    pub spot_hedging_qty: String,              // 现货对冲数量
    pub unrealised_pnl: String,                // 浮动盈亏
    pub collateral_switch: bool,               // 保证金切换
    pub borrow_amount: String,                 // 借款金额
    #[serde(rename = "totalPositionIM")]
    pub total_position_im: String, // 总仓位IM
    pub wallet_balance: String,                // 钱包余额
    pub cum_realised_pnl: String,              // 累计实现盈亏
    pub locked: String,                        // 锁仓量
    pub margin_collateral: bool,               // 保证金抵押
    pub coin: String,                          // 资产类型
}

impl From<CoinBalance> for Balance {
    fn from(value: CoinBalance) -> Self {
        Self {
            asset: value.coin,
            balance: value.wallet_balance.parse().unwrap_or_default(),
            available_balance: value
                .available_to_withdraw
                .unwrap_or_default()
                .parse()
                .unwrap_or_default(),
            unrealized_pnl: value.unrealised_pnl.parse().unwrap_or_default(),
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BbFeeRate {
    pub symbol: String,
    pub taker_fee_rate: String,
    pub maker_fee_rate: String,
}

impl From<BbFeeRate> for FeeRate {
    // 转换方法：将 BbBalance 转换为 Balance
    fn from(value: BbFeeRate) -> Self {
        Self {
            taker: value.taker_fee_rate.parse().unwrap_or_default(),
            maker: value.maker_fee_rate.parse().unwrap_or_default(),
            buyer: 0.0,
            seller: 0.0,
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct NewOrderRequest {
    pub category: String,
    pub symbol: String,
    pub side: String,
    pub order_type: String,
    pub qty: f64,
    pub price: Option<f64>,
    pub market_unit: Option<String>,
    pub time_in_force: Option<String>,
    pub position_idx: Option<i32>,
    pub order_link_id: Option<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct NewOrderListRequest {
    pub category: String,
    pub request: Vec<NewOrderRequest>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct BbTickerSnapshot {
    pub topic: String,
    #[serde(rename = "type")]
    pub type_: String,
    pub data: BbTickerData,
    pub cs: u64,
    pub ts: u64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BbTickerData {
    pub symbol: String,
    pub tick_direction: Option<String>,
    pub price_24h_pcnt: Option<String>,
    pub last_price: Option<String>,
    pub prev_price_24h: Option<String>,
    pub high_price_24h: Option<String>,
    pub low_price_24h: Option<String>,
    pub prev_price_1h: Option<String>,
    pub mark_price: Option<String>,
    pub index_price: Option<String>,
    pub open_interest: Option<String>,
    pub open_interest_value: Option<String>,
    pub turnover_24h: Option<String>,
    pub volume_24h: Option<String>,
    pub next_funding_time: Option<String>,
    pub funding_rate: Option<String>,
    pub bid_1_price: Option<String>,
    pub bid_1_size: Option<String>,
    pub ask_1_price: Option<String>,
    pub ask_1_size: Option<String>,
}

impl BbTickerData {
    pub fn update(&mut self, delta: &BbTickerData) {
        let update_field = |field: &mut Option<String>, delta_field: &Option<String>| {
            if let Some(value) = delta_field {
                *field = Some(value.clone());
            }
        };

        // 使用闭包更新每个字段
        update_field(&mut self.tick_direction, &delta.tick_direction);
        update_field(&mut self.price_24h_pcnt, &delta.price_24h_pcnt);
        update_field(&mut self.last_price, &delta.last_price);
        update_field(&mut self.prev_price_24h, &delta.prev_price_24h);
        update_field(&mut self.high_price_24h, &delta.high_price_24h);
        update_field(&mut self.low_price_24h, &delta.low_price_24h);
        update_field(&mut self.prev_price_1h, &delta.prev_price_1h);
        update_field(&mut self.mark_price, &delta.mark_price);
        update_field(&mut self.index_price, &delta.index_price);
        update_field(&mut self.open_interest, &delta.open_interest);
        update_field(&mut self.open_interest_value, &delta.open_interest_value);
        update_field(&mut self.turnover_24h, &delta.turnover_24h);
        update_field(&mut self.volume_24h, &delta.volume_24h);
        update_field(&mut self.next_funding_time, &delta.next_funding_time);
        update_field(&mut self.funding_rate, &delta.funding_rate);
        update_field(&mut self.bid_1_price, &delta.bid_1_price);
        update_field(&mut self.bid_1_size, &delta.bid_1_size);
        update_field(&mut self.ask_1_price, &delta.ask_1_price);
        update_field(&mut self.ask_1_size, &delta.ask_1_size);
    }
}

impl From<BbTickerSnapshot> for BboTicker {
    fn from(b: BbTickerSnapshot) -> Self {
        let bb_ticker = b.data;
        debug!("{:?}", bb_ticker);
        let symbol = Symbol::split_no_separator_symbol::<BybitQuote>(&bb_ticker.symbol).unwrap();

        let bid_price = bb_ticker.bid_1_price.unwrap().parse().unwrap_or(0.0);
        let bid_qty = bb_ticker.bid_1_size.unwrap().parse().unwrap_or(0.0);

        let ask_price = bb_ticker.ask_1_price.unwrap().parse().unwrap_or(0.0);
        let ask_qty = bb_ticker.ask_1_size.unwrap().parse().unwrap_or(0.0);

        BboTicker {
            symbol,
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
            timestamp: b.ts.try_into().unwrap_or_default(),
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
pub struct PublicTradeSnapshot {
    pub topic: String,
    pub r#type: String,
    pub ts: u64,
    pub data: Vec<PublicTradeData>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct PublicTradeData {
    #[serde(rename = "T")]
    pub timestamp: u64,

    #[serde(rename = "s")]
    pub symbol: String,

    #[serde(rename = "S")]
    pub side: String,

    #[serde(rename = "v")]
    pub amount: String,

    #[serde(rename = "p")]
    pub price: String,

    #[serde(rename = "i")]
    pub id: String,

    #[serde(rename = "BT")]
    pub big_trade: bool,
}

impl From<PublicTradeData> for Trade {
    fn from(value: PublicTradeData) -> Self {
        Trade {
            id: value.id,
            symbol: Symbol::split_no_separator_symbol::<BybitQuote>(&value.symbol).unwrap(),
            timestamp: value.timestamp.try_into().unwrap_or_default(),
            price: value.price.parse().unwrap_or_default(),
            amount: value.amount.parse().unwrap_or_default(),
            side: match value.side.as_str() {
                "Buy" => OrderSide::Buy,
                _ => OrderSide::Sell,
            },
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbOrderBookSnapshot {
    pub topic: String,
    pub r#type: String,
    pub ts: u64,
    pub data: BbOrderBookData,
    pub cts: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbOrderBookData {
    pub s: String,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    pub b: Vec<(f64, f64)>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    pub a: Vec<(f64, f64)>,
    pub u: u64,   // Update ID
    pub seq: u64, // Sequence number
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbPriRsp<T> {
    pub id: String,
    pub topic: String,
    pub data: T,
    #[serde(rename = "creationTime")]
    pub creation_time: u64,
}

impl From<BbOrderBookSnapshot> for Depth {
    fn from(value: BbOrderBookSnapshot) -> Self {
        Depth {
            symbol: Symbol::split_no_separator_symbol::<BybitQuote>(&value.data.s).unwrap(),
            bids: value.data.b.into_iter().map(DepthEntry::from).collect(),
            asks: value.data.a.into_iter().map(DepthEntry::from).collect(),
            timestamp: value.ts.try_into().unwrap_or_default(),
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BbAvailableWithdrawal {
    pub available_withdrawal: String,
    pub available_withdrawal_map: Option<HashMap<String, String>>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_update() {
        // 初始数据
        let mut full_data = BbTickerData {
            symbol: "BTCUSDT".to_string(),
            tick_direction: None,
            price_24h_pcnt: None,
            last_price: Some("50000".to_string()),
            prev_price_24h: None,
            high_price_24h: None,
            low_price_24h: None,
            prev_price_1h: None,
            mark_price: None,
            index_price: None,
            open_interest: None,
            open_interest_value: None,
            turnover_24h: None,
            volume_24h: None,
            next_funding_time: None,
            funding_rate: None,
            bid_1_price: None,
            bid_1_size: None,
            ask_1_price: None,
            ask_1_size: None,
        };

        // 增量数据
        let update_data = BbTickerData {
            symbol: "BTCUSDT".to_string(),
            tick_direction: Some("up".to_string()),
            price_24h_pcnt: Some("1.5".to_string()),
            last_price: Some("50500".to_string()),
            prev_price_24h: Some("50000".to_string()),
            high_price_24h: Some("51000".to_string()),
            low_price_24h: None,
            prev_price_1h: Some("50400".to_string()),
            mark_price: None,
            index_price: None,
            open_interest: Some("1000".to_string()),
            open_interest_value: None,
            turnover_24h: None,
            volume_24h: Some("500".to_string()),
            next_funding_time: Some("2024-11-15T12:00:00".to_string()),
            funding_rate: None,
            bid_1_price: Some("50450".to_string()),
            bid_1_size: None,
            ask_1_price: Some("50550".to_string()),
            ask_1_size: None,
        };

        // 调用 update 方法
        full_data.update(&update_data);

        // 断言字段更新是否按预期进行
        assert_eq!(full_data.tick_direction, Some("up".to_string()));
        assert_eq!(full_data.price_24h_pcnt, Some("1.5".to_string()));
        assert_eq!(full_data.last_price, Some("50500".to_string()));
        assert_eq!(full_data.prev_price_24h, Some("50000".to_string()));
        assert_eq!(full_data.high_price_24h, Some("51000".to_string()));
        assert_eq!(full_data.prev_price_1h, Some("50400".to_string()));
        assert_eq!(full_data.open_interest, Some("1000".to_string()));
        assert_eq!(full_data.volume_24h, Some("500".to_string()));
        assert_eq!(
            full_data.next_funding_time,
            Some("2024-11-15T12:00:00".to_string())
        );
        assert_eq!(full_data.bid_1_price, Some("50450".to_string()));
        assert_eq!(full_data.ask_1_price, Some("50550".to_string()));

        // 这些字段没有在增量数据中提供，应该保持原值
        assert_eq!(full_data.symbol, "BTCUSDT".to_string()); // symbol字段不更新
        assert_eq!(full_data.mark_price, None); // mark_price字段未提供
        assert_eq!(full_data.index_price, None); // index_price字段未提供
        assert_eq!(full_data.open_interest_value, None); // open_interest_value字段未提供
        assert_eq!(full_data.turnover_24h, None); // turnover_24h字段未提供
        assert_eq!(full_data.low_price_24h, None); // low_price_24h字段未提供
        assert_eq!(full_data.ask_1_size, None); // ask_1_size字段未提供
    }
}

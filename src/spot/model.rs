use std::str::FromStr;

use hmac::{Hmac, Mac};
use once_cell::sync::OnceCell;
use quant_common::base::*;
use quant_common::*;
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};
use sha2::Sha512;

// 自定义反序列化器，处理 id 字段可能是 u64、i64 或字符串的情况
fn de_id_from_any<'de, D>(deserializer: D) -> Result<u64, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::de::{self, Visitor};
    use std::fmt;

    struct IdVisitor;

    impl Visitor<'_> for IdVisitor {
        type Value = u64;

        fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
            formatter.write_str("a u64, i64, or string representing an id")
        }

        fn visit_u64<E>(self, value: u64) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            Ok(value)
        }

        fn visit_i64<E>(self, value: i64) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            if value < 0 {
                Err(E::custom(format!("id cannot be negative: {value}")))
            } else {
                Ok(value as u64)
            }
        }

        fn visit_str<E>(self, value: &str) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            value.parse::<u64>().map_err(E::custom)
        }

        fn visit_string<E>(self, value: String) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            value.parse::<u64>().map_err(E::custom)
        }
    }

    deserializer.deserialize_any(IdVisitor)
}

pub const EXCHANGE: &str = "GateSpot";

pub const REST_BASE_HOST: &str = "api.gateio.ws";
pub const REST_COLO_BASE_HOST: &str = "apiv4-private.gateapi.io";
pub const REST_TEST_BASE_HOST: &str = "api-testnet.gateapi.io";

pub const WS_HOST: &str = "api.gateio.ws";
pub const WS_COLO_HOST: &str = "spotws-private.gateapi.io";
pub const WS_TEST_HOST: &str = "ws-testnet.gate.io";
pub const WS_WEB_HOST: &str = "webws.gateio.live";

pub const PATH_UNI_ACCOUNT: &str = "/api/v4/unified/accounts";
pub const PATH_ACCOUNT_DETAIL: &str = "/api/v4/account/detail";
pub const PATH_BATCH_ORDER: &str = "/api/v4/spot/batch_orders";
pub const PATH_ORDER: &str = "/api/v4/spot/orders";
pub const PATH_PRICE_ORDER: &str = "/api/v4/spot/price_orders";
pub const PATH_TICKER: &str = "/api/v4/spot/candlesticks";
pub const PATH_TICKERS: &str = "/api/v4/spot/tickers";
pub const PATH_ORDERS_HISTORY: &str = "/api/v4/spot/orders";
pub const PATH_ORDERS_ALL_OPEN: &str = "/api/v4/spot/open_orders";
pub const PATH_BATCH_CANCEL_OIDS: &str = "/api/v4/spot/cancel_batch_orders";
pub const PATH_TRANSFER: &str = "/api/v4/wallet/transfers";

pub const PATH_DEPTH: &str = "/api/v4/spot/order_book";
pub const PATH_CURRENCY_PAIRS: &str = "/api/v4/spot/currency_pairs";
pub const PATH_ACCOUNT: &str = "/api/v4/spot/accounts";
pub const PATH_FEE: &str = "/api/v4/wallet/fee";

pub const ACCOUNT_MODE: &str = "/api/v4/unified/unified_mode";

pub static MULTIPLIER_CACHE: OnceCell<FxHashMap<String, f64>> = OnceCell::new();

#[derive(Clone, Serialize, Deserialize, Default, PartialEq, Hash, Eq)]
pub struct GateSpotSymbol(pub Symbol);
impl std::fmt::Debug for GateSpotSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        self.0.fmt(f)
    }
}
impl std::fmt::Display for GateSpotSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        self.0.fmt(f)
    }
}
impl From<Symbol> for GateSpotSymbol {
    fn from(value: Symbol) -> Self {
        Self(value)
    }
}
impl From<GateSpotSymbol> for Symbol {
    fn from(value: GateSpotSymbol) -> Self {
        value.0
    }
}

#[derive(Deserialize)]
pub struct GateErr {
    pub label: String,
}

//交易对的发出的请求, 转化为 currency_pair= GateSpotSymbol.Serialize
#[derive(Serialize)]
pub struct CurrencyPairReq {
    currency_pair: GateSpotSymbol,
    limit: Option<u32>,
    interval: Option<String>,
}

impl CurrencyPairReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            currency_pair: symbol.into(),
            limit: Some(1),
            interval: Some("1d".to_string()),
        }
    }
}

#[derive(Deserialize, Debug)]
pub struct GateCandlestick {
    #[serde(deserialize_with = "de_from_str")]
    pub timestamp: i64, // 开始时间
    #[serde(deserialize_with = "de_from_str")]
    pub quote_volume: f64, // 交易额
    #[serde(deserialize_with = "de_from_str")]
    pub close: f64, // 收盘价
    #[serde(deserialize_with = "de_from_str")]
    pub high: f64, // 最高价
    #[serde(deserialize_with = "de_from_str")]
    pub low: f64, // 最低价
    #[serde(deserialize_with = "de_from_str")]
    pub open: f64, // 开盘价
    #[serde(deserialize_with = "de_from_str")]
    pub volume: f64, // 基础货币成交量
    #[serde(deserialize_with = "de_from_str")]
    pub is_end: bool, //窗口是否关闭
}

#[derive(Serialize)]
pub struct OrderBookReq {
    pub currency_pair: GateSpotSymbol,
    pub with_id: bool,
    pub limit: Option<u32>,
}
#[derive(Deserialize)]
pub(crate) struct DepthRest {
    pub update: i64,
    pub asks: Vec<OrderBookItem>,
    pub bids: Vec<OrderBookItem>,
}
impl DepthRest {
    pub fn convert(self, symbol: GateSpotSymbol, limit: usize) -> Depth {
        Depth {
            symbol: symbol.into(),
            timestamp: self.update * 1000,
            bids: self
                .bids
                .into_iter()
                .take(limit)
                .map(|item| DepthEntry::from((item.p, item.s)))
                .collect::<Vec<DepthEntry>>(),

            asks: self
                .asks
                .into_iter()
                .take(limit)
                .map(|item| DepthEntry::from((item.p, item.s)))
                .collect::<Vec<DepthEntry>>(),
        }
    }
    pub fn into_bbo_ticker(self, symbol: GateSpotSymbol) -> BboTicker {
        let ask1 = self.asks.first().cloned().unwrap_or_default();
        let bid1 = self.bids.first().cloned().unwrap_or_default();
        BboTicker {
            symbol: symbol.into(),
            bid_price: bid1.p,
            ask_price: ask1.p,
            timestamp: self.update,
            bid_qty: bid1.s,
            ask_qty: ask1.s,
        }
    }
}

#[derive(Deserialize, Clone, Default)]
#[cfg_attr(test, derive(PartialEq, Debug))]
pub struct OrderBookItem {
    /// price
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) p: f64,
    // size
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) s: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum GateInsState {
    Tradable,
    Untradable,
    Buyable,
    Sellable,
}

impl From<GateInsState> for InsState {
    fn from(value: GateInsState) -> Self {
        match value {
            GateInsState::Tradable => InsState::Normal,
            GateInsState::Untradable => InsState::Close,
            GateInsState::Buyable => InsState::Close,
            GateInsState::Sellable => InsState::LimitOpen,
        }
    }
}

#[derive(Deserialize, Debug)]
pub struct CurrencyPair {
    //对应symbol
    pub id: GateSpotSymbol,

    pub base: String,

    pub quote: String,
    #[serde(deserialize_with = "de_from_str")]
    pub fee: f64,
    //最小交易数量，min_qty
    #[serde(deserialize_with = "de_from_str")]
    pub min_base_amount: f64,
    //最小名义价值，对应 min_notional
    #[serde(deserialize_with = "de_from_str")]
    pub min_quote_amount: f64,

    #[serde(deserialize_with = "de_from_str")]
    #[serde(default)]
    pub max_base_amount: i32,
    // #[serde(deserialize_with = "de_from_option_str")]
    // pub(crate) max_quote_amount: Option<i32>,

    //数量的精度对应 amount_precision
    pub amount_precision: i32,
    //对应价格的精度price_precision
    pub precision: i32,
    pub trade_status: GateInsState,
    pub sell_start: i32,
    pub buy_start: i32,
}

impl From<CurrencyPair> for Instrument {
    fn from(v: CurrencyPair) -> Self {
        let price_tick = 10f64.powi(-v.precision);
        let amount_tick = 10f64.powi(-v.amount_precision);

        Self {
            symbol: v.id.into(),
            state: v.trade_status.into(),
            price_tick,
            amount_tick,
            price_precision: v.precision,
            amount_precision: v.amount_precision,
            min_qty: v.min_base_amount,
            min_notional: v.min_quote_amount,
            price_multiplier: 1.0,
            amount_multiplier: 1.0,
        }
    }
}
#[derive(Serialize)]
pub struct CurrencyReq {
    currency_pair: GateSpotSymbol,
}

impl CurrencyReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            currency_pair: symbol.into(),
        }
    }
}

#[derive(Deserialize)]
pub struct AccountFee {
    #[serde(deserialize_with = "de_from_str")]
    maker_fee: f64,
    #[serde(deserialize_with = "de_from_str")]
    taker_fee: f64,
}

impl From<AccountFee> for FeeRate {
    fn from(v: AccountFee) -> Self {
        Self {
            maker: v.maker_fee,
            taker: v.taker_fee,
            ..Default::default()
        }
    }
}

#[derive(Deserialize, Debug)]
pub struct GateSpotBalance {
    currency: String,
    #[serde(deserialize_with = "de_from_str")]
    available: f64,
    #[serde(deserialize_with = "de_from_str")]
    locked: f64,
}

impl From<GateSpotBalance> for Balance {
    fn from(raw: GateSpotBalance) -> Self {
        let available: f64 = raw.available;
        let locked: f64 = raw.locked;
        let total_balance = available + locked;

        Balance {
            asset: raw.currency,
            balance: total_balance,
            available_balance: available,
            unrealized_pnl: 0.0, // 没有提供浮动盈亏的数据，默认设为 0
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "snake_case")]
pub enum GateAccount {
    Spot,
    Normal,
    Margin,
    CrossMargin,
    Unified,
}

impl GateAccount {
    pub fn new(is_unified: bool, margin_mode: &Option<MarginMode>) -> Self {
        match (is_unified, margin_mode) {
            (true, _) => GateAccount::Unified,
            (false, Some(MarginMode::Cross)) => GateAccount::CrossMargin,
            (false, Some(MarginMode::Isolated)) => GateAccount::Margin,
            (_, None) => GateAccount::Spot,
        }
    }

    pub fn new_price_order(is_unified: bool) -> Self {
        if is_unified {
            GateAccount::Unified
        } else {
            GateAccount::Normal
        }
    }

    // pub fn auto_borrow(&self) -> bool {
    //     match self {
    //         GateAccount::Spot | GateAccount::Unified => false,
    //         GateAccount::Margin | GateAccount::CrossMargin => true,
    //     }
    // }
}

#[derive(Debug, Deserialize, Serialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum GateOrderSide {
    Buy,
    Sell,
}

impl From<GateOrderSide> for OrderSide {
    fn from(v: GateOrderSide) -> Self {
        match v {
            GateOrderSide::Buy => OrderSide::Buy,
            GateOrderSide::Sell => OrderSide::Sell,
        }
    }
}

impl From<OrderSide> for GateOrderSide {
    fn from(v: OrderSide) -> Self {
        match v {
            OrderSide::Buy => GateOrderSide::Buy,
            OrderSide::Sell => GateOrderSide::Sell,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum GateOrderType {
    Limit,
    Market,
}

impl From<OrderType> for GateOrderType {
    fn from(value: OrderType) -> Self {
        match value {
            OrderType::Limit => GateOrderType::Limit,
            OrderType::Market => GateOrderType::Market,
        }
    }
}

impl From<GateOrderType> for OrderType {
    fn from(value: GateOrderType) -> Self {
        match value {
            GateOrderType::Limit => OrderType::Limit,
            GateOrderType::Market => OrderType::Market,
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum GateTimeInForce {
    IOC,
    GTC,
    FOK,
    POC,
}

impl From<TimeInForce> for GateTimeInForce {
    fn from(value: TimeInForce) -> Self {
        match value {
            TimeInForce::IOC => GateTimeInForce::IOC,
            TimeInForce::GTC => GateTimeInForce::GTC,
            TimeInForce::FOK => GateTimeInForce::FOK,
            TimeInForce::PostOnly => GateTimeInForce::POC,
        }
    }
}

impl From<GateTimeInForce> for TimeInForce {
    fn from(value: GateTimeInForce) -> Self {
        match value {
            GateTimeInForce::IOC => TimeInForce::IOC,
            GateTimeInForce::GTC => TimeInForce::GTC,
            GateTimeInForce::FOK => TimeInForce::FOK,
            GateTimeInForce::POC => TimeInForce::PostOnly,
        }
    }
}

#[derive(Serialize, Debug)]
pub struct GateSpotOrderReq {
    pub currency_pair: GateSpotSymbol, // 交易对，比如 "BTC_USDT"
    pub side: GateOrderSide,
    pub amount: String,
    #[serde(rename = "type")]
    pub my_type: GateOrderType,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub price: Option<String>,

    #[serde(rename = "text")]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub client_order_id: Option<String>,

    pub time_in_force: GateTimeInForce,

    pub account: GateAccount,
    // pub auto_borrow: bool,  // 暂不开启
}

impl GateSpotOrderReq {
    pub(crate) fn new(order: Order, account: GateAccount) -> Result<Self, Error> {
        // 判断订单的方向（买入或卖出）
        let side = order.side.into();
        let price = order.price.map(|p| p.to_string());

        // amount: 交易数量
        // type为limit时: 指交易货币，即需要交易的货币
        //   如BTC_USDT中指BTC。
        // type为market时，根据买卖不同指代不同
        //   side : buy 指代计价货币，BTC_USDT中指USDT
        //   side : sell 指代交易货币，BTC_USDT中指BTC
        let amount = match (&order.order_type, &side) {
            (OrderType::Market, GateOrderSide::Buy) => {
                match order.price {
                    None => {
                        if order.quote_amount.is_some() {
                            match order.quote_amount {
                                Some(quote_amount) => quote_amount.to_string(),
                                None => return Err(qerror!("quote_amount unexpectedly None")),
                            }
                        } else {
                            return Err(qerror!(
                                "prices is none. please use quote amount to place order"
                            ));
                        }
                    }
                    Some(price) => match order.amount {
                        Some(amount) => (price * amount).round().to_string(), // turn btc to usdt
                        None => {
                            return Err(qerror!(
                                "amount is none. please provide amount for market buy order"
                            ));
                        }
                    },
                }
            }

            // return err not unwarp
            (OrderType::Market, GateOrderSide::Sell) => match order.amount {
                Some(amount) => amount.to_string(),
                None => return Err(qerror!("amount is none. please use amount to place order")),
            },
            _ => match order.amount {
                Some(amount) => amount.to_string(),
                None => return Err(qerror!("amount is none. please use amount to place order")),
            },
        };
        let cid = match order.cid {
            Some(cid) => cid,
            None => exchange::Exchange::GateSpot.create_cid(None),
        };

        // 创建 GateSpotOrderReq 实例
        Ok(GateSpotOrderReq {
            account,
            // auto_borrow: account.auto_borrow(),
            currency_pair: order.symbol.into(),
            side,
            amount, // 订单数量
            price,  // 订单价格（如果是限价单）
            my_type: order.order_type.clone().into(),
            client_order_id: Some(cid),
            time_in_force: order.time_in_force.into(),
        })
    }
}

impl From<GateSpotOrderReq> for PriceOrderPut {
    fn from(req: GateSpotOrderReq) -> Self {
        PriceOrderPut {
            account: req.account,
            order_type: req.my_type,
            side: req.side,
            price: req.price,
            amount: req.amount,
            time_in_force: req.time_in_force,
            text: req.client_order_id,
        }
    }
}

#[derive(Deserialize)]
pub struct OrderRsp {
    pub id: String,
}
#[derive(Deserialize)]
pub struct OrderPriceRsp {
    pub id: u64,
}

#[derive(Serialize)]
pub struct GateSpotAmendOrderReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub amount: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub price: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub account: Option<GateAccount>,
}

#[derive(Serialize, Deserialize, Clone, Copy, PartialEq, Debug, PartialOrd, Eq, Hash)]
pub enum Channel {
    /// Pong frame
    #[serde(rename = "")]
    Empty,
    #[serde(rename = "spot.ping")]
    Ping,
    #[serde(rename = "spot.pong")]
    Pong,

    /// public
    #[serde(rename = "spot.book_ticker")]
    BookTicker,
    #[serde(rename = "spot.order_book")]
    OrderBook,
    #[serde(rename = "spot.order_book_update")]
    OrderBookUpdate,
    #[serde(rename = "spot.trades_v2")]
    Trades,
    #[serde(rename = "spot.tickers")]
    Tickers,
    #[serde(rename = "spot.candlesticks")]
    Candlesticks,

    /// private
    #[serde(rename = "spot.balances")]
    Balance,
    #[serde(rename = "spot.orders_v2")]
    Orders,
    #[serde(rename = "spot.usertrades_v2")]
    UserTrades,
    #[serde(rename = "spot.positions")]
    Position,

    /// api
    #[serde(rename = "spot.login")]
    Login,
    #[serde(rename = "spot.order_place")]
    PlaceOrder,
}
#[derive(Serialize, Deserialize, Clone, Copy, PartialEq, Debug, PartialOrd, Eq, Hash)]
pub enum ChannelV3Subscribe {
    /// Pong frame
    #[serde(rename = "")]
    Empty,
    #[serde(rename = "server.ping")]
    Ping,
    #[serde(rename = "pong")]
    Pong,

    #[serde(rename = "depth.subscribe")]
    OrderBook,
    #[serde(rename = "trades.subscribe")]
    Trades,

    /// private
    #[serde(rename = "spot.balances")]
    Balance,
    #[serde(rename = "spot.orders")]
    Orders,
    #[serde(rename = "spot.usertrades")]
    UserTrades,
    #[serde(rename = "spot.positions")]
    Position,

    /// api
    #[serde(rename = "spot.login")]
    Login,
    #[serde(rename = "spot.order_place")]
    PlaceOrder,
}
#[derive(Deserialize, Debug)]
pub enum ChannelV3Update {
    // subscribe
    #[serde(rename = "depth.subscribe")]
    OrderBook,
    #[serde(rename = "trades.subscribe")]
    Trades,
    // update
    #[serde(rename = "depth.update")]
    OrderBookUpdate,
    #[serde(rename = "trades.update")]
    TradesUpdate,
}
impl From<Channel> for ChannelV3Subscribe {
    fn from(value: Channel) -> Self {
        match value {
            Channel::Empty => Self::Empty,
            Channel::Ping => Self::Ping,
            Channel::Pong => Self::Pong,
            Channel::OrderBook => Self::OrderBook,
            Channel::Trades => Self::Trades,
            Channel::Balance => Self::Balance,
            Channel::Orders => Self::Orders,
            Channel::UserTrades => Self::UserTrades,
            Channel::Position => Self::Position,
            Channel::Login => Self::Login,
            Channel::PlaceOrder => Self::PlaceOrder,
            _ => Self::Empty,
        }
    }
}
#[derive(Deserialize, Debug)]
pub struct GateEventV3 {
    pub method: ChannelV3Update,
    pub error: Option<SubscribeError>,
    // #[serde(borrow)]
    pub result: Option<RawValue>,
    pub params: Option<Vec<RawValue>>,
    pub id: Option<u64>,
}
impl Channel {
    fn is_public(self) -> bool {
        matches!(
            self,
            Self::BookTicker
                | Self::OrderBook
                | Self::OrderBookUpdate
                | Self::Trades
                | Self::Tickers
                | Self::Candlesticks
        )
    }
}
impl std::fmt::Display for Channel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        std::fmt::Display::fmt(&serde_plain::to_string(self).unwrap(), f)
    }
}

#[derive(Deserialize, Debug)]
pub struct GateEvent {
    pub channel: Channel,
    pub event: String,
    // #[serde(borrow)]
    pub result: RawValue,
    pub error: Option<SubscribeError>,
    // time_ms: u64
}

#[derive(Deserialize, Debug)]
pub struct SubscribeError {
    pub code: i32,
    pub message: String,
}

#[derive(Deserialize, Debug)]
pub struct SubscribeStatus {
    pub status: String,
}

#[derive(Deserialize)]
pub struct GateBookTiker {
    #[serde(rename = "s")]
    pub symbol: GateSpotSymbol,
    #[serde(rename = "b")]
    #[serde(deserialize_with = "de_from_str")]
    bid_price: f64,
    #[serde(rename = "B")]
    #[serde(deserialize_with = "de_from_str")]
    bid_qty: f64,
    #[serde(rename = "a")]
    #[serde(deserialize_with = "de_from_str")]
    ask_price: f64,
    #[serde(rename = "A")]
    #[serde(deserialize_with = "de_from_str")]
    ask_qty: f64,
    #[serde(rename = "t")]
    pub timestamp_ms: i64,
}

impl From<GateBookTiker> for BboTicker {
    fn from(v: GateBookTiker) -> Self {
        Self {
            symbol: v.symbol.into(),
            timestamp: v.timestamp_ms,
            // local_time_ns: common::time_ns(),
            bid_price: v.bid_price,
            bid_qty: v.bid_qty,
            ask_price: v.ask_price,
            ask_qty: v.ask_qty,
        }
    }
}

#[derive(Deserialize)]
#[allow(dead_code)]
pub struct GateTicker {
    pub currency_pair: GateSpotSymbol,
    #[serde(deserialize_with = "de_from_str")]
    pub high_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub low_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub last: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub change_percentage: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub quote_volume: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub base_volume: f64,
}

impl From<GateTicker> for Ticker {
    fn from(v: GateTicker) -> Self {
        let price_change = (v.last * v.change_percentage / 100.0).trunc();
        Self {
            symbol: v.currency_pair.into(),
            high: v.high_24h,
            low: v.low_24h,
            open: v.last - price_change,
            close: v.last,
            volume: v.base_volume,
            quote_volume: v.quote_volume,
            change: price_change,
            change_percent: v.change_percentage,
            timestamp: 0,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum GateOrderStatus {
    Open,
    Closed,
    Cancelled,
    // IOC,
    // STP,
}

impl GateOrderStatus {
    pub fn into_base(self, left: bool) -> OrderStatus {
        match (self, left) {
            (GateOrderStatus::Open, true) => OrderStatus::PartiallyFilled,
            (GateOrderStatus::Open, false) => OrderStatus::Open,
            (GateOrderStatus::Closed, _) => OrderStatus::Filled,
            (GateOrderStatus::Cancelled, _) => OrderStatus::Canceled,
        }
    }
}

#[derive(Deserialize, Debug)]
pub struct GateFill {
    pub order_id: String,
    #[serde(rename = "text")]
    pub client_order_id: String,
    pub create_time_ms: i64,
    pub currency_pair: GateSpotSymbol,
    pub side: GateOrderSide,
    #[serde(deserialize_with = "de_from_str")]
    pub amount: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub price: f64,
}
impl From<GateFill> for Order {
    fn from(v: GateFill) -> Self {
        Self {
            id: v.order_id,
            cid: Some(v.client_order_id),
            timestamp: v.create_time_ms,
            status: OrderStatus::Filled,
            symbol: v.currency_pair.into(),
            order_type: OrderType::Market,
            side: v.side.into(),
            pos_side: None,
            time_in_force: TimeInForce::GTC,
            price: Some(v.price),
            amount: Some(v.amount),
            filled: v.amount,
            filled_avg_price: v.price,
            quote_amount: None,
            source: OrderSource::UserTrade,
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum FillOrOrder {
    Fill,
    Order,
}
#[derive(Deserialize, Debug)]
pub struct GateOrder {
    /// 订单ID
    pub id: String,

    /// 客户自定义的订单ID，可选
    #[serde(rename = "text")]
    pub cid: Option<String>,

    /// 订单创建的Unix时间戳，以毫秒为单位
    #[serde(deserialize_with = "de_from_str")]
    pub create_time: i64,
    #[serde(deserialize_with = "de_from_str")]
    pub update_time_ms: i64,

    /// 交易对符号
    pub currency_pair: GateSpotSymbol,

    /// 订单类型
    #[serde(rename = "type")]
    pub order_type: GateOrderType,

    /// 订单方向
    pub side: GateOrderSide,

    /// 订单价格，可能为空
    ///
    #[serde(deserialize_with = "de_from_str")]
    pub price: f64,

    /// 订单的基准货币数量
    #[serde(deserialize_with = "de_from_str")]
    pub amount: f64,

    /// 已成交的基准货币数量
    #[serde(deserialize_with = "de_from_str")]
    pub filled_total: f64,

    #[serde(deserialize_with = "de_from_str")]
    pub filled_amount: f64,

    /// 订单成交均价
    #[serde(deserialize_with = "de_from_str")]
    pub avg_deal_price: f64,

    pub time_in_force: GateTimeInForce,

    pub status: Option<GateOrderStatus>,

    #[serde(deserialize_with = "de_from_str")]
    pub left: f64,

    pub finish_as: String,
}

impl GateOrder {
    pub fn to_local_order(self) -> Order {
        let status = match self.finish_as.to_lowercase().as_str() {
            "open" => {
                if self.filled_total > 0.0 {
                    OrderStatus::PartiallyFilled
                } else {
                    OrderStatus::Open
                }
            }
            "filled" => OrderStatus::Filled,
            "cancelled" => OrderStatus::Canceled,
            "ioc" => {
                if self.left != 0.0 {
                    OrderStatus::Canceled
                } else {
                    OrderStatus::Filled
                }
            }
            "stp" => OrderStatus::Canceled, // 自成交预防取消，视为取消
            _ => OrderStatus::default(),    // 处理未知状态，返回默认值 Open
        };

        let time_in_force = self.time_in_force.into();
        Order {
            id: self.id,
            cid: self.cid,
            timestamp: self.update_time_ms,
            status,
            symbol: self.currency_pair.into(),
            order_type: self.order_type.into(),
            side: self.side.into(),
            pos_side: None,
            time_in_force,
            price: Some(self.price),
            amount: Some(self.amount.abs()),
            filled: self.filled_amount.abs(),
            filled_avg_price: self.avg_deal_price,
            quote_amount: None,
            source: OrderSource::Order,
        }
    }
}

impl From<GateOrder> for Order {
    fn from(gate_order: GateOrder) -> Self {
        let status = match gate_order.status.unwrap() {
            GateOrderStatus::Open => {
                if gate_order.filled_total > 0.0 {
                    OrderStatus::PartiallyFilled
                } else {
                    OrderStatus::Open
                }
            }
            GateOrderStatus::Closed => OrderStatus::Filled,
            GateOrderStatus::Cancelled => OrderStatus::Canceled,
            // _ => unreachable!(),
            // other => {
            //     if gate_order.left > 0.0 {
            //         OrderStatus::PartiallyFilled
            //     } else {
            //         match other {
            //             GateOrderStatus::IOC => OrderStatus::Filled,
            //             GateOrderStatus::STP => OrderStatus::Canceled,
            //             _ => unreachable!(),
            //         }
            //     }
            // }
        };
        let time_in_force = gate_order.time_in_force.into();

        Self {
            id: gate_order.id,
            cid: gate_order.cid,
            timestamp: gate_order.update_time_ms,
            status,
            symbol: gate_order.currency_pair.into(),
            order_type: gate_order.order_type.into(),
            side: gate_order.side.into(),
            pos_side: Some(PosSide::Long),
            time_in_force,
            price: Some(gate_order.price),
            amount: Some(gate_order.amount.abs()),
            filled: gate_order.filled_amount.abs(),
            filled_avg_price: gate_order.avg_deal_price,
            quote_amount: None,
            source: OrderSource::Order,
        }
    }
}

#[derive(Deserialize)]
pub struct OrderBooks {
    #[serde(rename = "s")]
    pub currency_pair: GateSpotSymbol,
    #[serde(rename = "t")]
    pub timestamp: i64,
    pub asks: Vec<OrderBookItem>,
    pub bids: Vec<OrderBookItem>,
}
#[derive(Deserialize)]
pub struct OrderBooksV3 {
    pub current: f64,
    pub asks: Vec<OrderBookItem>,
    pub bids: Vec<OrderBookItem>,
}
#[derive(Deserialize)]
pub struct TradeV3 {
    #[serde(deserialize_with = "de_id_from_any")]
    id: u64,
    pub time: f64,
    #[serde(deserialize_with = "de_from_str")]
    price: f64,
    #[serde(deserialize_with = "de_from_str")]
    amount: f64,
    r#type: GateOrderSide,
}
impl TradeV3 {
    pub fn into_trade(self, symbol: GateSpotSymbol) -> Trade {
        let ts = (self.time * 1000.0) as i64;
        Trade {
            amount: self.amount,
            price: self.price,
            side: self.r#type.into(),
            symbol: symbol.0,
            timestamp: ts,
            id: self.id.to_string(),
        }
    }
}
impl OrderBooksV3 {
    pub fn into_depth(self, symbol: GateSpotSymbol) -> Depth {
        Depth {
            symbol: symbol.0,
            bids: self
                .bids
                .into_iter()
                .map(|item| DepthEntry {
                    price: item.p,
                    amount: item.s,
                })
                .collect::<Vec<DepthEntry>>(),
            asks: self
                .asks
                .into_iter()
                .map(|item| DepthEntry {
                    price: item.p,
                    amount: item.s,
                })
                .collect::<Vec<DepthEntry>>(),
            timestamp: (self.current * 1000.0) as i64,
        }
    }
}

impl From<OrderBooks> for Depth {
    fn from(value: OrderBooks) -> Self {
        Self {
            symbol: value.currency_pair.0,
            bids: value
                .bids
                .into_iter()
                .map(|item| DepthEntry {
                    price: item.p,
                    amount: item.s,
                })
                .collect::<Vec<DepthEntry>>(),
            asks: value
                .asks
                .into_iter()
                .map(|item| DepthEntry {
                    price: item.p,
                    amount: item.s,
                })
                .collect::<Vec<DepthEntry>>(),
            timestamp: value.timestamp,
        }
    }
}

#[derive(Deserialize, Debug)]
pub struct GateTrade {
    pub id: i64,
    /// 创建交易的Unix时间戳，以毫秒为单位 (作为字符串，需要转换)
    #[serde(deserialize_with = "de_from_str")]
    pub create_time_ms: f64,

    pub side: GateOrderSide,

    /// 交易对符号
    pub currency_pair: GateSpotSymbol,

    /// 交易数量 (作为字符串，需要转换)
    #[serde(deserialize_with = "de_from_str")]
    pub amount: f64,

    /// 交易价格 (作为字符串，需要转换)
    #[serde(deserialize_with = "de_from_str")]
    pub price: f64,

    /// 范围 (不知道具体作用，暂时忽略)
    pub range: String,
}
impl From<GateTrade> for Trade {
    fn from(trade_data: GateTrade) -> Self {
        Self {
            id: trade_data.id.to_string(),               // 将 id 转换为字符串
            symbol: trade_data.currency_pair.into(),     // 假设 Symbol 类型可以从字符串转换
            timestamp: trade_data.create_time_ms as i64, // 解析时间戳并转换为 i64
            price: trade_data.price,                     // 解析价格
            amount: trade_data.amount,                   // 解析成交量
            side: trade_data.side.into(),
        }
    }
}
#[derive(Debug, Deserialize, Clone)]
pub struct GateAccountInfo {
    balances: FxHashMap<String, UnifiedBalance>,
    /// 总权益
    pub unified_account_total_equity: String,
    /// 总可用
    pub total_available_margin: String,
    /// 总保证金率
    pub total_maintenance_margin_rate: String,
}
#[derive(Debug, Deserialize, Clone)]
pub struct UnifiedBalance {
    #[serde(deserialize_with = "de_from_str")]
    available: f64,
    #[serde(deserialize_with = "de_from_str")]
    equity: f64,
    // total_freeze: f64,
}
impl GateAccountInfo {
    pub fn to_acct_info(&self) -> AccountInfo {
        AccountInfo {
            total_mmr: self
                .total_maintenance_margin_rate
                .parse::<f64>()
                .unwrap_or_default(),
            total_equity: self
                .unified_account_total_equity
                .parse::<f64>()
                .unwrap_or_default(),
            total_available: self
                .total_available_margin
                .parse::<f64>()
                .unwrap_or_default(),
        }
    }

    pub fn get_balances(self) -> Vec<Balance> {
        self.balances
            .into_iter()
            .map(|(k, v)| Balance {
                asset: k,
                balance: v.equity,
                available_balance: v.available,
                unrealized_pnl: 0.0,
            })
            .collect()
    }
}

#[allow(dead_code)]
#[derive(Deserialize, Debug)]
pub struct AccountDetail {
    ip_whitelist: Vec<String>,
    /// 交易对白名单
    currency_pairs: Vec<GateSpotSymbol>,
    pub user_id: u32,
    /// 用户vip等级
    pub tier: u8,
    pub key: AccountDetailKey,
}

#[derive(Deserialize, Debug)]
pub struct AccountDetailKey {
    /// 1 - 经典模式 2 - 统一账户unified
    pub mode: u8,
}
#[derive(Serialize)]
struct WsReqAuth {
    /// api_key
    method: &'static str,
    #[serde(rename = "KEY")]
    key: String,
    #[serde(rename = "SIGN")]
    sign: String,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(untagged)]
enum ReqValue {
    String(String),
    Number(i64),
    Float(f64),
    Array(Vec<ReqValue>),
    // 可以添加更多类型
}
#[derive(Serialize, Debug)]
pub struct WebWsReq {
    id: i64,
    method: ChannelV3Subscribe,
    params: Vec<ReqValue>,
}

pub(crate) fn new_v3_req(channel: Channel, payload: Option<Vec<String>>, time: i64) -> WebWsReq {
    // trade
    let mut params = vec![];
    match channel {
        Channel::Trades => {
            params = payload
                .unwrap_or_default()
                .into_iter()
                .map(ReqValue::String)
                .collect();
        }
        // depth
        Channel::OrderBook => {
            let payload = payload.unwrap_or_default();

            let symbol = payload.first().cloned().unwrap_or_else(|| "".to_string());
            let levels = payload.get(1).cloned().unwrap_or_else(|| "5".to_string());
            let levels = levels.parse::<u32>().unwrap_or_default();
            let precision = payload.get(2).cloned().unwrap_or_else(|| "0.1".to_string());
            params = vec![
                ReqValue::String(symbol.clone()),
                ReqValue::Number(levels as i64),
                ReqValue::String(precision),
            ];
        }
        _ => {}
    }
    WebWsReq {
        id: time,
        method: channel.into(),
        params,
    }
}
#[derive(Serialize)]
pub struct WsReq<T: Serialize> {
    time: i64,
    pub channel: Channel,
    event: &'static str,

    #[serde(skip_serializing_if = "Option::is_none")]
    payload: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    auth: Option<WsReqAuth>,
}
/// event: subscribe or unsubscribe
pub(crate) fn new_ws_subscribe_req<T: Serialize>(
    channel: Channel,
    event: &'static str,
    payload: Option<T>,
    time: i64,
    key: &str,
    secret: &str,
) -> WsReq<T> {
    // ws 下单用的另一种鉴权方法
    if channel.is_public() {
        return WsReq {
            time,
            channel,
            event,
            payload,
            auth: None,
        };
    }

    let s = format!("channel={channel}&event={event}&time={time}");

    let mut mac = Hmac::<Sha512>::new_from_slice(secret.as_bytes()).unwrap();
    mac.update(s.as_bytes());
    let digest = mac.finalize().into_bytes();
    let sign = format!("{digest:02x}");

    let auth = WsReqAuth {
        method: "api_key",
        key: key.to_string(),
        sign,
    };

    WsReq {
        time,
        channel,
        event,
        payload,
        auth: Some(auth),
    }
}

#[derive(Serialize, Deserialize, Debug, Clone, Default)]
pub struct GateSpotWsBalance {
    pub currency: String,
    #[serde(deserialize_with = "de_from_str")]
    pub total: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub available: f64,
}

impl From<GateSpotWsBalance> for Balance {
    fn from(raw: GateSpotWsBalance) -> Self {
        Balance {
            asset: raw.currency,
            balance: raw.total,
            available_balance: raw.available,
            unrealized_pnl: 0.0, // 如果没有提供浮动盈亏，可以设置为 0.0 或者根据需要计算
        }
    }
}

#[derive(Serialize)]
pub struct GateSpotOrdersReq {
    pub status: String,
    pub currency_pair: GateSpotSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub from: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub to: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub account: Option<GateAccount>,
}

impl GateSpotOrdersReq {
    pub fn new(
        status: String,
        currency_pair: GateSpotSymbol,
        account: Option<GateAccount>,
    ) -> Self {
        GateSpotOrdersReq {
            status,
            currency_pair,
            from: None,
            to: None,
            account,
        }
    }
}

#[derive(Deserialize, Debug)]
pub struct GateOrderRSP {
    /// 订单ID
    pub id: String,

    /// 客户自定义的订单ID，可选
    #[serde(rename = "client_order_id")]
    pub cid: Option<String>,

    /// 订单创建的Unix时间戳，以毫秒为单位
    #[serde(deserialize_with = "de_from_str")]
    pub create_time: i64,

    pub create_time_ms: i64,

    /// 交易对符号
    pub currency_pair: GateSpotSymbol,

    /// 订单类型
    #[serde(rename = "type")]
    pub order_type: GateOrderType,

    /// 订单方向
    pub side: GateOrderSide,

    /// 订单价格，可能为空
    ///
    #[serde(deserialize_with = "de_from_str")]
    pub price: f64,

    /// 订单的基准货币数量
    #[serde(deserialize_with = "de_from_str")]
    pub amount: f64,

    /// 已成交的基准货币数量
    #[serde(deserialize_with = "de_from_str")]
    pub filled_amount: f64,

    /// 订单成交均价
    #[serde(default, deserialize_with = "de_from_str")]
    pub avg_deal_price: f64,

    pub time_in_force: GateTimeInForce,

    pub status: GateOrderStatus,

    #[serde(deserialize_with = "de_from_str")]
    pub left: f64,
}

impl From<GateOrderRSP> for Order {
    fn from(gate_order: GateOrderRSP) -> Self {
        Self {
            id: gate_order.id,
            cid: gate_order.cid,
            timestamp: gate_order.create_time_ms,
            status: gate_order.status.into_base(gate_order.left > 0.0),
            symbol: gate_order.currency_pair.into(),
            order_type: gate_order.order_type.into(),
            side: gate_order.side.into(),
            pos_side: Some(PosSide::Long),
            time_in_force: gate_order.time_in_force.into(),
            price: Some(gate_order.price),
            amount: Some(gate_order.amount),
            filled: gate_order.filled_amount,
            filled_avg_price: gate_order.avg_deal_price,
            quote_amount: None,
            source: OrderSource::Order,
        }
    }
}
#[derive(Deserialize, Debug, Default)]
pub struct AllOpenOrdersRsp {
    pub items: Vec<OpenOrdersRsp>,
}

#[derive(Serialize, Debug)]
pub struct OpenOrdersReq {
    pub(crate) account: GateAccount,
}
#[derive(Deserialize, Debug, Default)]
pub struct OpenOrdersRsp {
    pub currency_pair: GateSpotSymbol,
    pub total: i64,
    pub orders: Vec<GateOrderRSP>,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "snake_case")]
pub enum TransferWallet {
    Spot,
    Margin,
    CrossMargin,
    Futures,
    Delivery, // 交割合约
    Options,
}

impl From<WalletType> for TransferWallet {
    fn from(value: WalletType) -> Self {
        match value {
            WalletType::Spot => TransferWallet::Spot,
            WalletType::Margin => TransferWallet::CrossMargin,
            WalletType::UsdtFuture => TransferWallet::Futures,
            WalletType::CoinFuture => TransferWallet::Futures,
            WalletType::IsolatedMargin => TransferWallet::Margin,
        }
    }
}

#[derive(Serialize, Debug)]
#[allow(dead_code)]
pub(crate) struct TransferReq {
    currency: String,
    from: TransferWallet,
    to: TransferWallet,
    amount: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    currency_pair: Option<String>, // 杠杆交易对。转入或转出杠杆账户时必填
    #[serde(skip_serializing_if = "Option::is_none")]
    settle: Option<String>, // 合约结算币种。 转入转出合约账户时必填
}

impl From<Transfer> for TransferReq {
    fn from(value: Transfer) -> Self {
        Self {
            currency: value.asset,
            from: value.from.into(),
            to: value.to.into(),
            amount: value.amount.to_string(),
            currency_pair: Some("SOL_USDT".to_string()), // todo!();
            settle: None,
        }
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct TransferRsp {}

#[derive(Deserialize, Debug)]
pub struct AccountModeRsp {
    pub mode: GateAccountMode,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "snake_case")]
pub enum GateAccountMode {
    Classic,
    MultiCurrency,
    Portfolio,
}

impl From<GateAccountMode> for AccountMode {
    fn from(value: GateAccountMode) -> Self {
        match value {
            GateAccountMode::Classic => AccountMode::Classic,
            GateAccountMode::MultiCurrency => AccountMode::MultiCurrency,
            GateAccountMode::Portfolio => AccountMode::Portfolio,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum TriggerRule {
    #[serde(rename = ">=")]
    GreaterEqual,
    #[serde(rename = "<=")]
    LessEqual,
}

#[derive(Serialize, Debug)]
pub struct PriceTrigger {
    pub price: String,
    pub rule: TriggerRule,
    pub expiration: u64,
}

#[derive(Serialize, Debug)]
pub struct PriceOrderPut {
    #[serde(rename = "type")]
    pub order_type: GateOrderType,
    pub side: GateOrderSide,
    pub price: Option<String>,
    pub amount: String,
    pub account: GateAccount,
    pub time_in_force: GateTimeInForce,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub text: Option<String>,
}

#[derive(Serialize, Debug)]
pub struct GateSpotPriceOrderReq {
    pub trigger: PriceTrigger,
    pub put: PriceOrderPut,
    pub market: GateSpotSymbol,
}

// 响应结构体
#[derive(Deserialize, Debug)]
pub struct PriceOrderRsp {
    pub id: u64,
}

impl GateSpotPriceOrderReq {
    pub fn new(
        trigger_price: f64,
        trigger_rule: TriggerRule,
        order: Order,
        account: GateAccount,
        trigger: CloseTrigger,
    ) -> Result<Self, Error> {
        let symbol = order.symbol.clone().into();
        let (order_type, limit_price) = match trigger.trigger_action {
            TriggerAction::Limit(price) => (GateOrderType::Limit, Some(price)),
            TriggerAction::Market => (GateOrderType::Market, None),
        };
        let put = PriceOrderPut {
            account,
            price: limit_price.map(|p| p.to_string()),
            amount: order.amount.unwrap_or(0.0).to_string(),
            order_type,
            side: order.side.into(),
            time_in_force: GateTimeInForce::GTC,
            text: None,
        };

        Ok(GateSpotPriceOrderReq {
            trigger: PriceTrigger {
                price: trigger_price.to_string(),
                rule: trigger_rule,
                expiration: 0,
            },
            put,
            market: symbol,
        })
    }
}

#[derive(Deserialize, Debug, Clone)]
pub struct GateKline {
    /// 时间戳
    #[serde(rename = "t")]
    #[serde(deserialize_with = "de_from_str")]
    pub timestamp: i64,
    /// 成交量
    #[serde(rename = "v")]
    #[serde(deserialize_with = "de_from_str")]
    pub volume: f64,
    /// 收盘价格
    #[serde(rename = "c", deserialize_with = "de_from_str")]
    pub close: f64,
    /// 最高价格
    #[serde(rename = "h", deserialize_with = "de_from_str")]
    pub high: f64,
    /// 最低价格
    #[serde(rename = "l", deserialize_with = "de_from_str")]
    pub low: f64,
    /// 开盘价格
    #[serde(rename = "o", deserialize_with = "de_from_str")]
    pub open: f64,
    /// 合约名称（包含时间间隔前缀，如 "1m_BTC_USD"）
    #[serde(rename = "n")]
    pub name: String,
    /// 成交原始币种数量
    #[serde(rename = "a", deserialize_with = "de_from_str")]
    pub amount: f64,
}

impl From<GateKline> for Kline {
    fn from(v: GateKline) -> Self {
        // 从名称中提取symbol，去掉时间间隔前缀
        let mut interval = KlineInterval::default();
        let symbol_str = if let Some(index) = v.name.find('_') {
            interval = KlineInterval::from_str(&v.name[..index]).unwrap_or_default();
            &v.name[index + 1..]
        } else {
            &v.name
        };

        let symbol: Symbol = serde_plain::from_str(symbol_str).unwrap_or_default();
        Self {
            symbol,
            interval,
            candles: vec![Candle {
                close: v.close,
                timestamp: v.timestamp * 1000,
                open: v.open,
                high: v.high,
                low: v.low,
                quote_volume: v.volume,
                volume: v.amount,
                ..Default::default()
            }],
        }
    }
}

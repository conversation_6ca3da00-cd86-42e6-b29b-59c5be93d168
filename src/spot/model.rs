use once_cell::sync::OnceCell;
use quant_common::base::traits::GetKlineParams;
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::str::FromStr;
use std::time::Duration;

use quant_common::base::{FeeRate, OrderParams, Trade, Transfer, WalletType, model::*};
use quant_common::utils::{de_from_str, deserialize_f64_pairs};
use quant_common::{Error, Result, qerror, time_ms};

use crate::util::{
    BnOrderItemRsp, PrefixFormat, is_ed25519_secret, parse_prefix, sign_ws_api_params,
};

use super::rest::BinanceSpot;

pub(crate) const EXCHANGE: &str = "BinanceSpot";

pub(crate) const WS_URL: &str = "stream.binance.com";
pub(crate) const WS_TEST_URL: &str = "stream.testnet.binance.vision";

pub(crate) const WS_API_URL: &str = "ws-api.binance.com";
pub(crate) const WS_API_TEST_URL: &str = "ws-api.testnet.binance.vision";

pub(crate) const BOOK_TICKER_CHANNEL: &str = "@bookTicker";
pub(crate) const DEPTH_CHANNEL: &str = "@depth@100ms";
pub(crate) const KLINE_CHANNEL: &str = "kline";
pub(crate) const TRADE_CHANNEL: &str = "@trade";

pub(crate) const REST_SPOT_BASE_URL: &str = "api.binance.com";
pub(crate) const REST_TEST_BASE_URL: &str = "testnet.binance.vision/api";

pub(crate) const HEADER_KEY: &str = "X-MBX-APIKEY";

/// public api
pub(crate) const PATH_BOOKTICKER: &str = "/api/v3/ticker/bookTicker";
pub(crate) const PATH_DEPTH: &str = "/api/v3/depth";
pub(crate) const PATH_EXCHANGE_INFO: &str = "/api/v3/exchangeInfo";
pub(crate) const PATH_TICKER_24H: &str = "/api/v3/ticker/24hr";
pub(crate) const PATH_KLINE: &str = "/api/v3/klines";

/// private api
pub(crate) const PATH_ACCOUNT: &str = "/api/v3/account";
pub(crate) const PATH_ALL_ORDERS: &str = "/api/v3/allOrders";
pub(crate) const PATH_BNB_BURN: &str = "/sapi/v1/bnbBurn";
pub(crate) const PATH_CANCEL_REPLACE_ORDER: &str = "/api/v3/order/cancelReplace";
pub(crate) const PATH_CAPITALS: &str = "/sapi/v1/capital/config/getall";
pub(crate) const PATH_COMMISSION_RATE: &str = "/api/v3/account/commission";
pub(crate) const PATH_DEPOSIT: &str = "/sapi/v1/capital/deposit/address";
pub(crate) const PATH_LISTEN_KEY: &str = "/api/v3/userDataStream";
pub(crate) const PATH_OPEN_ORDERS: &str = "/api/v3/openOrders";
pub(crate) const PATH_ORDER: &str = "/api/v3/order";
pub(crate) const PATH_SUB_TRANSFER: &str = "/sapi/v1/sub-account/universalTransfer";
pub(crate) const PATH_TRANSFER: &str = "/sapi/v1/asset/transfer";
pub(crate) const PATH_WITHDRAW: &str = "/sapi/v1/capital/withdraw/apply";

pub(crate) const DEFAULT_RECV_WINDOW: u32 = 5000; // 5000 ms

#[derive(Debug, Serialize, Default, Deserialize, Clone, Copy)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnOrderSide {
    #[default]
    Buy,
    Sell,
}

#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum BnOrderState {
    New,
    PartiallyFilled,
    Filled,
    Expired,
    Canceled,
    ExpiredInMatch,
}

impl From<BnOrderState> for OrderStatus {
    fn from(value: BnOrderState) -> Self {
        match value {
            BnOrderState::New => Self::Open,
            BnOrderState::PartiallyFilled => Self::PartiallyFilled,
            BnOrderState::Filled => Self::Filled,
            _ => Self::Canceled,
        }
    }
}

pub struct SpecialSymbols {
    symbols_prefix: FxHashMap<Symbol, PrefixFormat>,
    bn_assets: FxHashMap<String, (PrefixFormat, String)>, // bn asset => (prefix, asset)
    assets: FxHashMap<String, (PrefixFormat, String)>,    // asset => (prefix, bn asset)
}

impl SpecialSymbols {
    #[inline]
    pub fn get_symbol_prefix(&self, symbol: &Symbol) -> Option<PrefixFormat> {
        self.symbols_prefix.get(symbol).copied()
    }

    #[inline]
    pub fn get_prefix_by_bn(&self, asset: &str) -> Option<(PrefixFormat, String)> {
        self.bn_assets.get(asset).cloned()
    }

    #[inline]
    pub fn get_prefix_by_asset(&self, asset: &str) -> Option<(PrefixFormat, String)> {
        self.assets.get(asset).cloned()
    }
}

static SPECIAL_SYMBOLS: OnceCell<SpecialSymbols> = OnceCell::new();

pub(crate) async fn init_special_symbols(rest: &BinanceSpot) -> Result<()> {
    if SPECIAL_SYMBOLS.get().is_some() {
        return Ok(());
    }

    for i in (0..5).rev() {
        match rest.symbol_infos().await {
            Ok(info) => {
                let asset_prefix_bn = info
                    .iter()
                    .filter(|s| BnQuote::is_other(&s.symbol.inner.quote))
                    .filter_map(|s| match s.symbol.prefix_mul {
                        PrefixFormat::Numeric(1) => None,
                        _ => Some((
                            s.symbol.inner.base.clone(),
                            s.symbol.prefix_mul,
                            s.base_asset.clone(),
                        )),
                    })
                    .collect::<Vec<_>>();

                let bn_assets = asset_prefix_bn
                    .iter()
                    .map(|(asset, prefix, bn)| (bn.clone(), (*prefix, asset.clone())))
                    .collect::<FxHashMap<_, _>>();

                let assets = asset_prefix_bn
                    .into_iter()
                    .map(|(asset, prefix, bn)| (asset, (prefix, bn)))
                    .collect::<FxHashMap<_, _>>();

                let symbols_prefix = info
                    .into_iter()
                    .map(|s| (s.symbol.inner, s.symbol.prefix_mul))
                    .collect::<FxHashMap<_, _>>();

                let _ = SPECIAL_SYMBOLS.set(SpecialSymbols {
                    symbols_prefix,
                    bn_assets,
                    assets,
                });

                return Ok(());
            }
            Err(e) if i == 0 => return Err(e),
            Err(e) => {
                warn!("symbol_infos failed: {e}");
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        }
    }
    Err(qerror!("get exchange info failed"))
}

#[inline]
fn spot_prefix_mul_by_symbol(symbol: &Symbol) -> PrefixFormat {
    SPECIAL_SYMBOLS
        .get()
        .unwrap()
        .get_symbol_prefix(symbol)
        .unwrap_or_default()
}

#[inline]
pub(crate) fn bn_asset_prefix(asset: &str) -> (PrefixFormat, String) {
    match SPECIAL_SYMBOLS.get().unwrap().get_prefix_by_bn(asset) {
        Some(prefix) => prefix,
        None => (PrefixFormat::Numeric(1), asset.to_string()),
    }
}

#[inline]
pub(crate) fn asset_prefix(asset: &str) -> (PrefixFormat, String) {
    match SPECIAL_SYMBOLS.get().unwrap().get_prefix_by_asset(asset) {
        Some(prefix) => prefix,
        None => (PrefixFormat::Numeric(1), asset.to_string()),
    }
}

#[derive(Clone, PartialEq, Default, Eq, Hash)]
pub struct BnSymbol {
    pub inner: Symbol,
    pub prefix_mul: PrefixFormat,
}

impl BnSymbol {
    pub(crate) fn to_lowercase(&self) -> String {
        self.to_string().to_ascii_lowercase()
    }

    pub(crate) fn prefix(&self) -> u32 {
        self.prefix_mul.prefix()
    }

    pub(crate) fn prefix_f64(&self) -> f64 {
        self.prefix_mul.prefix() as f64
    }
}

impl From<BnSymbol> for Symbol {
    fn from(value: BnSymbol) -> Self {
        value.inner
    }
}

impl From<Symbol> for BnSymbol {
    fn from(inner: Symbol) -> Self {
        let prefix_mul = spot_prefix_mul_by_symbol(&inner);
        Self { inner, prefix_mul }
    }
}

impl std::fmt::Display for BnSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let base = self.prefix_mul.to_string(&self.inner.base);
        write!(f, "{base}{}", self.inner.quote)
    }
}

impl std::fmt::Debug for BnSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        self.inner.fmt(f)
    }
}

impl FromStr for BnSymbol {
    type Err = Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(serde_plain::from_str(s)?)
    }
}

impl Serialize for BnSymbol {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}

#[derive(Deserialize, Serialize)]
#[allow(clippy::upper_case_acronyms)]
pub enum BnQuote {
    USDT,
    USDC,
    BUSD,
    FDUSD,
    TUSD,

    USD,
    EUR,
    JPY,

    ARS,
    BNB,
    BRL,
    BTC,
    COP,
    CZK,
    DAI,
    DOGE,
    ETH,
    EURI,
    MXN,
    PLN,
    RON,
    SOL,
    TRX,
    TRY,
    UAH,
    XRP,
    ZAR,
}

impl From<BnQuote> for QuoteCcy {
    fn from(value: BnQuote) -> Self {
        match value {
            BnQuote::USDT => QuoteCcy::USDT,
            BnQuote::USDC => QuoteCcy::USDC,
            BnQuote::USD => QuoteCcy::USD,
            quote => QuoteCcy::OTHER(serde_plain::to_string(&quote).unwrap_or_default()),
        }
    }
}

impl BnQuote {
    pub(crate) fn is_other(quote_ccy: &QuoteCcy) -> bool {
        Self::from_quote_ccy(quote_ccy).is_some()
    }

    pub(crate) fn from_quote_ccy(quote_ccy: &QuoteCcy) -> Option<Self> {
        match quote_ccy {
            QuoteCcy::USDT => Some(BnQuote::USDT),
            QuoteCcy::USDC => Some(BnQuote::USDC),
            QuoteCcy::USD => Some(BnQuote::USD),
            QuoteCcy::OTHER(s) => serde_plain::from_str(s).ok(),
        }
    }
}

impl<'de> Deserialize<'de> for BnSymbol {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let symbol = String::deserialize(deserializer)?.to_uppercase();
        let (prefix_mul, symbol) = parse_prefix(symbol).map_err(serde::de::Error::custom)?;
        let inner = Symbol::split_no_separator_symbol::<BnQuote>(&symbol)
            .map_err(serde::de::Error::custom)?;

        Ok(Self { inner, prefix_mul })
    }
}

impl From<OrderSide> for BnOrderSide {
    fn from(value: OrderSide) -> Self {
        match value {
            OrderSide::Sell => Self::Sell,
            OrderSide::Buy => Self::Buy,
        }
    }
}

impl From<BnOrderSide> for OrderSide {
    fn from(value: BnOrderSide) -> Self {
        match value {
            BnOrderSide::Sell => Self::Sell,
            BnOrderSide::Buy => Self::Buy,
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Default, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum BnOrderType {
    #[default]
    Limit, // 限价单
    Market,     //  市价单
    LimitMaker, //  限价做市单

    // 以下先不支持, 因为:
    //   1. 该市场没有改单接口，利用 "撤消挂单再下单" 兼容的功能。 这时没有order_params计算以下类型了
    //   2. 程序会通过其他方式实现止盈止损
    StopLoss,        //  止损单
    StopLossLimit,   //  限价止损单
    TakeProfit,      //  止盈单
    TakeProfitLimit, //  限价止盈单
}

impl From<BnOrderType> for OrderType {
    fn from(value: BnOrderType) -> OrderType {
        match value {
            BnOrderType::Limit => OrderType::Limit,
            BnOrderType::LimitMaker => OrderType::Limit,
            BnOrderType::StopLossLimit => OrderType::Limit,
            BnOrderType::TakeProfitLimit => OrderType::Limit,

            BnOrderType::Market => OrderType::Market,
            BnOrderType::StopLoss => OrderType::Market,
            BnOrderType::TakeProfit => OrderType::Market,
        }
    }
}

impl BnOrderType {
    pub(crate) fn with_tif(order_type: &OrderType, time_in_force: &TimeInForce) -> Self {
        if TimeInForce::PostOnly == *time_in_force {
            BnOrderType::LimitMaker
        } else {
            match order_type {
                OrderType::Limit => BnOrderType::Limit,
                OrderType::Market => BnOrderType::Market,
            }
        }
    }

    pub(crate) fn with_params(
        order_type: &OrderType,
        time_in_force: &TimeInForce,
        params: &OrderParams,
    ) -> Result<Self> {
        if TimeInForce::PostOnly == *time_in_force {
            Ok(BnOrderType::LimitMaker)
        } else {
            match (
                order_type,
                params.take_profit.is_some(),
                params.stop_loss.is_some(),
            ) {
                (_, true, true) => Err(qerror!("无下同时止盈止损")),
                (OrderType::Limit, true, false) => Ok(BnOrderType::TakeProfitLimit),
                (OrderType::Limit, false, true) => Ok(BnOrderType::StopLossLimit),
                (OrderType::Limit, false, false) => Ok(BnOrderType::Limit),
                (OrderType::Market, true, false) => Ok(BnOrderType::TakeProfit),
                (OrderType::Market, false, true) => Ok(BnOrderType::StopLoss),
                (OrderType::Market, false, false) => Ok(BnOrderType::Market),
            }
        }
    }
}

// MARKET 市价单
// LIMIT 限价单
// STOP 止损单
// TAKE_PROFIT 止盈单
// LIQUIDATION 强平单
#[derive(Debug, Serialize, Default, Deserialize, Clone)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnWsOrderType {
    #[default]
    Limit,
    Market,
    Stop,
    TakeProfit,
    Liquidation,
}

impl From<BnWsOrderType> for OrderType {
    fn from(value: BnWsOrderType) -> Self {
        match value {
            BnWsOrderType::Limit => OrderType::Limit,
            _ => OrderType::Market,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Default, PartialEq, Clone)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnTimeInForce {
    #[default]
    Gtc,
    Ioc,
    Fok,
}

impl BnTimeInForce {
    pub(crate) fn to_time_in_force_with_order_type(&self, order_type: &BnOrderType) -> TimeInForce {
        match order_type {
            BnOrderType::LimitMaker => TimeInForce::PostOnly,
            _ => match self {
                BnTimeInForce::Gtc => TimeInForce::GTC,
                BnTimeInForce::Ioc => TimeInForce::IOC,
                BnTimeInForce::Fok => TimeInForce::FOK,
            },
        }
    }
}

impl From<TimeInForce> for BnTimeInForce {
    fn from(value: TimeInForce) -> Self {
        match value {
            TimeInForce::GTC => Self::Gtc,
            TimeInForce::IOC => Self::Ioc,
            TimeInForce::FOK => Self::Fok,
            TimeInForce::PostOnly => Self::Gtc,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BnTicker {
    pub(crate) symbol: BnSymbol,
    #[serde(deserialize_with = "de_from_str")]
    price_change: f64,
    #[serde(deserialize_with = "de_from_str")]
    price_change_percent: f64,
    // weighted_avg_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) last_price: f64,
    // last_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    open_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    high_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    low_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    volume: f64,
    #[serde(deserialize_with = "de_from_str")]
    quote_volume: f64,
    // open_time: u64,
    close_time: i64,
}

impl From<BnTicker> for Ticker {
    fn from(value: BnTicker) -> Self {
        let prefix_mul = f64::from(value.symbol.prefix_mul.prefix());
        Self {
            symbol: value.symbol.into(),
            timestamp: value.close_time,
            high: prefix_mul.price2base(value.high_price),
            low: prefix_mul.price2base(value.low_price),
            open: prefix_mul.price2base(value.open_price),
            close: prefix_mul.price2base(value.last_price),
            volume: prefix_mul.qty2base(value.volume),
            quote_volume: value.quote_volume,
            change: value.price_change,
            change_percent: value.price_change_percent,
        }
    }
}

#[derive(Debug, Deserialize)]
pub(crate) struct StreamData {
    pub(crate) stream: String,
    pub(crate) data: RawValue,
}

/// websocket bookticker
#[derive(Debug, Deserialize)]
pub(crate) struct BookTickerPayload {
    #[serde(rename = "s")]
    symbol: BnSymbol,
    #[serde(rename = "b")]
    #[serde(deserialize_with = "de_from_str")]
    bid_price: f64,
    #[serde(rename = "B")]
    #[serde(deserialize_with = "de_from_str")]
    bid_qty: f64,
    #[serde(rename = "a")]
    #[serde(deserialize_with = "de_from_str")]
    ask_price: f64,
    #[serde(rename = "A")]
    #[serde(deserialize_with = "de_from_str")]
    ask_qty: f64,
}

impl From<BookTickerPayload> for BboTicker {
    fn from(v: BookTickerPayload) -> BboTicker {
        let bid_price;
        let bid_qty;
        let ask_price;
        let ask_qty;
        let prefix = v.symbol.prefix();
        if prefix == 1 {
            bid_price = v.bid_price;
            bid_qty = v.bid_qty;
            ask_price = v.ask_price;
            ask_qty = v.ask_qty;
        } else {
            let mul = f64::from(prefix);
            bid_price = v.bid_price / mul;
            bid_qty = v.bid_qty * mul;
            ask_price = v.ask_price / mul;
            ask_qty = v.ask_qty * mul;
        }
        BboTicker {
            symbol: v.symbol.into(),
            timestamp: time_ms(),
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
        }
    }
}

#[derive(Debug, Serialize)]
pub(crate) struct SubscriptionRequest {
    pub(crate) id: i64,
    pub(crate) method: SubReqMethod,
    pub(crate) params: Vec<String>,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum SubReqMethod {
    Subscribe,
    // Unsubscribe,
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct OrderResponse {
    pub(crate) order_id: u64,
    // pub(crate)client_order_id: String,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct NewOrderRequest {
    symbol: BnSymbol,
    side: BnOrderSide,
    #[serde(rename = "type")]
    order_type: BnOrderType,
    #[serde(skip_serializing_if = "Option::is_none")]
    time_in_force: Option<BnTimeInForce>,
    #[serde(skip_serializing_if = "Option::is_none")]
    quantity: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    price: Option<String>,
    recv_window: u32,
    #[serde(skip_serializing_if = "Option::is_none")]
    new_client_order_id: Option<String>,
}

impl NewOrderRequest {
    pub(crate) fn new(order: Order, params: &OrderParams) -> Result<Self> {
        let symbol: BnSymbol = order.symbol.clone().into();
        let order_type = BnOrderType::with_params(&order.order_type, &order.time_in_force, params)?;
        let time_in_force = match (&order.order_type, order.time_in_force) {
            (OrderType::Market, _) => None,
            (OrderType::Limit, TimeInForce::PostOnly) => None,
            (OrderType::Limit, time_in_force) => Some(time_in_force.into()),
        };

        let price = match order.order_type {
            OrderType::Limit => order.price.map(|p| p.to_string()),
            OrderType::Market => None,
        };

        Ok(Self {
            symbol,
            side: order.side.into(),
            order_type,
            price,
            quantity: order.amount.map(|a| a.to_string()),
            time_in_force,
            recv_window: DEFAULT_RECV_WINDOW,
            new_client_order_id: order.cid,
        })
    }
}

#[derive(Deserialize)]
pub(crate) struct TrivialResponse {}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct RestBookTicker {
    symbol: BnSymbol,
    #[serde(deserialize_with = "de_from_str")]
    bid_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    bid_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    ask_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    ask_qty: f64,
}

impl From<RestBookTicker> for BboTicker {
    fn from(v: RestBookTicker) -> Self {
        let prefix_mul = v.symbol.prefix() as f64;
        let timestamp = time_ms();
        Self {
            symbol: v.symbol.into(),
            bid_price: prefix_mul.price2base(v.bid_price),
            bid_qty: prefix_mul.qty2base(v.bid_qty),
            ask_price: prefix_mul.price2base(v.ask_price),
            ask_qty: prefix_mul.qty2base(v.ask_qty),
            timestamp,
        }
    }
}

#[derive(Serialize)]
pub(crate) struct SymbolArg {
    symbol: BnSymbol,
}

impl From<Symbol> for SymbolArg {
    fn from(symbol: Symbol) -> Self {
        Self {
            symbol: symbol.into(),
        }
    }
}

/// 账户余额
#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AccountBalance {
    asset: String,
    #[serde(deserialize_with = "de_from_str")]
    free: f64,
    #[serde(deserialize_with = "de_from_str")]
    locked: f64,
}

impl From<AccountBalance> for Balance {
    fn from(v: AccountBalance) -> Self {
        let (prefix_mul, asset) = bn_asset_prefix(&v.asset);
        let mul = prefix_mul.prefix() as f64;
        Self {
            asset,
            balance: mul * (v.free + v.locked),
            available_balance: mul * v.free,
            unrealized_pnl: 0f64,
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Account {
    pub(crate) balances: Vec<AccountBalance>,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct ExchangeInfo {
    pub(crate) symbols: Vec<SymbolInfo>,
}

/// 合约状态 (contractStatus, status):
#[derive(Deserialize, Debug, Eq, PartialEq)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum SpotStatus {
    PreTrading,   // 盘前交易
    Trading,      // 正常交易中
    PostTrading,  // 盘后交易
    EndOfDay,     // 收盘
    Halt,         // 交易终止(该交易对已下线)
    AuctionMatch, // 集合竞价
    Break,        // 交易暂停
}

impl From<SpotStatus> for InsState {
    fn from(value: SpotStatus) -> Self {
        match value {
            SpotStatus::Trading => Self::Normal,
            _ => Self::Close,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct SymbolInfo {
    pub base_asset: String,
    pub quote_asset: String,
    status: SpotStatus,
    pub symbol: BnSymbol,
    // quote_asset_precision: i32,
    // base_asset_precision: i32,
    pub filters: Vec<Value>,
}

impl SymbolInfo {
    pub(crate) fn is_open(&self) -> bool {
        self.status == SpotStatus::Trading
    }
}

#[derive(Deserialize, Debug)]
#[serde(
    tag = "filterType",
    rename_all = "SCREAMING_SNAKE_CASE",
    rename_all_fields = "camelCase"
)]
pub(crate) enum SymbolFilter {
    PriceFilter {
        // #[serde(deserialize_with = "de_from_str")]
        // min_price: f64,
        // #[serde(deserialize_with = "de_from_str")]
        // max_price: f64,
        #[serde(deserialize_with = "de_from_str")]
        tick_size: f64,
    },
    LotSize {
        #[serde(deserialize_with = "de_from_str")]
        min_qty: f64,
        // pub(crate)max_qty: f64,
        #[serde(deserialize_with = "de_from_str")]
        step_size: f64,
    },
    MarketLotSize {
        // pub(crate)min_qty: f64,
        // pub(crate)max_qty: f64,
        // pub(crate)step_size: f64,
    },
    MaxNumOrders {
        // max_num_orders: usize,
    },
    MaxNumAlgoOrders {
        // max_num_algo_orders: usize,
    },
    PercentPrice {
        // multiplier_up: f64,
        // multiplier_down: f64,
        // multiplier_decimal: f64,
    },
    IcebergParts {
        // limit: usize,
    },
    TrailingDelta {
        // min_trailing_above_delta: usize,
        // max_trailing_above_delta: usize,
        // min_trailing_below_delta: usize,
        // max_trailing_below_delta: usize,
    },

    PercentPriceBySide {
        // #[serde(deserialize_with = "de_from_str")]
        // bid_multiplier_up: f64,
        // #[serde(deserialize_with = "de_from_str")]
        // bid_multiplier_down: f64,
        // #[serde(deserialize_with = "de_from_str")]
        // ask_multiplier_up: f64,
        // #[serde(deserialize_with = "de_from_str")]
        // ask_multiplier_down: f64,
        // avg_price_mins: f64,
    },

    Notional {
        #[serde(deserialize_with = "de_from_str")]
        min_notional: f64,
        // apply_min_to_market: bool,
        // #[serde(deserialize_with = "de_from_str")]
        // max_notional: f64,
        // apply_max_to_market: bool,
        // avg_price_mins: f64,
    },
    MaxPosition {
        // #[serde(deserialize_with = "de_from_str")]
        // max_position: f64,
    },
}

impl From<SymbolInfo> for Instrument {
    fn from(value: SymbolInfo) -> Self {
        let filters = value
            .filters
            .iter()
            .filter_map(|v| {
                let filter: Option<SymbolFilter> = serde_json::from_value(v.clone()).ok();
                if filter.is_none() {
                    let v = serde_json::to_string(v).unwrap();
                    warn!("symbol filter error: {}", v);
                }
                filter
            })
            .collect::<Vec<SymbolFilter>>();

        let (min_qty, step_size) = filters
            .iter()
            .find_map(|f: &SymbolFilter| match f {
                SymbolFilter::LotSize {
                    min_qty, step_size, ..
                } => Some((*min_qty, step_size)),
                _ => None,
            })
            .unwrap();
        let prefix_mul = value.symbol.prefix_mul;
        let prefix_mul_f64 = f64::from(prefix_mul.prefix());

        let tick_size = filters
            .iter()
            .find_map(|f| match f {
                SymbolFilter::PriceFilter { tick_size, .. } => Some(tick_size),
                _ => None,
            })
            .unwrap();

        let min_notional = filters
            .iter()
            .find_map(|f| match f {
                SymbolFilter::Notional { min_notional, .. } => Some(*min_notional),
                _ => None,
            })
            .unwrap_or_default();

        Self {
            symbol: value.symbol.into(),
            state: value.status.into(),
            min_qty,
            min_notional,
            price_precision: -(tick_size.log10() as i32),
            amount_precision: -(step_size.log10() as i32),
            price_tick: *tick_size,
            amount_tick: *step_size,
            price_multiplier: prefix_mul_f64,
            amount_multiplier: 1.0 / prefix_mul_f64,
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct RspListenKey {
    pub(crate) listen_key: String,
}

#[derive(Debug, Deserialize)]
#[serde(tag = "e", rename_all = "camelCase")]
pub(crate) enum AccountPayload {
    OutboundAccountPosition(Box<OutboundAccountPosition>), // 每当帐户余额发生更改时发送，包含可能由生成余额变动的事件而变动的资产。
    ExecutionReport(Box<ExecutionReport>),
    BalanceUpdate, // 借币还币时发生
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum BnExecutionType {
    New,             // 新订单已被引擎接受。
    Canceled,        // 订单被用户取消。
    Replaced,        // (保留字段，当前未使用)
    Rejected, // 新订单被拒绝 （这信息只会在撤消挂单再下单中发生，下新订单被拒绝但撤消挂单请求成功）。
    Trade,    // 订单有新成交。
    Expired, // 订单已根据 Time In Force 参数的规则取消（e.g. 没有成交的 LIMIT FOK 订单或部分成交的 LIMIT IOC 订单）或者被交易所取消（e.g. 强平或维护期间取消的订单）。
    TradePrevention, // 订单因 STP 触发而过期。
}

#[derive(Deserialize, Debug)]
pub(crate) struct ExecutionReport {
    #[serde(rename = "s")]
    symbol: BnSymbol, // 交易对

    #[serde(rename = "c")]
    client_order_id: String,

    #[serde(rename = "S")]
    order_side: BnOrderSide, // 订单方向

    #[serde(rename = "o")]
    order_type: BnOrderType, // 订单类型

    #[serde(rename = "f")]
    time_in_force: BnTimeInForce, // 有效方式

    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "q")]
    order_qty: f64, // 订单原始数量

    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "p")]
    order_price: f64, // 订单原始价格

    #[serde(rename = "X")]
    order_status: BnOrderState, // 订单的当前状态

    #[serde(rename = "i")]
    order_id: f64,

    // 订单累计已成交量
    #[serde(rename = "z")]
    #[serde(deserialize_with = "de_from_str")]
    order_qty_filled: f64,

    // 订单创建时间
    #[serde(rename = "O")]
    order_create_time: i64,

    // 订单累计已成交金额
    #[serde(rename = "Z")]
    #[serde(deserialize_with = "de_from_str")]
    order_filled_amount: f64,
    //// 暂时不需要
    // #[serde(rename = "E")]
    // event_time: u64, // 事件时间
    // #[serde(deserialize_with = "de_from_str")]
    // #[serde(rename = "P")]
    // order_trigger_price: f64, // 止盈止损单触发价格
    // #[serde(deserialize_with = "de_from_str")]
    // #[serde(rename = "F")]
    // ice_order_qty: f64, // 冰山订单数量
    // #[serde(rename = "g")]
    // oco_order_list_id: f64, // OCO订单 OrderListId
    // #[serde(rename = "C")]
    // org_order_id: String, // 原始订单自定义ID(原始订单，指撤单操作的对象。撤单本身被视为另一个订单)
    // #[serde(rename = "x")]
    // execution_type: BnExecutionType, // 本次事件的具体执行类型
    // #[serde(rename = "r")]
    // order_reject_case: String, // 订单被拒绝的原因
    // #[serde(rename = "l")]
    // #[serde(deserialize_with = "de_from_str")]
    // order_qty_unfill: f64,
    // #[serde(rename = "L")]
    // #[serde(deserialize_with = "de_from_str")]
    // order_last_fill_price: f64, // 订单末次成交价格
    // #[serde(rename = "n")]
    // #[serde(deserialize_with = "de_from_str")]
    // fee_qty: f64, // 手续费数量
    // #[serde(rename = "N")]
    // #[serde(deserialize_with = "de_from_str")]
    // fee_type: String, // 手续费资产类别
    // #[serde(rename = "T")]
    // fill_time: u64, // 手续费资产类别
    // #[serde(rename = "w")]
    // on_order_book: bool, // 手续费资产类别
    // #[serde(rename = "m")]
    // is_maker_order: bool, // 该成交是作为挂单成交吗？
    // #[serde(rename = "Y")]
    // #[serde(deserialize_with = "de_from_str")]
    // order_unfill_amount: f64, // 订单末次成交金额
    // #[serde(rename = "Q")]
    // #[serde(deserialize_with = "de_from_str")]
    // quote_order_quantity: f64, // Quote Order Quantity
    // #[serde(rename = "D")]
    // trailing_time: i64, // 追踪时间; 这仅在追踪止损订单已被激活时可见
    // #[serde(rename = "W")]
    // working_time: i64, // Working Time; 订单被添加到 order book 的时间
    // #[serde(rename = "V")]
    // self_trade_prevention_mode: String, // Working Time; 订单被添加到 order book 的时间
}

impl ExecutionReport {
    pub(crate) fn into_order(self) -> Order {
        let time_in_force = self
            .time_in_force
            .to_time_in_force_with_order_type(&self.order_type);
        let prefix_mul = self.symbol.prefix() as f64;
        let filled_avg_price = match self.order_qty_filled > 0.0 {
            true => self.order_filled_amount / self.order_qty_filled,
            false => 0.0,
        };

        Order {
            id: self.order_id.to_string(),
            cid: Some(self.client_order_id),
            timestamp: self.order_create_time,
            status: self.order_status.into(),
            symbol: self.symbol.into(),
            order_type: self.order_type.into(),
            side: self.order_side.into(),
            pos_side: None,
            time_in_force,
            price: Some(prefix_mul.price2base(self.order_price)),
            amount: Some(prefix_mul.qty2base(self.order_qty)),
            filled: prefix_mul.qty2base(self.order_qty_filled),
            filled_avg_price: prefix_mul.price2base(filled_avg_price),
            quote_amount: None,
            ..Default::default()
        }
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct WsBalanceItem {
    #[serde(rename = "a")]
    asset: String, // 资产名称

    #[serde(rename = "f")]
    #[serde(deserialize_with = "de_from_str")]
    free: f64, // 可用余额

    #[serde(rename = "l")]
    #[serde(deserialize_with = "de_from_str")]
    lock: f64, // 冻结余额
}

impl From<WsBalanceItem> for Balance {
    fn from(value: WsBalanceItem) -> Self {
        let (prefix, asset) = parse_prefix(value.asset.clone()).unwrap_or_default();
        let prefix_mul = prefix.prefix() as f64;
        let balance = prefix_mul * (value.free + value.lock);
        let available_balance = prefix_mul * value.free;
        Self {
            asset,
            balance,
            available_balance,
            unrealized_pnl: 0.0,
        }
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct OutboundAccountPosition {
    // #[serde(rename = "E")]
    // event_time: u64, // 事件时间
    // #[serde(rename = "u")]
    // last_update_time: u64, // 账户末次更新时间戳
    #[serde(rename = "B")]
    pub(crate) balances: Vec<WsBalanceItem>,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BnRestDepth {
    last_update_id: i64,

    #[serde(deserialize_with = "deserialize_f64_pairs")]
    bids: Vec<(f64, f64)>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    asks: Vec<(f64, f64)>,
}

impl BnRestDepth {
    pub(crate) fn into_depth(self, symbol: Symbol) -> Depth {
        let prefix_mul = f64::from(spot_prefix_mul_by_symbol(&symbol).prefix());
        Depth {
            symbol,
            timestamp: time_ms(),
            asks: self
                .asks
                .into_iter()
                .map(|(p, a)| (prefix_mul.price2base(p), prefix_mul.qty2base(a)).into())
                .collect(),
            bids: self
                .bids
                .into_iter()
                .map(|(p, a)| (prefix_mul.price2base(p), prefix_mul.qty2base(a)).into())
                .collect(),
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct SymbolOrderId {
    symbol: BnSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    order_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    orig_client_order_id: Option<String>,

    // websocket 推送的cid是这个，不填则由交易所自动生成
    #[serde(skip_serializing_if = "Option::is_none")]
    new_client_order_id: Option<String>,
}

impl SymbolOrderId {
    pub(crate) fn new(symbol: Symbol, order_id: OrderId) -> Self {
        let (order_id, orig_client_order_id) = match order_id {
            OrderId::Id(order_id) => (Some(order_id), None),
            OrderId::ClientOrderId(client_order_id) => (None, Some(client_order_id)),
        };
        let new_client_order_id = orig_client_order_id.clone();
        Self {
            symbol: symbol.into(),
            order_id,
            orig_client_order_id,
            new_client_order_id,
        }
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct BinanceErr {
    pub(crate) code: i32,
    pub(crate) msg: String,
}

#[derive(Debug, Default, Serialize, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
enum CancelReplaceMode {
    // 如果撤消订单失败将不会继续重新下单
    #[default]
    StopOnFailure,
    // 不管撤消订单是否成功都会继续重新下单
    // AllowFailure,
}

#[derive(Debug, Default, Serialize, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct CancelReplaceOrderRequest {
    symbol: BnSymbol,
    side: BnOrderSide,
    r#type: BnOrderType,
    cancel_replace_mode: CancelReplaceMode,

    #[serde(rename = "cancelOrderId")]
    order_id: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    #[serde(rename = "cancelOrigClientOrderId")]
    cid: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    quantity: Option<f64>,
    price: f64,
    recv_window: u32,
    time_in_force: BnTimeInForce,
}

impl From<Order> for CancelReplaceOrderRequest {
    fn from(order: Order) -> Self {
        let symbol = order.symbol.into();

        Self {
            order_id: order.id,
            cid: order.cid,
            symbol,
            side: order.side.into(),
            quantity: order.amount,
            price: order.price.unwrap_or_default(),
            cancel_replace_mode: CancelReplaceMode::StopOnFailure,
            r#type: BnOrderType::with_tif(&order.order_type, &order.time_in_force),
            recv_window: DEFAULT_RECV_WINDOW,
            time_in_force: order.time_in_force.into(),
        }
    }
}

// #[derive(Deserialize)]
// #[serde(rename_all = "SCREAMING_SNAKE_CASE")]
// enum BnOpResult {
//     Success,
//     Failure,
//     NotAttempted,
// }

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct CancelReplaceOrderResponse {
    // pub(crate)cancel_result: BnOpResult,
    // pub(crate)new_order_result: BnOpResult,
    // cancel_response: OrderResponse,
    pub(crate) new_order_response: OrderResponse,
}

#[derive(Serialize)]
pub(crate) struct DepthReq {
    pub(crate) symbol: BnSymbol,
    pub(crate) limit: Option<u32>,
}

impl DepthReq {
    pub(crate) fn new(symbol: BnSymbol, limit: Option<u32>) -> Self {
        Self { symbol, limit }
    }
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct Network {
    network: String,
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
#[serde(rename_all = "camelCase")]
pub struct CapitalsRsp {
    coin: String,
    network_list: Vec<Network>,
}

/// 现货全量更新深度
#[derive(Deserialize, Clone)]
pub(crate) struct BnDepthUpdate {
    #[serde(rename = "E")]
    event_time: i64,

    #[serde(rename = "s")]
    pub(crate) symbol: BnSymbol,

    #[serde(rename = "U")]
    start_update_id: i64,

    #[serde(rename = "u")]
    end_update_id: i64,

    #[serde(deserialize_with = "deserialize_f64_pairs")]
    #[serde(rename = "b")]
    bids: Vec<(f64, f64)>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    #[serde(rename = "a")]
    asks: Vec<(f64, f64)>,
}

#[derive(Debug)]
pub(crate) struct LocalDepth {
    depth: Depth,
    update_id: i64,
    prefix: f64,
}

impl LocalDepth {
    pub(crate) fn new(
        symbol: Symbol,
        depth: BnRestDepth,
        pendings: &[BnDepthUpdate],
    ) -> Result<Self> {
        let prefix = spot_prefix_mul_by_symbol(&symbol).prefix() as f64;
        let update_id = depth.last_update_id;
        let mut local_depth = Self {
            depth: depth.into_depth(symbol),
            update_id,
            prefix,
        };

        for update in pendings {
            local_depth.update((*update).clone())?;
        }

        Ok(local_depth)
    }

    pub(crate) fn update(&mut self, update: BnDepthUpdate) -> Result<Option<usize>> {
        if update.end_update_id < self.update_id {
            return Ok(None);
        }

        if self.update_id + 1 >= update.start_update_id {
            self.depth.timestamp = update.event_time;
            self.update_id = update.end_update_id;
            let prefix = self.prefix;
            let asks = update
                .asks
                .into_iter()
                .map(|(p, q)| (prefix.price2base(p), prefix.qty2base(q)))
                .collect();
            let bids = update
                .bids
                .into_iter()
                .map(|(p, q)| (prefix.price2base(p), prefix.qty2base(q)))
                .collect();
            let min_level = self.depth.update(asks, bids);
            return Ok(Some(min_level));
        }

        let err = qerror!(
            "失序, update: {} -> {} ; local: {}",
            update.start_update_id,
            update.end_update_id,
            self.update_id
        );
        Err(err)
    }

    pub(crate) fn depth(&self, limit: usize) -> Depth {
        self.depth.clone_limited(limit)
    }
}

#[derive(Deserialize)]
pub(crate) struct WsId {
    // id: i64,
}

// {
//   "e": "trade",     // 事件类型
//   "E": 1672515782136,   // 事件时间
//   "s": "BNBBTC",    // 交易对
//   "t": 12345,       // 交易ID
//   "p": "0.001",     // 成交价格
//   "q": "100",       // 成交数量
//   "T": 1672515782136,   // 成交时间
//   "m": true,        // 买方是否是做市方。如true，则此次成交是一个主动卖出单，否则是一个主动买入单。
//   "M": true         // 请忽略该字段
// }
#[derive(Deserialize)]
pub(crate) struct BnTrade {
    #[serde(rename = "s")]
    symbol: BnSymbol,
    #[serde(rename = "t")]
    trade_id: u64,

    #[serde(rename = "p")]
    #[serde(deserialize_with = "de_from_str")]
    price: f64,
    #[serde(rename = "q")]
    #[serde(deserialize_with = "de_from_str")]
    quantity: f64,
    #[serde(rename = "T")]
    fill_time: i64,
    #[serde(rename = "m")]
    is_sell_type: bool,
}

impl From<BnTrade> for Trade {
    fn from(value: BnTrade) -> Self {
        let prefix_multiplier = value.symbol.prefix() as f64;
        Trade {
            id: value.trade_id.to_string(),
            symbol: value.symbol.into(),
            timestamp: value.fill_time,
            price: prefix_multiplier.price2base(value.price),
            amount: prefix_multiplier.qty2base(value.quantity),
            side: if value.is_sell_type {
                OrderSide::Sell
            } else {
                OrderSide::Buy
            },
        }
    }
}

#[derive(Debug, Deserialize)]
pub(crate) struct BnCommission {
    #[serde(deserialize_with = "de_from_str")]
    maker: f64,
    #[serde(deserialize_with = "de_from_str")]
    taker: f64,
    #[serde(deserialize_with = "de_from_str")]
    buyer: f64,
    #[serde(deserialize_with = "de_from_str")]
    seller: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct DiscountInfo {
    enabled_for_account: bool,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct DiscountFeeRsp {
    discount: DiscountInfo,
}

impl From<DiscountFeeRsp> for bool {
    fn from(value: DiscountFeeRsp) -> Self {
        value.discount.enabled_for_account
    }
}

#[derive(Debug, Deserialize, Serialize)]
pub(crate) struct BnbBurnInfo {
    #[serde(rename = "spotBNBBurn")]
    spot_bnb_burn: bool,
    #[serde(rename = "interestBNBBurn")]
    interest_bnb_burn: bool,
}

impl From<BnbBurnInfo> for bool {
    fn from(value: BnbBurnInfo) -> Self {
        value.spot_bnb_burn
    }
}

impl BnbBurnInfo {
    pub(crate) fn new(enable: bool) -> Self {
        Self {
            spot_bnb_burn: enable,
            interest_bnb_burn: false,
        }
    }
}

#[derive(Debug, Deserialize)]
pub(crate) struct BnbBurnRsp {}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct TradeFee {
    standard_commission: BnCommission,
    tax_commission: BnCommission,
}

impl From<TradeFee> for FeeRate {
    fn from(val: TradeFee) -> Self {
        let s = &val.standard_commission;
        let t = &val.tax_commission;
        FeeRate {
            maker: s.maker + t.maker,
            taker: s.taker + t.taker,
            buyer: s.buyer + t.buyer,
            seller: s.seller + t.seller,
        }
    }
}

// 主订单结构
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct CancelOrder {
    symbol: BnSymbol,
    orig_client_order_id: Option<String>,
    client_order_id: Option<String>,
    order_id: Option<i64>,
    // order_list_id: i64,
    // transact_time: Option<u64>,
    // price: String,
    // orig_qty: String,
    // executed_qty: String,
    // status: String,
    // time_in_force: BnTimeInForce,
    // #[serde(rename = "type")]
    // order_type: String,
    // side: BnOrderSide,
    // self_trade_prevention_mode: Option<String>,
}

impl From<CancelOrder> for BnOrderItemRsp {
    fn from(value: CancelOrder) -> Self {
        Self::Succ(SuccessOrder {
            id: value.order_id.map(|id| id.to_string()),
            cid: value.client_order_id,
        })
    }
}

// OCO 订单结构
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct CancelOcoOrder {
    order_list_id: u64,
    contingency_type: String,
    list_status_type: String,
    list_order_status: String,
    list_client_order_id: String,
    transaction_time: u64,
    symbol: BnSymbol,
    orders: Vec<OcoSubOrder>,
    order_reports: Vec<OrderReport>,
}

impl From<CancelOcoOrder> for BnOrderItemRsp {
    fn from(_value: CancelOcoOrder) -> Self {
        Self::Fail(FailOrder {
            id: Default::default(),
            cid: Default::default(),
            error_code: Default::default(),
            error: "目前不支持OCO订单".to_string(),
        })
    }
}

// OCO 子订单结构
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
struct OcoSubOrder {
    symbol: String,
    order_id: u64,
    client_order_id: String,
}

// OCO 子订单报告结构
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct OrderReport {
    symbol: BnSymbol,
    orig_client_order_id: String,
    order_id: u64,
    order_list_id: u64,
    client_order_id: String,
    transact_time: u64,
    price: String,
    orig_qty: String,
    executed_qty: String,
    status: String,
    time_in_force: BnTimeInForce,
    #[serde(rename = "type")]
    order_type: String,
    side: BnOrderSide,
    stop_price: Option<String>,
    iceberg_qty: Option<String>,
    self_trade_prevention_mode: Option<String>,
}

// 综合订单响应，包含普通订单和 OCO 订单
#[derive(Debug, Serialize, Deserialize)]
#[serde(untagged)]
pub(crate) enum CancelOpenOrder {
    StandardOrder(CancelOrder),
    OcoOrder(CancelOcoOrder),
}

impl From<CancelOpenOrder> for BnOrderItemRsp {
    fn from(value: CancelOpenOrder) -> Self {
        match value {
            CancelOpenOrder::StandardOrder(order) => order.into(),
            CancelOpenOrder::OcoOrder(order) => order.into(),
        }
    }
}

#[derive(Serialize, Clone, Copy, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnWalletType {
    /// 现货钱包
    Main,
    /// U本位合约钱包
    Umfuture,
    /// 币本位合约钱包
    Cmfuture,
    /// 杠杆全仓钱包
    Margin,
    /// 杠杆逐仓钱包
    Isolatedmargin,
}

impl From<WalletType> for BnWalletType {
    fn from(val: WalletType) -> Self {
        match val {
            WalletType::Spot => Self::Main,
            WalletType::UsdtFuture => Self::Umfuture,
            WalletType::CoinFuture => Self::Cmfuture,
            WalletType::Margin => Self::Margin,
            WalletType::IsolatedMargin => Self::Isolatedmargin,
        }
    }
}

#[derive(Serialize, Clone, Copy, Debug)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum BnSubWalletType {
    /// 现货钱包
    Spot,
    /// U本位合约钱包
    UsdtFuture,
    /// 币本位合约钱包
    CoinFuture,
    /// 杠杆全仓钱包
    Margin,
    /// 杠杆逐仓钱包
    IsolatedMargin,
}

impl From<WalletType> for BnSubWalletType {
    fn from(val: WalletType) -> Self {
        match val {
            WalletType::Spot => Self::Spot,
            WalletType::UsdtFuture => Self::UsdtFuture,
            WalletType::CoinFuture => Self::CoinFuture,
            WalletType::Margin => Self::Margin,
            WalletType::IsolatedMargin => Self::IsolatedMargin,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct TransferReq {
    r#type: String,
    asset: String,
    amount: f64,
    from_symbol: Option<BnWalletType>,
    to_symbol: Option<BnWalletType>,
}

impl From<Transfer> for TransferReq {
    fn from(val: Transfer) -> Self {
        let from = val.from.into();
        let to = val.to.into();
        Self {
            asset: val.asset,
            amount: val.amount,
            from_symbol: Some(from),
            to_symbol: Some(to),
            r#type: format!("{:?}_{:?}", from, to).to_uppercase(),
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct TransferRsp {
    // pub(crate)tran_id: i64,
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct SubTransferReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    from_email: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    to_email: Option<String>,
    from_account_type: BnSubWalletType,
    to_account_type: BnSubWalletType,
    #[serde(skip_serializing_if = "Option::is_none")]
    client_tran_id: Option<String>,
    asset: String,
    amount: String,
}

impl From<SubTransfer> for SubTransferReq {
    fn from(value: SubTransfer) -> Self {
        Self {
            from_account_type: value.from.into(),
            to_account_type: value.to.into(),
            client_tran_id: value.cid,
            asset: value.asset,
            amount: value.amount.to_string(),
            from_email: value.from_account,
            to_email: value.to_account,
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) struct SubTransferRsp {}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) struct TransferToMasterRsq {}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub enum BnNetwork {
    ETH,
    TRX,
    BSC,
    SOL,
    MATIC,
    ARBITRUM,
    OPTIMISM,
    TON,
    AVAX,
}

impl From<Chain> for BnNetwork {
    fn from(value: Chain) -> Self {
        match value {
            Chain::Erc20 => Self::ETH,
            Chain::Trc20 => Self::TRX,
            Chain::Bep20 => Self::BSC,
            Chain::Sol => Self::SOL,
            Chain::Polygon => Self::MATIC,
            Chain::ArbitrumOne => Self::ARBITRUM,
            Chain::Optimism => Self::OPTIMISM,
            Chain::Ton => Self::TON,
            Chain::AVAXC => Self::AVAX,
        }
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct DepositAddressReq {
    coin: String,
    network: Option<BnNetwork>,
    amount: Option<String>,
}

impl DepositAddressReq {
    pub(crate) fn new(ccy: String, chain: Option<Chain>, amount: Option<f64>) -> Self {
        Self {
            coin: ccy,
            network: chain.map(|c| {
                let c: BnNetwork = c.into();
                c
            }),
            amount: amount.map(|a| a.to_string()),
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct DepositAddressRsp {
    address: String,
    coin: String,
    tag: Option<String>,
    url: Option<String>,
}

impl DepositAddressRsp {
    pub fn into_base(self, chain: Option<Chain>) -> Vec<DepositAddress> {
        vec![DepositAddress {
            address: self.address,
            asset: self.coin,
            tag: self.tag,
            url: self.url,
            chain,
        }]
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct WithdrawalReq {
    coin: String,
    amount: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    withdraw_order_id: Option<String>,
    network: BnNetwork,
    address: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    address_tag: Option<String>,

    #[serde(skip_serializing_if = "Option::is_none")]
    wallet_type: Option<i32>, // 0为现货钱包
                              // 1为资金钱包。
                              // 默认walletType为"充币账户"是您设置在钱包->现货账户或资金账户->充值
}

impl TryFrom<WithDrawlParams> for WithdrawalReq {
    type Error = Error;

    fn try_from(value: WithDrawlParams) -> Result<Self> {
        match value.addr {
            WithdrawalAddr::OnChain(addr) => Ok(Self {
                coin: value.asset,
                amount: value.amt.to_string(),
                withdraw_order_id: value.cid,
                network: addr.chain.into(),
                address: addr.address,
                address_tag: addr.tag,
                wallet_type: None,
            }),
            WithdrawalAddr::InternalTransfer(..) => Err(qerror!(
                "binance API接口只支持链上提现(不支持手机号、邮箱和用户ID)"
            )),
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct WithdrawRsp {
    id: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetOrderReq {
    symbol: BnSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    order_id: Option<u64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    orig_client_order_id: Option<String>,
}

impl GetOrderReq {
    pub fn new(symbol: Symbol, order_id: Option<OrderId>) -> Result<Self> {
        let symbol: BnSymbol = symbol.into();
        let req = match order_id {
            Some(a) => match a {
                OrderId::Id(oid) => Self {
                    symbol,
                    order_id: Some(oid.parse()?),
                    orig_client_order_id: None,
                },
                OrderId::ClientOrderId(cid) => Self {
                    symbol,
                    order_id: None,
                    orig_client_order_id: Some(cid),
                },
            },
            None => Self {
                symbol,
                order_id: None,
                orig_client_order_id: None,
            },
        };
        Ok(req)
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct OpenOrdersReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    symbol: Option<BnSymbol>,
    #[serde(skip_serializing_if = "Option::is_none")]
    start_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    end_time: Option<i64>,
}

impl OpenOrdersReq {
    pub(crate) fn new(
        symbol: Option<Symbol>,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Self {
        Self {
            symbol: symbol.map(|s| s.into()),
            start_time,
            end_time,
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BnOrder {
    order_id: u64,
    client_order_id: String,
    time: i64,
    status: BnOrderStatus,
    symbol: BnSymbol,
    r#type: BnOrderType,
    side: BnOrderSide,
    time_in_force: BnTimeInForce,
    #[serde(deserialize_with = "de_from_str")]
    price: f64,
    #[serde(deserialize_with = "de_from_str")]
    orig_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    executed_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    cummulative_quote_qty: f64,
}

impl From<BnOrder> for Order {
    fn from(v: BnOrder) -> Self {
        let order_type = v.r#type;
        let time_in_force = v
            .time_in_force
            .to_time_in_force_with_order_type(&order_type);
        let prefix_mul = v.symbol.prefix() as f64;
        let filled_avg_price = match v.executed_qty > 0. {
            true => prefix_mul.qty2base(v.cummulative_quote_qty / v.executed_qty),
            false => 0.,
        };
        Self {
            id: v.order_id.to_string(),
            cid: Some(v.client_order_id),
            timestamp: v.time,
            status: v.status.into(),
            symbol: v.symbol.into(),
            order_type: order_type.into(),
            side: v.side.into(),
            pos_side: None,
            time_in_force,
            price: Some(prefix_mul.price2base(v.price)),
            amount: Some(prefix_mul.qty2base(v.orig_qty)),
            filled: prefix_mul.qty2base(v.executed_qty),
            filled_avg_price,
            quote_amount: None,
            ..Default::default()
        }
    }
}

#[derive(Deserialize)]
// SCREAMING_SNAKE_CASE means that all the field names in the serialized form will be in uppercase letters with underscores separating words.
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
enum BnOrderStatus {
    // 新建订单
    New,
    // 部分成交
    PartiallyFilled,
    // 全部成交
    Filled,
    // 已撤销
    Canceled,
    // 订单被拒绝
    Rejected,
    // 订单过期(根据timeInForce参数规则)
    Expired,
    // 订单被STP过期
    ExpiredInMatch,
}

impl From<BnOrderStatus> for OrderStatus {
    fn from(v: BnOrderStatus) -> Self {
        match v {
            BnOrderStatus::New => Self::Open,
            BnOrderStatus::PartiallyFilled => Self::PartiallyFilled,
            BnOrderStatus::Filled => Self::Filled,
            BnOrderStatus::Canceled => Self::Canceled,
            _ => Self::Canceled,
        }
    }
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct KlineReq {
    pub(crate) symbol: BnSymbol,
    interval: KlineInterval,
    #[serde(skip_serializing_if = "Option::is_none")]
    start_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    end_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    limit: Option<u32>,
}

impl From<GetKlineParams> for KlineReq {
    fn from(params: GetKlineParams) -> Self {
        Self {
            symbol: params.symbol.into(),
            interval: params.interval,
            start_time: params.start_time,
            end_time: params.end_time,
            limit: Some(params.limit.unwrap_or(100)),
        }
    }
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub(crate) struct BnCandle {
    open_timestamp: i64, // 开盘时间
    #[serde(deserialize_with = "de_from_str")]
    open: f64, // 开盘价
    #[serde(deserialize_with = "de_from_str")]
    high: f64, // 最高价
    #[serde(deserialize_with = "de_from_str")]
    low: f64, // 最低价
    #[serde(deserialize_with = "de_from_str")]
    close: f64, // 收盘价(当前K线未结束的即为最新价)
    #[serde(deserialize_with = "de_from_str")]
    volume: f64, // 成交量
    close_time: i64,     // 收盘时间
    #[serde(deserialize_with = "de_from_str")]
    quote_volume: f64, // 成交额
    trades: u32,         // 成交笔数
    #[serde(deserialize_with = "de_from_str")]
    taker_buy_volume: f64, // 主动买入成交量
    #[serde(deserialize_with = "de_from_str")]
    taker_buy_quote_volume: f64, // 主动买入成交额

    _ignore: String, // 忽略字段
}

impl BnCandle {
    pub(crate) fn into_base(self, prefix: f64) -> Candle {
        let confirm = time_ms() - self.close_time > 1000;
        Candle::new(
            self.open_timestamp,
            prefix.price2base(self.open),
            prefix.price2base(self.high),
            prefix.price2base(self.low),
            prefix.price2base(self.close),
            prefix.qty2base(self.volume),
            self.quote_volume,
            Some(self.trades),
            Some(prefix.qty2base(self.taker_buy_volume)),
            Some(self.taker_buy_quote_volume),
            confirm,
        )
    }
}

///////////////////////// ws api /////////////////////////

#[derive(Serialize, Debug, PartialEq, Eq)]
enum WsApiMethod {
    #[serde(rename = "session.logon")]
    Logon,
    #[serde(rename = "order.place")]
    Post,
    #[serde(rename = "order.cancel")]
    Cancel,
    #[serde(rename = "order.cancelReplace")]
    CancelReplace,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct WsApiReq {
    id: u64, // INT / STRING / null
    method: WsApiMethod,
    params: serde_json::Value, // Object
}

impl WsApiReq {
    fn sign(
        id: u64,
        method: WsApiMethod,
        secret: &str,
        mut params: serde_json::Value,
        timestamp: i64,
        api_key: String,
    ) -> Result<Self> {
        let timestamp = match timestamp {
            0 => time_ms(),
            _ => timestamp,
        };
        params["timestamp"] = timestamp.into();
        if !is_ed25519_secret(secret) || method == WsApiMethod::Logon {
            params["apiKey"] = api_key.into();

            let sign = sign_ws_api_params(secret, &params)?;
            params["signature"] = sign.into();
        }

        Ok(Self { id, method, params })
    }

    pub(crate) fn logon(id: u64, api_key: String, secret: &str) -> Result<Self> {
        let params = serde_json::to_value(WsApiLogonParams)?;
        Self::sign(id, WsApiMethod::Logon, secret, params, 0, api_key)
    }

    pub(crate) fn post_order(
        req_id: u64,
        api_key: String,
        secret: &str,
        order: Order,
    ) -> Result<Self> {
        let timestamp = order.timestamp;
        let params = serde_json::to_value(WsApiPostOrderParams::new(order))?;
        Self::sign(
            req_id,
            WsApiMethod::Post,
            secret,
            params,
            timestamp,
            api_key,
        )
    }

    pub(crate) fn cancel_order(
        req_id: u64,
        api_key: String,
        secret: &str,
        symbol: Symbol,
        order_id: OrderId,
    ) -> Result<Self> {
        let params = WsApiCancelOrderParams {
            symbol: symbol.into(),
            order_id: None,
            orig_client_order_id: None,
            new_client_order_id: None,
        };
        let req = match order_id {
            OrderId::Id(oid) => WsApiCancelOrderParams {
                order_id: Some(oid),
                ..params
            },
            OrderId::ClientOrderId(cid) => WsApiCancelOrderParams {
                orig_client_order_id: Some(cid.clone()),
                new_client_order_id: Some(cid.clone()),
                ..params
            },
        };
        let req = serde_json::to_value(req)?;
        Self::sign(req_id, WsApiMethod::Cancel, secret, req, time_ms(), api_key)
    }

    pub(crate) fn cancel_replace_order(
        req_id: u64,
        api_key: String,
        secret: &str,
        order: Order,
    ) -> Result<Self> {
        let order_type = BnOrderType::with_tif(&order.order_type, &order.time_in_force);
        let time_in_force = match order.time_in_force {
            TimeInForce::PostOnly => None,
            _ => Some(order.time_in_force.into()),
        };
        let params = WsApiCancelReplaceOrderParams {
            symbol: order.symbol.into(),
            cancel_replace_mode: BnCancelReplaceMode::StopOnFailure,
            cancel_orig_client_order_id: order.cid,
            side: order.side.into(),
            r#type: order_type,
            time_in_force,
            price: order.price.map(|p| p.to_string()),
            quantity: order.amount.map(|a| a.to_string()),
        };
        let params = serde_json::to_value(params)?;
        Self::sign(
            req_id,
            WsApiMethod::CancelReplace,
            secret,
            params,
            order.timestamp,
            api_key,
        )
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct WsApiLogonParams;

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct WsApiError {
    code: Option<i64>,
    msg: String,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct WsApiRsp {
    id: Option<serde_json::Value>,
    status: i32,
    result: Option<sonic_rs::Value>,
    error: Option<WsApiError>,
    rate_limits: Vec<RateLimit>,
}

impl WsApiRsp {
    pub(crate) fn id(&self) -> u64 {
        match &self.id {
            Some(serde_json::Value::Number(n)) => n.as_u64(),
            _ => None,
        }
        .unwrap_or_default()
    }

    pub(crate) fn into_logon_rsp(self) -> Result<()> {
        if let Some(error) = self.error.as_ref() {
            return Err(qerror!("logon error: {error:?}"));
        }
        Ok(())
    }

    pub(crate) fn post_order_rsp(&self) -> Result<String> {
        if let Some(error) = &self.error {
            return Err(qerror!("{error:?}"));
        }
        match &self.result {
            Some(result) => {
                let rsp: PostOrderRsp = sonic_rs::from_value(result)?;
                Ok(rsp.order_id.to_string())
            }
            None => Err(qerror!("result is None")),
        }
    }

    pub(crate) fn cancel_order_rsp(&self) -> Result<String> {
        if let Some(error) = &self.error {
            return Err(qerror!("{error:?}"));
        }
        match &self.result {
            Some(result) => {
                let rsp: CancelOrderRsp = sonic_rs::from_value(result)?;
                Ok(rsp.order_id.to_string())
            }
            None => Err(qerror!("result is None")),
        }
    }

    pub(crate) fn cancel_replace_order_rsp(&self) -> Result<String> {
        if let Some(error) = &self.error {
            return Err(qerror!("{error:?}"));
        }

        match &self.result {
            Some(result) => {
                let rsp: CancelReplaceOrderRsp = sonic_rs::from_value(result)?;
                Ok(rsp.new_order_response.order_id.to_string())
            }
            None => Err(qerror!("result is None")),
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
struct PostOrderRsp {
    order_id: u64,
    // symbol: BnSymbol,
    // order_list_id: i64,
    // client_order_id: String,
    // transact_time: i64,
    // price: String,
    // orig_qty: String,
    // executed_qty: String,
    // cummulative_quote_qty: String,
    // status: String,
    // time_in_force: String,
    // r#type: String,
    // side: String,
    // working_time: i64,
    // self_trade_prevention_mode: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
struct CancelOrderRsp {
    order_id: u64,
    // symbol: BnSymbol,
    // orig_client_order_id: String,
    // order_list_id: i64,
    // client_order_id: String,
    // transact_time: i64,
    // price: String,
    // orig_qty: String,
    // executed_qty: String,
    // cummulative_quote_qty: String,
    // status: String,
    // time_in_force: String,
    // r#type: String,
    // side: String,
    // stop_price: String,
    // trailing_delta: i64,
    // iceberg_qty: String,
    // strategy_id: i64,
    // strategy_type: i64,
    // self_trade_prevention_mode: String,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
struct NewOrderResponse {
    // symbol: BnSymbol,
    order_id: u64,
    // order_list_id: i64,
    // client_order_id: String,
    // transact_time: i64,
    // price: String,
    // orig_qty: String,
    // executed_qty: String,
    // cummulative_quote_qty: String,
    // status: String,
    // time_in_force: String,
    // r#type: String,
    // side: String,
    // self_trade_prevention_mode: String,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
struct CancelReplaceOrderRsp {
    new_order_response: NewOrderResponse,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct RateLimit {
    rate_limit_type: RateLimitType,
    interval: Interval,
    interval_num: i32,
    limit: i32,
    count: i32,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum RateLimitType {
    RequestWeight,
    Orders,
    RawRequests,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum Interval {
    Second,
    Minute,
    Hour,
    Day,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct WsApiPostOrderParams {
    symbol: BnSymbol,
    side: BnOrderSide,
    r#type: BnOrderType,
    #[serde(skip_serializing_if = "Option::is_none")]
    time_in_force: Option<BnTimeInForce>,
    #[serde(skip_serializing_if = "Option::is_none")]
    price: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    quantity: Option<String>,
    new_client_order_id: Option<String>,
}

impl WsApiPostOrderParams {
    pub(crate) fn new(order: Order) -> Self {
        let order_type = BnOrderType::with_tif(&order.order_type, &order.time_in_force);
        let (time_in_force, price) = match order.order_type {
            OrderType::Limit => {
                let time_in_force = match order.time_in_force {
                    TimeInForce::PostOnly => None,
                    _ => Some(order.time_in_force.into()),
                };
                (time_in_force, order.price.map(|p| p.to_string()))
            }
            OrderType::Market => (None, None),
        };

        Self {
            symbol: order.symbol.into(),
            side: order.side.into(),
            r#type: order_type,
            time_in_force,
            price,
            quantity: order.amount.map(|a| a.to_string()),
            new_client_order_id: order.cid,
        }
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct WsApiCancelOrderParams {
    symbol: BnSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    order_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    orig_client_order_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    new_client_order_id: Option<String>,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
#[allow(dead_code)]
enum BnCancelReplaceMode {
    StopOnFailure,
    AllowFailure,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct WsApiCancelReplaceOrderParams {
    symbol: BnSymbol,
    cancel_replace_mode: BnCancelReplaceMode,
    cancel_orig_client_order_id: Option<String>,
    side: BnOrderSide,
    r#type: BnOrderType,
    #[serde(skip_serializing_if = "Option::is_none")]
    time_in_force: Option<BnTimeInForce>,
    price: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    quantity: Option<String>,
}

#[derive(Deserialize, Debug)]
struct WsCandle {
    #[serde(rename = "t")]
    start_time: i64, // 这根K线的起始时间
    #[serde(rename = "i")]
    interval: KlineInterval, // K线间隔

    // #[serde(rename = "T")]
    // end_time: i64, // 这根K线的结束时间
    // #[serde(rename = "f")]
    // first_update_id: i64, // 这根K线期间第一笔更新ID
    // #[serde(rename = "L")]
    // last_update_id: i64, // 这根K线期间末一笔更新ID
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "o")]
    open: f64, // 这根K线期间第一笔成交价
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "c")]
    close: f64, // 这根K线期间末一笔成交价
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "h")]
    high: f64, // 这根K线期间最高成交价
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "l")]
    low: f64, // 这根K线期间最低成交价
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "v")]
    volume: f64, // 这根K线期间成交量
    #[serde(rename = "n")]
    trades: u32, // 这根K线期间成交笔数
    #[serde(rename = "x")]
    is_closed: bool, // 这根K线是否完结(是否已经开始下一根K线)
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "q")]
    quote_volume: f64, // 这根K线期间成交额
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "V")]
    taker_buy_volume: f64, // 主动买入的成交量
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "Q")]
    taker_buy_quote_volume: f64, // 主动买入的成交额
                                 // #[serde(deserialize_with = "de_from_str")]
                                 // #[serde(rename = "B")]
                                 // taker_buy_base_volume: f64, // 忽略此参数
}

impl WsCandle {
    pub(crate) fn into_base(self, prefix: f64) -> Candle {
        Candle::new(
            self.start_time,
            prefix.price2base(self.open),
            prefix.price2base(self.high),
            prefix.price2base(self.low),
            prefix.price2base(self.close),
            prefix.qty2base(self.volume),
            self.quote_volume,
            Some(self.trades),
            Some(prefix.qty2base(self.taker_buy_volume)),
            Some(self.taker_buy_quote_volume),
            self.is_closed,
        )
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct WsKline {
    // #[serde(rename = "e")]
    // event: String, // 事件类型 (continuous_kline)
    // #[serde(rename = "E")]
    // event_time: i64, // 事件时间
    #[serde(rename = "s")]
    symbol: BnSymbol, // 标的交易对
    // #[serde(rename = "ct")]
    // contract_type: BnContractType, // 合约类型
    #[serde(rename = "k")]
    candle: WsCandle,
}

impl From<WsKline> for Kline {
    fn from(value: WsKline) -> Self {
        let interval = value.candle.interval.clone();
        let prefix = value.symbol.prefix_f64();
        let candle = value.candle.into_base(prefix);
        Kline::new(value.symbol.into(), interval, vec![candle])
    }
}

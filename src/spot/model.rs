use std::fmt::{self, Formatter};

use quant_common::base::*;
use quant_common::*;
use serde::{Deserialize, Deserializer, Serialize, Serializer};
use sonic_rs::Value;

pub const ERROR_CODE_TOO_MANY_REQUESTS: i32 = 50011;

pub const API_PREFIX: &str = "/api";
pub const EXCHANGE: &str = "OkxSpot";
pub const HEADER_CONTENT_TYPE: &str = "Content-Type";
pub const HEADER_JSON: &str = "application/json";
pub const HEADER_KEY: &str = "OK-ACCESS-KEY";
pub const HEADER_SIGN: &str = "OK-ACCESS-SIGN";
pub const HEADER_TIMESTAMP: &str = "OK-ACCESS-TIMESTAMP";
pub const HEADER_PASSPHRASE: &str = "OK-ACCESS-PASSPHRASE";
pub const HEADER_TESTNET: &str = "x-simulated-trading";
pub const PATH_ACCOUNT_CONFIG: &str = "account/config";
pub const PATH_ACCOUNT_BALANCE: &str = "account/balance";
pub const PATH_DEPTH: &str = "market/books";
pub const PATH_DEPOSIT_ADDRESS: &str = "asset/deposit-address";
pub const PATH_FUNDING_RATE: &str = "public/funding_rate";
pub const PATH_INSTRUMENTS: &str = "public/instruments";
pub const PATH_ORDERS: &str = "trade/orders-pending";
pub const PATH_ORDERS_HISTORY: &str = "trade/orders-history-archive";
pub const PATH_POST_ORDER: &str = "trade/order";
pub const PATH_POST_ORDER_BATCH: &str = "trade/batch-orders";
pub const PATH_POST_ORDER_CANCEL: &str = "trade/cancel-order";
pub const PATH_CANCEL_BATCH_ORDER: &str = "trade/cancel-batch-orders";
pub const PATH_POST_ORDER_AMEND: &str = "trade/amend-order";
pub const PATH_SET_LEVERAGE: &str = "account/set-leverage";
pub const PATH_SET_POSITION_MODE: &str = "account/set-position-mode";
pub const PATH_TICKER: &str = "market/ticker";
pub const PATH_TICKERS: &str = "market/tickers";
pub const PATH_MARK_PRICE: &str = "public/mark-price";
pub const PATH_TRADE_FEE: &str = "account/trade-fee";
pub const PATH_WITHDRAWAL: &str = "asset/withdrawal";
pub const REST_BASE_URL: &str = "https://www.okx.com";
pub const REST_AWS_BASE_URL: &str = "https://aws.okx.com";
pub const VERSION: &str = "/v5/";
// pub const WS_URL_PUBLIC: &str = "wss://ws.okx.com:8443/ws/v5/public";
// pub const WS_URL_PRIVATE: &str = "wss://ws.okx.com:8443/ws/v5/private";
// pub const WS_URL_BUSINESS: &str = "wss://ws.okx.com:8443/ws/v5/business";
// pub const WS_AWS_URL_PUBLIC: &str = "wss://wsaws.okx.com:8443/ws/v5/public";
// pub const WS_AWS_URL_PRIVATE: &str = "wss://wsaws.okx.com:8443/ws/v5/private";
// pub const WS_AWS_URL_BUSINESS: &str = "wss://wsaws.okx.com:8443/ws/v5/business";
// pub const WS_TEST_URL_PUBLIC: &str = "wss://wspap.okx.com:8443/ws/v5/public";
// pub const WS_TEST_URL_PRIVATE: &str = "wss://wspap.okx.com:8443/ws/v5/private";
// pub const WS_TEST_URL_BUSINESS: &str = "wss://wspap.okx.com:8443/ws/v5/business";
pub const WS_AWS_URL: &str = "wsaws.okx.com:8443";
pub const WS_TEST_URL: &str = "wspap.okx.com:8443";
pub const WS_URL: &str = "ws.okx.com:8443";

#[derive(serde::Deserialize)]
pub struct OkxResp {
    #[serde(deserialize_with = "de_from_num_or_str")]
    pub code: i32,
    #[serde(default)]
    pub msg: String,
    #[serde(default)]
    pub data: Value,
}

impl fmt::Debug for OkxResp {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "OkxResp {{ code: {}, msg: {}, data: {} }}",
            self.code, self.msg, self.data
        )
    }
}

#[derive(Clone, PartialEq, Default, Eq, Hash)]
pub struct OkxSymbol(pub Symbol);

impl From<Symbol> for OkxSymbol {
    fn from(symbol: Symbol) -> Self {
        Self(symbol)
    }
}

impl From<OkxSymbol> for Symbol {
    fn from(symbol: OkxSymbol) -> Self {
        symbol.0
    }
}

impl std::fmt::Debug for OkxSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}-{}", self.0.base, self.0.quote)
    }
}

impl std::fmt::Display for OkxSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}-{}", self.0.base, self.0.quote)
    }
}

impl<'de> Deserialize<'de> for OkxSymbol {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        let parts: Vec<&str> = s.split('-').collect();
        if parts.len() < 2 {
            return Err(serde::de::Error::custom("Invalid format"));
        }

        let base = parts[0].to_string();
        let quote = parts[1];
        let quote = serde_plain::from_str(quote).unwrap_or(QuoteCcy::OTHER);

        Ok(OkxSymbol(Symbol { base, quote }))
    }
}

impl Serialize for OkxSymbol {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        format!("{}-{}", self.0.base, self.0.quote).serialize(serializer)
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTickersReq {
    pub inst_type: OkxInstType,
}

impl Default for OkxTickersReq {
    fn default() -> Self {
        Self {
            inst_type: OkxInstType::Spot,
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTickerResp {
    pub inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub last: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub ask_px: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub ask_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub bid_px: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub bid_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub open_24h: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub high_24h: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub low_24h: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub vol_ccy_24h: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub vol_24h: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub ts: i64,
}

impl From<OkxTickerResp> for Ticker {
    fn from(value: OkxTickerResp) -> Self {
        Ticker {
            symbol: value.inst_id.into(),
            timestamp: value.ts,
            high: value.high_24h,
            low: value.low_24h,
            open: value.open_24h,
            close: value.last,
            volume: value.vol_24h,
            quote_volume: value.vol_ccy_24h,
            change: value.last - value.open_24h,
            change_percent: (value.last - value.open_24h) / value.open_24h,
        }
    }
}

impl From<OkxTickerResp> for BboTicker {
    fn from(value: OkxTickerResp) -> Self {
        let symbol = value.inst_id;
        BboTicker {
            symbol: symbol.into(),
            bid_price: value.bid_px,
            bid_qty: value.bid_sz,
            ask_price: value.ask_px,
            ask_qty: value.ask_sz,
            timestamp: value.ts,
        }
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxMarkPriceReq {
    inst_type: InstType,
    #[serde(skip_serializing_if = "Option::is_none")]
    inst_id: Option<String>,
}

impl OkxMarkPriceReq {
    pub fn new(inst_id: Option<Symbol>) -> Self {
        Self {
            inst_type: InstType::Margin,
            inst_id: inst_id.map(|s| format!("{}-{}", s.base, s.quote)),
        }
    }
}

#[derive(Default, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxMarkPriceResp {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    mark_px: f64,
}

impl From<OkxMarkPriceResp> for MarkPrice {
    fn from(value: OkxMarkPriceResp) -> Self {
        MarkPrice {
            symbol: value.inst_id.into(),
            price: value.mark_px,
        }
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxDepthReq {
    pub inst_id: OkxSymbol,
    // 深度档位数量，最大值可传400，即买卖深度共800条
    // 不填写此参数，默认返回1档深度数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sz: Option<String>,
}

impl OkxDepthReq {
    pub fn new(symbol: Symbol, sz: Option<u32>) -> Self {
        let mut req = Self::default();
        req.set_symbol(symbol);
        if let Some(sz) = sz {
            req.set_sz(sz);
        }
        req
    }

    pub fn set_symbol(&mut self, symbol: Symbol) {
        self.inst_id = symbol.into();
    }

    pub fn set_sz(&mut self, sz: u32) {
        if sz > 400 {
            self.sz = Some("400".to_string());
        }
        self.sz = Some(sz.to_string());
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxDepthResp {
    symbol: Option<OkxSymbol>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    bids: Vec<(f64, f64)>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    asks: Vec<(f64, f64)>,
    #[serde(deserialize_with = "de_from_str")]
    ts: i64,
}

impl From<OkxDepthResp> for BboTicker {
    fn from(value: OkxDepthResp) -> Self {
        let symbol = value.symbol.unwrap();
        let bid = value.bids.first().unwrap_or(&(0., 0.));
        let ask = value.asks.first().unwrap_or(&(0., 0.));
        BboTicker {
            symbol: symbol.into(),
            bid_price: bid.0,
            bid_qty: bid.1,
            ask_price: ask.0,
            ask_qty: ask.1,
            timestamp: value.ts,
        }
    }
}

impl OkxDepthResp {
    pub fn set_symbol(&mut self, symbol: Symbol) {
        self.symbol = Some(symbol.into());
    }

    pub fn convert_to_depth(self) -> Depth {
        let symbol = self.symbol.unwrap();
        Depth {
            symbol: symbol.into(),
            timestamp: self.ts,
            asks: self
                .asks
                .into_iter()
                .map(|(p, a)| DepthEntry {
                    price: p,
                    amount: a,
                })
                .collect(),
            bids: self
                .bids
                .into_iter()
                .map(|(p, a)| DepthEntry {
                    price: p,
                    amount: a,
                })
                .collect(),
        }
    }
}

impl From<OkxDepthResp> for Depth {
    fn from(value: OkxDepthResp) -> Self {
        Depth {
            symbol: value.symbol.unwrap().into(),
            timestamp: value.ts,
            bids: value.bids.into_iter().map(Into::into).collect(),
            asks: value.asks.into_iter().map(Into::into).collect(),
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxInstrumentReq {
    // 产品类型
    // SPOT：币币
    pub inst_type: OkxInstType,
    // 产品ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inst_id: Option<String>,
}

impl Default for OkxInstrumentReq {
    fn default() -> Self {
        Self {
            inst_type: OkxInstType::Spot,
            inst_id: None,
        }
    }
}

impl OkxInstrumentReq {
    pub fn set_symbol(&mut self, symbol: Symbol) {
        self.inst_id = Some(format!("{}-{}", symbol.base, symbol.quote));
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxInstrumentResp {
    pub inst_id: OkxSymbol,
    pub state: String,
    pub settle_ccy: String,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub tick_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub lot_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub min_sz: f64,
}

impl From<OkxInstrumentResp> for Instrument {
    fn from(value: OkxInstrumentResp) -> Self {
        let state = match value.state.as_str() {
            "live" => InsState::Normal,
            "suspended" => InsState::Maintenance,
            _ => InsState::Close,
        };

        Instrument {
            symbol: value.inst_id.into(),
            state,
            price_tick: value.tick_sz,
            amount_tick: value.lot_sz,
            price_precision: -value.tick_sz.log10() as i32,
            amount_precision: -value.lot_sz.log10() as i32,
            min_qty: value.min_sz,
            min_notional: 0.,
            price_multiplier: 1.,
            amount_multiplier: 1.,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum OkxPosSide {
    Long,
    Short,
    Net,
    #[serde(rename = "")]
    Empty,
}

impl From<OkxPosSide> for Option<PosSide> {
    fn from(value: OkxPosSide) -> Self {
        match value {
            OkxPosSide::Long => Some(PosSide::Long),
            OkxPosSide::Short => Some(PosSide::Short),
            _ => None,
        }
    }
}

impl From<PosSide> for OkxPosSide {
    fn from(value: PosSide) -> Self {
        match value {
            PosSide::Long => OkxPosSide::Long,
            PosSide::Short => OkxPosSide::Short,
        }
    }
}

#[derive(Serialize, Debug, Default)]
#[serde(rename_all = "UPPERCASE")]
pub enum OkxInstType {
    #[default]
    Spot,
    Margin,
    Futures,
    Option,
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxOrdersReq {
    pub inst_type: OkxInstType,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inst_id: Option<OkxSymbol>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub start_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub end_time: Option<i64>,
}

impl Default for OkxOrdersReq {
    fn default() -> Self {
        OkxOrdersReq {
            inst_type: OkxInstType::Spot,
            inst_id: None,
            start_time: None,
            end_time: None,
        }
    }
}

impl OkxOrdersReq {
    pub fn open_orders(inst_type: OkxInstType, symbol: Symbol) -> Self {
        OkxOrdersReq {
            inst_type,
            inst_id: Some(symbol.into()),
            ..Default::default()
        }
    }

    pub fn all_open_orders(inst_type: OkxInstType) -> Self {
        Self {
            inst_type,
            ..Default::default()
        }
    }

    pub fn history_orders(inst_type: OkxInstType, symbol: Symbol, start: i64, end: i64) -> Self {
        Self {
            inst_type,
            inst_id: Some(symbol.into()),
            start_time: Some(start),
            end_time: Some(end),
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxGetOrderByIdReq {
    inst_id: OkxSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
}

impl OkxGetOrderByIdReq {
    pub fn new(symbol: Symbol, order_id: OrderId) -> Self {
        let (ord_id, cl_ord_id) = match order_id {
            OrderId::Id(id) => (Some(id), None),
            OrderId::ClientOrderId(cid) => (None, Some(cid)),
        };
        Self {
            inst_id: symbol.into(),
            ord_id,
            cl_ord_id,
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum TgtCcy {
    BaseCcy,
    QuoteCcy,
    #[serde(rename = "")]
    Default,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxOrderResp {
    ord_id: String,
    cl_ord_id: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    c_time: i64,
    state: OkxOrderState,
    inst_id: OkxSymbol,
    ord_type: OkxOrderType,
    // 订单方向 buy：买 sell：卖
    side: OrderSideLower,
    // 持仓方向 long：多 short：空 买卖模式返回 net
    pos_side: OkxPosSide,
    px: String,
    #[serde(deserialize_with = "de_from_str")]
    sz: f64,
    #[serde(deserialize_with = "de_from_str")]
    acc_fill_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    avg_px: f64,
    tgt_ccy: TgtCcy,
}

impl From<OkxOrderResp> for Order {
    fn from(value: OkxOrderResp) -> Self {
        let px = value.px.parse().ok();
        // let amount_multiplier = value.inst_id.multiplier();
        let pos_side = value.pos_side.into();
        let order_type = value.ord_type.clone().into();

        let amount = match order_type {
            OrderType::Limit => value.sz,
            OrderType::Market => {
                let tgt_ccy = match value.tgt_ccy {
                    TgtCcy::QuoteCcy | TgtCcy::BaseCcy => value.tgt_ccy,
                    _ => match value.side {
                        OrderSideLower::Buy => TgtCcy::QuoteCcy,
                        OrderSideLower::Sell => TgtCcy::BaseCcy,
                    },
                };
                match (tgt_ccy, value.avg_px) {
                    (TgtCcy::BaseCcy, _) => value.sz,
                    (TgtCcy::QuoteCcy, 0.0) => {
                        warn!("amount以quote计算,但没有成交价。 返回原始 amount");
                        value.sz
                    }
                    (TgtCcy::QuoteCcy, avg_px) => value.sz / avg_px,
                    _ => unreachable!("tgt_ccy and avg_px should match"),
                }
            }
        };

        Self {
            id: value.ord_id,
            cid: value.cl_ord_id,
            symbol: value.inst_id.into(),
            order_type,
            side: value.side.into(),
            timestamp: value.c_time,
            status: value.state.into(),
            time_in_force: value.ord_type.into(),
            price: px,
            amount: Some(amount),
            quote_amount: None,
            filled: value.acc_fill_sz,
            filled_avg_price: value.avg_px,
            pos_side,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxPostAmendOrderReq {
    inst_id: OkxSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    new_sz: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    new_px: Option<String>,
    // #[serde(skip_serializing_if = "Option::is_none")]
    // attach_algo_ords: Option<Vec<OkxPostNewAttach>>,
}

impl OkxPostAmendOrderReq {
    pub fn new(order: Order) -> Self {
        Self {
            inst_id: order.symbol.into(),
            ord_id: Some(order.id),
            cl_ord_id: order.cid,
            new_sz: Some(order.amount.unwrap().to_string()),
            new_px: order.price.map(|p| p.to_string()),
            // attach_algo_ords: None,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
enum OkxOrderState {
    Canceled,
    Live,
    PartiallyFilled,
    Filled,
    MmpCanceled,
}

impl From<OkxOrderState> for OrderStatus {
    fn from(value: OkxOrderState) -> Self {
        match value {
            OkxOrderState::Live => OrderStatus::Open,
            OkxOrderState::PartiallyFilled => OrderStatus::PartiallyFilled,
            OkxOrderState::Filled => OrderStatus::Filled,
            OkxOrderState::Canceled => OrderStatus::Canceled,
            OkxOrderState::MmpCanceled => OrderStatus::Canceled,
        }
    }
}

#[derive(Serialize, Debug, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum OkxTradeMode {
    Isolated,
    Cross,
    SpotIsolated,
    Cash,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct OkxPostOrderReq {
    inst_id: OkxSymbol,
    td_mode: OkxTradeMode,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
    side: OrderSideLower,
    ord_type: OkxOrderType,
    sz: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    attach_algo_ords: Option<Vec<OkxPostAttach>>,
    // #[serde(skip_serializing_if = "Option::is_none")]
    // pos_side: Option<OkxPosSide>, // Margin 和 Spot 都没有开平仓模式
    #[serde(skip_serializing_if = "Option::is_none")]
    tgt_ccy: Option<TgtCcy>,
    #[serde(skip_serializing_if = "Option::is_none")]
    ccy: Option<String>,
}

// 账户模式
pub enum OkxAccountMode {
    Spot,           // 现货模式
    SpotAndFutures, // 现货和合约模式
    CrossMargin,    // 跨币种保证金模式
    CombinedMargin, // 组合保证金模式
}

impl OkxPostOrderReq {
    pub fn new(order: Order, order_params: OrderParams, is_spot_and_futures: bool) -> Result<Self> {
        let td_mode = match &order_params.margin_mode {
            Some(MarginMode::Cross) => OkxTradeMode::Cross,
            Some(MarginMode::Isolated) => OkxTradeMode::Isolated,
            None => OkxTradeMode::Cash,
        };

        // 保证金币种，仅适用于现货和合约模式下的全仓杠杆订单
        let ccy = match is_spot_and_futures && td_mode == OkxTradeMode::Cross {
            true => Some(order.symbol.quote.to_string()),
            false => None,
        };

        //// 市价单委托数量sz的单位，仅适用于币币市价订单
        //// base_ccy: 交易货币 ；quote_ccy：计价货币
        //// 买单默认quote_ccy， 卖单默认base_ccy
        // 市价买单，大部分应该sz * amount; 但是币币可以设置tgt_ccy
        let calc_notional = order.order_type == OrderType::Market && order.side == OrderSide::Buy;
        let spot = order_params.margin_mode.is_none();
        let amount = order.amount.unwrap();
        let (tgt_ccy, amount) = match (calc_notional, spot) {
            (false, _) => (None, amount),
            (true, true) => (Some(TgtCcy::BaseCcy), amount),
            (true, false) => {
                let price = order
                    .price
                    .ok_or(qerror!("市价买单需要 price 来转换成 quote_ccy"))?;
                (None, price * amount)
            }
        };

        let attach_algo_ords =
            match order_params.stop_loss.is_some() || order_params.take_profit.is_some() {
                true => Some(vec![order_params.into()]),
                false => None,
            };

        let order_type = match (order.order_type, order.time_in_force) {
            (OrderType::Market, _) => OkxOrderType::Market,
            (OrderType::Limit, TimeInForce::GTC) => OkxOrderType::Limit,
            (OrderType::Limit, TimeInForce::FOK) => OkxOrderType::Fok,
            (OrderType::Limit, TimeInForce::IOC) => OkxOrderType::Ioc,
            (OrderType::Limit, TimeInForce::PostOnly) => OkxOrderType::PostOnly,
        };

        Ok(Self {
            inst_id: order.symbol.into(),
            td_mode,
            cl_ord_id: order.cid,
            side: order.side.into(),
            ord_type: order_type,
            sz: amount.to_string(),
            px: order.price.map(|p| p.to_string()),
            attach_algo_ords,
            tgt_ccy,
            ccy,
        })
    }

    pub fn new_ws_req(
        order: Order,
        order_params: OrderParams,
        is_spot_and_futures: bool,
    ) -> Result<Self> {
        let td_mode = match &order_params.margin_mode {
            Some(MarginMode::Cross) => OkxTradeMode::Cross,
            Some(MarginMode::Isolated) => OkxTradeMode::Isolated,
            None => OkxTradeMode::Cash,
        };

        // 保证金币种，仅适用于现货和合约模式下的全仓杠杆订单
        let ccy = match is_spot_and_futures && td_mode == OkxTradeMode::Cross {
            true => Some(order.symbol.quote.to_string()),
            false => None,
        };

        //// 市价单委托数量sz的单位，仅适用于币币市价订单
        //// base_ccy: 交易货币 ；quote_ccy：计价货币
        //// 买单默认quote_ccy， 卖单默认base_ccy
        // 市价买单，大部分应该sz * amount; 但是币币可以设置tgt_ccy
        let calc_notional = order.order_type == OrderType::Market && order.side == OrderSide::Buy;
        let spot = order_params.margin_mode.is_none();
        let amount = order.amount.unwrap();
        let (tgt_ccy, amount) = match (calc_notional, spot) {
            (false, _) => (None, amount),
            (true, true) => (Some(TgtCcy::BaseCcy), amount),
            (true, false) => {
                let price = order
                    .price
                    .ok_or(qerror!("市价买单需要 price 来转换成 quote_ccy"))?;
                (None, price * amount)
            }
        };

        let order_type = match (order.order_type, order.time_in_force) {
            (OrderType::Market, _) => OkxOrderType::Market,
            (OrderType::Limit, TimeInForce::GTC) => OkxOrderType::Limit,
            (OrderType::Limit, TimeInForce::FOK) => OkxOrderType::Fok,
            (OrderType::Limit, TimeInForce::IOC) => OkxOrderType::Ioc,
            (OrderType::Limit, TimeInForce::PostOnly) => OkxOrderType::PostOnly,
        };

        Ok(Self {
            inst_id: order.symbol.into(),
            td_mode,
            cl_ord_id: order.cid,
            side: order.side.into(),
            ord_type: order_type,
            sz: amount.to_string(),
            px: order.price.map(|p| p.to_string()),
            attach_algo_ords: None,
            tgt_ccy,
            ccy,
        })
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "snake_case")]
enum OkxOrderType {
    // 市价单
    Market,
    // 限价单
    Limit,
    // 做maker单
    PostOnly,
    // 全部成交或立即取消
    Fok,
    // 立即成交并取消剩余
    Ioc,
    // 市价委托立即成交并取消剩余（仅适用交割、永续）
    OptimalLimitIoc,
    // 做市商保护(仅适用于组合保证金账户模式下的期权订单)
    Mmp,
    // 做市商保护且只做maker单(仅适用于组合保证金账户模式下的期权订单)
    MmpAndPostOnly,
    // 期权简选（全部成交或立即取消）
    OpFok,
}

impl From<OkxOrderType> for OrderType {
    fn from(value: OkxOrderType) -> Self {
        match value {
            OkxOrderType::Market => OrderType::Market,
            OkxOrderType::Limit => OrderType::Limit,
            _ => OrderType::Limit,
        }
    }
}

impl From<OrderType> for OkxOrderType {
    fn from(value: OrderType) -> Self {
        match value {
            OrderType::Market => OkxOrderType::Market,
            OrderType::Limit => OkxOrderType::Limit,
        }
    }
}

impl From<OkxOrderType> for TimeInForce {
    fn from(value: OkxOrderType) -> Self {
        match value {
            OkxOrderType::Fok => TimeInForce::FOK,
            OkxOrderType::Ioc => TimeInForce::IOC,
            OkxOrderType::PostOnly => TimeInForce::PostOnly,
            _ => TimeInForce::GTC,
        }
    }
}

#[derive(Default, Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct OkxPostAttach {
    #[serde(skip_serializing_if = "Option::is_none")]
    tp_ord_px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tp_trigger_px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tp_trigger_px_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    sl_ord_px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    sl_trigger_px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    sl_trigger_px_type: Option<String>,
}

impl From<OrderParams> for OkxPostAttach {
    fn from(value: OrderParams) -> Self {
        let mut attach = OkxPostAttach::default();
        if let Some(profit_trigger) = value.take_profit {
            match profit_trigger.trigger_price {
                TriggerPrice::MarkPrice(price) => {
                    attach.tp_trigger_px = Some(price.to_string());
                    attach.tp_trigger_px_type = Some("mark".to_string());
                }
                TriggerPrice::ContractPrice(price) => {
                    attach.tp_trigger_px = Some(price.to_string());
                    attach.tp_trigger_px_type = Some("last".to_string());
                }
            }

            match profit_trigger.trigger_action {
                TriggerAction::Limit(price) => {
                    attach.tp_ord_px = Some(price.to_string());
                }
                TriggerAction::Market => {
                    attach.tp_ord_px = Some((-1).to_string());
                }
            }
        }

        if let Some(stop_loss) = value.stop_loss {
            match stop_loss.trigger_price {
                TriggerPrice::MarkPrice(price) => {
                    attach.sl_trigger_px = Some(price.to_string());
                    attach.sl_trigger_px_type = Some("mark".to_string());
                }
                TriggerPrice::ContractPrice(price) => {
                    attach.sl_trigger_px = Some(price.to_string());
                    attach.sl_trigger_px_type = Some("last".to_string());
                }
            }

            match stop_loss.trigger_action {
                TriggerAction::Limit(price) => {
                    attach.sl_ord_px = Some(price.to_string());
                }
                TriggerAction::Market => {
                    attach.sl_ord_px = Some((-1).to_string());
                }
            }
        }
        attach
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxPostCancelOrderReq {
    inst_id: OkxSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
}

impl OkxPostCancelOrderReq {
    pub fn new(symbol: Symbol, order_id: Option<String>, c_id: Option<String>) -> Self {
        Self {
            inst_id: symbol.into(),
            ord_id: order_id,
            cl_ord_id: c_id,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxBatchCancelOrderReq {
    inst_id: OkxSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
}

impl OkxBatchCancelOrderReq {
    pub fn new(symbol: Symbol, ids: Option<Vec<String>>, cids: Option<Vec<String>>) -> Vec<Self> {
        let mut reqs = vec![];
        if let Some(ids) = ids {
            reqs.extend(ids.into_iter().map(|id| Self {
                inst_id: symbol.clone().into(),
                ord_id: Some(id),
                cl_ord_id: None,
            }));
        }
        if let Some(cids) = cids {
            reqs.extend(cids.into_iter().map(|cid| Self {
                inst_id: symbol.clone().into(),
                ord_id: None,
                cl_ord_id: Some(cid),
            }));
        }
        reqs
    }
}
#[derive(Deserialize)]
pub struct BatchOrderRespWrapper(Vec<OkxBatchOrderResp>);

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxBatchOrderResp {
    ord_id: String,
    cl_ord_id: String,
    #[serde(deserialize_with = "de_from_str")]
    s_code: i32,
    s_msg: String,
}

impl From<BatchOrderRespWrapper> for BatchOrderRsp {
    fn from(value: BatchOrderRespWrapper) -> Self {
        let (success_list, failure_list): (Vec<_>, Vec<_>) =
            value.0.into_iter().partition(|r| r.s_code == 0);

        BatchOrderRsp {
            success_list: success_list
                .into_iter()
                .map(|r| SuccessOrder {
                    id: Some(r.ord_id),
                    cid: Some(r.cl_ord_id),
                })
                .collect(),
            failure_list: failure_list
                .into_iter()
                .map(|r| FailOrder {
                    id: Some(r.ord_id),
                    cid: Some(r.cl_ord_id),
                    error_code: r.s_code,
                    error: r.s_msg,
                })
                .collect(),
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxPostOrderResp {
    pub ord_id: String,
    #[serde(deserialize_with = "de_from_str")]
    pub s_code: i32,
    pub s_msg: String,
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTradeFeeReq {
    inst_type: OkxInstType,
    inst_id: OkxSymbol,
}

impl OkxTradeFeeReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            inst_type: OkxInstType::Spot,
            inst_id: symbol.into(),
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTradeFeeResp {
    #[serde(deserialize_with = "de_from_str")]
    pub taker: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub maker: f64,
}

impl From<OkxTradeFeeResp> for FeeRate {
    fn from(value: OkxTradeFeeResp) -> Self {
        Self {
            taker: value.taker.abs(),
            maker: value.maker.abs(),
            ..Default::default()
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxBalancesResp {
    pub details: Vec<OkxBalanceDetails>,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    total_eq: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    imr: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    mgn_ratio: f64,
}

impl From<OkxBalancesResp> for AccountInfo {
    fn from(value: OkxBalancesResp) -> Self {
        let total_available = value.total_eq - value.imr;
        let mgn_ratio = if value.mgn_ratio == 0. {
            999.0
        } else {
            value.mgn_ratio
        };
        Self {
            total_mmr: mgn_ratio,
            total_equity: value.total_eq,
            total_available,
        }
    }
}

// impl OkxBalancesResp {
//     pub fn get_margin_ratio(&self) -> f64 {
//         if self.mgn_ratio == 0. {
//             10000.
//         } else {
//             self.mgn_ratio
//         }
//     }
// }

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxBalanceDetails {
    ccy: String,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    eq: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    avail_bal: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    upl: f64,
}

impl From<OkxBalanceDetails> for Balance {
    fn from(value: OkxBalanceDetails) -> Self {
        Balance {
            asset: value.ccy,
            balance: value.eq,
            available_balance: value.avail_bal,
            unrealized_pnl: value.upl,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxSetLeverageReq {
    inst_id: OkxSymbol,
    lever: String,
    mgn_mode: String,
}

impl OkxSetLeverageReq {
    pub fn new(symbol: Symbol, lever: u8) -> Self {
        Self {
            inst_id: symbol.into(),
            lever: lever.to_string(),
            mgn_mode: "cross".to_owned(), // 币币杠杆固定为cross
        }
    }
}

#[derive(Default, Serialize)]
pub struct OkxDepositAddressReq {
    ccy: String,
}

impl OkxDepositAddressReq {
    pub fn new(ccy: String) -> Self {
        Self {
            ccy: ccy.to_uppercase(),
        }
    }
}

pub enum OkxChain {
    TRC20,
    ERC20,
    Aptos,
    ArbitrumOne,
    AvalancheC,
    OKTC,
    Optimism,
    Polygon,
    Solana,
    TON,
    XLayer,
}

#[derive(Deserialize)]
pub struct OkxDepositAddressResp {
    ccy: String,
    chain: String,
    // to: String,
    addr: String,
    tag: Option<String>,
}

impl From<OkxDepositAddressResp> for DepositAddress {
    fn from(value: OkxDepositAddressResp) -> Self {
        // like "USDT-TRC20" => "TRC20"
        let chain_str = value
            .chain
            .split('-')
            .nth(1)
            .unwrap_or_default()
            .to_string();
        let chain = match chain_str.as_str() {
            "TRC20" => Chain::Trc20,
            "ERC20" => Chain::Erc20,
            "Solana" => Chain::Sol,
            "Polygon" => Chain::Polygon,
            "Arbitrum One" => Chain::ArbitrumOne,
            "Optimism" => Chain::Optimism,
            "TON" => Chain::Ton,
            "Avalanche C-Chain" => Chain::AVAXC,
            _ => Chain::Erc20, // 默认fallback
        };
        Self {
            asset: value.ccy,
            chain: Some(chain),
            address: value.addr,
            tag: value.tag,
            url: None,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxWithdrawalReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    client_id: Option<String>,
    ccy: String,
    amt: String,
    // 提币方式, 3-内部转账, 4-链上提币
    dest: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    chain: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    area_code: Option<String>,
    to_addr: String,
}

impl OkxWithdrawalReq {
    pub fn new(withdrawal: WithDrawlParams) -> Self {
        let base = Self {
            client_id: withdrawal.cid,
            ccy: withdrawal.asset.clone(),
            amt: withdrawal.amt.to_string(),
            to_addr: String::new(), // Will be overwritten
            dest: String::new(),    // Will be overwritten
            chain: None,
            area_code: None,
        };

        match &withdrawal.addr {
            WithdrawalAddr::OnChain(params) => Self {
                to_addr: params.tag.as_ref().map_or_else(
                    || params.address.clone(),
                    |tag| format!("{}:{}", params.address, tag),
                ),
                dest: "4".to_owned(),
                chain: Some(chain_to_str(withdrawal.asset, params.chain.clone())),
                ..base
            },
            WithdrawalAddr::InternalTransfer(params) => Self {
                to_addr: match params {
                    InternalTransferParams::Email(email) => email.to_owned(),
                    InternalTransferParams::Phone(phone) => phone.1.to_owned(),
                    InternalTransferParams::Uid(uid) => uid.to_owned(),
                },
                dest: "3".to_owned(),
                area_code: if let InternalTransferParams::Phone(phone) = params {
                    Some(phone.0.to_owned())
                } else {
                    None
                },
                ..base
            },
        }
    }
}

fn chain_to_str(ccy: String, chain: Chain) -> String {
    match chain {
        Chain::Erc20 => format!("{}-ERC20", ccy),
        Chain::Trc20 => format!("{}-TRC20", ccy),
        Chain::Bep20 => format!("{}-BEP20", ccy),
        Chain::Sol => format!("{}-Solana", ccy),
        Chain::Polygon => format!("{}-Polygon", ccy),
        Chain::ArbitrumOne => format!("{}-Arbitrum One", ccy),
        Chain::Optimism => format!("{}-Optimism", ccy),
        Chain::Ton => format!("{}-TON", ccy),
        Chain::AVAXC => format!("{}-Avalanche C-Chain", ccy),
    }
}

#[derive(Default, Deserialize, Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct OkxAccountConfig {
    uid: String,
    pub pos_mode: OkxPostMode,
}

impl OkxAccountConfig {
    pub fn is_dual_side(&self) -> bool {
        match self.pos_mode {
            OkxPostMode::LongShortMode => true,
            OkxPostMode::NetMode => false,
        }
    }

    pub fn set_position_mode(&mut self, is_dual_side: bool) {
        if is_dual_side {
            self.pos_mode = OkxPostMode::LongShortMode;
        } else {
            self.pos_mode = OkxPostMode::NetMode;
        }
    }

    pub fn get_uid(&self) -> String {
        self.uid.clone()
    }
}

#[derive(Default, Deserialize, Serialize, Debug)]
#[serde(rename_all = "snake_case")]
pub enum OkxPostMode {
    #[default]
    LongShortMode,
    NetMode,
}

#[derive(Serialize, Deserialize, Clone, Copy, PartialEq, Debug)]
pub enum Channel {
    /// public
    #[serde(rename = "mark-price")]
    MarkPrice,
    // #[serde(rename = "tickers")]
    #[serde(rename = "bbo-tbt")]
    BookTicker,
    #[serde(rename = "books5")]
    Depth,
    #[serde(rename = "trades")]
    Trade,
    #[serde(rename = "instruments")]
    Instruments,

    // login
    #[serde(rename = "login")]
    Login,
    // private
    #[serde(rename = "orders")]
    Orders,
    // #[serde(rename = "positions")]
    // Positions,
    #[serde(rename = "account")]
    Balances,
}
#[derive(Serialize)]
pub struct SubscriptionRequest {
    #[serde(rename = "op")]
    pub operation: Operation,
    pub args: Vec<RequestArgs>,
}

impl SubscriptionRequest {
    pub fn new(is_private: bool, channel: Channel, symbol: String, is_margin: bool) -> Self {
        if is_private {
            Self {
                operation: Operation::Subscribe,
                args: vec![RequestArgs::Private(PrivateArgs::new(
                    channel, symbol, is_margin,
                ))],
            }
        } else {
            Self {
                operation: Operation::Subscribe,
                args: vec![RequestArgs::Public(PublicArgs::new(channel, symbol))],
            }
        }
    }
}

#[derive(Serialize)]
// 序列化的时候不会不会包含 Public 和 Private 标签。
#[serde(untagged)]
pub enum RequestArgs {
    Public(PublicArgs),
    Private(PrivateArgs),
}

#[derive(Default, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum Operation {
    #[default]
    Subscribe,
    Unsubscribe,
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PublicArgs {
    pub channel: Channel,
    pub inst_id: String,
}

impl PublicArgs {
    pub fn new(channel: Channel, symbol: String) -> Self {
        PublicArgs {
            channel,
            inst_id: symbol,
        }
    }
}

#[derive(Clone, Copy)]
pub enum InstIdFormat {
    Default,
    Swap,
}

#[derive(Debug, Deserialize)]
pub struct OkxStreamData {
    pub arg: OkxStreamArg,
    pub data: Value,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxStreamArg {
    pub channel: Channel,
    pub uid: Option<String>,
    pub inst_id: Option<OkxSymbol>,
    pub inst_type: Option<InstType>,
    pub inst_family: Option<String>,
}

#[derive(Debug, Default, Deserialize, Serialize)]
#[serde(rename_all = "UPPERCASE")]
pub enum InstType {
    // 币币
    Spot,
    // 永续合约
    #[default]
    Swap,
    // 交割合约
    Futures,
    // 期权
    Option,
    // 币币杠杆
    Margin,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxStreamEventSuccess {
    pub event: String,
    pub arg: OkxStreamArg,
    pub conn_id: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxStreamEventError {
    pub event: String,
    #[serde(deserialize_with = "de_from_str")]
    pub code: i32,
    pub msg: String,
    pub conn_id: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxStreamChannelConnCount {
    pub event: String,
    pub channel: String,
    #[serde(deserialize_with = "de_from_str")]
    pub conn_count: i32,
    pub conn_id: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsMarkPrice {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    mark_px: f64,
}

impl From<WsMarkPrice> for MarkPrice {
    fn from(value: WsMarkPrice) -> Self {
        MarkPrice {
            symbol: value.inst_id.into(),
            price: value.mark_px,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsBbo {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    bid_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    bid_sz: f64,
    #[serde(deserialize_with = "de_from_str")]
    ask_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    ask_sz: f64,
    #[serde(deserialize_with = "de_from_str")]
    ts: i64,
}

impl From<WsBbo> for BboTicker {
    fn from(value: WsBbo) -> Self {
        BboTicker {
            symbol: value.inst_id.into(),
            bid_price: value.bid_px,
            bid_qty: value.bid_sz,
            ask_price: value.ask_px,
            ask_qty: value.ask_sz,
            timestamp: value.ts,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsFundingRate {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    funding_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    funding_time: i64,
    #[serde(deserialize_with = "de_from_str")]
    next_funding_time: i64,
}

impl From<WsFundingRate> for Funding {
    fn from(value: WsFundingRate) -> Self {
        let interval = (value.next_funding_time - value.funding_time) / 1000 / 3600;
        Funding {
            symbol: value.inst_id.into(),
            funding_rate: value.funding_rate,
            next_funding_at: value.funding_time,
            funding_interval: Some(interval as u8),
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsTrade {
    inst_id: OkxSymbol,
    trade_id: String,
    #[serde(deserialize_with = "de_from_str")]
    ts: i64,
    #[serde(deserialize_with = "de_from_str")]
    px: f64,
    #[serde(deserialize_with = "de_from_str")]
    sz: f64,
    side: OrderSideLower,
}

impl From<WsTrade> for Trade {
    fn from(value: WsTrade) -> Self {
        Trade {
            id: value.trade_id,
            symbol: value.inst_id.into(),
            timestamp: value.ts,
            price: value.px,
            amount: value.sz,
            side: value.side.into(),
        }
    }
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct WsLogin {
    pub op: Channel,
    pub args: Vec<WsLoginInner>,
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct WsLoginInner {
    api_key: String,
    passphrase: String,
    timestamp: String,
    sign: String,
}

impl WsLogin {
    pub fn new(api_key: &str, passphrase: &str, timestamp: String, sign: String) -> Self {
        Self {
            op: Channel::Login,
            args: vec![WsLoginInner {
                api_key: api_key.to_owned(),
                passphrase: passphrase.to_owned(),
                timestamp,
                sign,
            }],
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PrivateArgs {
    channel: Channel,
    #[serde(skip_serializing_if = "Option::is_none")]
    inst_type: Option<InstType>,
    #[serde(skip_serializing_if = "Option::is_none")]
    inst_id: Option<String>,
}

impl PrivateArgs {
    pub fn new(channel: Channel, symbol: String, is_margin: bool) -> Self {
        match channel {
            Channel::Balances => Self {
                channel,
                inst_type: None,
                inst_id: None,
            },
            _ => Self {
                channel,
                inst_type: Some(if is_margin {
                    InstType::Margin
                } else {
                    InstType::Spot
                }),
                inst_id: Some(symbol),
            },
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsPosition {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    c_time: i64,
    mgn_mode: OkxMarginMode,
    pos_side: PosSideLower,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    lever: u8,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pos: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    avg_px: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    upl: f64,
}

impl From<WsPosition> for Position {
    fn from(value: WsPosition) -> Self {
        let side = match value.pos_side {
            PosSideLower::Long => PosSide::Long,
            PosSideLower::Short => PosSide::Short,
            PosSideLower::Net => PosSide::Long,
        };
        Position {
            symbol: value.inst_id.into(),
            timestamp: value.c_time,
            margin_mode: value.mgn_mode.into(),
            side,
            leverage: value.lever,
            amount: value.pos,
            entry_price: value.avg_px,
            unrealized_pnl: value.upl,
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum OkxMarginMode {
    Cross,
    Isolated,
}

impl From<MarginMode> for OkxMarginMode {
    fn from(value: MarginMode) -> Self {
        match value {
            MarginMode::Cross => OkxMarginMode::Cross,
            MarginMode::Isolated => OkxMarginMode::Isolated,
        }
    }
}

impl From<OkxMarginMode> for MarginMode {
    fn from(value: OkxMarginMode) -> Self {
        match value {
            OkxMarginMode::Cross => MarginMode::Cross,
            OkxMarginMode::Isolated => MarginMode::Isolated,
        }
    }
}

#[derive(Serialize, Debug)]
pub struct WsApiReq {
    pub(crate) id: u64,
    pub(crate) op: WsApiMethod,
    pub(crate) args: serde_json::Value,
}

impl WsApiReq {
    pub fn post_order(id: u64, order: Order, params: OrderParams, is_margin: bool) -> Result<Self> {
        if order.amount.is_none() {
            return Err(qerror!("Amount is required"));
        }
        let req = OkxPostOrderReq::new_ws_req(order, params, is_margin)?;
        let args = serde_json::to_value(vec![req])?;
        debug!("args: {args:?}");
        Ok(Self {
            id,
            op: WsApiMethod::Post,
            args,
        })
    }

    pub fn batch_post_order(
        id: u64,
        orders: Vec<Order>,
        params: OrderParams,
        is_margin: bool,
    ) -> Result<Self> {
        // let req = OkxPostBatchOrderReq::new(orders, params);
        let mut reqs = vec![];
        for order in orders {
            if order.amount.is_none() {
                return Err(qerror!("Amount is required"));
            }
            let req = OkxPostOrderReq::new_ws_req(order, params.clone(), is_margin)?;
            reqs.push(req);
        }
        let args = serde_json::to_value(reqs)?;
        Ok(Self {
            id,
            op: WsApiMethod::BatchPost,
            args,
        })
    }

    pub fn cancel_order(
        id: u64,
        symbol: Symbol,
        order_id: Option<String>,
        client_id: Option<String>,
    ) -> Result<Self> {
        let req = OkxPostCancelOrderReq::new(symbol, order_id, client_id);
        let args = serde_json::to_value(vec![req])?;
        Ok(Self {
            id,
            op: WsApiMethod::Cancel,
            args,
        })
    }

    pub fn batch_cancel_order_by_ids(
        id: u64,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<Self> {
        if symbol.is_none() || (ids.is_none() && cids.is_none()) {
            // symbol is required, and at least one of ids or cids is required
            return Err(qerror!("symbol or ids and cids are required"));
        }

        let reqs = OkxBatchCancelOrderReq::new(symbol.unwrap(), ids, cids);
        let args = serde_json::to_value(reqs)?;
        Ok(Self {
            id,
            op: WsApiMethod::BatchCancel,
            args,
        })
    }

    pub fn amend_order(id: u64, order: Order) -> Result<Self> {
        let req = OkxPostAmendOrderReq::new(order);
        let args = serde_json::to_value(vec![req])?;
        Ok(Self {
            id,
            op: WsApiMethod::Amend,
            args,
        })
    }
}

#[derive(Serialize, Debug)]
pub enum WsApiMethod {
    #[serde(rename = "order")]
    Post,
    #[serde(rename = "batch-orders")]
    BatchPost,
    #[serde(rename = "cancel-order")]
    Cancel,
    #[serde(rename = "batch-cancel-orders")]
    BatchCancel,
    #[serde(rename = "amend-order")]
    Amend,
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct WsApiRsp {
    pub(crate) id: String,
    pub(crate) op: String,
    pub(crate) code: String,
    pub(crate) msg: String,
    pub(crate) data: Option<serde_json::Value>,
}

impl WsApiRsp {
    pub(crate) fn id(&self) -> u64 {
        self.id.parse::<u64>().unwrap()
    }

    pub(crate) fn into_common_order_rsp(self) -> Result<String> {
        let resp: Vec<OkxPostOrderResp> = match self.data {
            Some(result) => serde_json::from_value(result)?,
            None => return Err(qerror!("result is null")),
        };

        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No order rsp"))?;
        match resp.s_code {
            0 => Ok(resp.ord_id.to_string()),
            _ => Err(qerror!("{:?}", resp)),
        }
    }

    pub(crate) fn into_batch_common_order_rsp(self) -> Result<BatchOrderRsp> {
        let resp: BatchOrderRespWrapper = match self.data {
            Some(result) => serde_json::from_value(result)?,
            None => return Err(qerror!("result is null")),
        };
        Ok(resp.into())
    }
}

// use apex::swap::rest::ApexSwap;
use binance::future::rest::BinanceFuture;
use binance::margin::rest::BinanceMargin;
use binance::spot::rest::BinanceSpot;
use binance::swap::rest::BinanceSwap;
use bingx::spot::rest::BingxSpot;
use bingx::swap::rest::BingxSwap;
use bitget::margin::rest::BitgetMargin;
use bitget::spot::rest::BitgetSpot;
use bitget::swap::rest::BitgetSwap;
use bitmart::spot::rest::BitmartSpot;
use bitmart::swap::rest::BitmartSwap;
use bitmex::spot::rest::BitmexSpot;
use bitmex::swap::rest::BitmexSwap;
use bybit::spot::rest::BybitSpot;
use bybit::swap::rest::BybitSwap;
use coinbase::spot::rest::CoinbaseSpot;
use coinbase::swap::rest::CoinbaseSwap;
use coinex::spot::rest::CoinexSpot;
use coinex::swap::rest::CoinexSwap;
use coinw::spot::rest::CoinwSpot;
use coinw::swap::rest::CoinwSwap;
use crypto::spot::rest::CryptoSpot;
use crypto::swap::rest::CryptoSwap;
use deepcoin::spot::rest::DeepcoinSpot;
use deepcoin::swap::rest::DeepcoinSwap;
use deribit::future::rest::DeribitFuture;
use deribit::option::rest::DeribitOption;
use deribit::spot::rest::DeribitSpot;
use deribit::swap::rest::DeribitSwap;
use dydx::swap::rest::DydxSwap;
use gate::margin::rest::GateMargin;
use gate::spot::rest::GateSpot;
use gate::swap::rest::GateSwap;
use huobi::spot::rest::HuobiSpot;
use huobi::swap::rest::HuobiSwap;
use hyperliquid::spot::rest::HyperLiquidSpot;
use hyperliquid::swap::rest::HyperLiquidSwap;
use kraken::spot::rest::KrakenSpot;
use kucoin::margin::rest::KucoinMargin;
use kucoin::spot::rest::KucoinSpot;
use kucoin::swap::rest::KucoinSwap;
use mexc::spot::rest::MexcSpot;
use mexc::swap::rest::MexcSwap;
use okx::deribit_future::rest::OkxDeribitFuture as OkxFuture;
use okx::margin::rest::OkxMargin;
use okx::spot::rest::OkxSpot;
use okx::swap::rest::OkxSwap;
use okx::swap_usd::rest::OkxSwapUsd;
use phemex::spot::rest::PhemexSpot;
use phemex::swap::rest::PhemexSwap;
use whitebit::spot::rest::WhitebitSpot;
use whitebit::swap::rest::WhitebitSwap;
use xt::spot::rest::XTSpot;
use xt::swap::rest::XTSwap;
// use zoomex::spot::rest::ZoomexSpot;
// use zoomex::swap::rest::ZoomexSwap;

use crate::order_processor::*;
use quant_common::base::Rest;
use quant_common::base::model::balance::stable_balance_non_zero;
use quant_common::base::traits::model::*;
use quant_common::base::*;
use quant_common::{Result, qerror};

// 使用工厂函数创建交易所对象
macro_rules! define_exchanges_rest {
    ($($name:ident),*) => {
        #[derive(Clone)]
        #[allow(clippy::large_enum_variant)]
        pub enum ExchangeRest {
            $(
                $name($name),
            )*
            NotImplemented(Exchange),
        }

        pub async fn create_public_rest(exchange: Exchange) -> ExchangeRest {
            match exchange {
                $(
                    Exchange::$name => ExchangeRest::$name($name::new(exchange.config()).await),
                )*
                _ => ExchangeRest::NotImplemented(exchange),
            }
        }

        pub async fn create_private_rest(config: ExConfig) -> ExchangeRest {
            match config.exchange {
                $(
                    Exchange::$name => ExchangeRest::$name($name::new(config).await),
                )*
                _ => ExchangeRest::NotImplemented(config.exchange),
            }
        }

        impl Rest for ExchangeRest {
            fn name(&self) -> &'static str {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.name(),
                    )*
                    ExchangeRest::NotImplemented(_) => "未实现",
                }
            }

            async fn request(&self, req: UserRequest) -> Result<sonic_rs::Value> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.request(req).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_ticker(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_tickers(&self) -> Result<Vec<Ticker>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_tickers().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_bbo_ticker(
                &self,
                symbol: Symbol,
            ) -> Result<BboTicker> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_bbo_ticker(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_bbo_tickers().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_depth(
                &self,
                symbol: Symbol,
                limit: Option<u32>,
            ) -> Result<Depth> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_depth(symbol, limit).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_instrument(
                &self,
                symbol: Symbol,
            ) -> Result<Instrument> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_instrument(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_instruments(&self) -> Result<Vec<Instrument>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_instruments().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_funding_rates().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn post_order(
                &self,
                mut order: Order,
                params: OrderParams,
            ) -> Result<String> {
                // 先应用订单处理器
                match process_order(&mut order, &params, match self {
                    $(
                        ExchangeRest::$name(_) => Exchange::$name,
                    )*
                    ExchangeRest::NotImplemented(exchange) => return Err(qerror!("交易所 {:?} 未实现", exchange)),
                }).await {
                    Ok(_) => {}
                    Err(e) => {
                        return Err(e);
                    }
                }

                // 调用交易所API下单
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.post_order(order, params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn post_stop_order(&self, mut params: PostStopOrderParams) -> Result<StopOrderRsp> {
                process_stop_order_params(&mut params, match self {
                    $(
                        ExchangeRest::$name(_) => Exchange::$name,
                    )*
                    ExchangeRest::NotImplemented(exchange) => return Err(qerror!("交易所 {:?} 未实现", exchange)),
                }).await?;

                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.post_stop_order(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn post_batch_order(
                &self,
                mut orders: Vec<Order>,
                params: OrderParams,
            ) -> Result<BatchOrderRsp> {
                // 获取当前交易所
                let exchange = match self {
                    $(
                        ExchangeRest::$name(_) => Exchange::$name,
                    )*
                    ExchangeRest::NotImplemented(exchange) => return Err(qerror!("交易所 {:?} 未实现", exchange)),
                };

                // 对每个订单应用处理
                for order in orders.iter_mut() {
                    match process_order(order, &params, exchange).await {
                        Ok(_) => {}
                        Err(e) => {
                            return Err(e);
                        }
                    }
                }

                // 调用交易所API批量下单
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.post_batch_order(orders, params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn amend_order(&self, mut order: Order) -> Result<String> {
                // 先获取当前交易所
                let exchange = match self {
                    $(
                        ExchangeRest::$name(_) => Exchange::$name,
                    )*
                    ExchangeRest::NotImplemented(exchange) => return Err(qerror!("交易所 {:?} 未实现", exchange)),
                };

                // 先应用订单处理器
                match process_order(&mut order, &OrderParams::default(), exchange).await {
                    Ok(_) => {}
                    Err(e) => {
                        return Err(e);
                    }
                }
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.amend_order(order).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn cancel_order(
                &self,
                symbol: Symbol,
                order_id: OrderId,
            ) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.cancel_order(symbol, order_id).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.batch_cancel_order(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_fee_rate(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_usdt_balance(&self) -> Result<Balance> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => {
                            Ok(stable_balance_non_zero(rest.get_usdt_balance().await?))
                        },
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_balance(&self, coin: &str) -> Result<Balance> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => {
                            let balance = rest.get_balance(coin).await?;
                            Ok(stable_balance_non_zero(balance))
                        },
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_balances(&self) -> Result<Vec<Balance>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => {
                            let balances = rest.get_balances().await?;
                            let balances = balances.into_iter().map(stable_balance_non_zero).collect::<Vec<_>>();
                            Ok(balances)
                        },
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_orders(
                &self,
                symbol: Symbol,
                start: i64,
                end: i64
            ) -> Result<Vec<Order>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_orders(symbol, start, end).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_open_orders(
                &self,
                symbol: Symbol,
            ) -> Result<Vec<Order>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_open_orders(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_all_open_orders().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_position(
                &self,
                symbol: Symbol,
            ) -> Result<Vec<Position>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_position(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_positions(&self) -> Result<Vec<Position>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_positions().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_leverage(&self, symbol: Symbol, leverage: u8) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_leverage(symbol, leverage).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn is_dual_side(&self) -> Result<bool> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.is_dual_side().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_dual_side(&self, is_dual_side: bool) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_dual_side(is_dual_side).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_fee_discount_info(&self) -> Result<Option<Discount>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_fee_discount_info().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn is_fee_discount_enabled(&self) -> Result<bool> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.is_fee_discount_enabled().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_fee_discount_enabled(&self, enable: bool) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_fee_discount_enabled(enable).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn transfer(&self, transfer: Transfer) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.transfer(transfer).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_max_position(&self, symbol: Symbol, leverage: u8) -> Result<MaxPosition> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_max_position(symbol, leverage).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_max_leverage(&self, symbol: Symbol) -> Result<u8> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_max_leverage(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn batch_cancel_order_by_ids(
                &self,
                symbol: Option<Symbol>,
                ids: Option<Vec<String>>,
                cids: Option<Vec<String>>,
            ) -> Result<BatchOrderRsp>{
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.batch_cancel_order_by_ids(symbol, ids, cids).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_order_by_id(symbol, order_id).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_margin_mode(&self, symbol: Symbol, margin_coin: String) -> Result<MarginMode> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_margin_mode(symbol, margin_coin).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_margin_mode(
                &self,
                symbol: Symbol,
                margin_coin: String,
                margin_mode: MarginMode,
            ) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_margin_mode(symbol, margin_coin, margin_mode).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn borrow_coin(&self, coin: String, amount: f64) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.borrow_coin(coin, amount).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_borrow(&self, coin: Option<String>) -> Result<Vec<Borrow>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_borrow(coin).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn repay_coin(&self, coin: String, amount: f64) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.repay_coin(coin, amount).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_account_info(&self) -> Result<AccountInfo> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_account_info().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_borrow_rate(&self, coin: Option<String>) -> Result<Vec<BorrowRate>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_borrow_rate(coin).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_borrow_limits(&self, is_vip: Option<bool>, coin: String) -> Result<BorrowLimit> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_borrow_limits(is_vip, coin).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_mark_price(&self, symbol: Option<Symbol>) -> Result<Vec<MarkPrice>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_mark_price(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_funding_fee(
                &self,
                symbol: Symbol,
                start_time: Option<i64>,
                end_time: Option<i64>,
            ) -> Result<Vec<FundingFee>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_funding_fee(symbol, start_time, end_time).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn is_dual_side_ext(&self, params: IsDualSideParams) -> Result<bool> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.is_dual_side_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_dual_side_ext(&self, params: SetDualSideParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_dual_side_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn amend_order_ext(&self, mut params: AmendOrderParams) -> Result<String> {
                // 先获取当前交易所
                let exchange = match self {
                    $(
                        ExchangeRest::$name(_) => Exchange::$name,
                    )*
                    ExchangeRest::NotImplemented(exchange) => return Err(qerror!("交易所 {:?} 未实现", exchange)),
                };

                // 先应用订单处理器
                match process_order(&mut params.order, &OrderParams::default(), exchange).await {
                    Ok(_) => {}
                    Err(e) => {
                        return Err(e);
                    }
                }


                // 调用交易所API修改订单
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.amend_order_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn cancel_order_ext(&self, params: CancelOrderParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.cancel_order_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_ticker_ext(&self, params: GetTickerParams) -> Result<Ticker> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_ticker_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_tickers_ext(&self, params: GetTickersParams) -> Result<Vec<Ticker>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_tickers_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_mark_price_ext(&self, params: GetMarkPriceParams) -> Result<Vec<MarkPrice>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_mark_price_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_bbo_ticker_ext(&self, params: GetBboTickerParams) -> Result<BboTicker> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_bbo_ticker_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_bbo_tickers_ext(&self, params: GetBboTickersParams) -> Result<Vec<BboTicker>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_bbo_tickers_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_depth_ext(&self, params: GetDepthParams) -> Result<Depth> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_depth_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_instrument_ext(&self, params: GetInstrumentParams) -> Result<Instrument> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_instrument_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_instruments_ext(&self, params: GetInstrumentsParams) -> Result<Vec<Instrument>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_instruments_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_funding_rate(&self, symbol: Symbol) -> Result<Funding> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_funding_rate(symbol).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_funding_rate_ext(&self, params: GetFundingRateParams) -> Result<Funding> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_funding_rate_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_funding_rates_ext(&self, params: GetFundingRatesParams) -> Result<Vec<FundingHistory>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_funding_rates_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_funding_rates_history_ext(
                &self,
                params: GetFundingRateHistoryParams,
            ) -> Result<Vec<FundingHistory>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_funding_rates_history_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_klines_ext(&self, params: GetKlineParams) -> Result<Kline> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_klines_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_leverage_ext(&self, params: SetLeverageParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_leverage_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_fee_rate_ext(&self, params: GetFeeRateParams) -> Result<FeeRate> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_fee_rate_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_funding_fee_ext(&self, params: GetFundingFeeParams) -> Result<Vec<FundingFee>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_funding_fee_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_balance_ext(&self, params: GetBalanceParams) -> Result<Balance> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_balance_ext(params).await.map(stable_balance_non_zero),
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_balances_ext(&self, params: GetBalancesParams) -> Result<Vec<Balance>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => {
                            let balances = rest.get_balances_ext(params).await?;
                            let balances = balances.into_iter().map(stable_balance_non_zero).collect();
                            Ok(balances)
                        },
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_fee_discount_info_ext(&self, params: GetFeeDiscountInfoParams) -> Result<Option<Discount>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_fee_discount_info_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn is_fee_discount_enabled_ext(&self, params: IsFeeDiscountEnabledParams) -> Result<bool> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.is_fee_discount_enabled_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_fee_discount_enabled_ext(&self, params: SetFeeDiscountEnabledParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_fee_discount_enabled_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_max_leverage_ext(&self, params: GetMaxLeverageParams) -> Result<u8> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_max_leverage_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_deposit_address(&self, ccy: String, chain: Option<Chain>, amount: Option<f64>) -> Result<Vec<DepositAddress>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_deposit_address(ccy, chain, amount).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_deposit_address_ext(&self, params: GetDepositAddressParams) -> Result<Vec<DepositAddress>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_deposit_address_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn withdrawal(&self, withdrwl_params: WithDrawlParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.withdrawal(withdrwl_params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn withdrawal_ext(&self, params: WithdrawalParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.withdrawal_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_user_id(&self) -> Result<String> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => {
                            let user_id = rest.get_user_id().await?;
                            Ok(user_id.to_string())
                        },
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_user_id_ext(&self, params: GetUserIdParams) -> Result<String> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_user_id_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn transfer_ext(&self, params: TransferParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.transfer_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn sub_transfer(&self, sub_transfer: SubTransfer) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.sub_transfer(sub_transfer).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn sub_transfer_ext(&self, params: SubTransferParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.sub_transfer_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_max_position_ext(&self, params: GetMaxPositionParams) -> Result<MaxPosition> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_max_position_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_margin_mode_ext(&self, params: GetMarginModeParams) -> Result<MarginMode> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_margin_mode_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_margin_mode_ext(&self, params: SetMarginModeParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_margin_mode_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn post_order_ext(&self, mut params: PostOrderParams) -> Result<String> {
                // 先获取当前交易所
                let exchange = match self {
                    $(
                        ExchangeRest::$name(_) => Exchange::$name,
                    )*
                    ExchangeRest::NotImplemented(exchange) => return Err(qerror!("交易所 {:?} 未实现", exchange)),
                };

                // 先应用订单处理器
                match process_order(&mut params.order, &params.params, exchange).await {
                    Ok(_) => {}
                    Err(e) => {
                        return Err(e);
                    }
                }


                // 调用交易所API下单
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.post_order_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn post_batch_order_ext(&self, mut params: PostBatchOrderParams) -> Result<BatchOrderRsp> {
                // 获取当前交易所
                let exchange = match self {
                    $(
                        ExchangeRest::$name(_) => Exchange::$name,
                    )*
                    ExchangeRest::NotImplemented(exchange) => return Err(qerror!("交易所 {:?} 未实现", exchange)),
                };

                // 对每个订单应用处理

                for order in params.orders.iter_mut() {
                    match process_order(order, &params.params, exchange).await {
                        Ok(_) => {}
                        Err(e) => {
                            return Err(e);
                        }
                    }
                }


                // 调用交易所API批量下单
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.post_batch_order_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_order_by_id_ext(&self, params: GetOrderByIdParams) -> Result<Order> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_order_by_id_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_open_orders_ext(&self, params: GetOpenOrdersParams) -> Result<Vec<Order>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_open_orders_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_all_open_orders_ext(&self, params: GetAllOpenOrdersParams) -> Result<Vec<Order>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_all_open_orders_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_orders_ext(&self, params: GetOrdersParams) -> Result<Vec<Order>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_orders_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn batch_cancel_order_ext(&self, params: BatchCancelOrderParams) -> Result<BatchOrderRsp> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.batch_cancel_order_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn batch_cancel_order_by_ids_ext(&self, params: BatchCancelOrderByIdsParams) -> Result<BatchOrderRsp> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.batch_cancel_order_by_ids_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_position_ext(&self, params: GetPositionParams) -> Result<Vec<Position>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_position_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_positions_ext(&self, params: GetPositionsParams) -> Result<Vec<Position>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_positions_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn borrow_coin_ext(&self, params: BorrowCoinParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.borrow_coin_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_borrow_ext(&self, params: GetBorrowParams) -> Result<Vec<Borrow>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_borrow_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_borrow_rate_ext(&self, params: GetBorrowRateParams) -> Result<Vec<BorrowRate>> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_borrow_rate_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_borrow_limits_ext(&self, params: GetBorrowLimitsParams) -> Result<BorrowLimit> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_borrow_limits_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn repay_coin_ext(&self, params: RepayCoinParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.repay_coin_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_account_info_ext(&self, params: GetAccountInfoParams) -> Result<AccountInfo> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_account_info_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn get_account_mode(&self) -> Result<AccountMode> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.get_account_mode().await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_account_mode(&self, mode: AccountMode) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_account_mode(mode).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }

            async fn set_account_mode_ext(&self, params: SetAccountModeParams) -> Result<()> {
                match self {
                    $(
                        ExchangeRest::$name(rest) => rest.set_account_mode_ext(params).await,
                    )*
                    ExchangeRest::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 未实现", exchange)),
                }
            }
        }
    };
}
define_exchanges_rest!(
    BinanceSwap,
    BinanceSpot,
    BinanceMargin,
    BinanceFuture,
    GateSwap,
    GateSpot,
    GateMargin,
    CoinexSwap,
    CoinexSpot,
    BitgetSwap,
    BitgetSpot,
    BitgetMargin,
    OkxSwap,
    OkxSpot,
    OkxMargin,
    OkxSwapUsd,
    OkxFuture,
    HuobiSwap,
    HuobiSpot,
    KucoinSwap,
    KucoinSpot,
    KucoinMargin,
    BingxSwap,
    BingxSpot,
    BitmexSwap,
    BitmexSpot,
    CryptoSwap,
    CryptoSpot,
    KrakenSpot,
    CoinbaseSpot,
    CoinbaseSwap,
    BybitSwap,
    BybitSpot,
    DeepcoinSwap,
    DeepcoinSpot,
    CoinwSwap,
    CoinwSpot,
    // ApexSwap,
    MexcSpot,
    MexcSwap,
    DydxSwap,
    PhemexSwap,
    PhemexSpot,
    HyperLiquidSwap,
    HyperLiquidSpot,
    BitmartSwap,
    BitmartSpot,
    // ZoomexSwap,
    // ZoomexSpot,
    DeribitSwap,
    DeribitSpot,
    DeribitFuture,
    DeribitOption,
    WhitebitSwap,
    WhitebitSpot,
    XTSwap,
    XTSpot
);

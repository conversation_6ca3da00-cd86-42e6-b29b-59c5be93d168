use once_cell::sync::OnceCell;

use quant_common::{
    Error,
    base::{ContractType, QuoteCcy, Symbol},
};
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, <PERSON>ialEq, Eq, Hash, Default)]
pub struct BbSymbol {
    pub inner: Symbol,
    /// e.g. 1000 in 1000BONKUSDT
    pub prefix_mul: u32,
}

// 存储特殊标识的哈希表
pub static SPECIAL_SYMBOLS: OnceCell<FxHashMap<String, u32>> = OnceCell::new();

// 获取前缀乘数和格式
#[inline]
pub(crate) fn swap_prefix_mul_and_format_by_base(base_ccy: &str) -> u32 {
    SPECIAL_SYMBOLS
        .get()
        .unwrap()
        .get(base_ccy)
        .copied()
        .unwrap_or(1)
}

// 向后兼容的函数
#[inline]
pub(crate) fn swap_prefix_mul_by_base(base_ccy: &str) -> u32 {
    swap_prefix_mul_and_format_by_base(base_ccy)
}

impl BbSymbol {
    #[inline]
    pub fn new(v: Symbol) -> Self {
        let prefix_mul = swap_prefix_mul_and_format_by_base(&v.base);
        Self {
            inner: v,
            prefix_mul,
        }
    }
}

impl From<Symbol> for BbSymbol {
    fn from(value: Symbol) -> Self {
        BbSymbol::new(value)
    }
}

/// 不能解析 bitget 的模拟盘 USDT 否则 SATSUSDT 会错误解析成 SAT SUSDT
impl<'de> Deserialize<'de> for BbSymbol {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let symbol = String::deserialize(deserializer)?;
        let bytes = symbol.as_bytes();
        let len = bytes.len();

        let mut start = 0;
        let mut prefix_mul = 1u32;

        // 检查是否以数字开头
        if len > 2 && bytes[0].is_ascii_digit() {
            if bytes[0] == b'1' && len > 2 && bytes[1] == b'0' {
                // 处理纯数字前缀
                let mut i = 1;
                while i < len && bytes[i] == b'0' {
                    i += 1;
                }

                // 解析前缀数字
                let prefix = unsafe { std::str::from_utf8_unchecked(&bytes[0..i]) }
                    .parse::<u32>()
                    .unwrap_or(1);
                prefix_mul = prefix;
                start = i;
            } else {
                start = 0;
                prefix_mul = 1;
            }
        }

        let end = len;

        for quote_len in [4, 3] {
            let mid = end - quote_len;
            if mid <= start {
                continue; // 防止越界
            }

            let (base, quote) = unsafe {
                (
                    symbol.get_unchecked(start..mid),
                    symbol.get_unchecked(mid..end),
                )
            };

            let quote = serde_plain::from_str(quote);
            if let Ok(quote) = quote {
                // 构造结果
                return Ok(Self {
                    inner: Symbol {
                        base: base.to_string(),
                        quote,
                        contract_type: ContractType::Normal,
                    },
                    prefix_mul,
                });
            }

            // parse fail fallback to unknown quote
            if quote_len == 3 {
                return Ok(Self {
                    inner: Symbol {
                        base: base.to_string(),
                        quote: QuoteCcy::OTHER("".to_string()),
                        contract_type: ContractType::Normal,
                    },
                    prefix_mul,
                });
            }
        }
        unreachable!()
    }
}

impl std::fmt::Display for BbSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        if self.prefix_mul == 1 {
            write!(f, "{}{}", self.inner.base, self.inner.quote)
        } else {
            // 其他情况使用数字格式
            write!(
                f,
                "{}{}{}",
                self.prefix_mul, self.inner.base, self.inner.quote
            )
        }
    }
}

impl From<BbSymbol> for Symbol {
    fn from(value: BbSymbol) -> Self {
        value.inner
    }
}

impl std::str::FromStr for BbSymbol {
    type Err = Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(serde_plain::from_str(s)?)
    }
}

impl Serialize for BbSymbol {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        String::serialize(&self.to_string(), serializer)
    }
}

#[cfg(test)]
mod test {
    use std::str::FromStr;

    use super::*;

    #[test]
    fn test_deserialize() {
        let symbol = BbSymbol::from_str("1000PEPEUSDT").unwrap();
        assert_eq!(symbol.inner.base, "PEPE");
        assert_eq!(symbol.inner.quote, QuoteCcy::USDT);
        assert_eq!(symbol.prefix_mul, 1000);

        // USDC
        let symbol = BbSymbol::from_str("1000PEPEUSDC").unwrap();
        assert_eq!(symbol.inner.base, "PEPE");
        assert_eq!(symbol.inner.quote, QuoteCcy::USDC);
        assert_eq!(symbol.prefix_mul, 1000);

        // BTC
        let symbol = BbSymbol::from_str("BTCUSDT").unwrap();
        assert_eq!(symbol.inner.base, "BTC");
        assert_eq!(symbol.inner.quote, QuoteCcy::USDT);
        assert_eq!(symbol.prefix_mul, 1);
    }
}

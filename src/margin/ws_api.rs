use std::{
    collections::VecDeque,
    time::{Duration, Instant},
};

use async_channel::Receiver;
use fastwebsockets::{Frame, OpCode};
use quant_common::{
    base::{
        traits::ws_api::{AsyncResHandle, WebSocketAPI},
        AsyncCmd, ExConfig, Exchange, OrderId,
    },
    fast_proxy::connect_ws_with_proxy,
    qerror,
    utils::time,
    QuantError, Result,
};
use rustc_hash::FxHashMap;

use crate::spot::model::*;
use crate::spot::ws::generate_login;

const MAX_RETRY: usize = 10;
const REQUEST_TIMEOUT_SECS: u64 = 60; // 请求超时时间（秒）
const MAX_CACHE_SIZE: usize = 1000; // 最大缓存条目数
const PING_INTERVAL: u64 = 20; // 心跳间隔（秒）

#[derive(<PERSON><PERSON>, Debug)]
struct CachedRequest {
    cmd: AsyncCmd,
    timestamp: Instant,
}

#[derive(Clone, Debug)]
pub struct OkxMarginWsApi {
    url_private: String,
    config: ExConfig,
    req_cache: FxHashMap<u64, CachedRequest>,
    req_queue: VecDeque<u64>, // 按时间顺序存储请求ID，用于超时检查
}

impl OkxMarginWsApi {
    pub async fn new(config: ExConfig) -> Self {
        let default_host = if config.is_testnet {
            WS_TEST_URL
        } else if config
            .params
            .get("aws")
            .map(|v| v == "true")
            .unwrap_or(false)
        {
            WS_AWS_URL
        } else {
            WS_URL
        }
        .to_string();

        let host = config
            .ws_api_host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(default_host);

        let url_private = format!("wss://{host}/ws/v5/private");

        OkxMarginWsApi {
            url_private,
            config,
            req_cache: FxHashMap::default(),
            req_queue: VecDeque::with_capacity(MAX_CACHE_SIZE),
        }
    }

    fn cache_request_if_needed(&mut self, req_id: u64, cmd: &AsyncCmd) {
        match cmd {
            AsyncCmd::PlaceOrder(_)
            | AsyncCmd::AmendOrder(_)
            | AsyncCmd::CancelOrder(_)
            | AsyncCmd::BatchPlaceOrder(_)
            // | AsyncCmd::BatchCancelOrder(_)
            | AsyncCmd::BatchCancelOrderByIds(_) => {
                // 清理缓存，防止无限增长
                self.cleanup_cache();

                // 添加新请求到缓存
                self.req_cache.insert(
                    req_id,
                    CachedRequest {
                        cmd: cmd.clone(),
                        timestamp: Instant::now(),
                    },
                );
                self.req_queue.push_back(req_id);

                debug!(
                    "已缓存请求: req_id={}, cmd_type={}",
                    req_id,
                    self.get_cmd_type_str(cmd)
                );
            }
            _ => {}
        }
    }

    // 清理超时和过多的缓存条目
    fn cleanup_cache(&mut self) {
        // 检查并移除超时请求
        let now = Instant::now();
        let timeout = Duration::from_secs(REQUEST_TIMEOUT_SECS);

        // 从队列前端检查，移除超时的请求
        while let Some(&req_id) = self.req_queue.front() {
            if let Some(cached_req) = self.req_cache.get(&req_id) {
                if now.duration_since(cached_req.timestamp) > timeout {
                    // 该请求已超时，移除
                    self.req_queue.pop_front();
                    self.req_cache.remove(&req_id);
                    warn!("请求超时已自动清理: req_id={}", req_id);
                } else {
                    // 如果最早的请求没有超时，后面的更不会超时
                    break;
                }
            } else {
                // 队列中的ID不在缓存中，清理队列
                self.req_queue.pop_front();
            }
        }

        // 如果缓存大小超过上限，移除最早的请求
        while self.req_cache.len() > MAX_CACHE_SIZE {
            if let Some(req_id) = self.req_queue.pop_front() {
                self.req_cache.remove(&req_id);
                warn!("缓存已达到最大大小，移除最早的请求: req_id={}", req_id);
            } else {
                break;
            }
        }
    }

    // 获取命令类型的字符串表示，用于日志记录
    fn get_cmd_type_str(&self, cmd: &AsyncCmd) -> &'static str {
        match cmd {
            AsyncCmd::PlaceOrder(_) => "PlaceOrder",
            AsyncCmd::CancelOrder(_) => "CancelOrder",
            AsyncCmd::AmendOrder(_) => "AmendOrder",
            AsyncCmd::BatchPlaceOrder(_) => "BatchPlaceOrder",
            AsyncCmd::BatchCancelOrder(_) => "BatchCancelOrder",
            AsyncCmd::BatchCancelOrderByIds(_) => "BatchCancelOrderByIds",
        }
    }

    async fn handle_error<T>(
        &self,
        handler: &T,
        req_id: u64,
        err: QuantError,
        account_id: usize,
        cmd: AsyncCmd,
    ) -> Result<()>
    where
        T: AsyncResHandle,
    {
        match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                let result = quant_common::base::PlaceOrderResult {
                    result: Err(err),
                    order: place_cmd.order,
                };
                handler
                    .handle_post_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let result = quant_common::base::CancelOrderResult {
                    result: Err(err),
                    order_id: cancel_cmd.order_id,
                    symbol: cancel_cmd.symbol,
                };
                handler
                    .handle_cancel_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchPlaceOrder(_) => {
                handler
                    .handle_post_batch_order(account_id, req_id, Err(err))
                    .await?;
            }
            AsyncCmd::AmendOrder(order) => {
                let result = quant_common::base::AmendOrderResult {
                    result: Err(err),
                    order,
                };
                handler
                    .handle_amend_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchCancelOrder(_) => {
                handler
                    .handle_batch_cancel_order(account_id, req_id, Err(err))
                    .await?;
            }
            AsyncCmd::BatchCancelOrderByIds(_) => {
                handler
                    .handle_batch_cancel_order(account_id, req_id, Err(err))
                    .await?;
            }
        }

        Ok(())
    }

    // 处理响应
    async fn handle_response<T>(
        &self,
        handler: &T,
        account_id: usize,
        req_id: u64,
        rsp: WsApiRsp,
        cmd: AsyncCmd,
    ) -> Result<()>
    where
        T: AsyncResHandle,
    {
        match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                let order_rsp = rsp.into_common_order_rsp();
                let result = quant_common::base::PlaceOrderResult {
                    result: order_rsp,
                    order: place_cmd.order,
                };
                handler
                    .handle_post_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let cancel_rsp = rsp.into_common_order_rsp();
                let result = quant_common::base::CancelOrderResult {
                    result: cancel_rsp.map(|_| ()),
                    order_id: cancel_cmd.order_id,
                    symbol: cancel_cmd.symbol,
                };
                handler
                    .handle_cancel_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::AmendOrder(order) => {
                let amend_rsp = rsp.into_common_order_rsp();
                let result = quant_common::base::AmendOrderResult {
                    result: amend_rsp,
                    order,
                };
                handler
                    .handle_amend_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchPlaceOrder(_) => {
                let batch_rsp = rsp.into_batch_common_order_rsp();
                handler
                    .handle_post_batch_order(account_id, req_id, batch_rsp)
                    .await?;
            }
            AsyncCmd::BatchCancelOrderByIds(_) => {
                let batch_rsp = rsp.into_batch_common_order_rsp();
                handler
                    .handle_batch_cancel_order_by_ids(account_id, req_id, batch_rsp)
                    .await?;
            }

            cmd => {
                warn!(
                    "收到不支持的命令类型响应: req_id={}, cmd_type={:?}",
                    req_id,
                    self.get_cmd_type_str(&cmd)
                );
            }
        }

        Ok(())
    }
}

impl WebSocketAPI for OkxMarginWsApi {
    fn exchange(&self) -> Exchange {
        Exchange::OkxMargin
    }

    async fn run<H: AsyncResHandle>(
        mut self,
        account_id: usize,
        handler: H,
        mut rx: Receiver<(u64, AsyncCmd)>,
    ) -> quant_common::Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(account_id, &handler, &mut rx).await {
                Ok(_) => {
                    warn!("websocket断开连接，尝试重连");
                }
                Err(e) => {
                    error!("websocket连接错误: {:?}", e);
                    if time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            let err = qerror!("OkxMargin websocket重试失败次数过多: {e}");
                            return Err(err);
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl OkxMarginWsApi {
    async fn run_inner<H: AsyncResHandle>(
        &mut self,
        account_id: usize,
        handler: &H,
        rx: &mut Receiver<(u64, AsyncCmd)>,
    ) -> Result<()> {
        let mut ws = connect_ws_with_proxy(&self.url_private).await?;

        // 首先进行登录
        let login_message = self.login().await?;
        debug!("Sending login request...");
        ws.write_frame(Frame::text(login_message.as_bytes().into()))
            .await?;

        // 等待登录响应
        let login_rsp = ws.read_frame().await?;
        match login_rsp.opcode {
            OpCode::Text => {
                let rsp_str = String::from_utf8_lossy(&login_rsp.payload);
                debug!("Login response: {}", rsp_str);
                // 验证登录是否成功
                if rsp_str.contains("error") {
                    return Err(qerror!("Login failed: {}", rsp_str));
                }
            }
            _ => return Err(qerror!("Unexpected login response type")),
        }

        // 定期清理缓存的时间间隔（秒）
        let cleanup_interval = Duration::from_secs(REQUEST_TIMEOUT_SECS / 2);
        let mut last_cleanup = Instant::now();

        // 心跳机制：上次收到消息的时间
        let mut last_message_time = Instant::now();
        // 心跳间隔设置为20秒，小于官方建议的30秒
        let heartbeat_interval = Duration::from_secs(PING_INTERVAL);
        // 创建一个心跳超时检查的间隔，设置为5秒
        let mut heartbeat_check_interval =
            tokio::time::interval(tokio::time::Duration::from_secs(5));
        // 标记是否已发送ping而等待pong响应
        let mut ping_sent = false;

        loop {
            // 定期检查和清理过期请求
            if last_cleanup.elapsed() > cleanup_interval {
                self.cleanup_cache();
                last_cleanup = Instant::now();
            }

            tokio::select! {
                cmd = rx.recv() => {
                    debug!("收到请求");
                    let (req_id, cmd) = match cmd {
                        Ok((req_id, cmd)) => (req_id, cmd),
                        Err(e) => {
                            warn!("异步通道已关闭: {e:?}");
                            break;
                        }
                    };

                    // 缓存需要的请求类型（下单、修改订单、撤单）
                    self.cache_request_if_needed(req_id, &cmd);

                    let message = match self.req_to_message(req_id, cmd).await {
                        Ok(message) => message,
                        Err(e) => {
                            error!("发送订单请求错误: {:?}", e);
                            // 出错时移除并获取缓存的命令
                            if let Some(cached_req) = self.req_cache.remove(&req_id) {
                                // 从队列中也移除该请求ID
                                let index = self.req_queue.iter().position(|&id| id == req_id);
                                if let Some(index) = index {
                                    self.req_queue.remove(index);
                                }
                                self.handle_error(handler, req_id, e, account_id, cached_req.cmd).await?;
                            }
                            continue;
                        }
                    };

                    match ws.write_frame(message).await {
                        Ok(()) => {}
                        Err(e) => {
                            error!("发送订单请求错误: {:?}", e);
                            let err = qerror!("发送订单请求错误: {e}");
                            // 出错时移除并获取缓存的命令
                            if let Some(cached_req) = self.req_cache.remove(&req_id) {
                                // 从队列中也移除该请求ID
                                let index = self.req_queue.iter().position(|&id| id == req_id);
                                if let Some(index) = index {
                                    self.req_queue.remove(index);
                                }
                                self.handle_error(handler, req_id, err, account_id, cached_req.cmd).await?;
                            }
                            return Err(e.into());
                        }
                    }
                }
                rsp = ws.read_frame() => {
                    // 更新最后收到消息的时间
                    last_message_time = Instant::now();
                    ping_sent = false;

                    let rsp = rsp?;
                    let rsp: WsApiRsp = match rsp.opcode {
                        OpCode::Text => {
                            let rsp_str = String::from_utf8_lossy(&rsp.payload);
                            debug!("收到响应: {}", rsp_str);
                            // 检查是否收到了pong字符串
                            if rsp_str.trim() == "pong" {
                                debug!("收到pong响应");
                                continue;
                            }

                            match sonic_rs::from_slice(&rsp.payload) {
                                Ok(r) => r,
                                Err(e) => {
                                    // 如果不是有效的JSON，但是是pong响应，则继续
                                    let rsp_str = String::from_utf8_lossy(&rsp.payload);
                                    if rsp_str.contains("pong") {
                                        debug!("收到非标准pong响应: {}", rsp_str);
                                        continue;
                                    }
                                    error!("解析响应JSON失败: {:?}, 原始数据: {:?}", e, rsp_str);
                                    continue;
                                }
                            }
                        }
                        OpCode::Binary => {
                            debug!("收到二进制响应: {:?}", rsp.payload);
                            match sonic_rs::from_slice(&rsp.payload) {
                                Ok(r) => r,
                                Err(e) => {
                                    error!("解析二进制响应失败: {:?}", e);
                                    continue;
                                }
                            }
                        }
                        OpCode::Ping => {
                            ws.write_frame(Frame::pong(rsp.payload)).await?;
                            continue;
                        }
                        OpCode::Pong => continue,
                        OpCode::Close => {
                            warn!("websocket断开连接，尝试重连");
                            break;
                        }
                        _ => continue,
                    };

                    let req_id = rsp.id();

                    // 直接从缓存中移除并获取对应的请求，一步到位
                    if let Some(cached_req) = self.req_cache.remove(&req_id) {
                        // 从队列中也移除该请求ID
                        let index = self.req_queue.iter().position(|&id| id == req_id);
                        if let Some(index) = index {
                            self.req_queue.remove(index);
                        }

                        let cmd_type = self.get_cmd_type_str(&cached_req.cmd);
                        debug!("处理响应: req_id={}, cmd_type={}", req_id, cmd_type);

                        // 使用辅助方法处理响应
                        self.handle_response(handler, account_id, req_id, rsp, cached_req.cmd).await?;
                    } else {
                        warn!("收到未知请求ID的响应: {}", req_id);
                    }
                }
                _ = heartbeat_check_interval.tick() => {
                    // 检查是否需要发送心跳
                    let elapsed = last_message_time.elapsed();

                    // 如果距离上次消息已经过了心跳间隔时间，并且还没有发送过ping
                    if elapsed >= heartbeat_interval && !ping_sent {
                        debug!("发送心跳ping消息");

                        // 发送ping消息
                        match ws.write_frame(Frame::text(b"ping".to_vec().into())).await {
                            Ok(_) => {
                                ping_sent = true;
                                // 发送ping后重置last_message_time时间戳，避免在等待pong时过早超时
                                last_message_time = Instant::now();
                            }
                            Err(e) => {
                                error!("发送ping消息失败: {:?}", e);
                                return Err(e.into());
                            }
                        }
                    }
                    // 如果发送ping后超过30秒还没有收到响应，则认为连接已断开
                    // 这里使用ping_sent作为发送了ping消息的标志
                    else if ping_sent && last_message_time.elapsed() >= Duration::from_secs(30) {
                        warn!("心跳超时，未收到pong响应，尝试重连");
                        break;
                    }
                }
            }
        }
        Ok(())
    }

    async fn login(&mut self) -> Result<String> {
        let api_key = self.config.key.clone();
        let secret_key = self.config.secret.clone();
        let passphrase = self.config.passphrase.clone();
        let login = generate_login(&api_key, &secret_key, &passphrase);
        let req_str = sonic_rs::to_string(&login)?;
        Ok(req_str)
    }

    async fn req_to_message(&self, req_id: u64, cmd: AsyncCmd) -> Result<Frame> {
        let req = match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                WsApiReq::post_order(req_id, place_cmd.order, place_cmd.params, true)?
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let (order_id, client_id) = match cancel_cmd.order_id {
                    OrderId::Id(id) => (Some(id), None),
                    OrderId::ClientOrderId(cid) => (None, Some(cid)),
                };
                WsApiReq::cancel_order(req_id, cancel_cmd.symbol, order_id, client_id)?
            }
            AsyncCmd::AmendOrder(order) => WsApiReq::amend_order(req_id, order)?,
            AsyncCmd::BatchPlaceOrder(batch_cmd) => {
                WsApiReq::batch_post_order(req_id, batch_cmd.orders, batch_cmd.params, true)?
            }
            AsyncCmd::BatchCancelOrder(_) => return Err(qerror!("交易所未支持批量取消订单")),
            AsyncCmd::BatchCancelOrderByIds(batch_cmd) => WsApiReq::batch_cancel_order_by_ids(
                req_id,
                batch_cmd.symbol,
                batch_cmd.ids,
                batch_cmd.cids,
            )?,
        };

        debug!("发送请求: {:?}", req);
        Ok(Frame::text(serde_json::to_vec(&req)?.into()))
    }
}

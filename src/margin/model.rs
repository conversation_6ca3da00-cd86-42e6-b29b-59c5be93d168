use serde::{Deserialize, Serialize};

use quant_common::Result;
use quant_common::base::{OrderParams, model::*};
use quant_common::utils::de_from_str;

use crate::spot::model::{
    BnOrderSide, BnOrderType, BnSymbol, BnTimeInForce, DEFAULT_RECV_WINDOW, asset_prefix,
    bn_asset_prefix,
};

pub(crate) const EXCHANGE: &str = "BinanceMargin";

/// private api
pub(crate) const PATH_ACCOUNT: &str = "/sapi/v1/margin/account";
pub(crate) const PATH_ALL_ORDERS: &str = "/sapi/v1/margin/allOrders";
pub(crate) const PATH_BORROW_REPAY: &str = "/sapi/v1/margin/borrow-repay";
pub(crate) const PATH_LISTEN_KEY: &str = "/sapi/v1/userDataStream";
pub(crate) const PATH_OPEN_ORDERS: &str = "/sapi/v1/margin/openOrders";
pub(crate) const PATH_ORDER: &str = "/sapi/v1/margin/order";

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct NewOrderRequest {
    symbol: BnSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    is_isolated: Option<String>, // 是否逐仓杠杆，"TRUE", "FALSE", 默认 "FALSE"
    side: BnOrderSide,
    #[serde(rename = "type")]
    order_type: BnOrderType,
    #[serde(skip_serializing_if = "Option::is_none")]
    time_in_force: Option<BnTimeInForce>,
    #[serde(skip_serializing_if = "Option::is_none")]
    quantity: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    price: Option<String>,
    recv_window: u32,
    #[serde(skip_serializing_if = "Option::is_none")]
    new_client_order_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    side_effect_type: Option<String>, // NO_SIDE_EFFECT, MARGIN_BUY, AUTO_REPAY,AUTO_BORROW_REPAY;默认为 NO_SIDE_EFFECT
}

impl NewOrderRequest {
    pub(crate) fn new(order: Order, params: &OrderParams) -> Result<Self> {
        let symbol: BnSymbol = order.symbol.clone().into();
        let order_type = BnOrderType::with_params(&order.order_type, &order.time_in_force, params)?;

        let time_in_force = match (&order.order_type, order.time_in_force) {
            (OrderType::Market, _) => None,
            (OrderType::Limit, TimeInForce::PostOnly) => None,
            (OrderType::Limit, time_in_force) => Some(time_in_force.into()),
        };

        let price = match order.order_type {
            OrderType::Limit => order.price.map(|p| p.to_string()),
            OrderType::Market => None,
        };

        Ok(Self {
            symbol,
            side: order.side.into(),
            order_type,
            price,
            quantity: order.amount.map(|a| a.to_string()),
            time_in_force,
            recv_window: DEFAULT_RECV_WINDOW,
            new_client_order_id: order.cid,
            is_isolated: None,
            side_effect_type: None,
        })
    }
}

/// 账户余额
#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AccountBalance {
    asset: String,
    #[serde(deserialize_with = "de_from_str")]
    free: f64,
    #[serde(deserialize_with = "de_from_str")]
    locked: f64,
    #[serde(deserialize_with = "de_from_str")]
    borrowed: f64,
    #[serde(deserialize_with = "de_from_str")]
    interest: f64,
}

impl From<AccountBalance> for Balance {
    fn from(v: AccountBalance) -> Self {
        let (prefix, asset) = bn_asset_prefix(&v.asset);
        let prefix = prefix.prefix() as f64;
        Self {
            asset,
            balance: (v.free + v.locked) * prefix,
            available_balance: v.free * prefix,
            unrealized_pnl: 0f64,
        }
    }
}

impl From<AccountBalance> for Borrow {
    fn from(v: AccountBalance) -> Self {
        let (prefix, coin) = bn_asset_prefix(&v.asset);
        let prefix = prefix.prefix() as f64;
        let amount = (v.borrowed + v.interest) * prefix;
        Self { coin, amount }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Account {
    pub(crate) user_assets: Vec<AccountBalance>,
}

impl Account {
    pub(crate) fn get_borrow(self, coin: Option<String>) -> Vec<Borrow> {
        match coin {
            Some(coin) => {
                let (_, coin) = asset_prefix(&coin);
                self.user_assets
                    .into_iter()
                    .filter_map(|v| match v.asset == coin {
                        true => Some(v.into()),
                        false => None,
                    })
                    .collect()
            }
            None => self
                .user_assets
                .into_iter()
                .filter_map(|v| match v.borrowed {
                    0.0 => None,
                    _ => Some(v.into()),
                })
                .collect(),
        }
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BorrowRepayType {
    Borrow,
    Repay,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BorrowRepayReq {
    asset: String,
    // is_isolated: String, // 是否逐仓杠杆，TRUE, FALSE, 默认 FALSE
    // symbol: BnSymbol,
    amount: String,
    #[serde(rename = "type")]
    borrow_repay_type: BorrowRepayType,
}

impl BorrowRepayReq {
    pub(crate) fn borrow(asset: String, amount: f64) -> Self {
        let (prefix, asset) = asset_prefix(&asset);
        let prefix = prefix.prefix() as f64;
        Self {
            asset,
            amount: (amount / prefix).to_string(),
            borrow_repay_type: BorrowRepayType::Borrow,
        }
    }

    pub(crate) fn repay(asset: String, amount: f64) -> Self {
        let (prefix, asset) = asset_prefix(&asset);
        let prefix = prefix.prefix() as f64;
        Self {
            asset,
            amount: (amount / prefix).to_string(),
            borrow_repay_type: BorrowRepayType::Repay,
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BorrowRepayRsp {
    // tran_id: i64,
}

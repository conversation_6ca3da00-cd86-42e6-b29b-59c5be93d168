use quant_common::{
    base::{<PERSON><PERSON>, <PERSON><PERSON>},
    utils::de_from_str,
};
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};

pub(crate) const EXCHANGE: &str = "GateMargin";

// pub(crate) const PATH_MARGIN_ACCOUNT: &str = "/api/v4/margin/accounts";
pub(crate) const PATH_MARGIN_CROSS_ACCOUNT: &str = "/api/v4/margin/cross/accounts";
pub(crate) const PATH_UNI_LOANS: &str = "/api/v4/unified/loans/";

// #[derive(Deserialize, Debug)]
// pub(crate) struct GateMarginBase {
//     currency: String,
//     #[serde(deserialize_with = "de_from_str")]
//     available: f64,
//     #[serde(deserialize_with = "de_from_str")]
//     locked: f64,
//     #[serde(deserialize_with = "de_from_str")]
//     borrowed: f64,
//     #[serde(deserialize_with = "de_from_str")]
//     interest: f64,
// }

// #[derive(Deserialize, Debug)]
// pub(crate) struct GateMarginBalance {
//     currency_pair: String,
//     base: GateMarginBase,
//     quote: GateMarginBase,
// }

#[derive(Deserialize, Debug)]
pub(crate) struct CrossMarginBalance {
    #[serde(deserialize_with = "de_from_str")]
    available: f64,
    #[serde(deserialize_with = "de_from_str")]
    freeze: f64,
    // "borrowed": "0",
    // "interest": "0",
    // "negative_liab": "0",
    // "futures_pos_liab": "0",
    // "equity": "12016.1",
    // "total_freeze": "0",
    // "total_liab": "0"
}

impl From<CrossMarginBalance> for Balance {
    fn from(value: CrossMarginBalance) -> Self {
        Self {
            asset: "".to_string(),
            balance: value.available + value.freeze,
            available_balance: value.available,
            unrealized_pnl: 0.,
        }
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct MarginCrossBalanceRsq {
    balances: FxHashMap<String, CrossMarginBalance>,
}

impl From<MarginCrossBalanceRsq> for Vec<Balance> {
    fn from(value: MarginCrossBalanceRsq) -> Self {
        value
            .balances
            .into_iter()
            .map(|(asset, balance)| {
                let balance: Balance = balance.into();
                Balance { asset, ..balance }
            })
            .collect()
    }
}

// #[derive(Debug, Serialize, Deserialize)]
// #[serde(rename_all = "lowercase")]
// pub(crate) enum BorrowType {
//     Platform,
//     Margin,
// }

#[derive(Debug, Serialize)]
#[serde(rename_all = "lowercase")]
pub(crate) struct GetBorrowReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    currency: Option<String>,
    // #[serde(skip_serializing_if = "Option::is_none")]
    // r#type: Option<BorrowType>,
}

impl GetBorrowReq {
    pub(crate) fn new(currency: Option<String>) -> Self {
        Self { currency }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub(crate) struct BorrowItem {
    currency: String,
    #[serde(deserialize_with = "de_from_str")]
    amount: f64,
    // r#type: BorrowType,
    // "currency_pair": "GT_USDT",
    // "change_time": 1673247054000,
    // "create_time": 1673247054000
}

impl From<BorrowItem> for Borrow {
    fn from(value: BorrowItem) -> Self {
        Self {
            coin: value.currency,
            amount: value.amount,
        }
    }
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "lowercase")]
pub(crate) enum PostLoansType {
    Borrow,
    Repay,
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "snake_case")]
pub(crate) struct PostLoansReq {
    currency: String,
    r#type: PostLoansType,
    amount: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    repaid_all: Option<bool>, // 全部还款，仅还款操作使用
    #[serde(skip_serializing_if = "Option::is_none")]
    text: Option<String>, // 用户自定义 ID
}

impl PostLoansReq {
    pub(crate) fn new_borrow(currency: String, amount: f64) -> Self {
        Self {
            currency,
            r#type: PostLoansType::Borrow,
            amount: amount.to_string(),
            repaid_all: None,
            text: None,
        }
    }

    pub(crate) fn new_repay(currency: String, amount: f64) -> Self {
        Self {
            currency,
            r#type: PostLoansType::Repay,
            amount: amount.to_string(),
            repaid_all: None,
            text: None,
        }
    }
}

// #[derive(Debug, Deserialize)]
// pub(crate) struct PostLoansRsq {}

// #[derive(Debug, Deserialize)]
// #[serde(rename_all = "snake_case")]
// pub(crate) struct MarginRatioRsp {
//     #[serde(deserialize_with = "de_from_str")]
//     total_maintenance_margin_rate: f64,
//     #[serde(deserialize_with = "de_from_str")]
//     unified_account_total: f64,
// }

// impl MarginRatioRsp {
//     pub(crate) fn get_margin_ratio(&self) -> f64 {
//         self.total_maintenance_margin_rate
//     }

//     pub(crate) fn get_uni_account_total(&self) -> f64 {
//         self.unified_account_total
//     }
// }

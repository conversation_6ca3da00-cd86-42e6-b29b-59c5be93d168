use quant_common::base::Borrow;
use serde::{Deserialize, Serialize};

pub const EXCHANGE: &str = "BybitMargin";

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbMarginOrderResult {
    pub order_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbMarginCoinOrderResult {
    pub collateral_amount: String,
    pub collateral_currency: String,
    #[serde(rename = "currentLTV")]
    pub current_ltv: String,
    pub expiration_time: String,
    pub hourly_interest_rate: String,
    pub loan_currency: String,
    pub loan_term: String,
    pub order_id: String,
    pub residual_interest: String,
    pub residual_penalty_interest: String,
    pub total_debt: String,
}

impl From<BbMarginCoinOrderResult> for Borrow {
    fn from(value: BbMarginCoinOrderResult) -> Self {
        Self {
            coin: value.collateral_currency,
            amount: value.collateral_amount.parse().unwrap(),
        }
    }
}

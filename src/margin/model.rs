use quant_common::{
    base::{BorrowLimit, BorrowRate, VipDetail},
    de_empty_str_to_default, de_from_str,
};
use serde::{Deserialize, Serialize};

pub(crate) const EXCHANGE: &str = "OkxMargin";
pub const PATH_BORROW_LIMIT: &str = "account/interest-limits";
pub const PATH_BORROW_RATE: &str = "account/interest-rate";

#[derive(Debug, Serialize)]
pub struct BorrowRateReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    ccy: Option<String>,
}

impl BorrowRateReq {
    pub fn new(coin: Option<String>) -> Self {
        Self { ccy: coin }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BorrowRateResp {
    #[serde(deserialize_with = "de_from_str")]
    interest_rate: f64,
    ccy: String,
}

impl From<BorrowRateResp> for BorrowRate {
    fn from(value: BorrowRateResp) -> Self {
        Self {
            ccy: value.ccy,
            rate: value.interest_rate,
        }
    }
}

#[derive(Debug, Serialize)]
pub struct BorrowLimitReq {
    r#type: String,
    ccy: String,
}

impl BorrowLimitReq {
    pub fn new(is_vip: Option<bool>, coin: String) -> Self {
        let r#type = if is_vip.unwrap_or(false) {
            "1".to_string()
        } else {
            "2".to_string()
        };
        Self { r#type, ccy: coin }
    }
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BorrowLimitResp {
    #[serde(deserialize_with = "de_empty_str_to_default")]
    debt: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    interest: f64,
    records: Vec<BorrowLimitRecord>,
}

impl From<BorrowLimitResp> for BorrowLimit {
    fn from(value: BorrowLimitResp) -> Self {
        let record = match value.records.first() {
            Some(record) => record,
            None => {
                return Self {
                    debt: value.debt,
                    interest: value.interest,
                    coin: String::new(),
                    rate: 0.0,
                    vip_detail: None,
                    borrow_limit: 0.0,
                }
            }
        };

        let vip_detail =
            if record.pos_loan != 0.0 || record.avail_loan != 0.0 || record.used_loan != 0.0 {
                Some(VipDetail {
                    pos_loan: record.pos_loan,
                    available_loan: record.avail_loan,
                    used_loan: record.used_loan,
                })
            } else {
                None
            };

        let borrow_limit = record.loan_quota.min(record.surplus_lmt);

        Self {
            debt: value.debt,
            interest: value.interest,
            coin: record.ccy.clone(),
            rate: record.rate,
            vip_detail,
            borrow_limit,
        }
    }
}

#[derive(Clone, Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BorrowLimitRecord {
    ccy: String,
    #[serde(deserialize_with = "de_from_str")]
    rate: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    loan_quota: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    surplus_lmt: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pos_loan: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    avail_loan: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    used_loan: f64,
}

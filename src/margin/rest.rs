use quant_common::base::*;
use quant_common::*;

use reqwest::Method;
use sonic_rs::Value;

use crate::margin::model::*;
use crate::spot::model::{GateAccount, GateSpotOrderReq};
use crate::spot::rest::GateSpot;

#[derive(Debug, Clone)]
pub struct GateMargin {
    pub client: reqwest::Client,
    pub config: ExConfig,
    spot: GateSpot,
    margin_mode: Option<MarginMode>,
}

impl GateMargin {
    pub async fn new(config: base::model::ExConfig) -> Self {
        let margin_mode = config
            .params
            .get("margin_mode")
            .map(|m| serde_plain::from_str(m).unwrap())
            .unwrap_or(MarginMode::Cross);
        let client = new_reqwest_client();
        let spot = GateSpot::new(ExConfig {
            exchange: Exchange::GateSpot,
            ..config.clone()
        })
        .await;

        Self {
            config,
            margin_mode: Some(margin_mode),
            client,
            spot,
        }
    }
}

impl Rest for GateMargin {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, user_request: UserRequest) -> Result<Value> {
        self.spot.request(user_request).await
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        self.spot.get_ticker(symbol).await
    }

    async fn get_tickers(&self) -> Result<Vec<base::model::Ticker>> {
        unimplemented!("{} 不支持所有ticker", self.name())
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<base::model::BboTicker> {
        self.spot.get_bbo_ticker(symbol).await
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<base::model::BboTicker>> {
        unimplemented!("{} does not support getting all BBO tickers", self.name())
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        self.spot.get_depth(symbol, limit).await
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        self.spot.get_instrument(symbol).await
    }

    async fn get_instruments(&self) -> Result<Vec<base::model::Instrument>> {
        self.spot.get_instruments().await
    }

    async fn get_funding_rates(&self) -> Result<Vec<base::model::Funding>> {
        unimplemented!("{} 没有资金费率接口", self.name())
    }

    //amount: 交易数量
    // type为limit时，指交易货币，即需要交易的货币，如BTC_USDT中指BTC。
    // type为market时，根据买卖不同指代不同
    // side : buy 指代计价货币，BTC_USDT中指USDT
    // side : sell 指代交易货币，BTC_USDT中指BTC
    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        let params = OrderParams {
            margin_mode: self.margin_mode.clone(),
            ..params
        };
        self.spot.post_order(order, params).await
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let params = OrderParams {
            margin_mode: self.margin_mode.clone(),
            ..params
        };
        self.spot.post_batch_order(orders, params).await
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        let oid = order.id.clone();
        let account = GateAccount::new(self.config.is_unified, &self.margin_mode);
        let req = GateSpotOrderReq::new(order, account)?;
        let rsp = self.spot.amend_gate_order(oid, req).await?;
        Ok(rsp.id.to_string())
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        self.spot
            .open_orders(GateAccount::new(self.config.is_unified, &self.margin_mode))
            .await
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        self.spot.get_open_orders(symbol).await
    }

    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        self.spot.get_orders(symbol, start_time, end_time).await
    }

    async fn cancel_order(
        &self,
        symbol: base::model::Symbol,
        order_id: base::model::OrderId,
    ) -> Result<()> {
        self.spot.cancel_order(symbol, order_id).await
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
        self.spot.batch_cancel_order(symbol).await
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        self.spot.get_fee_rate(symbol).await
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        if self.config.is_unified {
            return self.spot.unified_balances().await;
        }

        match self.margin_mode {
            Some(MarginMode::Isolated) => unimplemented!("目前不支持 {} 逐仓模型", self.name()),
            Some(MarginMode::Cross) => (),
            None => unreachable!(),
        }

        let rsp: MarginCrossBalanceRsq =
            self.spot.get_signed(PATH_MARGIN_CROSS_ACCOUNT, "").await?;
        Ok(rsp.into())
    }

    async fn get_position(
        &self,
        _symbol: base::model::Symbol,
    ) -> Result<Vec<base::model::Position>> {
        unimplemented!("{} does not support get_position", self.name())
    }

    async fn get_positions(&self) -> Result<Vec<base::model::Position>> {
        unimplemented!("{} does not support get_positions", self.name())
    }

    async fn set_leverage(&self, _symbol: base::model::Symbol, _leverage: u8) -> Result<()> {
        unimplemented!("{} does not support set_leverage", self.name())
    }

    async fn is_dual_side(&self) -> Result<bool> {
        unimplemented!("{} does not support dual_side", self.name())
    }

    async fn set_dual_side(&self, _is_dual_side: bool) -> Result<()> {
        unimplemented!("{} does not support set_dual_side", self.name())
    }

    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        self.spot.batch_cancel_order_by_ids(symbol, ids, cids).await
    }

    // async fn get_margin_ratio(&self) -> Result<f64> {
    //     let rsp: MarginRatioRsp = self.spot.get_signed(PATH_UNI_ACCOUNT, "").await?;
    //     Ok(rsp.get_margin_ratio())
    // }

    // async fn get_uni_account_total(&self) -> Result<f64> {
    //     let rsp: MarginRatioRsp = self.spot.get_signed(PATH_UNI_ACCOUNT, "").await?;
    //     Ok(rsp.get_uni_account_total())
    // }

    async fn get_borrow(&self, coin: Option<String>) -> Result<Vec<Borrow>> {
        let req = GetBorrowReq::new(coin);
        let req = serde_urlencoded::to_string(&req)?;
        let rsp: Vec<BorrowItem> = self.spot.get_signed(PATH_UNI_LOANS, &req).await?;
        Ok(rsp.into_iter().map(|item| item.into()).collect())
    }

    async fn borrow_coin(&self, coin: String, amount: f64) -> Result<()> {
        self.uni_loans(PostLoansReq::new_borrow(coin, amount)).await
    }

    async fn repay_coin(&self, coin: String, amount: f64) -> Result<()> {
        self.uni_loans(PostLoansReq::new_repay(coin, amount)).await
    }

    async fn get_account_info(&self) -> Result<AccountInfo> {
        self.spot.get_account_info().await
    }
}

impl GateMargin {
    async fn uni_loans(&self, req: PostLoansReq) -> Result<()> {
        let req = sonic_rs::to_string(&req)?;
        self.spot
            .req_bytes(&Method::POST, PATH_UNI_LOANS, "", &req, true)
            .await?;
        Ok(())
    }
}

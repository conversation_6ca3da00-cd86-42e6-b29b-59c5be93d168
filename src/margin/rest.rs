use quant_common::base::model::*;
use quant_common::base::traits::rest::Rest;
use quant_common::*;

use sonic_rs::Value;

use crate::{
    margin::model::*,
    spot::{
        model::{OkxInstType, OkxOrdersReq, OkxPostOrderReq},
        rest::OkxSpot,
    },
    swap::{
        model::{InstType, OkxPositionReq},
        rest::OkxSwap,
    },
};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct OkxMargin {
    spot: OkxSpot,
    swap: OkxSwap,
}

impl OkxMargin {
    pub async fn new(config: ExConfig) -> Self {
        let spot = OkxSpot::new(ExConfig {
            exchange: Exchange::OkxSpot,
            ..config.clone()
        })
        .await;

        let swap = OkxSwap::new(ExConfig {
            exchange: Exchange::OkxSwap,
            ..config
        })
        .await;

        Self { spot, swap }
    }
}

impl Rest for OkxMargin {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, user_request: UserRequest) -> Result<Value> {
        self.spot.request(user_request).await
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        self.spot.get_ticker(symbol).await
    }

    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        self.spot.get_tickers().await
    }

    async fn get_mark_price(&self, symbol: Option<Symbol>) -> Result<Vec<MarkPrice>> {
        self.spot.get_mark_price(symbol).await
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        self.spot.get_bbo_ticker(symbol).await
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        self.spot.get_bbo_tickers().await
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        self.spot.get_depth(symbol, limit).await
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        self.spot.get_instrument(symbol).await
    }

    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        self.spot.get_instruments().await
    }

    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        unimplemented!("{} funding rates is not supported", self.name())
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        let req = OkxOrdersReq::open_orders(OkxInstType::Margin, symbol);
        self.spot.orders(req).await
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        let req = OkxOrdersReq::all_open_orders(OkxInstType::Margin);
        self.spot.orders(req).await
    }

    async fn get_orders(&self, symbol: Symbol, start: i64, end: i64) -> Result<Vec<Order>> {
        let req = OkxOrdersReq::history_orders(OkxInstType::Margin, symbol, start, end);
        self.spot.history_orders(req).await
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        let req = OkxPostOrderReq::new(order, params, true)?;
        self.spot.post_order_org(req).await
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let mut req = vec![];
        for order in orders {
            let order = OkxPostOrderReq::new(order, params.clone(), true)?;
            req.push(order);
        }
        self.spot.post_batch_order_org(req).await
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        self.spot.amend_order(order).await
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        self.spot.cancel_order(symbol, order_id).await
    }

    async fn batch_cancel_order(&self, _symbol: Symbol) -> Result<BatchOrderRsp> {
        unimplemented!("{} batch cancel order is not supported.Because it needs OrderId which is not supplied by trait", self.name());
    }

    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        self.spot.batch_cancel_order_by_ids(symbol, ids, cids).await
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        self.spot.get_fee_rate(symbol).await
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        self.spot.get_balances().await
    }

    async fn get_position(&self, symbol: Symbol) -> Result<Vec<Position>> {
        let req = OkxPositionReq::new(InstType::Margin, Some(symbol));
        self.swap.positions(req).await
    }

    async fn get_positions(&self) -> Result<Vec<Position>> {
        let req = OkxPositionReq::new(InstType::Margin, None);
        self.swap.positions(req).await
    }

    async fn set_leverage(&self, symbol: Symbol, leverage: u8) -> Result<()> {
        self.spot.set_leverage(symbol, leverage).await
    }

    async fn is_dual_side(&self) -> Result<bool> {
        self.spot.is_dual_side().await
    }

    async fn set_dual_side(&self, is_dual_side: bool) -> Result<()> {
        self.spot.set_dual_side(is_dual_side).await
    }

    async fn get_user_id(&self) -> Result<String> {
        self.spot.get_user_id().await
    }

    async fn get_deposit_address(
        &self,
        ccy: String,
        chain: Option<Chain>,
        amount: Option<f64>,
    ) -> Result<Vec<DepositAddress>> {
        self.spot.get_deposit_address(ccy, chain, amount).await
    }

    async fn withdrawal(&self, withdrwl_params: WithDrawlParams) -> Result<()> {
        self.spot.withdrawal(withdrwl_params).await
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        self.spot.get_order_by_id(symbol, order_id).await
    }

    async fn borrow_coin(&self, _coin: String, _amount: f64) -> Result<()> {
        todo!(
            "{} 当前急需的是`组合保证金模式`，而不是目前实现的`现货和合约模式`",
            self.name()
        );
    }

    async fn get_borrow(&self, _coin: Option<String>) -> Result<Vec<Borrow>> {
        todo!(
            "{} 当前急需的是`组合保证金模式`，而不是目前实现的`现货和合约模式`",
            self.name()
        );
    }

    async fn get_borrow_rate(&self, coin: Option<String>) -> Result<Vec<BorrowRate>> {
        let req = BorrowRateReq::new(coin);
        let res = self.spot.get(PATH_BORROW_RATE, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<BorrowRateResp>>(&res)?;
        let borrow_rate: Vec<BorrowRate> = resp.into_iter().map(|v| v.into()).collect();
        Ok(borrow_rate)
    }

    async fn get_borrow_limits(&self, is_vip: Option<bool>, coin: String) -> Result<BorrowLimit> {
        let req = BorrowLimitReq::new(is_vip, coin);
        let res = self.spot.get(PATH_BORROW_LIMIT, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<BorrowLimitResp>>(&res)?;
        resp.first()
            .cloned()
            .ok_or_else(|| qerror!("Empty borrow limit response"))
            .map(|limit| limit.into())
    }

    async fn repay_coin(&self, _coin: String, _amount: f64) -> Result<()> {
        todo!(
            "{} 当前急需的是`组合保证金模式`，而不是目前实现的`现货和合约模式`",
            self.name()
        );
    }

    // async fn get_margin_ratio(&self) -> Result<f64> {
    //     warn!(
    //         "按{}文档实现，但全局保证金率总是返回空，只在对应币种中有保证金率",
    //         self.name()
    //     );
    //     let balances: Vec<OkxBalancesResp> = self.spot.account_balance().await?;
    //     match balances.into_iter().next() {
    //         Some(balance) => Ok(balance.get_margin_ratio()),
    //         None => Err(qerror!("{} 返回了空数组", self.name())),
    //     }
    // }

    async fn get_account_info(&self) -> Result<AccountInfo> {
        let rsp = self.spot.account_balance().await?;
        let account_info = rsp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No account info"))?;
        Ok(account_info.into())
    }
}

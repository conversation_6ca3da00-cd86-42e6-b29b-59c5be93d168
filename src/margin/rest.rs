use quant_common::base::traits::GetKlineParams;
use quant_common::base::{model::*, traits::rest::Rest};
use quant_common::{QuantError, Result, qerror};
use reqwest::Method;
use serde::{Serialize, de::DeserializeOwned};
use sonic_rs::Value;

use crate::margin::model::*;
use crate::spot::model::{
    BnOrder, CancelOpenOrder, GetOrderReq, OpenOrdersReq, OrderResponse, RspListenKey, SymbolArg,
    SymbolOrderId, TrivialResponse,
};
use crate::spot::rest::BinanceSpot;
use crate::util::BnOrderItemRsp;

#[derive(Clone)]
pub struct BinanceMargin {
    spot: BinanceSpot,
}

impl BinanceMargin {
    pub async fn new(config: ExConfig) -> Self {
        let spot = config.clone();

        let spot = BinanceSpot::new(spot).await;
        Self { spot }
    }

    #[inline(always)]
    pub async fn get<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::GET.to_string(),
            path: path.to_string(),
            auth: false,
            query: Some(query),
            ..Default::default()
        };
        let value = self.request(user_req).await?;
        let rsp: T = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }

    #[inline(always)]
    pub async fn get_signed<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::GET.to_string(),
            path: path.to_string(),
            auth: true,
            query: Some(query),
            ..Default::default()
        };
        let value = self.request(user_req).await?;
        let rsp: T = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }

    #[inline(always)]
    pub async fn post<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::POST.to_string(),
            path: path.to_string(),
            auth: true,
            query: Some(query),
            ..Default::default()
        };
        let value = self.request(user_req).await?;

        match sonic_rs::from_value(&value) {
            Ok(rsp) => Ok(rsp),
            Err(err) => Err(qerror!("{err}, value: {value:?}")),
        }
    }

    #[inline(always)]
    pub async fn delete<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        let query = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::DELETE.to_string(),
            path: path.to_string(),
            auth: true,
            query: Some(query),
            ..Default::default()
        };
        let value = self.request(user_req).await?;
        let rsp: T = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }

    #[inline]
    pub async fn create_listen_key(&self) -> Result<String> {
        let path = PATH_LISTEN_KEY;
        let r = self.spot.listen_key_req(Method::POST, path, None).await?;
        let r: RspListenKey = sonic_rs::from_str(&r)?;
        Ok(r.listen_key)
    }

    #[inline]
    pub async fn refresh_listen_key(&self, listen_key: &str) -> Result<()> {
        let key = Some(listen_key);
        let path = PATH_LISTEN_KEY;
        let _ = self.spot.listen_key_req(Method::PUT, path, key).await?;
        Ok(())
    }

    #[inline]
    pub(crate) async fn get_account(&self) -> Result<Account> {
        self.get_signed(PATH_ACCOUNT, &()).await
    }
}

impl Rest for BinanceMargin {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        self.spot.get_ticker(symbol).await
    }

    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        self.spot.get_tickers().await
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        self.spot.get_bbo_ticker(symbol).await
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        self.spot.get_bbo_tickers().await
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        self.spot.get_depth(symbol, limit).await
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        self.spot.get_instrument(symbol).await
    }

    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        self.spot.get_instruments().await
    }

    async fn get_klines_ext(&self, params: GetKlineParams) -> Result<Kline> {
        self.spot.get_klines_ext(params).await
    }

    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        Err(QuantError::api_not_implemented(format!(
            "{} 没有提供 get_funding_rates 接口",
            self.name()
        )))
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        let req = NewOrderRequest::new(order, &params)?;
        let rsp: OrderResponse = self.post(PATH_ORDER, &req).await?;
        Ok(rsp.order_id.to_string())
    }

    async fn post_batch_order(
        &self,
        _orders: Vec<Order>,
        _params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let err = format!("{} 没有原生批量下单接口", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let req = GetOrderReq::new(symbol, Some(order_id))?;
        let rsp: BnOrder = self.get_signed(PATH_ORDER, &req).await?;
        Ok(rsp.into())
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        let req = OpenOrdersReq::new(Some(symbol), None, None);
        let rsp: Vec<BnOrder> = self.get_signed(PATH_OPEN_ORDERS, &req).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        let rsp: Vec<BnOrder> = self.get_signed(PATH_OPEN_ORDERS, &()).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        let req = OpenOrdersReq::new(Some(symbol), Some(start_time), Some(end_time));
        let rsp: Vec<BnOrder> = self.get_signed(PATH_ALL_ORDERS, &req).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        let _ = order;
        Err(QuantError::api_not_implemented(format!(
            "{} amend_order not implemented",
            self.name()
        )))
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        let cancel_request = SymbolOrderId::new(symbol, order_id);
        let _: TrivialResponse = self.delete(PATH_ORDER, &cancel_request).await?;
        Ok(())
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        self.spot.get_fee_rate(symbol).await
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        let account = self.get_account().await?;
        Ok(account.user_assets.into_iter().map(Into::into).collect())
    }

    async fn get_position(&self, symbol: Symbol) -> Result<Vec<Position>> {
        let _ = symbol;
        let err = format!("{} 没有 get_position 接口", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn get_positions(&self) -> Result<Vec<Position>> {
        let err = format!("{} 没有 get_positions 接口", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn set_leverage(&self, symbol: Symbol, leverage: u8) -> Result<()> {
        let _ = (symbol, leverage);
        let err = format!("{} 没有设置杠杆接口", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn is_dual_side(&self) -> Result<bool> {
        Ok(false)
    }

    async fn set_dual_side(&self, dual_side: bool) -> Result<()> {
        match dual_side {
            false => Ok(()),
            true => Err(qerror!("{} 没有双向持仓", self.name())),
        }
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
        let params: SymbolArg = symbol.into();
        let orders: Vec<CancelOpenOrder> = self.delete(PATH_OPEN_ORDERS, &params).await?;
        let rsp = orders
            .into_iter()
            .fold(BatchOrderRsp::default(), |mut r, item| {
                match item.into() {
                    BnOrderItemRsp::Succ(succ) => r.success_list.push(succ),
                    BnOrderItemRsp::Fail(fail) => r.failure_list.push(fail),
                }

                r
            });
        Ok(rsp)
    }

    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        let _ = (symbol, ids, cids);
        let err = format!("{} 未提供 batch_cancel_order_by_ids 接口", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn request(&self, user_req: UserRequest) -> Result<Value> {
        self.spot.request(user_req).await
    }

    async fn get_fee_discount_info(&self) -> Result<Option<Discount>> {
        self.spot.get_fee_discount_info().await
    }

    async fn is_fee_discount_enabled(&self) -> Result<bool> {
        self.spot.is_fee_discount_enabled().await
    }

    async fn set_fee_discount_enabled(&self, enable: bool) -> Result<()> {
        self.spot.set_fee_discount_enabled(enable).await
    }

    //////////////////// margin 接口 ///////////////

    async fn get_margin_mode(&self, symbol: Symbol, margin_coin: String) -> Result<MarginMode> {
        let _ = (symbol, margin_coin);
        let err = format!("{} 通过账户来确定杠杆模型", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn set_margin_mode(
        &self,
        symbol: Symbol,
        margin_coin: String,
        margin_mode: MarginMode,
    ) -> Result<()> {
        let _ = (symbol, margin_coin, margin_mode);
        let err = format!("{} 通过账户来确定杠杆模型", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn borrow_coin(&self, coin: String, amount: f64) -> Result<()> {
        let req = BorrowRepayReq::borrow(coin, amount);
        let _rsp: BorrowRepayRsp = self.post(PATH_BORROW_REPAY, &req).await?;
        Ok(())
    }

    async fn get_borrow(&self, coin: Option<String>) -> Result<Vec<Borrow>> {
        let account = self.get_account().await?;
        let borrow = account.get_borrow(coin);
        Ok(borrow)
    }

    async fn get_borrow_rate(&self, coin: Option<String>) -> Result<Vec<BorrowRate>> {
        let _ = coin;
        let err = format!("{} get_borrow_rate not implemented", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn get_borrow_limits(&self, is_vip: Option<bool>, coin: String) -> Result<BorrowLimit> {
        let _ = (is_vip, coin);
        let err = format!("{} get_borrow_limits not implemented", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn repay_coin(&self, coin: String, amount: f64) -> Result<()> {
        let req = BorrowRepayReq::repay(coin, amount);
        let _rsp: BorrowRepayRsp = self.post(PATH_BORROW_REPAY, &req).await?;
        Ok(())
    }

    async fn get_account_mode(&self) -> Result<AccountMode> {
        let err = format!("{} get_account_mode not implemented", self.name());
        Err(QuantError::api_not_implemented(err))
    }

    async fn set_account_mode(&self, mode: AccountMode) -> Result<()> {
        let _ = mode;
        let err = format!("{} set_account_mode not implemented", self.name());
        Err(QuantError::api_not_implemented(err))
    }
}

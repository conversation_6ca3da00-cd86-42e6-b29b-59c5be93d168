use crate::margin::model::{BbMarginCoin<PERSON>rder<PERSON><PERSON>ult, BbMarginOrderResult, EXCHANGE};
use crate::spot::model::{ApiResponse, BbList};
use crate::spot::rest::BybitSpot;
use quant_common::base::{
    AccountMode, Balance, BatchOrderRsp, BboTicker, Borrow, Chain, DepositAddress, Depth, Discount,
    ExConfig, Exchange, FeeRate, Funding, Instrument, MarginMode, MaxPosition, Order, OrderId,
    OrderParams, Position, Rest, SubTransfer, Symbol, Ticker, Transfer, UserRequest,
    WithDrawlParams,
};
use quant_common::qerror;
use reqwest::Method;
use rustc_hash::FxHashMap;
use sonic_rs::Value;
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct BybitMargin {
    pub spot: BybitSpot,
    pub _coin_ids: FxHashMap<String, String>,
}

impl BybitMargin {
    #[allow(dead_code)]
    pub async fn new(config: ExConfig) -> Self {
        let spot = BybitSpot::new(ExConfig {
            exchange: Exchange::OkxSpot,
            ..config.clone()
        })
        .await;
        Self {
            spot,
            _coin_ids: Default::default(),
        }
    }
}

impl Rest for BybitMargin {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, req: UserRequest) -> quant_common::Result<Value> {
        self.spot.request(req).await
    }

    async fn get_ticker(&self, symbol: Symbol) -> quant_common::Result<Ticker> {
        self.spot.get_ticker(symbol).await
    }

    async fn get_tickers(&self) -> quant_common::Result<Vec<Ticker>> {
        self.spot.get_tickers().await
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> quant_common::Result<BboTicker> {
        self.spot.get_bbo_ticker(symbol).await
    }

    async fn get_bbo_tickers(&self) -> quant_common::Result<Vec<BboTicker>> {
        self.spot.get_bbo_tickers().await
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> quant_common::Result<Depth> {
        self.spot.get_depth(symbol, limit).await
    }

    async fn get_instrument(&self, symbol: Symbol) -> quant_common::Result<Instrument> {
        self.spot.get_instrument(symbol).await
    }

    async fn get_instruments(&self) -> quant_common::Result<Vec<Instrument>> {
        self.spot.get_instruments().await
    }

    async fn get_funding_rates(&self) -> quant_common::Result<Vec<Funding>> {
        self.spot.get_funding_rates().await
    }

    async fn set_leverage(&self, _symbol: Symbol, _leverage: u8) -> quant_common::Result<()> {
        unimplemented!("现货杠杆暂不支持")
    }

    async fn is_dual_side(&self) -> quant_common::Result<bool> {
        self.spot.is_dual_side().await
    }

    async fn set_dual_side(&self, is_dual_side: bool) -> quant_common::Result<()> {
        self.spot.set_dual_side(is_dual_side).await
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> quant_common::Result<FeeRate> {
        self.spot.get_fee_rate(symbol).await
    }

    async fn get_usdt_balance(&self) -> quant_common::Result<Balance> {
        self.spot.get_usdt_balance().await
    }

    async fn get_balance(&self, asset: &str) -> quant_common::Result<Balance> {
        self.spot.get_balance(asset).await
    }

    async fn get_balances(&self) -> quant_common::Result<Vec<Balance>> {
        self.spot.get_balances().await
    }

    async fn get_fee_discount_info(&self) -> quant_common::Result<Option<Discount>> {
        self.spot.get_fee_discount_info().await
    }

    async fn is_fee_discount_enabled(&self) -> quant_common::Result<bool> {
        self.spot.is_fee_discount_enabled().await
    }

    async fn set_fee_discount_enabled(&self, enable: bool) -> quant_common::Result<()> {
        self.spot.set_fee_discount_enabled(enable).await
    }

    async fn get_max_leverage(&self, symbol: Symbol) -> quant_common::Result<u8> {
        self.spot.get_max_leverage(symbol).await
    }

    async fn get_deposit_address(
        &self,
        ccy: String,
        chain: Option<Chain>,
        amount: Option<f64>,
    ) -> quant_common::Result<Vec<DepositAddress>> {
        self.spot.get_deposit_address(ccy, chain, amount).await
    }

    async fn withdrawal(&self, withdrwl_params: WithDrawlParams) -> quant_common::Result<()> {
        self.spot.withdrawal(withdrwl_params).await
    }

    async fn get_user_id(&self) -> quant_common::Result<String> {
        self.spot.get_user_id().await
    }

    async fn transfer(&self, transfer: Transfer) -> quant_common::Result<()> {
        self.spot.transfer(transfer).await
    }

    async fn sub_transfer(&self, transfer: SubTransfer) -> quant_common::Result<()> {
        self.spot.sub_transfer(transfer).await
    }

    async fn get_margin_mode(
        &self,
        symbol: Symbol,
        margin_coin: String,
    ) -> quant_common::Result<MarginMode> {
        self.spot.get_margin_mode(symbol, margin_coin).await
    }

    async fn set_margin_mode(
        &self,
        symbol: Symbol,
        margin_coin: String,
        margin_mode: MarginMode,
    ) -> quant_common::Result<()> {
        self.spot
            .set_margin_mode(symbol, margin_coin, margin_mode)
            .await
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> quant_common::Result<String> {
        self.spot.post_order(order, params).await
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> quant_common::Result<BatchOrderRsp> {
        self.spot.post_batch_order(orders, params).await
    }

    async fn get_order_by_id(
        &self,
        symbol: Symbol,
        order_id: OrderId,
    ) -> quant_common::Result<Order> {
        self.spot.get_order_by_id(symbol, order_id).await
    }

    async fn get_open_orders(&self, symbol: Symbol) -> quant_common::Result<Vec<Order>> {
        self.spot.get_open_orders(symbol).await
    }

    async fn get_all_open_orders(&self) -> quant_common::Result<Vec<Order>> {
        self.spot.get_all_open_orders().await
    }

    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> quant_common::Result<Vec<Order>> {
        self.spot.get_orders(symbol, start_time, end_time).await
    }

    async fn amend_order(&self, order: Order) -> quant_common::Result<String> {
        self.spot.amend_order(order).await
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> quant_common::Result<()> {
        self.spot.cancel_order(symbol, order_id).await
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> quant_common::Result<BatchOrderRsp> {
        self.spot.batch_cancel_order(symbol).await
    }

    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> quant_common::Result<BatchOrderRsp> {
        self.spot.batch_cancel_order_by_ids(symbol, ids, cids).await
    }

    async fn get_position(&self, symbol: Symbol) -> quant_common::Result<Vec<Position>> {
        self.spot.get_position(symbol).await
    }

    async fn get_positions(&self) -> quant_common::Result<Vec<Position>> {
        self.spot.get_positions().await
    }

    async fn get_max_position(
        &self,
        symbol: Symbol,
        leverage: u8,
    ) -> quant_common::Result<MaxPosition> {
        self.spot.get_max_position(symbol, leverage).await
    }

    async fn borrow_coin(&self, coin: String, amount: f64) -> quant_common::Result<()> {
        let map = HashMap::from([
            ("loanCurrency", coin.clone()),
            ("loanAmount", amount.to_string()),
            ("collateralCurrency", coin),
        ]);

        let result = self
            .spot
            .req::<ApiResponse<BbMarginOrderResult>, _>(
                Method::POST,
                "/v5/crypto-loan/borrow",
                &map,
                true,
            )
            .await;
        let _result = result?.result.ok_or(qerror!("get result err"))?;
        Ok(())
    }

    async fn get_borrow(&self, coin: Option<String>) -> quant_common::Result<Vec<Borrow>> {
        let mut map = HashMap::from([("limit", "100".to_string())]);
        coin.map(|c| map.insert("loanCurrency", c));

        let result = self
            .spot
            .req::<ApiResponse<BbList<BbMarginCoinOrderResult>>, _>(
                Method::GET,
                "/v5/crypto-loan/ongoing-orders",
                &map,
                true,
            )
            .await;
        let a = result?.result.ok_or(qerror!("get result err"))?.list;
        Ok(a.into_iter().map(Borrow::from).collect())
    }

    async fn repay_coin(&self, coin: String, amount: f64) -> quant_common::Result<()> {
        let map = HashMap::from([
            ("loanCurrency", coin.clone()),
            ("loanAmount", amount.to_string()),
            ("collateralCurrency", coin),
        ]);

        let result = self
            .spot
            .req::<ApiResponse<BbMarginOrderResult>, _>(
                Method::POST,
                "/v5/crypto-loan/repay",
                &map,
                true,
            )
            .await;
        let _result = result?.result.ok_or(qerror!("get result err"))?;
        Ok(())
    }

    async fn get_account_mode(&self) -> quant_common::Result<AccountMode> {
        self.spot.get_account_mode().await
    }

    async fn set_account_mode(&self, mode: AccountMode) -> quant_common::Result<()> {
        self.spot.set_account_mode(mode).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use quant_common::test_config;
    use tracing::info;

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_borrow_coin() {
        // 初始化日志
        tracing_subscriber::fmt().pretty().init();

        // 获取测试配置
        let config = test_config();
        info!("{:#?}", config);

        // 创建 BybitMargin 实例
        let rest = BybitMargin::new(config).await;

        // 调用 borrow_coin 函数
        let result = rest.borrow_coin("BTC".to_string(), 1.0).await;

        // 打印结果
        info!("{:?}", result);

        // 断言结果
        assert!(result.is_ok());
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_get_borrow() {
        // 初始化日志
        tracing_subscriber::fmt().pretty().init();

        // 获取测试配置
        let config = test_config();
        info!("{:#?}", config);

        // 创建 BybitMargin 实例
        let rest = BybitMargin::new(config).await;

        // 调用 borrow_coin 函数
        let result = rest.get_borrow(Option::from("BTC".to_string())).await;

        // 打印结果
        info!("{:?}", result);

        // 断言结果
        assert!(result.is_ok());
    }

    #[tokio::test]
    #[ignore = "need proxy"]
    async fn test_repay_coin() {
        // 初始化日志
        tracing_subscriber::fmt().pretty().init();

        // 获取测试配置
        let config = test_config();
        info!("{:#?}", config);

        // 创建 BybitMargin 实例
        let rest = BybitMargin::new(config).await;

        // 调用 borrow_coin 函数
        let result = rest.repay_coin("BTC".to_string(), 1.0).await;

        // 打印结果
        info!("{:?}", result);

        // 断言结果
        assert!(result.is_ok());
    }
}

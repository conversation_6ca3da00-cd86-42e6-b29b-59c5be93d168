use quant_common::Result;
use quant_common::base::model::{ExConfig, Subscribes};
use quant_common::base::traits::ws::WebSocket;
use quant_common::base::{Exchange, WsHandler};

use crate::margin::{model::*, rest::BinanceMargin};
use crate::spot::ws::BinanceSpotWs;

pub struct BinanceMarginWs {
    inner: BinanceSpotWs,
}

impl BinanceMarginWs {
    pub async fn new(config: ExConfig) -> Self {
        if config.is_testnet {
            warn!("{} 没有模拟环境，只有正式环境", EXCHANGE);
        }
        let config = ExConfig {
            is_testnet: false,
            ..config
        };

        let margin = BinanceMargin::new(config.clone()).await;
        let inner = BinanceSpotWs::new_with_margin(config.clone(), margin).await;
        Self { inner }
    }
}

impl WebSocket for BinanceMarginWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.inner.exchange()
    }

    async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> Result<()> {
        self.inner.run(subs).await
    }
}

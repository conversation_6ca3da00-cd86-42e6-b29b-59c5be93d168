use quant_common::base::*;

use crate::spot::ws::OkxSpotWs;

#[derive(Default, Debug)]
pub struct OkxMarginWs {
    exchange: Exchange,
    spot: OkxSpotWs,
}

impl OkxMarginWs {
    pub async fn new(config: ExConfig) -> Self {
        let mut spot = OkxSpotWs::new(ExConfig {
            exchange: Exchange::OkxMargin,
            ..config
        })
        .await;

        spot.set_is_margin(true);

        OkxMarginWs {
            exchange: Exchange::OkxMargin,
            spot,
        }
    }
}

impl WebSocket for OkxMarginWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> quant_common::Result<()> {
        self.spot.run(subs).await
    }
}

pub fn symbols_from(symbols: &[Symbol]) -> Vec<String> {
    symbols
        .iter()
        .map(|x| format!("{}-{}", x.base, x.quote))
        .collect()
}

use quant_common::base::*;
use quant_common::*;

use crate::spot::ws::GateSpotWs;

#[derive(Debug)]
pub struct GateMarginWs {
    exchange: Exchange,
    spot: GateSpotWs,
}

impl GateMarginWs {
    pub async fn new(config: ExConfig) -> Self {
        let spot = GateSpotWs::new(ExConfig {
            exchange: Exchange::GateMargin,
            ..config.clone()
        })
        .await;

        GateMarginWs {
            exchange: config.exchange,
            spot,
        }
    }
}

impl WebSocket for GateMarginWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: base::Subscribes<H>) -> Result<()> {
        self.spot.run(subs).await
    }
}

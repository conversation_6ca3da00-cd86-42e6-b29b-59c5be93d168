use crate::cache::instruments::get_instrument;
use quant_common::base::MarketOrderMode;
use quant_common::base::traits::model::*;
use quant_common::base::*;
use quant_common::{Result, decimal_round, qerror, tick_ceil, tick_floor, tick_round};

/// 处理订单
pub async fn process_order(
    order: &mut Order,
    params: &OrderParams,
    exchange: Exchange,
) -> Result<()> {
    if order.amount.is_none() && order.quote_amount.is_none() {
        return Err(qerror!("订单数量和quote_amount不能同时为空"));
    }

    // 获取相关instrument
    let instrument = get_instrument(exchange, &order.symbol)
        .await?
        .ok_or_else(|| qerror!("未找到交易对 {}", order.symbol))?;

    // 处理市价单
    if order.order_type == OrderType::Market {
        match &params.market_order_mode {
            MarketOrderMode::Safe => {
                process_market_order(order, params.market_order_slippage)?;
            }
            MarketOrderMode::Normal => {}
        }
    }

    // 处理数量
    if let Some(amount) = order.amount {
        let normalized = normalize_quantity(amount, &instrument);
        order.amount = Some(normalized);
    }

    // 处理价格
    if order.price.is_some() {
        order.price = Some(normalize_price(
            order.price.unwrap(),
            &instrument,
            order.side,
        ));
    }

    Ok(())
}

/// 处理市价单，转为限价单+IOC
fn process_market_order(order: &mut Order, slippage: f64) -> Result<()> {
    let price = order.price.ok_or(qerror!(
        "安全市价单必须指定价格, 或者在OrderParams中设置market_order_mode为Normal"
    ))?;

    let price = match order.side {
        OrderSide::Buy => price * (1.0 + slippage),
        OrderSide::Sell => price * (1.0 - slippage),
    };

    order.price = Some(price);
    order.order_type = OrderType::Limit;
    order.time_in_force = TimeInForce::IOC;

    Ok(())
}

/// 处理订单数量
fn normalize_quantity(quantity: f64, instrument: &Instrument) -> f64 {
    // 乘以数量乘数
    let normalized = quantity * instrument.amount_multiplier;

    // 确保数量是amount_tick的整数倍
    let ticked = tick_round(normalized, instrument.amount_tick);

    // 四舍五入到合适的精度
    decimal_round(ticked, instrument.amount_precision)
}

/// 处理订单价格
fn normalize_price(price: f64, instrument: &Instrument, side: OrderSide) -> f64 {
    // 乘以价格乘数
    let normalized = price * instrument.price_multiplier;

    // 根据买卖方向调整价格到最近的tick
    let ticked = match side {
        // 向上取整
        OrderSide::Buy => tick_ceil(normalized, instrument.price_tick),
        // 向下取整
        OrderSide::Sell => tick_floor(normalized, instrument.price_tick),
    };

    // 四舍五入到合适的精度
    decimal_round(ticked, instrument.price_precision)
}

// 处理stop order params
pub async fn process_stop_order_params(
    params: &mut PostStopOrderParams,
    exchange: Exchange,
) -> Result<()> {
    let side = match params.pos_side {
        PosSide::Long => OrderSide::Buy,
        PosSide::Short => OrderSide::Sell,
    };
    let instrument = get_instrument(exchange, &params.symbol)
        .await?
        .ok_or_else(|| qerror!("未找到交易对 {}", params.symbol))?;

    if let Some(take_profit) = params.take_profit.as_mut() {
        process_trigger(take_profit, &instrument, side).await;
    }

    if let Some(stop_loss) = params.stop_loss.as_mut() {
        process_trigger(stop_loss, &instrument, side).await;
    }

    Ok(())
}

async fn process_trigger(trigger: &mut CloseTrigger, instrument: &Instrument, side: OrderSide) {
    match trigger.trigger_price {
        TriggerPrice::MarkPrice(price) => {
            trigger.trigger_price =
                TriggerPrice::MarkPrice(normalize_price(price, instrument, side));
        }
        TriggerPrice::ContractPrice(price) => {
            trigger.trigger_price =
                TriggerPrice::ContractPrice(normalize_price(price, instrument, side));
        }
    }
    match trigger.trigger_action {
        TriggerAction::Limit(price) => {
            trigger.trigger_action = TriggerAction::Limit(normalize_price(price, instrument, side));
        }
        TriggerAction::Market => {}
    }
}

#[cfg(test)]
mod tests {
    use crate::cache::instruments::init_exchange_cache;

    use super::*;
    use quant_common::base::model::{InsState, Symbol};

    // 近似相等比较，处理浮点数精度问题
    fn assert_f64_eq(a: f64, b: f64) {
        if a == 0.0 && b == 0.0 {
            return; // 两个0视为相等
        }

        let rel_diff = (a - b).abs() / a.abs().max(b.abs());
        assert!(
            rel_diff < 1e-8,
            "浮点数不相等: {a} != {b}, 相对差值: {rel_diff}"
        );
    }

    // 可选值浮点数比较
    fn assert_option_f64_eq(a: Option<f64>, b: Option<f64>) {
        match (a, b) {
            (Some(a_val), Some(b_val)) => assert_f64_eq(a_val, b_val),
            (None, None) => (),
            _ => panic!("期望值: {b:?}, 实际值: {a:?}"),
        }
    }

    // 创建模拟的instrument
    fn create_test_instrument() -> Instrument {
        Instrument {
            symbol: Symbol::new("BTC"),
            state: InsState::Normal,
            price_tick: 0.01,
            amount_tick: 0.0001,
            price_precision: 2,
            amount_precision: 4,
            min_qty: 0.0001,
            min_notional: 1.0,
            price_multiplier: 1.0,
            amount_multiplier: 1.0,
        }
    }

    // 创建测试订单
    fn create_test_order() -> Order {
        Order {
            symbol: Symbol::new("BTC"),
            side: OrderSide::Buy,
            order_type: OrderType::Limit,
            time_in_force: TimeInForce::GTC,
            price: Some(10000.0),
            amount: Some(0.01),
            ..Order::default()
        }
    }

    #[tokio::test]
    async fn test_normalize_quantity() {
        let instrument = create_test_instrument();

        // 标准情况
        assert_f64_eq(normalize_quantity(0.01, &instrument), 0.01);

        // 四舍五入到amount_tick
        assert_f64_eq(normalize_quantity(0.01234, &instrument), 0.0123);
        assert_f64_eq(normalize_quantity(0.01235, &instrument), 0.0124);

        // 使用乘数
        let mut test_instrument = instrument;
        test_instrument.amount_multiplier = 2.0;
        assert_f64_eq(normalize_quantity(0.01, &test_instrument), 0.02);
    }

    #[tokio::test]
    async fn test_normalize_price() {
        let instrument = create_test_instrument();

        // 买单价格向上取整
        assert_f64_eq(
            normalize_price(100.567, &instrument, OrderSide::Buy),
            100.57,
        );

        // 卖单价格向下取整
        assert_f64_eq(
            normalize_price(100.567, &instrument, OrderSide::Sell),
            100.56,
        );

        // 使用价格乘数
        let mut test_instrument = instrument;
        test_instrument.price_multiplier = 0.1;
        assert_f64_eq(
            normalize_price(100.0, &test_instrument, OrderSide::Buy),
            10.0,
        );
    }

    #[tokio::test]
    async fn test_process_market_order() {
        let mut order = create_test_order();
        order.order_type = OrderType::Market;

        // 测试买单滑点
        process_market_order(&mut order, 0.001).unwrap();
        assert_option_f64_eq(order.price, Some(10010.0));
        assert_eq!(order.order_type, OrderType::Limit);
        assert_eq!(order.time_in_force, TimeInForce::IOC);

        // 测试卖单滑点
        let mut order = create_test_order();
        order.order_type = OrderType::Market;
        order.side = OrderSide::Sell;
        process_market_order(&mut order, 0.001).unwrap();
        assert_option_f64_eq(order.price, Some(9990.0));

        // 测试无价格时的错误
        let mut order = create_test_order();
        order.order_type = OrderType::Market;
        order.price = None;
        let result = process_market_order(&mut order, 0.001);
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_process_order() {
        let instrument = create_test_instrument();
        init_exchange_cache(Exchange::BinanceSwap, vec![instrument])
            .await
            .unwrap();
        let mut order = create_test_order();

        // 测试正常情况
        process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap)
            .await
            .unwrap();

        // 测试市价单
        let mut order = create_test_order();
        order.order_type = OrderType::Market;

        process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap)
            .await
            .unwrap();

        // 测试数量
        let mut order = create_test_order();
        order.amount = Some(0.0001);
        process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap)
            .await
            .unwrap();

        // 测试价格
        let mut order = create_test_order();
        order.price = Some(10000.0);
        process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap)
            .await
            .unwrap();

        // 测试最小数量
        let mut order = create_test_order();
        order.amount = Some(0.00009);
        process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap)
            .await
            .unwrap();

        // 测试最小交易额
        let mut order = create_test_order();
        order.price = Some(900.0);
        order.amount = Some(0.01);
        process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap)
            .await
            .unwrap();

        // 测试无价格
        let mut order = create_test_order();
        order.price = None;
        process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap)
            .await
            .unwrap();

        // 测试无数量
        let mut order = create_test_order();
        order.price = Some(10000.0);
        order.amount = None;
        let res = process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap).await;
        assert!(res.is_err());

        // 测试市价单
        let mut order = create_test_order();
        order.order_type = OrderType::Market;
        let res = process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap).await;
        assert!(res.is_ok());

        // 市价单没有指定价格
        let mut order = create_test_order();
        order.order_type = OrderType::Market;
        order.price = None;
        let res = process_order(&mut order, &OrderParams::default(), Exchange::BinanceSwap).await;
        assert!(res.is_err());
    }
}

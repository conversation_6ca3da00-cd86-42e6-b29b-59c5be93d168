use quant_common::base::traits::GetKlineParams;
use sonic_rs::Value;

use quant_common::base::{model::*, traits::rest::Rest};
use quant_common::{Error, Result};

use crate::swap::model::KlineReq;
use crate::swap::rest::BinanceSwap;

use super::model::EXCHANGE;

#[derive(Clone)]
pub struct BinanceFuture {
    swap: BinanceSwap,
}

impl BinanceFuture {
    pub async fn new(config: ExConfig) -> Self {
        let swap = BinanceSwap::new_future(config.clone()).await;
        Self { swap }
    }
}

impl Rest for BinanceFuture {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, req: UserRequest) -> Result<Value> {
        self.swap.request(req).await
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        self.swap.get_ticker(symbol).await
    }

    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        self.swap.get_tickers().await
    }

    async fn get_mark_price(&self, symbol: Option<Symbol>) -> Result<Vec<MarkPrice>> {
        let _ = symbol;
        let err = format!("{} get_mark_price not implemented", self.name());
        Err(Error::api_not_implemented(err))
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        self.swap.get_bbo_ticker(symbol).await
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        let resp = self.swap.bbo_tickers().await?;
        Ok(resp
            .into_iter()
            .filter_map(|x| if x.is_future() { Some(x.into()) } else { None })
            .collect())
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        self.swap.get_depth(symbol, limit).await
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        self.swap.get_instrument(symbol).await
    }

    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        self.swap.get_instruments().await
    }

    async fn get_klines_ext(&self, params: GetKlineParams) -> Result<Kline> {
        let symbol = params.symbol.clone();
        let interval = params.interval.clone();
        let req = KlineReq::future_from_params(params)?;
        let candles = self.swap.klines(req).await?;
        Ok(Kline::new(symbol, interval, candles))
    }

    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        let err = format!("{} get_funding_rates not implemented", self.name());
        Err(Error::api_not_implemented(err))
    }

    async fn get_funding_fee(
        &self,
        symbol: Symbol,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Result<Vec<FundingFee>> {
        let _ = (symbol, start_time, end_time);
        let err = format!("{} get_funding_fee not implemented", self.name());
        Err(Error::api_not_implemented(err))
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        self.swap.post_order(order, params).await
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        self.swap.post_batch_order(orders, params).await
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        self.swap.get_order_by_id(symbol, order_id).await
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        self.swap.get_open_orders(symbol).await
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        let rsp = self.swap.all_open_orders().await?;
        Ok(rsp
            .into_iter()
            .filter_map(|x| if x.is_future() { Some(x.into()) } else { None })
            .collect())
    }

    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        self.swap.get_orders(symbol, start_time, end_time).await
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        self.swap.amend_order(order).await
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        self.swap.cancel_order(symbol, order_id).await
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        self.swap.get_fee_rate(symbol).await
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        self.swap.get_balances().await
    }

    async fn get_position(&self, symbol: Symbol) -> Result<Vec<Position>> {
        self.swap.get_position(symbol).await
    }

    async fn get_positions(&self) -> Result<Vec<Position>> {
        let rsp = self.swap.positions().await?;
        Ok(rsp
            .into_iter()
            .filter_map(|x| match x.symbol.is_future() {
                true => Some(x.into()),
                false => None,
            })
            .collect())
    }

    async fn get_max_leverage(&self, symbol: Symbol) -> Result<u8> {
        self.swap.get_max_leverage(symbol).await
    }

    async fn set_leverage(&self, symbol: Symbol, leverage: u8) -> Result<()> {
        self.swap.set_leverage(symbol, leverage).await
    }

    async fn is_dual_side(&self) -> Result<bool> {
        self.swap.is_dual_side().await
    }

    async fn set_dual_side(&self, dual_side: bool) -> Result<()> {
        self.swap.set_dual_side(dual_side).await
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
        self.swap.batch_cancel_order(symbol).await
    }

    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        self.swap.batch_cancel_order_by_ids(symbol, ids, cids).await
    }

    async fn get_usdt_balance(&self) -> Result<Balance> {
        self.swap.get_usdt_balance().await
    }

    async fn get_fee_discount_info(&self) -> Result<Option<Discount>> {
        self.swap.get_fee_discount_info().await
    }

    async fn is_fee_discount_enabled(&self) -> Result<bool> {
        self.swap.is_fee_discount_enabled().await
    }

    async fn set_fee_discount_enabled(&self, _enable: bool) -> Result<()> {
        self.swap.set_fee_discount_enabled(_enable).await
    }

    async fn get_max_position(&self, symbol: Symbol, leverage: u8) -> Result<MaxPosition> {
        self.swap.get_max_position(symbol, leverage).await
    }

    async fn get_margin_mode(&self, symbol: Symbol, _margin_coin: String) -> Result<MarginMode> {
        self.swap.get_margin_mode(symbol, _margin_coin).await
    }

    async fn set_margin_mode(
        &self,
        symbol: Symbol,
        _margin_coin: String,
        margin_mode: MarginMode,
    ) -> Result<()> {
        self.swap
            .set_margin_mode(symbol, _margin_coin, margin_mode)
            .await
    }
}

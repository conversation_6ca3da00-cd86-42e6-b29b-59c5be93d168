use std::sync::Arc;

use serde::Deserialize;
use serde_json::Value;
use tokio::task::spawn;
use trader::{
    execution::{ExecutionEngine, StaticExecutionEngine},
    model::event::ex_command::ExecutionCommand,
};

#[derive(Deserialize, Debug, Default)]
pub struct PythonReturn {
    #[serde(default)]
    pub cmds: Vec<ExecutionCommand>,
    #[serde(default)]
    pub logs: Vec<String>,
}

impl PythonReturn {
    #[inline(always)]
    pub async fn handle(self, execution: &Arc<StaticExecutionEngine>) -> quant_common::Result<()> {
        for cmd in self.cmds {
            execution.execution_async(cmd).await?;
        }
        if !self.logs.is_empty() {
            spawn(async move {
                tokio::task::yield_now().await;
                for log in self.logs {
                    info!("{}", log);
                }
            });
        }
        Ok(())
    }

    /// v2版本反序列化，ExecutionCommand先反序列化为Value，然后调用ExecutionCommand的from_flatten的方法
    pub fn from_flatten(mut value: Value) -> quant_common::Result<Self> {
        match value.as_object_mut() {
            Some(value) => {
                let cmds = value
                    .remove("cmds")
                    .and_then(|mut c| {
                        let mut cmds = vec![];
                        let cmds_values = c.as_array_mut()?;
                        while let Some(cmd) = cmds_values.pop() {
                            let cmd = ExecutionCommand::from_flatten(cmd).ok()?;
                            cmds.push(cmd);
                        }
                        Some(cmds)
                    })
                    .unwrap_or_default();
                let logs = value
                    .remove("logs")
                    .and_then(|mut l| {
                        let mut logs = vec![];
                        let logs_values = l.as_array_mut()?;
                        while let Some(log) = logs_values.pop() {
                            logs.push(log.as_str().unwrap_or_default().to_string());
                        }
                        Some(logs)
                    })
                    .unwrap_or_default();
                Ok(Self { cmds, logs })
            }
            None => Ok(Self::default()),
        }
    }
}

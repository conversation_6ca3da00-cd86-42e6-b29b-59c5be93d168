use binance::spot::ws_api::BinanceSpotWsApi;
use binance::swap::ws_api::BinanceSwapWsApi;

use async_channel::Receiver;
use bitget::spot::ws_api::BitgetSpotWsApi;
use bitget::swap::ws_api::BitgetSwapWsApi;
use gate::spot::ws_api::GateSpotWsApi;
use gate::swap::ws_api::GateSwapWsApi;
use okx::margin::ws_api::OkxMarginWsApi;
use okx::spot::ws_api::OkxSpotWsApi;
use okx::swap::ws_api::OkxSwapWsApi;
use quant_common::Result;
use quant_common::base::Exchange;
use quant_common::base::model::AsyncCmd;
use quant_common::base::model::ExConfig;
use quant_common::base::traits::ws_api::{AsyncResHandle, WebSocketAPI};
use quant_common::qerror;

macro_rules! define_exchanges_ws_api {
    (
        // 支持的交易所
        SUPPORTED: { $($name:ident => $ws_api:ident),* $(,)? },
        // 不支持的交易所
        UNSUPPORTED: { $($unsupported:ident),* $(,)? }
    ) => {
        #[derive(Clone)]
        #[allow(clippy::large_enum_variant)]
        pub enum ExchangeWsAPI {
            $(
                $name($ws_api),
            )*
            Unsupported(Exchange),
        }

        pub async fn create_public_ws_api(exchange: Exchange) -> Result<ExchangeWsAPI> {
            match exchange {
                $(
                    Exchange::$name => Ok(ExchangeWsAPI::$name($ws_api::new(exchange.config()).await)),
                )*
                $(
                    Exchange::$unsupported => Ok(ExchangeWsAPI::Unsupported(exchange)),
                )*
            }
        }

        pub async fn create_private_ws_api(config: ExConfig) -> Result<ExchangeWsAPI> {
            match config.exchange {
                $(
                    Exchange::$name => Ok(ExchangeWsAPI::$name($ws_api::new(config).await)),
                )*
                $(
                    Exchange::$unsupported => Ok(ExchangeWsAPI::Unsupported(config.exchange)),
                )*
            }
        }

        impl WebSocketAPI for ExchangeWsAPI {
            fn exchange(&self) -> Exchange {
                match self {
                    $(
                        ExchangeWsAPI::$name(ws_api) => ws_api.exchange(),
                    )*
                    ExchangeWsAPI::Unsupported(exchange) => *exchange,
                }
            }

            async fn run<H: AsyncResHandle>(
                self,
                account_id: usize,
                handler: H,
                rx: Receiver<(u64, AsyncCmd)>,
            ) -> Result<()> {
                match self {
                    $(
                        ExchangeWsAPI::$name(ws_api) => ws_api.run(account_id, handler, rx).await,
                    )*
                    ExchangeWsAPI::Unsupported(exchange) => {
                        Err(qerror!("交易所 {:?} 未实现", exchange))
                    }
                }
            }
        }

        // 自动生成 is_ws_api_supported 方法
        pub fn is_ws_api_supported(exchange: Exchange) -> bool {
            match exchange {
                $(
                    Exchange::$name => true,
                )*
                _ => false,
            }
        }
    };
}

define_exchanges_ws_api!(
    SUPPORTED: {
        BinanceSwap => BinanceSwapWsApi,
        BinanceSpot => BinanceSpotWsApi,
        OkxSwap => OkxSwapWsApi,
        OkxSpot => OkxSpotWsApi,
        OkxMargin => OkxMarginWsApi,
        GateSwap => GateSwapWsApi,
        GateSpot => GateSpotWsApi,
        BitgetSwap => BitgetSwapWsApi,
        BitgetSpot => BitgetSpotWsApi,
    },
    UNSUPPORTED: {
        // BinanceSwap,
        BitgetMargin,
        BinanceFuture,
        BinanceMargin,
        OkxFuture,
        OkxSwapUsd,
        KucoinMargin,
        BitmexSwap,
        BitmexSpot,
        BingxSwap,
        BingxSpot,
        CryptoSwap,
        CryptoSpot,
        CoinbaseSpot,
        CoinbaseSwap,
        KrakenSpot,
        CoinwSwap,
        CoinwSpot,
        GateMargin,
        CoinexSwap,
        CoinexSpot,
        HuobiSwap,
        HuobiSpot,
        KucoinSwap,
        KucoinSpot,
        BybitSwap,
        BybitSpot,
        DeepcoinSwap,
        DeepcoinSpot,
        ApexSwap,
        BitfinexSpot,
        BitfinexSwap,
        PhemexSpot,
        PhemexSwap,
        HyperLiquidSpot,
        HyperLiquidSwap,
        MexcSwap,
        MexcSpot,
        DydxSwap,
        BitmartSwap,
        BitmartSpot,
        XTSwap,
        XTSpot,
        DeribitSwap,
        DeribitSpot,
        DeribitFuture,
        DeribitOption,
        ZoomexSpot,
        ZoomexSwap,
        WhitebitSpot,
        WhitebitSwap,
    }
);

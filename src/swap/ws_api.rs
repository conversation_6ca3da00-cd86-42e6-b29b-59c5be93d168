use std::{
    collections::VecDeque,
    time::{Duration, Instant},
};

use async_channel::Receiver;
use ethers::signers::LocalWallet;
use fastwebsockets::Frame;
use log::info;
use quant_common::{
    base::{
        traits::ws_api::{AsyncResHandle, WebSocketAPI},
        AmendOrderResult, AsyncCmd, BatchOrderRsp, CancelOrderResult, ExConfig, Exchange,
        FailOrder, OrderId, PlaceOrderResult, Rest, SuccessOrder, MAX_RETRY,
    },
    fast_proxy::connect_ws_with_proxy,
    qerror, QuantError,
};
use quant_common::{time, Result};
use rustc_hash::{FxHashMap, FxHashSet};
use sonic_rs::Value;
use tracing::{debug, error, warn};

use crate::{
    define::{
        self,
        action::{
            Actions, BulkCancel, BulkCancelCloid, BulkModify, BulkOrder, CancelRequest,
            CancelRequestCloid, ClientOrderRequest, ExchangePayload, ModifyRequest,
        },
        model::{Request, Status, Subscribe, Subscription, WsRequest, WsResponse},
    },
    util::{
        self,
        signature::{agent::l1, create_signature::sign_typed_data},
    },
};

use super::rest::HyperLiquidSwap;

const REQUEST_TIMEOUT_SECS: u64 = 60; // 请求超时时间（秒）
const MAX_CACHE_SIZE: usize = 1000; // 最大缓存条目数

#[derive(Debug, Clone)]
struct CachedRequest {
    cmd: AsyncCmd,
    timestamp: Instant,
}

#[derive(Debug, Clone)]
pub struct HyperLiquidSwapWsApi {
    ws_url: String,
    config: ExConfig,
    rest: HyperLiquidSwap,
    req_cache: FxHashMap<u64, CachedRequest>,
    req_queue: VecDeque<u64>, // 按时间顺序存储请求ID，用于超时检查
    order_ids: FxHashSet<String>,
}

impl HyperLiquidSwapWsApi {
    pub async fn new(config: ExConfig) -> Self {
        let mut ws_url = if config.is_testnet {
            define::TEST_WS_API_HYPERLIQUID
        } else {
            define::WS_API_HYPERLIQUID
        }
        .to_string();
        if let Some(host) = config.ws_api_host.clone() {
            ws_url = format!("wss://{}/ws", host);
        }
        let rest = HyperLiquidSwap::new(config.clone()).await;
        let req_type = FxHashMap::default();
        let order_ids = FxHashSet::default();
        HyperLiquidSwapWsApi {
            ws_url,
            config,
            rest,
            req_cache: req_type,
            req_queue: VecDeque::new(),
            order_ids,
        }
    }
    fn cache_request_if_needed(&mut self, req_id: u64, cmd: &AsyncCmd) {
        // 清理缓存，防止无限增长
        self.cleanup_cache();

        // 添加新请求到缓存
        self.req_cache.insert(
            req_id,
            CachedRequest {
                cmd: cmd.clone(),
                timestamp: Instant::now(),
            },
        );
        self.req_queue.push_back(req_id);

        debug!(
            "已缓存请求: req_id={}, cmd_type={}",
            req_id,
            self.get_cmd_type_str(cmd)
        );
    }

    // 清理超时和过多的缓存条目
    fn cleanup_cache(&mut self) {
        // 检查并移除超时请求
        let now = Instant::now();
        let timeout = Duration::from_secs(REQUEST_TIMEOUT_SECS);

        // 从队列前端检查，移除超时的请求
        while let Some(&req_id) = self.req_queue.front() {
            if let Some(cached_req) = self.req_cache.get(&req_id) {
                if now.duration_since(cached_req.timestamp) > timeout {
                    // 该请求已超时，移除
                    self.req_queue.pop_front();
                    self.req_cache.remove(&req_id);
                    warn!("请求超时已自动清理: req_id={}", req_id);
                } else {
                    // 如果最早的请求没有超时，后面的更不会超时
                    break;
                }
            } else {
                // 队列中的ID不在缓存中，清理队列
                self.req_queue.pop_front();
            }
        }

        // 如果缓存大小超过上限，移除最早的请求
        while self.req_cache.len() > MAX_CACHE_SIZE {
            if let Some(req_id) = self.req_queue.pop_front() {
                self.req_cache.remove(&req_id);
                warn!("缓存已达到最大大小，移除最早的请求: req_id={}", req_id);
            } else {
                break;
            }
        }
    }
    // 获取命令类型的字符串表示，用于日志记录
    fn get_cmd_type_str(&self, cmd: &AsyncCmd) -> &'static str {
        match cmd {
            AsyncCmd::PlaceOrder(_) => "PlaceOrder",
            AsyncCmd::CancelOrder(_) => "CancelOrder",
            AsyncCmd::AmendOrder(_) => "AmendOrder",
            AsyncCmd::BatchPlaceOrder(_) => "BatchPlaceOrder",
            AsyncCmd::BatchCancelOrder(_) => "BatchCancelOrder",
            AsyncCmd::BatchCancelOrderByIds(_) => "BatchCancelOrderByIds",
        }
    }
    async fn handle_error<T>(
        &self,
        handler: &T,
        req_id: u64,
        err: QuantError,
        account_id: usize,
        cmd: AsyncCmd,
    ) -> Result<()>
    where
        T: AsyncResHandle,
    {
        match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                let result = quant_common::base::PlaceOrderResult {
                    result: Err(err),
                    order: place_cmd.order,
                };
                handler
                    .handle_post_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let result = quant_common::base::CancelOrderResult {
                    result: Err(err),
                    order_id: cancel_cmd.order_id,
                    symbol: cancel_cmd.symbol,
                };
                handler
                    .handle_cancel_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchPlaceOrder(_) => {
                handler
                    .handle_post_batch_order(account_id, req_id, Err(err))
                    .await?;
            }
            AsyncCmd::AmendOrder(order) => {
                let result = quant_common::base::AmendOrderResult {
                    result: Err(err),
                    order,
                };
                handler
                    .handle_amend_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchCancelOrder(_) => {
                handler
                    .handle_batch_cancel_order(account_id, req_id, Err(err))
                    .await?;
            }
            AsyncCmd::BatchCancelOrderByIds(_) => {
                handler
                    .handle_batch_cancel_order(account_id, req_id, Err(err))
                    .await?;
            }
        }

        Ok(())
    }
}

impl WebSocketAPI for HyperLiquidSwapWsApi {
    fn exchange(&self) -> Exchange {
        Exchange::HyperLiquidSwap
    }

    async fn run<H: AsyncResHandle>(
        mut self,
        account_id: usize,
        handler: H,
        mut rx: Receiver<(u64, AsyncCmd)>,
    ) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(account_id, &handler, &mut rx).await {
                Ok(_) => {
                    warn!("{} websocket disconnect, try to reconnect", self.exchange());
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
                Err(e) => {
                    error!("websocket connect error: {:?}", e);
                    if time() - start < 15 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            let err = qerror!(
                                "HyperLiquidSwap websocket retry failed too many times: {e}"
                            );
                            return Err(err);
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl HyperLiquidSwapWsApi {
    async fn run_inner<H: AsyncResHandle>(
        &mut self,
        account_id: usize,
        handler: &H,
        rx: &mut Receiver<(u64, AsyncCmd)>,
    ) -> Result<()> {
        let mut ws = connect_ws_with_proxy(&self.ws_url).await?;
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(15));
        //签名在发送请求的时候携带
        loop {
            tokio::select! {
                //保活
                _ =ping_interval.tick()=>{
                    let ping = Subscribe::<Subscription>{
                        method: "ping".to_string(),
                        subscription:None,
                     };
                    let sub_req = Frame::text(sonic_rs::to_vec(&ping)?.into());
                    ws.write_frame(sub_req).await?;
                },
                //写
                req_cmd =rx.recv()=>{
                    let (req_id,cmd)=match req_cmd{
                        Ok((id,cmd)) => (id,cmd),
                        Err(e) => {
                            warn!("{} async channel error: {:?}",self.exchange(),e);
                            break;
                        }
                    };
                    // 缓存需要的请求类型（下单、修改订单、撤单）
                    self.cache_request_if_needed(req_id, &cmd);
                    let message = self.req_to_message(req_id,cmd).await?;
                    let sub_req = Frame::text(message.into());
                    match ws.write_frame(sub_req).await {
                        Ok(()) => {}
                        Err(e) => {
                            error!("发送订单请求错误: {:?}", e);
                            let err = qerror!("发送订单请求错误: {e}");
                            // 出错时移除并获取缓存的命令
                            if let Some(cached_req) = self.req_cache.remove(&req_id) {
                                // 从队列中也移除该请求ID
                                let index = self.req_queue.iter().position(|&id| id == req_id);
                                if let Some(index) = index {
                                    self.req_queue.remove(index);
                                }
                                self.handle_error(handler, req_id, err, account_id, cached_req.cmd).await?;
                            }
                            return Err(e.into());
                        }
                    }
                },
                //读
                resp_msg = ws.read_frame()=>{
                    let rsp = resp_msg?;
                    let msg= match rsp.opcode {
                        fastwebsockets::OpCode::Text|fastwebsockets::OpCode::Binary => {
                            sonic_rs::from_slice(&rsp.payload)?
                        },
                        fastwebsockets::OpCode::Close => {
                            warn!("{} websocket disconnect, try to reconnect",self.exchange());
                            break;
                        },
                        _=>continue,
                    };
                    self.handle_message(account_id,handler,msg).await?;
                },
            }
        }
        Ok(())
    }
    async fn req_to_message(&mut self, req_id: u64, cmd: AsyncCmd) -> Result<Vec<u8>> {
        let action = match cmd {
            AsyncCmd::PlaceOrder(info) => {
                let mut orders = Vec::new();
                let item_orders =
                    ClientOrderRequest::from_order(info.order.clone(), info.params.clone());
                for item_order in item_orders {
                    //ws下单很奇怪，数量和http下单后的数量(http下单后的数量是sz*leverage)，这里sz是多少成交就是多少了，所以sz扩大leverage
                    // item_order.sz *= info.params.leverage as f64;
                    orders.push(item_order.convert(
                        &self.rest.coin_to_asset.token_ind,
                        self.rest.coin_meta[info.order.symbol.base.as_str()],
                        6,
                    )?);
                }
                let mut grouping = "na".to_string();
                if orders.len() > 1 {
                    grouping = "normalTpsl".to_string();
                }
                Actions::Order(BulkOrder {
                    orders,
                    grouping,
                    builder: None,
                })
            }
            AsyncCmd::BatchPlaceOrder(info) => {
                let mut items = Vec::new();
                let mut grouping = "na".to_string();
                for order in info.orders {
                    let item_orders =
                        ClientOrderRequest::from_order(order.clone(), info.params.clone());
                    if item_orders.len() > 1 {
                        grouping = "normalTpsl".to_string();
                    }
                    for item_order in item_orders {
                        // item_order.sz *= info.params.leverage as f64;
                        items.push(item_order.convert(
                            &self.rest.coin_to_asset.token_ind,
                            self.rest.coin_meta[order.symbol.base.as_str()],
                            6,
                        )?);
                    }
                }
                Actions::Order(BulkOrder {
                    orders: items,
                    grouping,
                    builder: None,
                })
            }
            AsyncCmd::AmendOrder(order) => {
                let modify_order: ClientOrderRequest = order.clone().into();
                Actions::BatchModify(BulkModify {
                    modifies: vec![ModifyRequest {
                        oid: order.id.parse()?,
                        order: modify_order.convert(
                            &self.rest.coin_to_asset.token_ind,
                            self.rest.coin_meta[order.symbol.base.as_str()],
                            6,
                        )?,
                    }],
                })
            }
            AsyncCmd::CancelOrder(info) => {
                let asset = self
                    .rest
                    .coin_to_asset
                    .token_ind
                    .get(&info.symbol.base)
                    .ok_or(qerror!("symbol not support:{}", info.symbol))?;
                match info.order_id {
                    OrderId::Id(id) => Actions::Cancel(BulkCancel {
                        cancels: vec![CancelRequest {
                            asset: *asset as u32,
                            oid: id.parse()?,
                        }],
                    }),
                    OrderId::ClientOrderId(cid) => Actions::CancelByCloid(BulkCancelCloid {
                        cancels: vec![CancelRequestCloid {
                            asset: *asset as u32,
                            cloid: cid,
                        }],
                    }),
                }
            }
            AsyncCmd::BatchCancelOrder(symbol) => {
                let orders = self.rest.get_open_orders(symbol.clone()).await?;
                let mut transformed_cancels = Vec::new();
                for order in orders {
                    if order.symbol.base == symbol.base {
                        let asset = self
                            .rest
                            .coin_to_asset
                            .token_ind
                            .get(&symbol.base)
                            .ok_or(qerror!("symbol not support:{}", symbol))?;
                        transformed_cancels.push(CancelRequest {
                            asset: *asset as u32,
                            oid: order.id.parse()?,
                        });
                        //缓存一份 oid 带后续备用
                        self.order_ids.insert(order.id.clone());
                    }
                }
                Actions::Cancel(BulkCancel {
                    cancels: transformed_cancels,
                })
            }
            AsyncCmd::BatchCancelOrderByIds(info) => {
                let asset = if let Some(symbol) = info.symbol {
                    let &asset = self
                        .rest
                        .coin_to_asset
                        .token_ind
                        .get(&symbol.base)
                        .ok_or(qerror!("symbol not support:{}", symbol))?;
                    asset
                } else {
                    return Err(qerror!("symbol is None"));
                };
                match info.ids {
                    Some(ids) => {
                        let mut transformed_cancels = Vec::new();
                        for id in ids {
                            transformed_cancels.push(CancelRequest {
                                asset: asset as u32,
                                oid: id.parse()?,
                            });
                        }
                        Actions::Cancel(BulkCancel {
                            cancels: transformed_cancels,
                        })
                    }
                    None => {
                        if let Some(cids) = info.cids {
                            let mut transformed_cancels = Vec::new();
                            for cid in cids {
                                transformed_cancels.push(CancelRequestCloid {
                                    asset: asset as u32,
                                    cloid: cid,
                                });
                            }
                            Actions::CancelByCloid(BulkCancelCloid {
                                cancels: transformed_cancels,
                            })
                        } else {
                            return Err(qerror!("ids and cids are None"));
                        }
                    }
                }
            }
        };
        //将action当签名参数
        let nonce = util::next_nonce();
        let connection_id = action.hash(nonce, None)?;
        let wallet: LocalWallet = self.config.secret.parse()?;
        let signature = sign_typed_data(
            &l1::Agent {
                source: if self.config.is_testnet {
                    String::from("b")
                } else {
                    String::from("a")
                }, //"a"主网,"b"测试网
                connection_id,
            },
            &wallet,
        )?;
        let payload = ExchangePayload {
            action: sonic_rs::to_value(&action)?,
            signature,
            nonce,
            is_frontend: None,
            vault_address: None,
            expires_after: None,
        };
        let data = WsRequest::<ExchangePayload> {
            method: "post".to_string(),
            id: req_id,
            request: Request {
                r#type: "action".to_string(),
                payload,
            },
        };
        let req = sonic_rs::to_vec(&data)?;
        Ok(req)
    }
    async fn handle_message<H: AsyncResHandle>(
        &mut self,
        account_id: usize,
        handler: &H,
        rsp_msg: Value,
    ) -> Result<()> {
        info!("rsp_msg={}", rsp_msg);
        let rsp: WsResponse<Status> = sonic_rs::from_value(&rsp_msg)?;
        if let Some(data) = rsp.data {
            info!("data={:?}", data);
            if let Some(cached_req) = self.req_cache.remove(&data.id) {
                info!("cached_req={:?}", cached_req);
                let index = self.req_queue.iter().position(|&id| id == data.id);
                if let Some(index) = index {
                    info!("index={:?}", index);
                    self.req_queue.remove(index);
                }
                match cached_req.cmd {
                    AsyncCmd::PlaceOrder(info) => {
                        let result = match data
                            .response
                            .payload
                            .response
                            .data
                            .statuses
                            .first()
                            .unwrap_or(&define::model::Statuse::Error(format!(
                                "{} async place order error",
                                self.exchange()
                            ))) {
                            define::model::Statuse::Resting(resting) => Ok(resting.oid.to_string()),
                            define::model::Statuse::Error(err) => Err(qerror!("{}", err)),
                            define::model::Statuse::Success => Err(qerror!("not find order id")),
                            define::model::Statuse::Filled(filled) => Ok(filled.oid.to_string()),
                        };
                        let res = PlaceOrderResult {
                            result,
                            order: info.order.clone(),
                        };
                        handler.handle_post_order(account_id, data.id, res).await?;
                    }
                    AsyncCmd::BatchPlaceOrder(info) => {
                        let mut res = quant_common::base::BatchOrderRsp {
                            success_list: Vec::new(),
                            failure_list: Vec::new(),
                        };
                        data.response
                            .payload
                            .response
                            .data
                            .statuses
                            .into_iter()
                            .for_each(|item| match item {
                                define::model::Statuse::Resting(resting) => {
                                    res.success_list.push(SuccessOrder {
                                        id: Some(resting.oid.to_string()),
                                        cid: resting.cloid.clone(),
                                    });
                                }
                                define::model::Statuse::Error(e) => {
                                    res.failure_list.push(FailOrder {
                                        id: None,
                                        cid: None,
                                        error_code: 0,
                                        error: e,
                                    });
                                }
                                define::model::Statuse::Success => (),
                                define::model::Statuse::Filled(filled) => {
                                    res.success_list.push(SuccessOrder {
                                        id: Some(filled.oid.to_string()),
                                        cid: filled.cloid.clone(),
                                    });
                                }
                            });
                        //附加
                        info.orders.iter().for_each(|v| {
                            if !res.success_list.iter().any(|item| item.cid == v.cid) {
                                res.failure_list.push(FailOrder {
                                    cid: v.cid.clone(),
                                    id: None,
                                    error_code: 0,
                                    error: "order failed".to_string(),
                                });
                            }
                        });
                        info!("res={:?}", res);
                        handler
                            .handle_post_batch_order(account_id, data.id, Ok(res))
                            .await?;
                    }
                    AsyncCmd::AmendOrder(order) => {
                        let result = match data
                            .response
                            .payload
                            .response
                            .data
                            .statuses
                            .first()
                            .unwrap_or(&define::model::Statuse::Error(format!(
                                "{} async place order error",
                                self.exchange()
                            ))) {
                            define::model::Statuse::Resting(resting) => Ok(resting.oid.to_string()),
                            define::model::Statuse::Error(err) => Err(qerror!("{}", err)),
                            define::model::Statuse::Success => Err(qerror!("not find order id")),
                            define::model::Statuse::Filled(filled) => Ok(filled.oid.to_string()),
                        };
                        let res = AmendOrderResult {
                            result,
                            order: order.clone(),
                        };
                        handler.handle_amend_order(account_id, data.id, res).await?;
                    }
                    AsyncCmd::CancelOrder(info) => {
                        let mut result = Ok(());
                        for status in data.response.payload.response.data.statuses {
                            if let define::model::Statuse::Error(e) = status {
                                result = Err(qerror!("order cancel faile:{}", e));
                            }
                        }
                        let res = CancelOrderResult {
                            result,
                            order_id: info.order_id.clone(),
                            symbol: info.symbol.clone(),
                        };
                        handler
                            .handle_cancel_order(account_id, data.id, res)
                            .await?;
                    }
                    AsyncCmd::BatchCancelOrder(symbol) => {
                        let mut res = BatchOrderRsp {
                            success_list: Vec::new(),
                            failure_list: Vec::new(),
                        };
                        data.response
                            .payload
                            .response
                            .data
                            .statuses
                            .into_iter()
                            .for_each(|item| match item {
                                define::model::Statuse::Resting(resting) => {
                                    res.success_list.push(SuccessOrder {
                                        id: Some(resting.oid.to_string()),
                                        cid: resting.cloid.clone(),
                                    });
                                }
                                define::model::Statuse::Error(e) => {
                                    res.failure_list.push(FailOrder {
                                        id: None,
                                        cid: None,
                                        error_code: 0,
                                        error: e,
                                    });
                                }
                                define::model::Statuse::Success => (),
                                define::model::Statuse::Filled(filled) => {
                                    res.success_list.push(SuccessOrder {
                                        id: Some(filled.oid.to_string()),
                                        cid: filled.cloid.clone(),
                                    });
                                }
                            });
                        //http 获取当前所有挂单ids 和之前存储ids比较，看谁取消失败了，谁取消成功了
                        let orders = self.rest.get_open_orders(symbol.clone()).await?;
                        if !orders.is_empty() {
                            //如果orders中有，order_ids中也有，则该订单取消失败
                            orders.iter().for_each(|v| {
                                if self.order_ids.contains(&v.id) {
                                    res.failure_list.push(FailOrder {
                                        id: Some(v.id.clone()),
                                        cid: v.cid.clone(),
                                        error_code: 0,
                                        error: "订单取消失败".to_string(),
                                    });
                                }
                            });
                            // 如果order_ids中有，orders中没有，则该订单取消成功
                            self.order_ids.iter().for_each(|order_id| {
                                if !orders.iter().any(|v| v.id == *order_id) {
                                    res.success_list.push(SuccessOrder {
                                        id: Some(order_id.clone()),
                                        cid: None, // 如果需要 cid，需要从其他地方获取
                                    });
                                }
                            });
                            res.success_list.retain(|v| v.id.is_some());
                        } else {
                            res.success_list = self
                                .order_ids
                                .iter()
                                .map(|order_id| SuccessOrder {
                                    id: Some(order_id.clone()),
                                    cid: None, // 如果需要 cid，需要从其他地方获取
                                })
                                .collect::<Vec<_>>();
                        }

                        handler
                            .handle_batch_cancel_order(account_id, data.id, Ok(res))
                            .await?;
                    }
                    AsyncCmd::BatchCancelOrderByIds(info) => {
                        let mut res = BatchOrderRsp {
                            success_list: Vec::new(),
                            failure_list: Vec::new(),
                        };
                        data.response
                            .payload
                            .response
                            .data
                            .statuses
                            .into_iter()
                            .for_each(|item| match item {
                                define::model::Statuse::Resting(resting) => {
                                    res.success_list.push(SuccessOrder {
                                        id: Some(resting.oid.to_string()),
                                        cid: resting.cloid.clone(),
                                    });
                                }
                                define::model::Statuse::Error(e) => {
                                    res.failure_list.push(FailOrder {
                                        id: None,
                                        cid: None,
                                        error_code: 0,
                                        error: e,
                                    });
                                }
                                define::model::Statuse::Success => (),
                                define::model::Statuse::Filled(filled) => {
                                    res.success_list.push(SuccessOrder {
                                        id: Some(filled.oid.to_string()),
                                        cid: filled.cloid.clone(),
                                    });
                                }
                            });
                        //http 获取当前所有挂单ids 和当前ids比较，看谁取消失败了，谁取消成功了
                        if let Some(symbol) = info.symbol.clone() {
                            let orders = self.rest.get_open_orders(symbol.clone()).await?;
                            match info.ids.clone() {
                                Some(ids) => {
                                    if !orders.is_empty() {
                                        //如果orders中有，ids中也有，则该订单取消失败
                                        orders.iter().for_each(|v| {
                                            if ids.contains(&v.id) {
                                                res.failure_list.push(FailOrder {
                                                    id: Some(v.id.clone()),
                                                    cid: v.cid.clone(),
                                                    error_code: 0,
                                                    error: "订单取消失败".to_string(),
                                                })
                                            };
                                        });
                                        // 如果ids中有，orders中没有，则该订单取消成功
                                        ids.iter().for_each(|id| {
                                            if !orders.iter().any(|v| v.id == *id) {
                                                res.success_list.push(SuccessOrder {
                                                    id: Some(id.clone()),
                                                    cid: None, // 如果需要 cid，需要从其他地方获取
                                                });
                                            }
                                        });
                                    } else {
                                        //全部取消成功
                                        res.success_list = ids
                                            .iter()
                                            .map(|id| SuccessOrder {
                                                id: Some(id.clone()),
                                                cid: None, // 如果需要 cid，需要从其他地方获取
                                            })
                                            .collect::<Vec<_>>();
                                    }
                                }
                                None => match info.cids.clone() {
                                    Some(cid) => {
                                        if !orders.is_empty() {
                                            //如果orders中有，cids中也有，则该订单取消失败
                                            orders.iter().for_each(|v| {
                                                if let Some(id) = &v.cid {
                                                    if cid.contains(id) {
                                                        res.failure_list.push(FailOrder {
                                                            id: Some(v.id.clone()),
                                                            cid: v.cid.clone(),
                                                            error_code: 0,
                                                            error: "订单取消失败".to_string(),
                                                        })
                                                    }
                                                }
                                            });
                                            // 如果cids中有，orders中没有，则该订单取消成功
                                            cid.iter().for_each(|cid| {
                                                if !orders.iter().any(|v| {
                                                    if let Some(vcid) = &v.cid {
                                                        vcid == cid
                                                    } else {
                                                        false
                                                    }
                                                }) {
                                                    res.success_list.push(SuccessOrder {
                                                        id: None,
                                                        cid: Some(cid.clone()),
                                                    });
                                                }
                                            })
                                        } else {
                                            res.success_list = cid
                                                .iter()
                                                .map(|cid| SuccessOrder {
                                                    id: None,
                                                    cid: Some(cid.clone()),
                                                })
                                                .collect::<Vec<_>>();
                                        }
                                    }
                                    None => return Ok(()),
                                },
                            }
                        }
                        handler
                            .handle_batch_cancel_order(account_id, data.id, Ok(res))
                            .await?;
                    }
                }
            }
        }
        Ok(())
    }
}

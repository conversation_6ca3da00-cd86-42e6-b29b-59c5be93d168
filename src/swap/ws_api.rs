use std::{
    collections::VecDeque,
    time::{Duration, Instant},
};

use async_channel::Receiver;
use fastwebsockets::{Frame, OpCode};
use quant_common::{
    base::{
        traits::ws_api::{AsyncResHandle, WebSocketAPI},
        AsyncCmd, ExConfig, Exchange, OrderId,
    },
    fast_proxy::connect_ws_with_proxy,
    qerror,
    utils::time,
    QuantError, Result,
};
use rustc_hash::FxHashMap;

use crate::swap::ws::generate_login;

use super::model::*;

const MAX_RETRY: usize = 10;
const REQUEST_TIMEOUT_SECS: u64 = 60; // 请求超时时间（秒）
const MAX_CACHE_SIZE: usize = 1000; // 最大缓存条目数
const PING_INTERVAL: u64 = 20; // 心跳间隔（秒），增加到20秒
const PING_TIMEOUT: u64 = 20; // ping超时时间（秒），增加到20秒
const MAX_RECONNECT_DELAY: u64 = 60; // 最大重连延迟（秒）

#[derive(Clone, Debug)]
struct CachedRequest {
    cmd: AsyncCmd,
    timestamp: Instant,
}

#[derive(Clone, Debug)]
pub struct OkxSwapWsApi {
    url_private: String,
    config: ExConfig,
    req_cache: FxHashMap<u64, CachedRequest>,
    req_queue: VecDeque<u64>, // 按时间顺序存储请求ID，用于超时检查
}

impl OkxSwapWsApi {
    pub async fn new(config: ExConfig) -> Self {
        // let url_private = if config.is_testnet {
        //     WS_TEST_URL_PRIVATE
        // } else if config
        //     .params
        //     .get("aws")
        //     .map(|v| v == "true")
        //     .unwrap_or(false)
        // {
        //     WS_AWS_URL_PRIVATE
        // } else {
        //     WS_URL_PRIVATE
        // };
        let default_host = if config.is_testnet {
            WS_TEST_URL
        } else if config
            .params
            .get("aws")
            .map(|v| v == "true")
            .unwrap_or(false)
        {
            WS_AWS_URL
        } else {
            WS_URL
        }
        .to_string();

        let host = config
            .ws_api_host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(default_host);

        let url_private = format!("wss://{host}/ws/v5/private");

        OkxSwapWsApi {
            url_private,
            config,
            req_cache: FxHashMap::default(),
            req_queue: VecDeque::with_capacity(MAX_CACHE_SIZE),
        }
    }

    fn cache_request_if_needed(&mut self, req_id: u64, cmd: &AsyncCmd) {
        match cmd {
            AsyncCmd::PlaceOrder(_)
            | AsyncCmd::AmendOrder(_)
            | AsyncCmd::CancelOrder(_)
            | AsyncCmd::BatchPlaceOrder(_)
            // | AsyncCmd::BatchCancelOrder(_)
            | AsyncCmd::BatchCancelOrderByIds(_) => {
                // 清理缓存，防止无限增长
                self.cleanup_cache();

                // 添加新请求到缓存
                self.req_cache.insert(
                    req_id,
                    CachedRequest {
                        cmd: cmd.clone(),
                        timestamp: Instant::now(),
                    },
                );
                self.req_queue.push_back(req_id);

                debug!(
                    "已缓存请求: req_id={}, cmd_type={}",
                    req_id,
                    self.get_cmd_type_str(cmd)
                );
            }
            _ => {}
        }
    }

    // 清理超时和过多的缓存条目
    fn cleanup_cache(&mut self) {
        // 检查并移除超时请求
        let now = Instant::now();
        let timeout = Duration::from_secs(REQUEST_TIMEOUT_SECS);

        // 从队列前端检查，移除超时的请求
        while let Some(&req_id) = self.req_queue.front() {
            if let Some(cached_req) = self.req_cache.get(&req_id) {
                if now.duration_since(cached_req.timestamp) > timeout {
                    // 该请求已超时，移除
                    self.req_queue.pop_front();
                    self.req_cache.remove(&req_id);
                    warn!("请求超时已自动清理: req_id={}", req_id);
                } else {
                    // 如果最早的请求没有超时，后面的更不会超时
                    break;
                }
            } else {
                // 队列中的ID不在缓存中，清理队列
                self.req_queue.pop_front();
            }
        }

        // 如果缓存大小超过上限，移除最早的请求
        while self.req_cache.len() > MAX_CACHE_SIZE {
            if let Some(req_id) = self.req_queue.pop_front() {
                self.req_cache.remove(&req_id);
                warn!("缓存已达到最大大小，移除最早的请求: req_id={}", req_id);
            } else {
                break;
            }
        }
    }

    // 删除get_cmd_type方法，保留get_cmd_type_str用于日志记录
    fn get_cmd_type_str(&self, cmd: &AsyncCmd) -> &'static str {
        match cmd {
            AsyncCmd::PlaceOrder(_) => "PlaceOrder",
            AsyncCmd::CancelOrder(_) => "CancelOrder",
            AsyncCmd::AmendOrder(_) => "AmendOrder",
            AsyncCmd::BatchPlaceOrder(_) => "BatchPlaceOrder",
            AsyncCmd::BatchCancelOrder(_) => "BatchCancelOrder",
            AsyncCmd::BatchCancelOrderByIds(_) => "BatchCancelOrderByIds",
        }
    }

    async fn handle_error<T>(
        &self,
        handler: &T,
        req_id: u64,
        err: QuantError,
        account_id: usize,
        cmd_type: AsyncCmd,
    ) -> Result<()>
    where
        T: AsyncResHandle,
    {
        match cmd_type {
            AsyncCmd::PlaceOrder(place_cmd) => {
                let result = quant_common::base::PlaceOrderResult {
                    result: Err(err),
                    order: place_cmd.order,
                };
                handler
                    .handle_post_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let result = quant_common::base::CancelOrderResult {
                    result: Err(err),
                    order_id: cancel_cmd.order_id,
                    symbol: cancel_cmd.symbol,
                };
                handler
                    .handle_cancel_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchPlaceOrder(_) => {
                handler
                    .handle_post_batch_order(account_id, req_id, Err(err))
                    .await?;
            }
            AsyncCmd::AmendOrder(order) => {
                let result = quant_common::base::AmendOrderResult {
                    result: Err(err),
                    order,
                };
                handler
                    .handle_amend_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchCancelOrder(_) => {
                handler
                    .handle_batch_cancel_order(account_id, req_id, Err(err))
                    .await?;
            }
            AsyncCmd::BatchCancelOrderByIds(_) => {
                handler
                    .handle_batch_cancel_order_by_ids(account_id, req_id, Err(err))
                    .await?;
            }
        }

        Ok(())
    }

    // 更新handle_response方法，直接使用AsyncCmd进行模式匹配
    async fn handle_response<T>(
        &self,
        handler: &T,
        account_id: usize,
        req_id: u64,
        rsp: WsApiRsp,
        cmd: AsyncCmd,
    ) -> Result<()>
    where
        T: AsyncResHandle,
    {
        match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                let order_rsp = rsp.into_common_order_rsp();
                let result = quant_common::base::PlaceOrderResult {
                    result: order_rsp,
                    order: place_cmd.order,
                };
                handler
                    .handle_post_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let cancel_rsp = rsp.into_common_order_rsp();
                let result = quant_common::base::CancelOrderResult {
                    result: cancel_rsp.map(|_| ()),
                    order_id: cancel_cmd.order_id,
                    symbol: cancel_cmd.symbol,
                };
                handler
                    .handle_cancel_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::AmendOrder(order) => {
                let amend_rsp = rsp.into_common_order_rsp();
                let result = quant_common::base::AmendOrderResult {
                    result: amend_rsp,
                    order,
                };
                handler
                    .handle_amend_order(account_id, req_id, result)
                    .await?;
            }
            AsyncCmd::BatchPlaceOrder(_batch_cmd) => {
                let batch_rsp = rsp.into_batch_common_order_rsp();
                handler
                    .handle_post_batch_order(account_id, req_id, batch_rsp)
                    .await?;
            }
            AsyncCmd::BatchCancelOrderByIds(_batch_cmd) => {
                let batch_rsp = rsp.into_batch_common_order_rsp();
                handler
                    .handle_batch_cancel_order_by_ids(account_id, req_id, batch_rsp)
                    .await?;
            }
            cmd => {
                warn!(
                    "收到不支持的命令类型响应: req_id={}, cmd_type={:?}",
                    req_id,
                    self.get_cmd_type_str(&cmd)
                );
            }
        }

        Ok(())
    }
}

impl WebSocketAPI for OkxSwapWsApi {
    fn exchange(&self) -> Exchange {
        Exchange::OkxSwap
    }

    async fn run<H: AsyncResHandle>(
        mut self,
        account_id: usize,
        handler: H,
        mut rx: Receiver<(u64, AsyncCmd)>,
    ) -> quant_common::Result<()> {
        let mut retry = 0;
        let mut reconnect_delay = 5; // 初始重连延迟5秒

        loop {
            let start = time();
            match self.run_inner(account_id, &handler, &mut rx).await {
                Ok(_) => {
                    warn!("WebSocket连接正常断开，将在{}秒后尝试重连", reconnect_delay);
                    // 连接正常断开，重置重试计数和延迟
                    if time() - start > 300 {
                        // 如果连接维持了超过5分钟，重置重试计数
                        retry = 0;
                        reconnect_delay = 5;
                    }
                }
                Err(e) => {
                    error!("WebSocket连接错误: {:?}", e);

                    // 判断是否为短期内频繁失败
                    if time() - start < 60 {
                        retry += 1;
                        // 使用指数退避策略增加重连延迟，最大到MAX_RECONNECT_DELAY
                        reconnect_delay = std::cmp::min(reconnect_delay * 2, MAX_RECONNECT_DELAY);

                        if retry > MAX_RETRY {
                            let err = qerror!(
                                "OKX Swap WebSocket重试失败次数过多: {e}，请检查网络或API配置"
                            );
                            return Err(err);
                        }
                    } else {
                        // 如果连接维持了一段时间才失败，不计入重试次数
                        debug!(
                            "WebSocket连接维持了{}秒后断开，不计入重试失败次数",
                            time() - start
                        );
                        retry = 0;
                        reconnect_delay = 5; // 重置重连延迟
                    }
                }
            }

            // 清理资源，确保下次连接前状态干净
            self.req_cache.clear();
            self.req_queue.clear();

            debug!("等待{}秒后重新连接...", reconnect_delay);
            tokio::time::sleep(tokio::time::Duration::from_secs(reconnect_delay)).await;
        }
    }
}

impl OkxSwapWsApi {
    async fn run_inner<H: AsyncResHandle>(
        &mut self,
        account_id: usize,
        handler: &H,
        rx: &mut Receiver<(u64, AsyncCmd)>,
    ) -> Result<()> {
        debug!("正在建立OKX Swap WebSocket连接: {}", self.url_private);
        let mut ws = connect_ws_with_proxy(&self.url_private).await?;

        // 首先进行登录
        let login_message = self.login().await?;
        debug!("发送登录请求");
        ws.write_frame(Frame::text(login_message.as_bytes().into()))
            .await?;

        // 等待登录响应
        let login_rsp = ws.read_frame().await?;
        match login_rsp.opcode {
            OpCode::Text => {
                let rsp_str = String::from_utf8_lossy(&login_rsp.payload);
                debug!("登录响应: {}", rsp_str);
                // 验证登录是否成功
                if rsp_str.contains("error") {
                    return Err(qerror!("登录失败: {}", rsp_str));
                }
                // info!("OKX Swap WebSocket登录成功");
            }
            _ => return Err(qerror!("收到意外的登录响应类型")),
        }

        let cleanup_interval = Duration::from_secs(REQUEST_TIMEOUT_SECS / 2);
        let mut last_cleanup = Instant::now();

        // 心跳机制优化
        let mut last_message_time = Instant::now();
        let heartbeat_interval = Duration::from_secs(PING_INTERVAL);
        let ping_timeout = Duration::from_secs(PING_TIMEOUT);
        let mut heartbeat_check_interval =
            tokio::time::interval(tokio::time::Duration::from_secs(5));

        // 加入连续ping失败计数
        let mut ping_sent = false;
        let mut ping_failures = 0;
        let max_ping_failures = 3; // 允许最多连续3次ping失败才断开

        loop {
            // 定期检查和清理过期请求
            if last_cleanup.elapsed() > cleanup_interval {
                self.cleanup_cache();
                last_cleanup = Instant::now();
            }

            tokio::select! {
                cmd = rx.recv() => {
                    debug!("收到请求");
                    let (req_id, cmd) = match cmd {
                        Ok((req_id, cmd)) => (req_id, cmd),
                        Err(e) => {
                            warn!("异步通道已关闭: {e:?}");
                            break;
                        }
                    };
                    // 缓存需要的请求类型
                    self.cache_request_if_needed(req_id, &cmd);

                    let message = match self.req_to_message(req_id, cmd).await {
                        Ok(message) => message,
                        Err(e) => {
                            error!("生成请求消息错误: {:?}", e);
                            if let Some(cached_req) = self.req_cache.remove(&req_id) {
                                let index = self.req_queue.iter().position(|&id| id == req_id);
                                if let Some(index) = index {
                                    self.req_queue.remove(index);
                                }
                                self.handle_error(handler, req_id, e, account_id, cached_req.cmd).await?;
                            }
                            continue;
                        }
                    };

                    // 发送命令并更新最后消息时间
                    match ws.write_frame(message).await {
                        Ok(()) => {
                            // 成功发送消息也更新最后消息时间
                            last_message_time = Instant::now();
                        }
                        Err(e) => {
                            error!("发送请求失败: {:?}", e);
                            let err = qerror!("发送请求失败: {e}");
                            if let Some(cached_req) = self.req_cache.remove(&req_id) {
                                let index = self.req_queue.iter().position(|&id| id == req_id);
                                if let Some(index) = index {
                                    self.req_queue.remove(index);
                                }
                                self.handle_error(handler, req_id, err, account_id, cached_req.cmd).await?;
                            }
                            return Err(e.into());
                        }
                    }
                }
                rsp = ws.read_frame() => {
                    // 更新最后收到消息的时间
                    last_message_time = Instant::now();

                    // 收到任何响应后重置ping状态
                    if ping_sent {
                        ping_sent = false;
                        ping_failures = 0; // 重置失败计数
                        debug!("收到响应，重置心跳状态");
                    }

                    match rsp {
                        Ok(rsp) => {
                            let rsp: WsApiRsp = match rsp.opcode {
                                OpCode::Text => {
                                    let rsp_str = String::from_utf8_lossy(&rsp.payload);

                                    // 改进pong响应处理，更宽容地识别pong
                                    if rsp_str.trim() == "pong" || rsp_str.contains("pong") {
                                        debug!("收到pong响应");
                                        continue;
                                    }

                                    match sonic_rs::from_slice(&rsp.payload) {
                                        Ok(r) => r,
                                        Err(e) => {
                                            // 更宽容地处理非JSON响应
                                            if rsp_str.contains("pong") {
                                                debug!("收到非标准pong响应: {}", rsp_str);
                                                continue;
                                            }

                                            // 记录更详细的日志，但不中断连接
                                            error!("解析响应JSON失败: {:?}, 原始数据: {}", e, rsp_str);
                                            continue;
                                        }
                                    }
                                }
                                OpCode::Binary => {
                                    match sonic_rs::from_slice(&rsp.payload) {
                                        Ok(r) => r,
                                        Err(e) => {
                                            error!("解析二进制响应失败: {:?}", e);
                                            continue;
                                        }
                                    }
                                }
                                OpCode::Ping => {
                                    debug!("收到ping消息，发送pong响应");
                                    match ws.write_frame(Frame::pong(rsp.payload)).await {
                                        Ok(_) => {
                                            debug!("成功发送pong响应");
                                        },
                                        Err(e) => {
                                            error!("发送pong响应失败: {:?}", e);
                                            return Err(e.into());
                                        }
                                    }
                                    continue;
                                }
                                OpCode::Pong => {
                                    debug!("收到标准pong消息");
                                    continue;
                                },
                                OpCode::Close => {
                                    info!("收到服务器断开连接消息");
                                    break;
                                }
                                _ => continue,
                            };

                            // 处理业务响应
                            let req_id = rsp.id();

                            // 从缓存中查找并处理请求
                            if let Some(cached_req) = self.req_cache.remove(&req_id) {
                                let index = self.req_queue.iter().position(|&id| id == req_id);
                                if let Some(index) = index {
                                    self.req_queue.remove(index);
                                }

                                let cmd_type = self.get_cmd_type_str(&cached_req.cmd);
                                debug!("处理响应: req_id={}, cmd_type={}", req_id, cmd_type);

                                match self.handle_response(handler, account_id, req_id, rsp, cached_req.cmd).await {
                                    Ok(_) => {},
                                    Err(e) => {
                                        error!("处理响应错误: {:?}", e);
                                        // 这里不返回错误，继续处理其他请求
                                    }
                                }
                            } else {
                                debug!("收到未知请求ID的响应: {}", req_id);
                            }
                        },
                        Err(e) => {
                            error!("读取WebSocket消息错误: {:?}", e);
                            return Err(e.into());
                        }
                    }
                }
                _ = heartbeat_check_interval.tick() => {
                    let elapsed = last_message_time.elapsed();

                    // 更新心跳检测逻辑
                    if elapsed >= heartbeat_interval && !ping_sent {
                        debug!("发送心跳ping消息 (已{}秒无活动)", elapsed.as_secs());

                        match ws.write_frame(Frame::text(b"ping".to_vec().into())).await {
                            Ok(_) => {
                                ping_sent = true;
                                // 不重置last_message_time，让它继续计时以监测超时
                            }
                            Err(e) => {
                                error!("发送ping消息失败: {:?}", e);
                                ping_failures += 1;

                                if ping_failures >= max_ping_failures {
                                    error!("连续{}次发送ping失败，准备重连", ping_failures);
                                    return Err(e.into());
                                }

                                // 如果ping发送失败但未达到最大失败次数，继续等待
                                ping_sent = false;
                                continue;
                            }
                        }
                    }
                    // 优化ping超时检测，增加容错
                    else if ping_sent && last_message_time.elapsed() >= ping_timeout {
                        ping_failures += 1;
                        ping_sent = false; // 重置ping状态，下次可以重试

                        warn!("心跳超时，未收到pong响应 (失败计数: {}/{})",
                              ping_failures, max_ping_failures);

                        if ping_failures >= max_ping_failures {
                            warn!("连续{}次心跳超时，准备重连", ping_failures);
                            break;
                        }
                    }
                }
            }
        }

        // 在返回前尝试优雅关闭连接
        debug!("尝试关闭WebSocket连接");
        let _ = ws.write_frame(Frame::close(1000, b"Normal closure")).await;

        Ok(())
    }

    async fn login(&mut self) -> Result<String> {
        let api_key = self.config.key.clone();
        let secret_key = self.config.secret.clone();
        let passphrase = self.config.passphrase.clone();
        let login = generate_login(&api_key, &secret_key, &passphrase);
        let req_str = sonic_rs::to_string(&login)?;
        Ok(req_str)
    }

    async fn req_to_message(&self, req_id: u64, cmd: AsyncCmd) -> Result<Frame> {
        let req = match cmd {
            AsyncCmd::PlaceOrder(place_cmd) => {
                WsApiReq::post_order(req_id, place_cmd.order, place_cmd.params)?
            }
            AsyncCmd::CancelOrder(cancel_cmd) => {
                let (symbol, order_id) = (cancel_cmd.symbol, cancel_cmd.order_id);
                let (order_id, client_id) = match order_id {
                    OrderId::Id(id) => (Some(id), None),
                    OrderId::ClientOrderId(cid) => (None, Some(cid)),
                };
                WsApiReq::cancel_order(req_id, symbol, order_id, client_id)?
            }
            AsyncCmd::AmendOrder(order) => WsApiReq::amend_order(req_id, order)?,
            AsyncCmd::BatchPlaceOrder(batch_cmd) => {
                WsApiReq::batch_post_order(req_id, batch_cmd.orders, batch_cmd.params)?
            }
            AsyncCmd::BatchCancelOrder(_) => return Err(qerror!("交易所未支持批量取消订单")),
            AsyncCmd::BatchCancelOrderByIds(batch_cmd) => WsApiReq::batch_cancel_order_by_ids(
                req_id,
                batch_cmd.symbol,
                batch_cmd.ids,
                batch_cmd.cids,
            )?,
        };
        debug!("发送请求: {:?}", req);
        Ok(Frame::text(serde_json::to_vec(&req)?.into()))
    }
}

use futures_util::{SinkExt, StreamExt};
use quant_common::base::*;
use quant_common::order_utils::OrderTradeCache;
use quant_common::*;
use rustc_hash::{FxHashMap, FxHashSet};
use sonic_rs::from_str;
use tokio_tungstenite::tungstenite::protocol::Message;

use crate::swap::model::*;
use crate::swap::rest::GateSwap;

#[derive(Debug)]
pub struct GateSwapWs {
    exchange: Exchange,
    url: String,
    rest: GateSwap,
    funding_filter: FxHashSet<String>,
    limit: usize,
    instrument: FxHashMap<String, String>,
    contract_multiplier: FxHashMap<String, f64>,
    last_bbo: FxHashMap<String, BboTicker>,

    order_cache: OrderTradeCache,
}

impl GateSwapWs {
    pub async fn new(config: ExConfig) -> Self {
        let host = config
            .ws_host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(if config.is_colo {
                WS_COLO_HOST.to_string()
            } else {
                WS_HOST.to_string()
            });
        let url = if config.is_testnet {
            format!("wss://{WS_TEST_HOST}/v4/ws/futures/usdt")
        } else {
            format!("wss://{host}/v4/ws/usdt")
        };
        let rest = GateSwap::new(config.clone()).await;
        let instruments = rest.contracts().await.unwrap_or_default();
        let mut instrument = FxHashMap::default();
        let mut contract_multiplier = FxHashMap::default();
        for ins in instruments {
            instrument.insert(ins.name.to_string(), ins.order_price_round.to_string());
            contract_multiplier.insert(ins.name.to_string(), ins.quanto_multiplier);
        }
        GateSwapWs {
            exchange: Exchange::GateSwap,
            url,
            rest,
            limit: usize::MAX,
            funding_filter: FxHashSet::default(),
            last_bbo: FxHashMap::default(),
            instrument,
            contract_multiplier,
            order_cache: OrderTradeCache::default(),
        }
    }

    fn is_weburl(&self) -> bool {
        self.url == "wss://fx-webws.gateio.live/v4/ws/usdt"
            || self.url == "wss://webws.gateio.live/v4/ws/usdt"
            || self.url == "wss://ws.gate.io/v4/ws/usdt"
            || self.url == "wss://ws.gateio.io/v4/ws/usdt"
    }
}
impl WebSocket for GateSwapWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(&subs).await {
                Ok(_) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    warn!("websocket disconnect");
                }
                Err(e) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    error!("websocket connect error: {e:?}");
                    if time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            return Err(qerror!(
                                "GateSwapWs websocket retry failed too many times"
                            ));
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl GateSwapWs {
    async fn run_inner<H: WsHandler>(&mut self, subs: &Subscribes<H>) -> Result<()> {
        let handler = &subs.handler;
        let has_pri = subs.has_private();
        let has_pub = subs.has_public();

        let mut writer_pub_opt = None;
        let mut reader_pub_opt = None;
        let mut writer_pri_opt = None;
        let mut reader_pri_opt = None;

        if has_pub {
            let (writer_pub, reader_pub) = self.connect(true).await?;
            writer_pub_opt = Some(writer_pub);
            reader_pub_opt = Some(reader_pub);
        }

        if has_pri {
            let (writer_pri, reader_pri) = self.connect(false).await?;
            writer_pri_opt = Some(writer_pri);
            reader_pri_opt = Some(reader_pri);
        }

        handler.on_connected(self.exchange, subs.id).await?;

        if has_pub && let Some(ref mut writer_pub) = writer_pub_opt {
            self.sub_pub(writer_pub, &subs.channels).await?;
        }

        if has_pri && let Some(ref mut writer_pri) = writer_pri_opt {
            self.sub_pri(writer_pri, &subs.channels).await?;
        }

        let ping = Message::Ping(vec![]);
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(10));
        loop {
            tokio::select! {
                msg = async {
                    if let Some(ref mut reader_pub) = reader_pub_opt {
                        reader_pub.next().await
                    } else {
                        futures_util::future::pending().await
                    }
                } => {
                    let msg = match msg {
                        Some(Ok(x)) => x,
                        Some(Err(err)) => {
                            error!("receive err: {err:?}");
                            break;
                        }
                        None => {
                            error!("receive None");
                            break;
                        }
                    };
                    match &msg {
                        Message::Text(_) | Message::Binary(_) =>  {self.handle_msg(msg, subs).await},
                        Message::Ping(_) => {
                            let message = Message::Pong(vec![]);
                            if let Some(ref mut writer_pub) = writer_pub_opt {
                                writer_pub.send(message).await?;
                            }
                        },
                        Message::Close(_) => break,
                        _ => {},
                    }
                }
                msg = async {
                    if let Some(ref mut reader_pri) = reader_pri_opt {
                        reader_pri.next().await
                    } else {
                        futures_util::future::pending().await
                    }
                } => {
                    let msg = match msg {
                        Some(Ok(x)) => x,
                        Some(Err(err)) => {
                            error!("receive err: {err:?}");
                            break;
                        }
                        None => {
                            error!("receive None");
                            break;
                        }
                    };
                    match &msg {
                        Message::Text(_) | Message::Binary(_) =>  {self.handle_msg(msg, subs).await},
                        Message::Ping(_) => {
                            let message = Message::Pong(vec![]);
                            if let Some(ref mut writer_pri) = writer_pri_opt {
                                writer_pri.send(message).await?;
                            }
                        },
                        Message::Close(_) => break,
                        _ => {},
                    }
                }
                _ = ping_interval.tick() => {
                    if let Some(ref mut writer_pub) = writer_pub_opt {
                        writer_pub.send(ping.clone()).await?;
                    }
                    if let Some(ref mut writer_pri) = writer_pri_opt {
                        writer_pri.send(ping.clone()).await?;
                    }
                }
            }
        }
        Ok(())
    }

    async fn connect(&mut self, is_pub: bool) -> Result<(Writer, Reader), QuantError> {
        // let (ws_stream, _) = connect_async(self.url).await?;
        let ws_stream = if is_pub {
            connect_ws_with_proxy(&self.url).await?
        } else if self.is_weburl() {
            connect_ws_with_proxy(&format!("wss://{WS_HOST}/v4/ws/usdt")).await?
        } else {
            connect_ws_with_proxy(&self.url).await?
        };
        let (writer, reader) = ws_stream.split();
        Ok((writer, reader))
    }

    async fn handle_msg_inner<H: WsHandler>(
        &mut self,
        channel: Channel,
        result: &RawValue,
        subs: &base::Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        let json = result.get();
        match channel {
            Channel::BookTicker => {
                if subs.has_bbo() {
                    let book_ticker =
                        BboTicker::from(from_str::<GateBookTiker>(json).map_err(|e| {
                            qerror!("parse BookTicker error: {:?}, json: {}", e, json)
                        })?);
                    handler.on_bbo(book_ticker, self.exchange).await?;
                }
            }
            Channel::Tickers => {
                let tickers = from_str::<Vec<GateTicker>>(json)
                    .map_err(|e| qerror!("parse Tickers error: {:?}, json: {}", e, json))?;
                if subs.has_mark_price() {
                    for ticker in &tickers {
                        handler.on_mark_price(ticker.into(), self.exchange).await?;
                    }
                }

                if subs.has_funding() {
                    for ticker in tickers {
                        handler.on_funding(ticker.into(), self.exchange).await?;
                    }
                }
            }
            Channel::Orders => {
                if subs.has_order() || subs.has_order_and_fill() {
                    for order in from_str::<Vec<GateOrder>>(json)
                        .map_err(|e| qerror!("parse Orders error: {:?}, json: {}", e, json))?
                    {
                        let order: Order = order.into_order();
                        if subs.has_order_and_fill()
                            && let Some(order) = self.order_cache.process_order(order.clone())
                        {
                            handler.on_order_and_fill(order, subs.id).await?;
                        }
                        if subs.has_order() {
                            handler.on_order(order, subs.id).await?;
                        }
                    }
                }
            }
            Channel::Position => {
                if subs.has_position() {
                    let position = from_str::<Vec<GatePositionWs>>(json)
                        .map_err(|e| qerror!("parse Position error: {:?}, json: {}", e, json))?;
                    for position in position {
                        handler.on_position(position.into(), subs.id).await?;
                    }
                }
            }
            Channel::Ping | Channel::Empty => {}
            Channel::OrderBook => {
                if self.is_weburl() {
                    let mut depth = from_str::<OrderBooks>(json).map_err(|e| {
                        qerror!("parse OrderBookUpdate error: {:?}, json: {}", e, json)
                    })?;
                    let multiplier = self
                        .contract_multiplier
                        .get(&depth.contract.to_string())
                        .unwrap_or(&1.0);
                    depth.asks = depth
                        .asks
                        .iter()
                        .map(|item| OrderBookItem {
                            p: item.p,
                            s: item.s * multiplier,
                        })
                        .collect();
                    depth.bids = depth
                        .bids
                        .iter()
                        .map(|item| OrderBookItem {
                            p: item.p,
                            s: item.s * multiplier,
                        })
                        .collect();
                    if subs.has_depth() {
                        let depth: Depth = depth.clone().into();
                        handler
                            .on_depth(depth.truncate_limited(self.limit), self.exchange)
                            .await?;
                    }
                    if subs.has_bbo() {
                        let bbo: BboTicker = depth.into();
                        if !self.unchange(&bbo) {
                            handler.on_bbo(bbo, self.exchange).await?;
                        }
                    }
                } else if subs.has_depth() {
                    let depth = from_str::<OrderBooks>(json).map_err(|e| {
                        qerror!("parse OrderBookUpdate error: {:?}, json: {}", e, json)
                    })?;
                    let depth: Depth = depth.into();
                    handler
                        .on_depth(depth.truncate_limited(self.limit), self.exchange)
                        .await?;
                }
            }
            Channel::Trades => {
                if subs.has_trade() {
                    let trades = from_str::<Vec<GateTrade>>(json)
                        .map_err(|e| qerror!("parse Trades error: {:?}, json: {}", e, json))?;
                    for trade in trades {
                        handler.on_trade(trade.into(), self.exchange).await?;
                    }
                }
            }
            Channel::Balance => {
                let balance = from_str::<Vec<GateBalance>>(json)
                    .map_err(|e| qerror!("parse Balance error: {:?}, json: {}", e, json))?;
                for balance in balance {
                    if balance.r#type == *"fund"
                        && let Some(funding_fee) = balance.clone().into()
                    {
                        handler.on_funding_fee(funding_fee, subs.id).await?;
                    }
                    handler.on_balance(balance.into(), subs.id).await?;
                }
            }
            Channel::UserTrades => {
                if subs.has_order_and_fill() {
                    for fill in from_str::<Vec<GateFill>>(json)
                        .map_err(|e| qerror!("parse Orders error: {:?}, json: {}", e, json))?
                    {
                        let order: Order = fill.into();
                        if let Some(order) = self.order_cache.process_order(order) {
                            handler.on_order_and_fill(order, subs.id).await?;
                        }
                    }
                }
            }
            Channel::Candlesticks => {
                if subs.has_kline() {
                    let klines = from_str::<Vec<GateKline>>(json).map_err(|e| {
                        qerror!("parse Candlesticks error: {:?}, json: {}", e, json)
                    })?;
                    for kline in klines {
                        handler.on_kline(kline.into(), self.exchange).await?;
                    }
                }
            }
            chan => {
                warn!("unhandle {chan:?}");
            }
        }
        Ok(())
    }

    async fn handle_msg<H: WsHandler>(&mut self, data: Message, handler: &base::Subscribes<H>) {
        let msg = match &data {
            Message::Text(t) => sonic_rs::from_str::<GateEvent>(t),
            Message::Binary(b) => sonic_rs::from_slice::<GateEvent>(b),
            _ => unreachable!(),
        };
        match msg {
            Ok(msg) => {
                if let Err(e) = self
                    .handle_msg_inner(msg.channel, &msg.result, handler)
                    .await
                {
                    if msg.event != "subscribe" {
                        error!("handle message error: {e:?}");
                    } else {
                        let json = msg.result.get();
                        if let Ok(value) = sonic_rs::from_str::<SubscribeStatus>(json)
                            && value.status != "success"
                        {
                            error!("handle message error: {:?}", msg.error);
                        }
                    }
                }
            }
            Err(e) => {
                error!("parse message error: {e:?}");
            }
        }
    }

    pub(crate) async fn sub_pub(
        &mut self,
        w: &mut Writer,
        channels: &[SubscribeChannel],
    ) -> Result<()> {
        let subs: Vec<SubscribeChannel> = channels
            .iter()
            .filter(|x| {
                matches!(
                    x,
                    SubscribeChannel::MarkPrice(_)
                        | SubscribeChannel::Funding(_)
                        | SubscribeChannel::Bbo(_)
                        | SubscribeChannel::Depth(_)
                        | SubscribeChannel::Trade(_)
                        | SubscribeChannel::Kline(_)
                )
            })
            .cloned()
            .collect();
        self.sub(w, &subs).await?;
        Ok(())
    }

    pub(crate) async fn sub_pri(
        &mut self,
        w: &mut Writer,
        channels: &[SubscribeChannel],
    ) -> Result<()> {
        let subs: Vec<SubscribeChannel> = channels
            .iter()
            .filter(|x| {
                matches!(
                    x,
                    SubscribeChannel::FundingFee(_)
                        | SubscribeChannel::Balance
                        | SubscribeChannel::OrderAndFill(_)
                        | SubscribeChannel::Order(_)
                        | SubscribeChannel::Position(_)
                )
            })
            .cloned()
            .collect();
        self.sub(w, &subs).await?;
        Ok(())
    }

    pub(crate) async fn sub(
        &mut self,
        w: &mut Writer,
        channels: &[SubscribeChannel],
    ) -> Result<()> {
        let mut params = FxHashSet::default();
        let mut fund_and_balance = false;

        for sub in channels {
            match sub {
                SubscribeChannel::MarkPrice(symbols) | SubscribeChannel::Funding(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    params.insert((Channel::Tickers, symbols));
                }
                SubscribeChannel::Bbo(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    for symbol in symbols {
                        if self.is_weburl() {
                            let default_s = "0".to_string();
                            let tick = self.instrument.get(&symbol).unwrap_or(&default_s);
                            params.insert((
                                Channel::OrderBook,
                                vec![symbol, "5".to_string(), tick.to_string()],
                            ));
                        } else {
                            params.insert((Channel::BookTicker, vec![symbol]));
                        }
                    }
                }
                SubscribeChannel::Kline(kline_params) => {
                    let symbols = Self::symbols_cvt(kline_params.symbols.as_ref().clone());
                    for symbol in symbols {
                        // 默认使用普通K线，支持标记价格和指数价格可以通过在symbol前加前缀实现
                        params.insert((
                            Channel::Candlesticks,
                            vec![kline_params.interval.to_string(), symbol],
                        ));
                    }
                }
                SubscribeChannel::Depth(depth_params) => {
                    self.limit = depth_params.levels as usize;
                    let symbols = Self::symbols_cvt(depth_params.symbols.as_ref().clone());
                    if depth_params.levels <= 0 {
                        warn!("depth limit is less than 0");
                    }
                    let level = if depth_params.levels <= 5 {
                        5
                    } else if depth_params.levels <= 10 {
                        10
                    } else if depth_params.levels <= 20 {
                        20
                    } else if depth_params.levels <= 50 {
                        50
                    } else {
                        100
                    };
                    for symbol in &symbols {
                        if self.is_weburl() {
                            let default_s = "0".to_string();
                            let tick = self.instrument.get(symbol.as_str()).unwrap_or(&default_s);
                            params.insert((
                                Channel::OrderBook,
                                vec![symbol.clone(), level.to_string(), tick.to_string()],
                            ));
                        } else {
                            params.insert((
                                Channel::OrderBook,
                                vec![symbol.clone(), level.to_string(), "0".to_string()],
                            ));
                        }
                    }
                }
                SubscribeChannel::OrderAndFill(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    let user_id = self.rest.get_user_id().await?;
                    for symbol in symbols.clone() {
                        params.insert((Channel::Orders, vec![user_id.clone(), symbol]));
                    }
                    params.insert((Channel::UserTrades, symbols));
                }
                SubscribeChannel::Trade(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    params.insert((Channel::Trades, symbols));
                }
                SubscribeChannel::Order(symbols) => {
                    let user_id = self.rest.get_user_id().await?;
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    for symbol in symbols {
                        params.insert((Channel::Orders, vec![user_id.clone(), symbol]));
                    }
                }
                SubscribeChannel::Position(symbols) => {
                    let user_id = self.rest.get_user_id().await?;
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    for symbol in symbols {
                        params.insert((Channel::Position, vec![user_id.clone(), symbol]));
                    }
                }
                SubscribeChannel::FundingFee(symbols) => {
                    let symbols = Self::symbols_cvt(symbols.as_ref().clone());
                    for symbol in symbols {
                        self.funding_filter.insert(symbol);
                    }
                    fund_and_balance = true;
                }
                SubscribeChannel::Balance => {
                    fund_and_balance = true;
                }
            }
        }

        if fund_and_balance {
            let user_id = self.rest.get_user_id().await?;
            params.insert((Channel::Balance, vec![user_id]));
        }
        // TODO position
        if params.is_empty() {
            warn!("sub params is empty");
            return Ok(());
        }
        let time = time();
        for (channel, payload) in params {
            let req = new_ws_subscribe_req(
                channel,
                "subscribe",
                &payload,
                time,
                &self.rest.config.key,
                &self.rest.config.secret,
            );
            let req_json = sonic_rs::to_string(&req)?;
            let message = Message::Text(req_json);
            w.send(message).await?;
        }

        debug!("send sub message success");
        Ok(())
    }

    fn symbols_cvt(symbols: Vec<Symbol>) -> Vec<String> {
        symbols
            .into_iter()
            .map(|x| GateSymbol::from(x).to_string())
            .collect()
    }

    fn unchange(&mut self, bbo: &BboTicker) -> bool {
        let last_bbo = self.last_bbo.get(&bbo.symbol.to_string()).cloned();
        let ret = if let Some(last_bbo) = last_bbo {
            bbo.bid_price == last_bbo.bid_price
                && bbo.ask_price == last_bbo.ask_price
                && bbo.bid_qty == last_bbo.bid_qty
                && bbo.ask_qty == last_bbo.ask_qty
        } else {
            false
        };
        self.last_bbo.insert(bbo.symbol.to_string(), bbo.clone());
        ret
    }
}

// #[cfg(test)]
// mod tests {
//     use super::*;
//     use quant_common::base::*;
//     use quant_common::*;
//     // use tracing_subscriber::fmt;

//     #[tokio::test]
//     #[ignore = "need proxy"]
//     async fn test_all() -> Result<()> {
//         // fmt().pretty().init();
//         let config = ExConfig::default();
//         let mut ws = GateSwapWs::new(config).await;
//         let symbols = vec![Symbol::new("BTC")];
//         let subs = Subscribes::default_swap(symbols);
//         ws.run(subs).await?;
//         Ok(())
//     }

//     #[tokio::test]
//     #[ignore = "need proxy"]
//     async fn test_pri() -> Result<()> {
//         // fmt().pretty().init();
//         let config = test_config();
//         let mut ws = GateSwapWs::new(config).await;
//         let symbols = vec![Symbol::new("SNT")];
//         let subs = Subscribes::default_swap_pri(symbols);
//         ws.run(subs).await?;
//         Ok(())
//     }
// }

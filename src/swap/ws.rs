use crate::common::symbol::BbSymbol;
use crate::swap::model::*;
use crate::swap::rest::BybitSwap;
use futures_util::{SinkExt, StreamExt};
use hmac::{Hmac, Mac};
use quant_common::base::{
    Balance, BboTicker, Depth, DepthEntry, Exchange, Funding, FundingFee, MarkPrice, Order,
    SubscribeChannel, Symbol, Trade, WsHandler,
    model::{ExConfig, MAX_RETRY, Reader, Subscribes, Writer},
    traits::ws::WebSocket,
};
use quant_common::base::{Candle, Kline, Position, PrefixMulExt};
use quant_common::order_utils::OrderTradeCache;
use quant_common::{QuantError, Result, connect_ws_with_proxy, qerror, time_ms};
use sha2::Sha256;
use sonic_rs::{JsonValueTrait, Value};
use std::{collections::HashMap, str::FromStr};

use tokio_tungstenite::tungstenite::protocol::Message;
use tracing::{debug, error, info};

#[derive(Debug)]
pub struct BybitSwapWs {
    rest: BybitSwap,
    exchange: Exchange,
    depth: HashMap<String, Depth>,
    topics: HashMap<String, bool>,
    order_trade_cache: OrderTradeCache,
    depth_limit: usize,
}

impl BybitSwapWs {
    pub async fn new(config: ExConfig) -> Self {
        let rest = BybitSwap::new(config).await;

        BybitSwapWs {
            rest,
            depth: HashMap::new(),
            topics: HashMap::new(),
            exchange: Exchange::BybitSwap,
            order_trade_cache: OrderTradeCache::new(),
            depth_limit: 100,
        }
    }

    fn transfer_depth(&mut self, new: BbOrderBookSnapshot, _topic: String) -> Depth {
        let sys_symbol = new.data.s;
        let symbol_str = sys_symbol.to_string();

        let prefix_mul = sys_symbol.prefix_mul as f64;

        let mut init_depth: Depth = Depth {
            symbol: Symbol::default(),
            bids: new
                .data
                .b
                .into_iter()
                .map(|e| {
                    if prefix_mul > 1.0 {
                        prefix_mul.price2base(e.0);
                        prefix_mul.qty2base(e.1);
                    }
                    DepthEntry::from(e)
                })
                .collect(),
            asks: new
                .data
                .a
                .into_iter()
                .map(|e| {
                    if prefix_mul > 1.0 {
                        prefix_mul.price2base(e.0);
                        prefix_mul.qty2base(e.1);
                    }
                    DepthEntry::from(e)
                })
                .collect(),
            timestamp: new.ts.try_into().unwrap_or_default(),
        };
        init_depth.symbol = sys_symbol.inner;
        if new.r#type == "snapshot" || !self.depth.contains_key(&symbol_str) {
            self.depth.insert(symbol_str, init_depth.clone());
            init_depth
        } else {
            self.depth
                .get_mut(&symbol_str)
                .unwrap()
                .update_depth(init_depth.clone());
            self.depth.get_mut(&symbol_str).unwrap().clone()
        }
    }
}

impl WebSocket for BybitSwapWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = quant_common::time();
            match self.run_inner(&subs).await {
                Ok(_) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    info!("BybitSwap websocket disconnect");
                }
                Err(e) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    error!("BybitSwap websocket connect error: {:?}", e);
                    if quant_common::time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            return Err(qerror!("BybitSwap websocket retry failed too many times"));
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl BybitSwapWs {
    async fn run_inner<H: WsHandler>(&mut self, subs: &Subscribes<H>) -> Result<()> {
        let (mut writer, mut reader) = self.connect(subs.has_private()).await?;
        subs.handler.on_connected(self.exchange, subs.id).await?;
        self.sub(&mut writer, &subs.channels).await?;

        let ping = Message::Text(r###"{"op": "ping"}"###.to_string());
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(20));
        loop {
            tokio::select! {
                msg = reader.next() => {
                    let msg = match msg {
                        Some(Ok(x)) => x,
                        Some(Err(err)) => {
                            error!("receive err: {:?}", err);
                            break;
                        }
                        None => {
                            error!("receive None");
                            break;
                        }
                    };
                    match &msg {
                        Message::Text(body) =>  {
                            let parsed : sonic_rs::Result<Value> = sonic_rs::from_str(body);
                            match parsed {
                                Ok(json) => {
                                    // 检查 JSON 是否包含 op: ping or subscribe
                                    if let Some(op) = json.get("op")
                                        && (op == "ping" || op == "pong" || op == "subscribe" || op == "auth") {
                                            // 跳过无用信息
                                            continue;
                                        }
                                    // 检查 JSON 是否错误
                                    if let Some(success) = json.get("success")
                                        && success == false {
                                            return Err(qerror!("websocket command err:{}", json.get("ret_msg").unwrap()));
                                        }

                                    match self.handle_msg(msg, subs).await {
                                        Ok(()) => (),
                                        Err(e) => error!("bybit handle_msg err: {e}"),
                                    };
                                },
                                Err(e) => {
                                    // 如果解析失败，可能是非法 JSON，继续处理其他消息
                                    error!("Failed to parse message body as JSON: {},because:{}", body,e);
                                }
                            }
                        },
                        Message::Close(_) => break,
                        _ => {},
                    }
                }
                _ = ping_interval.tick() => {
                    writer.send(ping.clone()).await?;
                }
            }
        }
        Ok(())
    }

    async fn connect(&mut self, private: bool) -> Result<(Writer, Reader), QuantError> {
        let config = self.rest.config.clone();
        let host: String =
            config
                .host
                .as_ref()
                .map(|h| h.to_string())
                .unwrap_or(if config.is_testnet {
                    WS_TEST_HOST.to_string()
                } else if config.is_colo {
                    WS_COLO_HOST.to_string()
                } else {
                    WS_HOST.to_string()
                });
        let url = if private {
            format!("wss://{host}/v5/private").to_string()
        } else {
            format!("wss://{host}/v5/public/linear").to_string()
        };
        debug!("websocket url:{}", url);
        let ws_stream = connect_ws_with_proxy(&url).await?;
        let (mut writer, reader) = ws_stream.split();
        // auth
        if private {
            let api_key = self.rest.config.key.clone();
            let timestamp = time_ms() + (24 * 60 * 60 * 1000);

            let message = format!("GET/realtime{timestamp}");

            let mut mac = Hmac::<Sha256>::new_from_slice(self.rest.config.secret.as_bytes())
                .expect("Invalid API secret length");

            mac.update(message.as_bytes());

            let result = mac.finalize();
            let signature = hex::encode(result.into_bytes());
            let json_data =
                format!(r#"{{"op":"auth","args":["{api_key}",{timestamp},"{signature}"]}}"#);
            debug!("send auth message {}", json_data);
            writer.send(Message::text(json_data)).await?;
        }
        Ok((writer, reader))
    }

    async fn handle_msg<H: WsHandler>(
        &mut self,
        data: Message,
        subs: &Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        match &data {
            Message::Text(t) => {
                debug!("real message body:{}", t);
                let result: sonic_rs::Result<Value> = sonic_rs::from_str(t);
                match result?.get("topic") {
                    None => return Err(qerror!("topic 不能为空")),
                    Some(topic) => {
                        let topic = topic.to_string().trim_matches('"').to_string();
                        debug!("subscribe topic:{}", topic);
                        match topic {
                            topic if topic.starts_with("tickers.") => {
                                let b: BbTickerSnapshot = sonic_rs::from_str(t)?;
                                if subs.has_bbo() {
                                    let bbo = BboTicker::from(b.clone());
                                    handler.on_bbo(bbo, self.exchange).await?;
                                }
                                if subs.has_funding() {
                                    let funding = Funding::from(b.clone());
                                    handler.on_funding(funding, self.exchange).await?;
                                }
                                if subs.has_mark_price() {
                                    let mark_price = MarkPrice::from(b);
                                    handler.on_mark_price(mark_price, self.exchange).await?;
                                }
                            }
                            topic if topic.starts_with("orderbook.") => {
                                let new: BbOrderBookSnapshot =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let d = self
                                    .transfer_depth(new, topic.clone())
                                    .truncate_limited(self.depth_limit);
                                handler.on_depth(d, self.exchange).await?
                            }

                            topic if topic.starts_with("publicTrade.") => {
                                let snapshot: PublicTradeSnapshot =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let trades: Vec<_> =
                                    snapshot.data.into_iter().map(Trade::from).collect();

                                for x in trades {
                                    handler.on_trade(x, self.exchange).await?
                                }
                            }

                            topic if topic.to_string().starts_with("position.linear") => {
                                debug!("position");
                                let snapshot: BbPriRsp<Vec<BbPosition>> =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let positions: Vec<_> = snapshot
                                    .data
                                    .into_iter()
                                    .filter_map(|p| {
                                        if p.seq == -10000000 {
                                            return None;
                                        }
                                        Some(Position::from(p))
                                    })
                                    .collect();
                                for x in positions {
                                    handler.on_position(x, subs.id).await?
                                }
                            }

                            topic if topic.to_string().starts_with("order.linear") => {
                                let snapshot: BbPriRsp<Vec<BbOrderData>> =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");

                                for o in snapshot.data {
                                    let order = Order::from(o);

                                    if subs.has_order_and_fill()
                                        && let Some(order) =
                                            self.order_trade_cache.process_order(order.clone())
                                    {
                                        handler.on_order_and_fill(order, subs.id).await?
                                    }
                                    if subs.has_order() {
                                        handler.on_order(order, subs.id).await?
                                    }
                                }
                            }

                            topic if topic.to_string().starts_with("execution.linear") => {
                                let snapshot: BbPriRsp<Vec<BbExecData>> =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let executions: Vec<_> = snapshot
                                    .data
                                    .into_iter()
                                    .filter_map(|o| {
                                        if o.exec_type != "Funding" {
                                            return None;
                                        }
                                        Some(FundingFee::from(o))
                                    })
                                    .collect();
                                for x in executions {
                                    handler.on_funding_fee(x, subs.id).await?
                                }
                            }
                            topic if topic.to_string().starts_with("execution.fast.linear") => {
                                if subs.has_order_and_fill() {
                                    let snapshot: BbPriRsp<Vec<BbFastExecData>> =
                                        sonic_rs::from_str(t).expect("Failed to parse JSON");

                                    for exec in snapshot.data {
                                        // execution.fast.linear 消息不包含 exec_type 字段，直接处理所有执行数据
                                        let order = Order::from(exec);
                                        if let Some(order) =
                                            self.order_trade_cache.process_order(order)
                                        {
                                            handler.on_order_and_fill(order, subs.id).await?
                                        }
                                    }
                                }
                            }
                            topic if topic.to_string().starts_with("wallet") => {
                                let mut snapshot: BbPriRsp<Vec<BbBalance>> =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");
                                let bb_balances =
                                    snapshot.data.pop().ok_or(qerror!("empty json"))?;
                                let balances: Vec<_> =
                                    bb_balances.coin.into_iter().map(Balance::from).collect();
                                for mut balance in balances {
                                    balance.available_balance = bb_balances
                                        .total_available_balance
                                        .parse::<f64>()
                                        .unwrap_or(0.0);
                                    handler.on_balance(balance, subs.id).await?
                                }
                            }
                            topic if topic.to_string().starts_with("kline") => {
                                debug!("kline");
                                let snapshot: BbKlineWsResponse =
                                    sonic_rs::from_str(t).expect("Failed to parse JSON");

                                // 从topic中提取symbol，格式: kline.{interval}.{symbol}
                                let topic_parts: Vec<&str> = topic.split('.').collect();
                                if topic_parts.len() != 3 {
                                    error!("Invalid kline topic format: {}", topic);
                                    return Err(qerror!("Invalid kline topic format: {}", topic));
                                }
                                let symbol_str = topic_parts[2]; // 获取symbol部分

                                for kline_data in snapshot.data {
                                    let sys_symbol = BbSymbol::from_str(symbol_str)?;

                                    let prefix_mul = sys_symbol.prefix_mul as f64;

                                    // 创建Candle数据
                                    let candle = Candle {
                                        timestamp: kline_data.timestamp as i64,
                                        open: prefix_mul.price2base(kline_data.open),
                                        high: prefix_mul.price2base(kline_data.high),
                                        low: prefix_mul.price2base(kline_data.low),
                                        close: prefix_mul.price2base(kline_data.close),
                                        volume: prefix_mul.qty2base(kline_data.volume),
                                        quote_volume: kline_data.turnover,
                                        trades: None, // Bybit K线数据中没有这个字段
                                        taker_buy_volume: None, // Bybit K线数据中没有这个字段
                                        taker_buy_quote_volume: None, // Bybit K线数据中没有这个字段
                                        confirm: kline_data.confirm,
                                    };

                                    // 创建Kline数据
                                    let kline = Kline {
                                        symbol: sys_symbol.inner,
                                        interval: convert_interval(&kline_data.interval)?,
                                        candles: vec![candle],
                                    };

                                    handler.on_kline(kline, self.exchange).await?;
                                }
                            }
                            _ => {}
                        };
                    }
                }
            }
            _ => unreachable!(),
        };
        Ok(())
    }

    pub(crate) async fn sub(
        &mut self,
        w: &mut Writer,
        channels: &[SubscribeChannel],
    ) -> Result<()> {
        for channel in channels {
            match channel {
                SubscribeChannel::Depth(params) => {
                    self.depth_limit = params.levels as usize;
                    for symbol in params.symbols.iter() {
                        let symbol = BbSymbol::new(symbol.clone()).to_string();
                        let levels = match params.levels {
                            2..=50 => 50,
                            51..=200 => 200,
                            201..=500 => 500,
                            _ => 1,
                        };
                        self.topics
                            .insert(format!("orderbook.{levels}.{symbol}"), false);
                    }
                }
                SubscribeChannel::Trade(symbols) => {
                    for symbol in symbols.iter() {
                        let symbol = BbSymbol::new(symbol.clone()).to_string();
                        self.topics.insert(format!("publicTrade.{symbol}"), false);
                    }
                }
                SubscribeChannel::Funding(symbols)
                | SubscribeChannel::MarkPrice(symbols)
                | SubscribeChannel::Bbo(symbols) => {
                    for symbol in symbols.iter() {
                        let symbol = BbSymbol::new(symbol.clone()).to_string();
                        self.topics.insert(format!("tickers.{symbol}"), false);
                    }
                }
                SubscribeChannel::Order(symbols) => {
                    let symbols = symbols
                        .iter()
                        .map(|symbol| BbSymbol::new(symbol.clone()).to_string())
                        .collect::<Vec<String>>()
                        .join(",")
                        .to_string();
                    let topic = format!("order.linear-{symbols}");
                    self.topics.insert(topic, true);
                }
                SubscribeChannel::Balance => {
                    self.topics.insert("wallet".to_string(), true);
                }
                SubscribeChannel::Position(symbols) => {
                    let symbols = symbols
                        .iter()
                        .map(|symbol| BbSymbol::new(symbol.clone()).to_string())
                        .collect::<Vec<String>>()
                        .join(",")
                        .to_string();
                    let topic = format!("position.linear-{symbols}");
                    self.topics.insert(topic, true);
                }
                SubscribeChannel::FundingFee(symbols) => {
                    let symbols = symbols
                        .iter()
                        .map(|symbol| BbSymbol::new(symbol.clone()).to_string())
                        .collect::<Vec<String>>()
                        .join(",")
                        .to_string();
                    let topic = format!("execution.linear-{symbols}");
                    self.topics.insert(topic, true);
                }
                SubscribeChannel::OrderAndFill(symbols) => {
                    let symbols = symbols
                        .iter()
                        .map(|symbol| BbSymbol::new(symbol.clone()).to_string())
                        .collect::<Vec<String>>()
                        .join(",")
                        .to_string();
                    let topic = format!("execution.fast.linear-{symbols}");
                    self.topics.insert(topic, true);
                }
                SubscribeChannel::Kline(params) => {
                    for symbol in params.symbols.iter() {
                        let symbol = BbSymbol::new(symbol.clone()).to_string();
                        let topic = format!(
                            "kline.{}.{}",
                            convert_interval_to_bybit(&params.interval)?,
                            symbol
                        );
                        self.topics.insert(topic, false);
                    }
                }
            }
        }

        if self.topics.is_empty() {
            return Ok(());
        }

        let toplist = self
            .topics
            .keys()
            .map(|s| s.replace('_', ""))
            .map(|t| {
                if let Some((before_dash, _)) = t.split_once('-') {
                    return before_dash.to_string();
                }
                t
            })
            .collect::<Vec<_>>()
            .join(r##"", ""##);

        debug!("topics{:?}", self.topics);
        let msg = format!(r##"{{"op": "subscribe", "args": ["{toplist}"]}}"##);
        debug!("send subscribe message: {}", msg);

        w.send(Message::Text(msg)).await?;
        Ok(())
    }
}

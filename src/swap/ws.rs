use std::collections::HashMap;

use crate::swap::rest::{crypto, OkxSwap};
use futures_util::{SinkExt, StreamExt};
use quant_common::{
    base::{
        self, ExConfig, Exchange, FundingFee, Reader, SubscribeChannel, Subscribes, Symbol,
        WebSocket, Writer, WsHandler, MAX_RETRY,
    },
    connect_ws_with_proxy, qerror, QuantError, Result,
};
use tokio_tungstenite::tungstenite::Message;

use crate::swap::model::*;

macro_rules! deserialize_message {
    ($data:expr, $type:ty) => {
        match &$data {
            Message::Text(t) => sonic_rs::from_str::<$type>(t),
            Message::Binary(b) => sonic_rs::from_slice::<$type>(b),
            _ => unreachable!(),
        }
    };
}

#[derive(Default, Debug)]
pub struct OkxSwapWs {
    url_public: String,
    url_private: String,
    need_auth: bool,
    key: String,
    secret: String,
    passphrase: String,
    exchange: Exchange,
    funding_fee_cache: HashMap<Symbol, FundingFeeCache>,
}

impl OkxSwapWs {
    pub async fn new(config: ExConfig) -> Self {
        // let (url_public, url_private) = if config.is_testnet {
        //     (WS_TEST_URL_PUBLIC, WS_TEST_URL_PRIVATE)
        // } else if config
        //     .params
        //     .get("aws")
        //     .map(|v| v == "true")
        //     .unwrap_or(false)
        // {
        //     (WS_AWS_URL_PUBLIC, WS_AWS_URL_PRIVATE)
        // } else {
        //     (WS_URL_PUBLIC, WS_URL_PRIVATE)
        // };
        let default_host = if config.is_testnet {
            WS_TEST_URL
        } else if config
            .params
            .get("aws")
            .map(|v| v == "true")
            .unwrap_or(false)
        {
            WS_AWS_URL
        } else {
            WS_URL
        }
        .to_string();
        let host = config
            .ws_host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(default_host);
        let url_public = format!("wss://{host}/ws/v5/public");
        let url_private = format!("wss://{host}/ws/v5/private");

        let _ = OkxSwap::new(config.clone()).await;

        OkxSwapWs {
            url_public,
            url_private,
            need_auth: false,
            key: config.key,
            secret: config.secret,
            passphrase: config.passphrase,
            exchange: Exchange::OkxSwap,
            funding_fee_cache: HashMap::new(),
        }
    }
}

impl WebSocket for OkxSwapWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = quant_common::time();
            match self.run_inner(&subs).await {
                Ok(_) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    warn!("websocket disconnect, try to reconnect");
                }
                Err(e) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    error!("websocket connect error: {:?}", e);
                    if quant_common::time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            return Err(qerror!("OkxSwap websocket retry failed too many times"));
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl OkxSwapWs {
    async fn run_inner<H: WsHandler>(&mut self, subs: &Subscribes<H>) -> Result<()> {
        let handler = &subs.handler;
        let (mut writer, mut reader) = self.connect(subs).await?;
        self.sub(&mut writer, &mut reader, &subs.channels).await?;
        handler.on_connected(self.exchange, subs.id).await?;
        let ping = Message::Ping(vec![]);
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(10));
        loop {
            tokio::select! {
                msg = reader.next() => {
                    let msg = match msg {
                        Some(Ok(x)) => x,
                        Some(Err(err)) => {
                            error!("receive err: {:?}", err);
                            break;
                        }
                        None => {
                            error!("receive None");
                            break;
                        }
                    };
                    match msg {
                        Message::Text(_) | Message::Binary(_) =>  {self.handle_msg(msg, subs).await},
                        Message::Ping(_) => {
                            let message = Message::Pong(vec![]);
                            writer.send(message).await?;
                        },
                        Message::Close(_) => break,
                        _ => {},
                    }
                }
                _ = ping_interval.tick() => {
                    writer.send(ping.clone()).await?;
                }
            }
        }
        Ok(())
    }

    async fn handle_msg<H: WsHandler>(&mut self, data: Message, subs: &base::Subscribes<H>) {
        if let Ok(msg) = deserialize_message!(data, OkxStreamData) {
            if let Err(e) = self.handle_data(msg, subs).await {
                error!("Handle data error: {:?}", e);
            }
        } else if let Ok(event) = deserialize_message!(data, OkxStreamEventSuccess) {
            match event.arg.inst_id {
                Some(inst_id) => debug!(
                    "Subscribe public success! Connection ID: {}, Channel: {:?}, Symbol: {:?}",
                    event.conn_id, event.arg.channel, inst_id
                ),
                None if event.arg.channel == Channel::Balances => debug!(
                    "Subscribe private success! Connection ID: {}, Channel: {:?}",
                    event.conn_id, event.arg.channel
                ),
                None => debug!(
                    "Subscribe private success! Connection ID: {}, Channel: {:?}, Type: {:?}, Symbol: {:?}",
                    event.conn_id, event.arg.channel, event.arg.inst_type.unwrap(), event.arg.inst_family.unwrap()
                ),
            }
        } else if let Ok(event_error) = deserialize_message!(data, OkxStreamEventError) {
            error!(
                "Subscribe error! Code: {}, Message: {}",
                event_error.code, event_error.msg
            );
        } else if let Ok(data) = deserialize_message!(data, OkxStreamChannelConnCount) {
            debug!("Channel connection count: {}", data.conn_count);
        } else {
            error!("Parse error");
        }
    }

    async fn handle_data<H: WsHandler>(
        &mut self,
        data: OkxStreamData,
        subs: &base::Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        match data.arg.channel {
            Channel::MarkPrice => {
                if subs.has_mark_price() {
                    let deserialized = sonic_rs::from_value::<Vec<WsMarkPrice>>(&data.data)?;
                    if let Some(result) = deserialized.into_iter().next() {
                        handler.on_mark_price(result.into(), self.exchange).await?;
                    }
                }
            }
            Channel::BookTicker => {
                if subs.has_bbo() {
                    // let deserialized = sonic_rs::from_value::<Vec<WsBbo>>(&data.data)?;
                    // if let Some(result) = deserialized.into_iter().next() {
                    //     handler.on_bbo(result.into()).await?;
                    // }

                    let symbol = data.arg.inst_id.map(|x| x.into()).unwrap_or_default();
                    let deserialized = sonic_rs::from_value::<Vec<OkxDepthResp>>(&data.data)?;
                    if let Some(mut result) = deserialized.into_iter().next() {
                        result.set_symbol(symbol);
                        handler.on_bbo(result.into(), self.exchange).await?;
                    }
                }
            }
            Channel::FundingRate => {
                if subs.has_funding() {
                    let deserialized = sonic_rs::from_value::<Vec<WsFundingRate>>(&data.data)?;
                    if let Some(result) = deserialized.into_iter().next() {
                        handler.on_funding(result.into(), self.exchange).await?;
                    }
                }
            }
            Channel::Depth => {
                if subs.has_depth() {
                    let symbol = data.arg.inst_id.map(|x| x.into()).unwrap_or_default();
                    let deserialized = sonic_rs::from_value::<Vec<OkxDepthResp>>(&data.data)?;
                    if let Some(mut result) = deserialized.into_iter().next() {
                        result.set_symbol(symbol);
                        let depth = result.convert_to_depth();
                        handler.on_depth(depth, self.exchange).await?;
                    }
                }
            }
            Channel::Trade => {
                if subs.has_trade() {
                    let deserialized = sonic_rs::from_value::<Vec<WsTrade>>(&data.data)?;
                    if let Some(result) = deserialized.into_iter().next() {
                        handler.on_trade(result.into(), self.exchange).await?;
                    }
                }
            }
            Channel::Balances => {
                if subs.has_balance() {
                    let deserialized = sonic_rs::from_value::<Vec<OkxBalancesResp>>(&data.data)?;
                    if let Some(result) = deserialized.into_iter().next() {
                        for balance in result.details {
                            handler.on_balance(balance.into(), subs.id).await?;
                        }
                    }
                }
            }
            Channel::Positions => {
                let deserialized = sonic_rs::from_value::<Vec<WsPosition>>(&data.data)?;
                if let Some(result) = deserialized.into_iter().next() {
                    if subs.has_funding_fee() && result.funding_fee != 0.0 {
                        let funding_fee: FundingFee = (&result).into();
                        let should_update = self
                            .funding_fee_cache
                            .get(&funding_fee.symbol)
                            .map(|cache| {
                                cache.funding_fee != funding_fee.funding_fee
                                    || cache.timestamp != funding_fee.timestamp
                            })
                            .unwrap_or(true);

                        if should_update {
                            self.funding_fee_cache.insert(
                                funding_fee.symbol.clone(),
                                FundingFeeCache {
                                    funding_fee: funding_fee.funding_fee,
                                    timestamp: funding_fee.timestamp,
                                },
                            );
                            handler.on_funding_fee(funding_fee, subs.id).await?;
                        }
                    }
                    if subs.has_position() {
                        handler.on_position(result.into(), subs.id).await?;
                    }
                }
            }
            Channel::Orders => {
                if subs.has_order() {
                    let deserialized = sonic_rs::from_value::<Vec<OkxOrderResp>>(&data.data)?;
                    if let Some(result) = deserialized.into_iter().next() {
                        handler.on_order(result.into(), subs.id).await?;
                    }
                }
            }
            _ => {
                unreachable!()
            }
        }
        Ok(())
    }

    async fn connect<H: WsHandler>(
        &mut self,
        subs: &Subscribes<H>,
    ) -> Result<(Writer, Reader), QuantError> {
        let url = if subs.has_private() {
            self.need_auth = true;
            &self.url_private
        } else {
            &self.url_public
        };

        debug!("Connecting to {}", url);
        let stream = connect_ws_with_proxy(url).await?;
        Ok(stream.split())
    }

    pub(crate) async fn sub(
        &mut self,
        w: &mut Writer,
        r: &mut Reader,
        channels: &[SubscribeChannel],
    ) -> Result<()> {
        let mut params = Vec::new();
        for channel in channels {
            match channel {
                SubscribeChannel::MarkPrice(symbols) => {
                    let symbols = symbols_from(symbols);
                    params.extend(generate_args(false, Channel::MarkPrice, &symbols));
                }
                SubscribeChannel::Bbo(symbols) => {
                    let symbols = symbols_from(symbols);
                    params.extend(generate_args(false, Channel::BookTicker, &symbols));
                }
                SubscribeChannel::Funding(symbols) => {
                    let symbols = symbols_from(symbols);
                    params.extend(generate_args(false, Channel::FundingRate, &symbols));
                }
                SubscribeChannel::Depth(depth_params) => {
                    let symbols = symbols_from(&depth_params.symbols);
                    params.extend(generate_args(false, Channel::Depth, &symbols));
                }
                SubscribeChannel::Trade(symbols) => {
                    let symbols = symbols_from(symbols);
                    params.extend(generate_args(false, Channel::Trade, &symbols));
                }
                SubscribeChannel::Order(symbols) => {
                    let symbols = symbols_from(symbols);
                    params.extend(generate_args(true, Channel::Orders, &symbols));
                }
                SubscribeChannel::Position(symbols) | SubscribeChannel::FundingFee(symbols) => {
                    let symbols = symbols_from(symbols);
                    params.extend(generate_args(true, Channel::Positions, &symbols));
                }
                SubscribeChannel::Balance => {
                    params.extend(generate_args(true, Channel::Balances, &[]));
                }
            }
        }

        if self.need_auth {
            // Login
            login(w, r, &self.key, &self.secret, &self.passphrase).await?;
        }

        if params.is_empty() {
            warn!("sub params is empty");
            return Ok(());
        }
        let req = SubscriptionRequest {
            operation: Operation::Subscribe,
            args: params,
        };
        let req_str = sonic_rs::to_string(&req)?;
        debug!("Subscribe request: {}", req_str);
        let message = Message::Text(req_str);
        w.send(message).await?;
        Ok(())
    }
}

async fn login(
    w: &mut Writer,
    r: &mut Reader,
    key: &str,
    secret: &str,
    passphrase: &str,
) -> Result<()> {
    let req = generate_login(key, secret, passphrase);
    let req_str = sonic_rs::to_string(&req)?;
    w.send(Message::Text(req_str)).await?;
    while let Some(msg) = r.next().await {
        if let Ok(msg) = msg {
            let msg = deserialize_message!(msg, OkxStreamEventError);
            if let Ok(msg) = msg {
                if msg.event == "login" {
                    debug!("Login success! Connection ID: {}", msg.conn_id);
                    break;
                } else {
                    error!("Login error! Code: {}, Message: {}", msg.code, msg.msg);
                    break;
                }
            }
        }
    }
    Ok(())
}

fn generate_args(is_private: bool, channel: Channel, symbols: &[String]) -> Vec<RequestArgs> {
    if symbols.is_empty() {
        return vec![RequestArgs::Private(PrivateArgs::new(
            channel,
            "".to_string(),
        ))];
    }
    symbols
        .iter()
        .map(|x| {
            if is_private {
                RequestArgs::Private(PrivateArgs::new(channel, x.to_owned()))
            } else {
                RequestArgs::Public(PublicArgs::new(channel, x.to_owned(), InstIdFormat::Swap))
            }
        })
        .collect()
}

pub fn generate_login(key: &str, secret: &str, passphrase: &str) -> WsLogin {
    let timestamp = quant_common::time().to_string();
    let sign_str = format!("{}{}{}", timestamp, "GET", "/users/self/verify");
    let sign = crypto(secret, sign_str);
    WsLogin::new(key, passphrase, timestamp, sign)
}

pub fn symbols_from(symbols: &[Symbol]) -> Vec<String> {
    symbols
        .iter()
        .map(|x| format!("{}-{}", x.base, x.quote))
        .collect()
}

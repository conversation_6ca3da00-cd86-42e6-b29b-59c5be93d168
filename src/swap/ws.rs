use std::{collections::HashSet, time::Duration};

use futures_util::{SinkExt, StreamExt};
use quant_common::{
    QuantError,
    base::{
        Exchange, MarkPrice, SubscribeChannel, WsHandler,
        model::{ExConfig, Funding, MAX_RETRY, Reader, Subscribes, Symbol, Writer},
        traits::ws::WebSocket,
    },
    order_utils::OrderTradeCache,
    time,
};
use quant_common::{Result, connect_ws_with_proxy, qerror};
use rustc_hash::FxHashMap;
use tokio::{task::Join<PERSON><PERSON><PERSON>, time::Instant};
use tokio_tungstenite::tungstenite::protocol::Message;

use crate::swap::{model::*, rest::BinanceSwap};

use super::model::{AccountUpdateReason, BnDepth, LocalDepth, WS_COLO_URL};

pub struct BinanceSwapWs {
    url: String,
    exchange: Exchange,
    rest: BinanceSwap,
    listen_key: String,
    depth_limit: usize,
    local_depths: FxHashMap<BnSymbol, LocalDepthState>,
    config: ExConfig,
    delay: Instant,

    position_symbols: HashSet<Symbol>,
    order_symbols: HashSet<Symbol>,
    order_and_fill_symbols: HashSet<Symbol>,

    order_cache: OrderTradeCache,
}

impl BinanceSwapWs {
    pub async fn new(config: ExConfig) -> Self {
        let host = match &config.ws_host {
            Some(host) => host.as_str(),
            None => match config.is_testnet {
                true => WS_TEST_URL,
                false => match config.is_colo {
                    true => WS_COLO_URL,
                    false => WS_URL,
                },
            },
        };
        let url = format!("wss://{host}/stream");

        let exchange = Exchange::BinanceSwap;
        let config = ExConfig { exchange, ..config };

        let rest = BinanceSwap::new(config.clone()).await;

        BinanceSwapWs {
            delay: Instant::now(),
            url,
            config,
            exchange,
            rest,
            listen_key: Default::default(),
            depth_limit: 1000,
            local_depths: Default::default(),
            position_symbols: Default::default(),
            order_symbols: Default::default(),
            order_and_fill_symbols: Default::default(),
            order_cache: OrderTradeCache::new(),
        }
    }
}

impl WebSocket for BinanceSwapWs {
    #[inline(always)]
    fn exchange(&self) -> Exchange {
        self.exchange
    }

    async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> Result<()> {
        let mut retry = 0;
        loop {
            let start = time();
            match self.run_inner(&subs).await {
                Ok(_) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    warn!("websocket disconnect, try to reconnect");
                }
                Err(e) => {
                    subs.handler.on_disconnected(self.exchange, subs.id).await?;
                    error!("websocket connect error: {:?}", e);
                    if time() - start < 60 {
                        retry += 1;
                        if retry > MAX_RETRY {
                            return Err(qerror!(
                                "BinanceSwap websocket retry failed too many times"
                            ));
                        }
                    } else {
                        retry = 0;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl BinanceSwapWs {
    async fn run_inner<H: WsHandler>(&mut self, subs: &Subscribes<H>) -> Result<()> {
        let (mut writer, mut reader) = self.connect().await?;
        let handler = &subs.handler;
        handler.on_connected(self.exchange, subs.id).await?;
        let channels = &subs.channels;
        self.sub(&mut writer, channels).await?;

        let pong = Message::Pong(vec![]);
        let mut ping_interval = tokio::time::interval(tokio::time::Duration::from_secs(10));
        let mut refresh_interval = tokio::time::interval(tokio::time::Duration::from_secs(30 * 60));
        loop {
            tokio::select! {
                msg = reader.next() => {
                    let msg = match msg {
                        Some(Ok(x)) => x,
                        Some(Err(err)) => {
                            error!("receive err: {:?}", err);
                            break;
                        }
                        None => {
                            error!("receive None");
                            break;
                        }
                    };
                    match msg {
                        Message::Text(_) | Message::Binary(_) => { self.handle_msg(msg, subs).await },
                        Message::Ping(p) => {
                            let message = Message::Pong(p);
                            writer.send(message).await?;
                        },
                        Message::Close(_) => break,
                        _ => {},
                    }
                }
                _ = ping_interval.tick() => {
                    writer.send(pong.clone()).await?;
                }
                _ = refresh_interval.tick() => {
                    if !self.listen_key.is_empty() {
                        // listen_key有效期延长至本次调用后60分钟
                        self.rest.refresh_listen_key(&self.listen_key).await?;
                    }
                }
            }
        }
        Ok(())
    }

    async fn connect(&mut self) -> Result<(Writer, Reader), QuantError> {
        // let (ws_stream, _) = connect_async(self.url).await?;
        let ws_stream = connect_ws_with_proxy(&self.url).await?;
        let (writer, reader) = ws_stream.split();
        Ok((writer, reader))
    }

    async fn handle_msg_inner<H: WsHandler>(
        &mut self,
        msg: StreamData,
        subs: &Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;
        if msg.stream == self.listen_key {
            let msg = msg.data.get();
            let payload = sonic_rs::from_str::<AccountPayload>(msg)
                .map_err(|e| qerror!("parse listenkey payload error: {msg} {:?}", e))?;

            match payload {
                AccountPayload::AccountUpdate(update) => {
                    if subs.has_position() {
                        if let Some(items) = update.data.position {
                            for pos in items {
                                if self.position_symbols.contains(&pos.symbol.clone().into()) {
                                    let position = pos.into_base(update.timestamp);
                                    handler.on_position(position, subs.id).await?;
                                }
                            }
                        }
                    }

                    if subs.has_funding_fee()
                        && update.data.reason == AccountUpdateReason::FundingFee
                    {
                        for balance in update.data.balance {
                            let funding_fee = balance.get_funding_fee(update.timestamp);
                            handler.on_funding_fee(funding_fee, subs.id).await?;
                        }
                    }
                }
                AccountPayload::OrderTradeUpdate(trade_update) => {
                    if subs.has_order() || subs.has_order_and_fill() {
                        let symbol = trade_update.get_symbol();
                        if self.order_and_fill_symbols.contains(&symbol) {
                            let order = trade_update.clone().into();
                            // info!("order trade update: {:?}", order);
                            if let Some(order) = self.order_cache.process_order(order) {
                                handler.on_order_and_fill(order, subs.id).await?;
                            }
                        }

                        if self.order_symbols.contains(&symbol) {
                            let order = trade_update.into();
                            // info!("order: {:?}", order);
                            handler.on_order(order, subs.id).await?;
                        }
                    }
                }
                AccountPayload::TradeLite(trade) => {
                    if subs.has_order_and_fill() {
                        let symbol = trade.get_symbol();
                        if self.order_and_fill_symbols.contains(&symbol) {
                            let order = trade.into();
                            // info!("trade lite: {:?}", order);
                            if let Some(order) = self.order_cache.process_order(order) {
                                handler.on_order_and_fill(order, subs.id).await?;
                            }
                        }
                    }
                }
                AccountPayload::AccountConfigUpdate | AccountPayload::StrategyUpdate => (),
                p => {
                    warn!("unhandle listenkey payload {p:?}");
                }
            }
        } else {
            let stream = msg.stream;
            let json = msg.data.get();

            if stream.ends_with(BOOK_TICKER_CHANNEL) {
                let bbo: BookTickerPayload = sonic_rs::from_str(json)?;
                handler.on_bbo(bbo.into(), self.exchange).await?;
            } else if stream.ends_with(DEPTH_UPDATE) {
                let update: DepthUpdate = sonic_rs::from_str(json)?;
                self.handle_depth_update(update, subs).await?;
            } else if stream.ends_with(MARK_PRICE_CHANNEL) {
                let mark_price: MarkPricePayload = sonic_rs::from_str(json)?;
                if subs.has_funding() {
                    let funding = Funding::from(&mark_price);
                    handler.on_funding(funding, self.exchange).await?;
                }

                if subs.has_mark_price() {
                    let mark_price: MarkPrice = mark_price.into();
                    handler.on_mark_price(mark_price, self.exchange).await?;
                }
            } else if stream.ends_with(TRADE_CHANNEL) {
                let trade: BnTrade = sonic_rs::from_str(json)?;
                handler.on_trade(trade.into(), self.exchange).await?;
            } else {
                match stream.split_once("@") {
                    Some((_body, channel)) if channel.starts_with(KLINE_CHANNEL) => {
                        let kline: WsKline = sonic_rs::from_str(json)?;
                        handler.on_kline(kline.into(), self.exchange).await?;
                    }
                    _ => warn!("unhandle stream: {stream:?}, json: {json}"),
                }
            }
        }
        Ok(())
    }

    async fn handle_depth_update<H: WsHandler>(
        &mut self,
        update: DepthUpdate,
        subs: &Subscribes<H>,
    ) -> Result<()> {
        let handler = &subs.handler;

        let local_depth_state = self
            .local_depths
            .entry(update.symbol.clone())
            .or_insert(LocalDepthState::Init);

        match local_depth_state {
            LocalDepthState::Init => {
                let symbol = update.symbol.clone();
                *local_depth_state = LocalDepthState::Pending {
                    pending: vec![update],
                    handle: depth_snapshot_task(&self.config, &mut self.delay, symbol).await,
                };
            }
            LocalDepthState::Working(local) => {
                match local.update(update) {
                    Ok(()) => {
                        let depth = local.depth.clone_limited(self.depth_limit);
                        handler.on_depth(depth, self.exchange).await?;
                    }
                    Err(err) => {
                        warn!("增量包失序: {err}");
                        *local_depth_state = LocalDepthState::Init;
                    }
                };
            }
            LocalDepthState::Pending { pending, handle } => {
                let symbol = update.symbol.clone();
                pending.push(update);

                if handle.is_finished() {
                    match create_local_depth(handle, &symbol, pending).await {
                        Ok(local) => {
                            let depth = local.depth.clone_limited(self.depth_limit);
                            *local_depth_state = LocalDepthState::Working(local);
                            handler.on_depth(depth, self.exchange).await?;
                        }
                        Err(err) => {
                            warn!("err: {err}");
                            let config = &self.config;
                            let delay = &mut self.delay;
                            *handle = depth_snapshot_task(config, delay, symbol).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    async fn handle_msg<H: WsHandler>(&mut self, data: Message, handler: &Subscribes<H>) {
        let msg = match &data {
            Message::Text(t) => sonic_rs::from_str::<StreamData>(t),
            Message::Binary(b) => sonic_rs::from_slice::<StreamData>(b),
            _ => unreachable!(),
        };
        match msg {
            Ok(msg) => {
                if let Err(e) = self.handle_msg_inner(msg, handler).await {
                    error!("handle message error: {:?}", e);
                }
            }
            Err(e) => {
                if sonic_rs::from_str::<WsId>(&data.to_string()).is_err() {
                    error!("parse message error: {:?}", e);
                }
            }
        }
    }

    pub(crate) fn symbols_convert(symbols: &[Symbol]) -> Vec<String> {
        symbols
            .iter()
            .map(|x| {
                let x: BnSymbol = x.clone().into();
                x.to_string().to_ascii_lowercase()
            })
            .collect::<Vec<_>>()
    }

    pub(crate) async fn sub(
        &mut self,
        w: &mut Writer,
        channels: &[SubscribeChannel],
    ) -> Result<()> {
        debug!("sub channels: {channels:?}");
        let mut params = HashSet::new();

        for sub in channels {
            match sub {
                SubscribeChannel::MarkPrice(symbols) => {
                    let symbols = Self::symbols_convert(symbols);
                    for symbol in symbols {
                        params.insert(format!("{symbol}{MARK_PRICE_CHANNEL}"));
                    }
                }
                SubscribeChannel::Bbo(symbols) => {
                    let symbols = Self::symbols_convert(symbols);
                    for symbol in symbols {
                        params.insert(format!("{symbol}{BOOK_TICKER_CHANNEL}",));
                    }
                }
                SubscribeChannel::Depth(depth_params) => {
                    let symbols = Self::symbols_convert(&depth_params.symbols);
                    self.depth_limit = depth_params.levels as usize;

                    for symbol in &symbols {
                        params.insert(format!("{symbol}{DEPTH_UPDATE}"));
                    }
                }
                SubscribeChannel::Funding(symbols) => {
                    let symbols = Self::symbols_convert(symbols);
                    for symbol in symbols {
                        params.insert(format!("{symbol}{MARK_PRICE_CHANNEL}"));
                    }
                }
                SubscribeChannel::Trade(symbols) => {
                    let symbols = Self::symbols_convert(symbols);
                    for symbol in symbols {
                        params.insert(format!("{symbol}{TRADE_CHANNEL}"));
                    }
                }
                SubscribeChannel::Order(symbols) => {
                    self.order_symbols.clear();
                    self.order_symbols.extend(symbols.iter().cloned());

                    if self.listen_key.is_empty() {
                        self.listen_key = self.rest.create_listen_key().await?;
                    }
                    params.insert(self.listen_key.clone());
                }
                SubscribeChannel::Position(symbols) => {
                    self.position_symbols.clear();
                    self.position_symbols.extend(symbols.iter().cloned());

                    if self.listen_key.is_empty() {
                        self.listen_key = self.rest.create_listen_key().await?;
                    }
                    params.insert(self.listen_key.clone());
                }
                SubscribeChannel::OrderAndFill(symbols) => {
                    self.order_and_fill_symbols.clear();
                    self.order_and_fill_symbols.extend(symbols.iter().cloned());

                    if self.listen_key.is_empty() {
                        self.listen_key = self.rest.create_listen_key().await?;
                    }
                    params.insert(self.listen_key.clone());
                }
                SubscribeChannel::Balance | SubscribeChannel::FundingFee(_) => {
                    if self.listen_key.is_empty() {
                        self.listen_key = self.rest.create_listen_key().await?;
                    }
                    params.insert(self.listen_key.clone());
                }
                SubscribeChannel::Kline(kline_ws_params) => {
                    let interval = &kline_ws_params.interval;
                    for symbol in kline_ws_params.symbols.iter() {
                        let contract_type = BnContractType::from_symbol(symbol)?;
                        let contract_type = serde_plain::to_string(&contract_type)?;
                        let symbol = Symbol::new_with_quote(&symbol.base, symbol.quote.clone());
                        let symbol: BnSymbol = symbol.into();
                        let body = format!("{symbol}_{contract_type}").to_lowercase();
                        let channel = format!("{body}@{KLINE_CHANNEL}_{interval}");
                        params.insert(channel);
                    }
                }
            }
        }

        if params.is_empty() {
            warn!("sub params is empty");
            return Ok(());
        }
        let req = SubscriptionRequest {
            id: time(),
            method: SubReqMethod::Subscribe,
            params: params.into_iter().collect(),
        };

        let req_str = sonic_rs::to_string(&req)?;
        let message = Message::Text(req_str);
        w.send(message).await?;
        debug!("send sub message success");
        Ok(())
    }
}

#[derive(Debug, Default)]
enum LocalDepthState {
    #[default]
    Init,
    Pending {
        pending: Vec<DepthUpdate>,
        handle: JoinHandle<Result<BnDepth>>,
    },
    Working(LocalDepth),
}

async fn depth_snapshot_task(
    config: &ExConfig,
    delay: &mut Instant,
    symbol: BnSymbol,
) -> JoinHandle<Result<BnDepth>> {
    let config = config.clone();
    *delay = (*delay).max(Instant::now()) + Duration::from_secs_f32(2400. / 20. / 60. + 0.1); // 一次调用20，一分钟不超过2400权重
    let wait = *delay;

    tokio::spawn(async move {
        tokio::time::sleep_until(wait).await;
        let rest = BinanceSwap::new(config).await;
        rest.depth(symbol, Some(1000)).await // 按文档要求
    })
}

async fn create_local_depth(
    handle: &mut JoinHandle<Result<BnDepth>>,
    symbol: &BnSymbol,
    pending: &[DepthUpdate],
) -> Result<LocalDepth> {
    let depth = handle.await??;
    LocalDepth::new(symbol.clone(), depth, pending.to_vec())
}

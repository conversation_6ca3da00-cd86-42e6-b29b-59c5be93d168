use std::collections::HashMap;

use ethers::signers::{LocalWallet, Signer};
use hex::ToHex;
use log::info;
use quant_common::{
    base::{
        traits::SetLeverageParams, ExConfig, FailOrder, InsState, OrderId, QuoteCcy, Rest, Symbol,
        UserRequest,
    },
    new_reqwest_client, qerror, tick_ceil, time_ms,
};
use rand::thread_rng;
use reqwest::{
    get,
    header::{HeaderMap, HeaderValue, CONTENT_TYPE},
    Method,
};
use serde::{de::DeserializeOwned, Serialize};
use sonic_rs::{JsonValueTrait, Value};
use url::Url;

use crate::{
    define::{
        self,
        action::{
            Actions, BulkCancel, BulkCancelCloid, BulkModify, BulkOrder, CancelRequest,
            CancelRequestCloid, CandleSnapshotRequest, ClientOrderRequest, ExchangePayload,
            ExchangeResponseStatus, FrontendOpenOrdersResp, FundingEntry, InfoRequest, MarketData,
            ModifyRequest, UpdateLeverage, UserFillsResponse, UserStateResponse,
        },
        model::{
            AssetInfo, AssetInfoFun, CandlesSnapshotResponse, L2SnapshotResponse, MarketData2,
            OrderInfo, OrderStatusResponse, UniverseInfo2,
        },
    },
    swap::SwapMeta,
    util::{
        self,
        signature::{agent::l1, create_signature::sign_typed_data},
    },
};

#[derive(Debug, Clone)]
pub struct HyperLiquidSwap {
    pub url: Url,
    pub(crate) client: reqwest::Client,
    pub(crate) config: ExConfig,
    pub(crate) coin_to_asset: AssetInfo,
    pub(crate) coin_meta: HashMap<String, u32>,
}

impl HyperLiquidSwap {
    pub async fn new(config: ExConfig) -> Self {
        let mut url_str = if config.is_testnet {
            define::TEST_API_HYPERLIQUID
        } else {
            define::API_HYPERLIQUID
        }
        .to_string();
        if let Some(host) = config.host.clone() {
            url_str = format!("https://{}", host);
        }
        let url: Url = url_str.parse().unwrap();
        let rest = new_reqwest_client();
        let input = InfoRequest::Meta;
        let params = sonic_rs::to_string(&input).unwrap();
        let response = rest
            .request(Method::POST, format!("{}{}", url_str, define::PATH_INFO))
            .body(params)
            .header(CONTENT_TYPE, HeaderValue::from_static("application/json"))
            .send()
            .await
            .unwrap();
        let bytes = response.bytes().await.unwrap();
        let info: SwapMeta = sonic_rs::from_slice(&bytes).unwrap();
        let mut coin_to_asset: AssetInfo = Default::default();
        let mut coin_meta: HashMap<String, u32> = Default::default();
        for (index, asset) in info.universe.iter().enumerate() {
            let mut quote = QuoteCcy::USDT;
            // the only USDC-denominated perpetual contracts are PURR-USD and HYPE-USD
            if asset.name == "PURR" || asset.name == "HYPE" {
                quote = QuoteCcy::USDC;
            }
            coin_to_asset.token_swap_symbol.insert(
                asset.name.clone(),
                Symbol::new_with_quote(&asset.name, quote),
            );
            coin_to_asset.swap_symbol_token.insert(
                Symbol::new_with_quote(&asset.name, quote),
                asset.name.clone(),
            );
            coin_meta.insert(asset.name.clone(), asset.sz_decimals);
            coin_to_asset.token_ind.insert(asset.name.clone(), index);
        }
        Self {
            url,
            client: rest,
            config,
            coin_to_asset,
            coin_meta,
        }
    }
    pub fn new_wallect() -> quant_common::Result<String> {
        // 生成一个新的随机私钥
        let wallet = LocalWallet::new(&mut thread_rng());
        let private_key = wallet.signer().to_bytes().encode_hex();
        Ok(private_key)
    }

    #[inline(always)]
    async fn post<P: Serialize, R: DeserializeOwned>(
        &self,
        path: &str,
        params: &P,
        with_auth: bool,
    ) -> quant_common::Result<R> {
        let body = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::POST.to_string(),
            path: path.to_string(),
            auth: with_auth,
            body: Some(body),
            ..Default::default()
        };
        let value: Value = self.request(user_req).await?;
        let rsp: R = sonic_rs::from_value(&value)?;
        Ok(rsp)
    }
    #[inline(always)]
    async fn post2<P: Serialize>(
        &self,
        path: &str,
        params: &P,
        with_auth: bool,
    ) -> quant_common::Result<Value> {
        let body = sonic_rs::to_value(params)?;
        let user_req = UserRequest {
            method: Method::POST.to_string(),
            path: path.to_string(),
            auth: with_auth,
            body: Some(body),
            ..Default::default()
        };
        self.request(user_req).await
    }
}

impl Rest for HyperLiquidSwap {
    fn name(&self) -> &'static str {
        define::EXCHANGESWAP
    }

    async fn request(
        &self,
        req: quant_common::base::UserRequest,
    ) -> quant_common::Result<sonic_rs::Value> {
        let mut url = match req.url {
            Some(url) => url.parse::<Url>()?.join(&req.path)?,
            None => self.url.join(&req.path)?,
        };
        #[allow(unused_mut)]
        let mut req_builder;
        let mut headers = HeaderMap::new();
        if req.auth {
            //需要认证，参数必须在body中
            //数据反序列化回去，然后签名
            let wallet: LocalWallet = self.config.secret.parse()?;
            if let Some(body) = req.body.clone() {
                let agrs: Actions = sonic_rs::from_value(&body)?;
                let nonce = util::next_nonce();
                let (data, signature) = match agrs.clone() {
                    Actions::UsdClassTransfer(action) => {
                        let mut data = action;
                        data.nonce = nonce;
                        let signature = sign_typed_data(&data, &wallet)?;
                        (Actions::UsdClassTransfer(data), signature)
                    }
                    _ => {
                        let connection_id = agrs.hash(nonce, None)?; //todo update  vault_address
                        let signature = sign_typed_data(
                            &l1::Agent {
                                source: if self.config.is_testnet {
                                    String::from("b")
                                } else {
                                    String::from("a")
                                }, //"a"主网,"b"测试网
                                connection_id,
                            },
                            &wallet,
                        )?;
                        (agrs, signature)
                    }
                };
                let payload = ExchangePayload {
                    action: sonic_rs::to_value(&data)?,
                    signature,
                    nonce,
                    is_frontend: None,
                    vault_address: None,
                    //Some actions support an optional field expiresAfter which is a timestamp in milliseconds after which the action will be rejected.
                    //User-signed actions such as Core USDC transfer do not support the expiresAfter field.
                    //Note that actions consume 5x the usual address-based rate limit when canceled due to a stale expiresAfter field.
                    expires_after: None,
                };
                let params = sonic_rs::to_string(&payload)?;
                if req.query.is_some() {
                    url.set_query(Some(params.as_str()));
                }
                let method = Method::from_bytes(req.method.as_bytes())?;
                // info!("url={}||method={}||params={}",url.to_string(),method,params);
                req_builder = self
                    .client
                    .request(method, url.clone().to_string())
                    .body(params)
            } else {
                return Err(qerror!("{} auth request body is none", self.name()));
            }
        } else {
            let query = match req.query {
                Some(query) => serde_urlencoded::to_string(query)?,
                None => "".to_string(),
            };
            let body = match req.body {
                Some(body) => sonic_rs::to_string(&body)?,
                None => "".to_string(),
            };
            if !query.is_empty() {
                url.set_query(Some(query.as_str()));
            }
            let method = Method::from_bytes(req.method.as_bytes())?;
            // info!("url={}||method={}||body={}||query={}",url.to_string(),method,body,query);
            req_builder = if method == Method::GET {
                self.client.request(method, url.clone().to_string())
            } else {
                self.client
                    .request(method, url.clone().to_string())
                    .body(body)
            };
        }
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        let response = req_builder.headers(headers).send().await?;
        if !response.status().is_success() {
            let status = response.status().to_string();
            let body = response.text().await.expect("get text err");
            return Err(qerror!(
                "{}{} status:{status},body:{body}",
                url.host_str().unwrap(),
                url.path()
            ));
        }
        let bytes = response.bytes().await?;
        sonic_rs::from_slice(&bytes).map_err(|err| {
            qerror!(
                "{}?{} {err} {}",
                url.path(),
                url.query().unwrap(),
                std::str::from_utf8(&bytes).unwrap()
            )
        })
    }
    async fn get_instrument(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<quant_common::base::Instrument> {
        let result = self.get_instruments().await?;
        if let Some(info) = result.into_iter().find(|x| x.symbol == symbol) {
            Ok(info)
        } else {
            Err(qerror!("not found symbol:{}", symbol))
        }
    }

    async fn get_instruments(&self) -> quant_common::Result<Vec<quant_common::base::Instrument>> {
        /*
        For perpetuals coin is the name returned in the meta response.
        For Spot, coin should be PURR/USDC for PURR, and @{index}
        e.g. @1 for all other spot tokens where index is the index in the universe field
        of the spotMeta response.
        */
        let params = InfoRequest::Meta;
        let metas: SwapMeta = self.post(define::PATH_INFO, &params, false).await?;
        //Prices can have up to 5 significant figures,
        // but no more than MAX_DECIMALS - szDecimals decimal places where MAX_DECIMALS is 6 for perps and 8 for spot.
        // For example,
        // for perps, 1234.5 is valid but 1234.56 is not (too many significant figures). 0.001234 is valid,
        // but 0.0012345 is not (more than 6 decimal places).
        // For spot, 0.0001234 is valid if szDecimals is 0 or 1,
        // but not if szDecimals is greater than 2 (more than 8-2 decimal places).
        // Integer prices are always allowed, regardless of the number of significant figures.
        // E.g. 123456.0 is a valid price even though 12345.6 is not.

        //Sizes are rounded to the szDecimals of that asset. For example, if szDecimals = 3 then 1.001 is a valid size but 1.0001 is not.
        Ok(metas
            .universe
            .into_iter()
            .map(|asset_info| {
                let price_tick = 6. - asset_info.sz_decimals as f64;
                let mut quote = QuoteCcy::USDT;
                // the only USDC-denominated perpetual contracts are PURR-USD and HYPE-USD
                if asset_info.name == "PURR" || asset_info.name == "HYPE" {
                    quote = QuoteCcy::USDC;
                }
                quant_common::base::Instrument {
                    symbol: Symbol::new_with_quote(&asset_info.name, quote),
                    state: InsState::Normal,
                    price_tick: 10f64.powf(-price_tick),
                    amount_tick: 10f64.powf(-(asset_info.sz_decimals as f64)),
                    price_precision: price_tick as i32,
                    amount_precision: asset_info.sz_decimals as i32,
                    min_qty: 0.0,
                    min_notional: 10., //Order must have minimum value of $10
                    price_multiplier: 1.,
                    amount_multiplier: 1.,
                }
            })
            .collect())
    }
    async fn set_leverage(
        &self,
        _symbol: quant_common::base::Symbol,
        _leverage: u8,
    ) -> quant_common::Result<()> {
        Err(qerror!(
            "{} set leverage not support recommend use set_leverage_ext",
            self.name()
        ))
    }

    /// 参数params extra
    ///
    /// key1:"is_cross"
    /// value1:true or false if updating cross-leverage,
    /// 当设置杠杆时，is_cross 的值取决于保证金模式（margin mode）：
    /// 如果使用逐仓模式（Isolated Margin），is_cross 为 false
    /// 如果使用全仓模式（Cross Margin），is_cross 为 true
    async fn set_leverage_ext(&self, params: SetLeverageParams) -> quant_common::Result<()> {
        if let Some(asset_index) = self.coin_to_asset.token_ind.get(&params.symbol.base) {
            let is_cross = match params.extra.get("is_cross") {
                Some(is_cross) => match serde_json::from_value::<bool>(is_cross.clone()) {
                    Ok(is_cross) => is_cross,
                    Err(_) => {
                        return Err(qerror!(
                            "{} set leverage extra key1:\"is_cross\" is required",
                            self.name()
                        ))
                    }
                },
                None => {
                    return Err(qerror!(
                        "{} set leverage extra key1:\"is_cross\" is required\n
                参数params extra\n
                key1:\"is_cross\"\n
                value1:true or false if updating cross-leverage,\n
                当设置杠杆时,is_cross 的值取决于保证金模式(margin mode):\n
                如果使用逐仓模式(Isolated Margin),is_cross 为 false\n
                如果使用全仓模式(Cross Margin),is_cross 为 true",
                        self.name()
                    ))
                }
            };
            let action = Actions::UpdateLeverage(UpdateLeverage {
                asset: *asset_index as u32,
                is_cross, // true or false if updating cross-leverage,
                leverage: params.leverage as u32,
            });
            let info: ExchangeResponseStatus =
                self.post(define::PAYH_EXCHANGE, &action, true).await?;
            match info {
                ExchangeResponseStatus::Ok(_exchange_response) => Ok(()),
                ExchangeResponseStatus::Err(_) => Err(qerror!("{:?}", info)),
            }
        } else {
            Err(qerror!(
                "{} set leverage symbol:{} not found",
                self.name(),
                params.symbol
            ))
        }
    }
    async fn get_fee_rate(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<quant_common::base::FeeRate> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let params = InfoRequest::UserFills {
            user: wallet.address(),
        };
        let rsp: Vec<UserFillsResponse> = self.post(define::PATH_INFO, &params, false).await?;
        for item in rsp {
            if item.coin == symbol.base && item.crossed {
                let fee = item.fee.parse::<f64>()?;
                let px = item.px.parse::<f64>()?;
                let sz = item.sz.parse::<f64>()?;
                let builder_fee = item.builder_fee.unwrap_or("0".to_string()).parse::<f64>()?;
                let taker = tick_ceil((fee + builder_fee) / (px * sz), 0.000001);
                return Ok(quant_common::base::FeeRate {
                    taker,
                    maker: taker,
                    buyer: taker,
                    seller: taker,
                });
            }
        }
        Err(qerror!("{}:{}get fee rate err", self.name(), symbol))
    }

    async fn get_balances(&self) -> quant_common::Result<Vec<quant_common::base::Balance>> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let input = InfoRequest::UserState {
            user: wallet.address(),
        };
        let rsp: UserStateResponse = self.post(define::PATH_INFO, &input, false).await?;
        let result = quant_common::base::Balance {
            asset: QuoteCcy::USDC.to_string(), //以usdc作为抵押物
            balance: rsp.margin_summary.total_raw_usd.parse()?,
            available_balance: rsp.margin_summary.account_value.parse()?,
            unrealized_pnl: rsp.margin_summary.total_ntl_pos.parse()?,
        };
        Ok(vec![result])
    }
    async fn get_open_orders(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<Vec<quant_common::base::Order>> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let params = InfoRequest::FrontendOpenOrders {
            user: wallet.address(),
        };
        let rsp: Vec<FrontendOpenOrdersResp> = self.post(define::PATH_INFO, &params, false).await?;
        let mut result = Vec::new();
        rsp.into_iter().for_each(|item| {
            if item.coin == symbol.base {
                result.push(item.to_order(symbol.clone()));
            }
        });
        Ok(result)
    }
    async fn get_all_open_orders(&self) -> quant_common::Result<Vec<quant_common::base::Order>> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let params = InfoRequest::FrontendOpenOrders {
            user: wallet.address(),
        };
        let rsp: Vec<FrontendOpenOrdersResp> = self.post(define::PATH_INFO, &params, false).await?;
        Ok(rsp
            .into_iter()
            .map(|item| {
                let mut quote = QuoteCcy::USDT;
                // the only USDC-denominated perpetual contracts are PURR-USD and HYPE-USD
                if item.coin == "PURR" || item.coin == "HYPE" {
                    quote = QuoteCcy::USDC;
                }
                item.to_order(Symbol::new_with_quote(&item.coin, quote))
            })
            .collect())
    }
    async fn get_order_by_id(
        &self,
        symbol: Symbol,
        order_id: OrderId,
    ) -> quant_common::Result<quant_common::base::Order> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let oid = match order_id {
            OrderId::Id(id) => id.parse()?,
            OrderId::ClientOrderId(cid) => cid.parse()?,
        };
        let input = InfoRequest::OrderStatus {
            user: wallet.address(),
            oid,
        };
        let rsp: OrderStatusResponse = self.post(define::PATH_INFO, &input, false).await?;
        if let Some(info) = rsp.order {
            return Ok(info.to_order(symbol));
        }
        Err(qerror!(
            "order not found||symbol={},order_id={}",
            symbol,
            oid
        ))
    }
    async fn get_orders(
        &self,
        symbol: quant_common::base::Symbol,
        start_time: i64,
        end_time: i64,
    ) -> quant_common::Result<Vec<quant_common::base::Order>> {
        if let Some(index) = self.coin_to_asset.get_swap(&symbol) {
            let wallet: LocalWallet = self.config.secret.parse()?;
            let input = InfoRequest::HistoricalOrders {
                user: wallet.address(),
            };
            let rsp: Vec<OrderInfo> = self.post(define::PATH_INFO, &input, false).await?;
            let mut result = Vec::new();
            for info in rsp {
                if info.order.coin != index {
                    continue;
                }
                if let Some(msymbol) = self
                    .coin_to_asset
                    .from_swap_to_symbol(info.order.coin.clone())
                {
                    if symbol != msymbol {
                        continue;
                    }
                    if (info.order.timestamp as i64) >= start_time
                        && (info.order.timestamp as i64) <= end_time
                    {
                        result.push(info.to_order(symbol.clone()));
                    }
                };
            }
            return Ok(result);
        }
        Err(qerror!(
            "{} order not  support|symbol={}",
            self.name(),
            symbol
        ))
    }
    async fn post_order(
        &self,
        order: quant_common::base::Order,
        params: quant_common::base::OrderParams,
    ) -> quant_common::Result<String> {
        let order_items = ClientOrderRequest::from_order(order, params);
        if order_items.is_empty() {
            return Err(qerror!("{} order price is required", self.name()));
        }
        let mut orders = Vec::new();
        for order_item in order_items {
            let asset = order_item.asset.clone();
            orders.push(order_item.convert(
                &self.coin_to_asset.token_ind,
                self.coin_meta[asset.as_str()],
                6,
            )?);
        }
        let mut grouping = "na".to_string();
        if orders.len() > 1 {
            grouping = "normalTpsl".to_string();
        }
        let action = Actions::Order(BulkOrder {
            orders,
            grouping,
            builder: None,
        });
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        match info {
            ExchangeResponseStatus::Ok(exchange_response) => match exchange_response.data {
                Some(data) => match data.statuses.first() {
                    Some(status) => match status {
                        define::action::ExchangeDataStatus::Resting(resting_order) => {
                            Ok(resting_order.oid.to_string())
                        }
                        define::action::ExchangeDataStatus::Filled(filled_order) => {
                            Ok(filled_order.oid.to_string())
                        }
                        _ => Err(qerror!("{:?}", status)),
                    },
                    None => Err(qerror!("{:?}", data.statuses)),
                },
                None => Err(qerror!("{:?}", exchange_response)),
            },
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    async fn post_batch_order(
        &self,
        orders: Vec<quant_common::base::Order>,
        params: quant_common::base::OrderParams,
    ) -> quant_common::Result<quant_common::base::BatchOrderRsp> {
        let mut transformed_orders = Vec::new();
        let mut grouping = "na".to_string();
        for order in orders.clone() {
            let order_items = ClientOrderRequest::from_order(order.clone(), params.clone());
            if order_items.is_empty() {
                return Err(qerror!("{} order price is required", self.name()));
            }
            let mut orders = Vec::new();
            for order_item in order_items {
                let asset = order_item.asset.clone();
                orders.push(order_item.convert(
                    &self.coin_to_asset.token_ind,
                    self.coin_meta[asset.as_str()],
                    6,
                )?);
            }
            if orders.len() > 1 {
                grouping = "normalTpsl".to_string();
            }
            transformed_orders.append(&mut orders);
        }
        let action = Actions::Order(BulkOrder {
            orders: transformed_orders,
            grouping,
            builder: None,
        });
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        let mut result = quant_common::base::BatchOrderRsp::default();
        match info {
            ExchangeResponseStatus::Ok(exchange_response) => match exchange_response.data {
                Some(data) => {
                    for status in data.statuses {
                        match status {
                            define::action::ExchangeDataStatus::Resting(resting_order) => {
                                result.success_list.push(quant_common::base::SuccessOrder {
                                    id: Some(resting_order.oid.to_string()),
                                    cid: resting_order.cloid,
                                })
                            }
                            define::action::ExchangeDataStatus::Filled(filled_order) => {
                                result.success_list.push(quant_common::base::SuccessOrder {
                                    id: Some(filled_order.oid.to_string()),
                                    cid: filled_order.cloid,
                                })
                            }
                            define::action::ExchangeDataStatus::Error(e) => {
                                result.failure_list.push(FailOrder {
                                    id: None,
                                    cid: None,
                                    error_code: 0,
                                    error: e,
                                });
                            }
                            _ => continue,
                        };
                    }
                    //进一步处理失败的订单
                    orders.into_iter().for_each(|v| {
                        if !result.success_list.iter().any(|item| item.cid == v.cid) {
                            result.failure_list.push(FailOrder {
                                id: Some(v.id),
                                cid: v.cid,
                                error_code: 0,
                                error: "order failed".to_string(),
                            });
                        }
                    });

                    Ok(result)
                }
                None => Err(qerror!("{:?}", exchange_response)),
            },
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }
    async fn amend_order(&self, order: quant_common::base::Order) -> quant_common::Result<String> {
        let modify: ClientOrderRequest = order.clone().into();
        let action = Actions::BatchModify(BulkModify {
            modifies: vec![ModifyRequest {
                oid: order.id.parse()?,
                order: modify.convert(
                    &self.coin_to_asset.token_ind,
                    self.coin_meta[order.symbol.base.as_str()],
                    6,
                )?,
            }],
        });
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        match info {
            ExchangeResponseStatus::Ok(exchange_response) => match exchange_response.data {
                Some(data) => match data.statuses.first() {
                    Some(status) => match status {
                        define::action::ExchangeDataStatus::Resting(resting_order) => {
                            Ok(resting_order.oid.to_string())
                        }
                        define::action::ExchangeDataStatus::Filled(filled_order) => {
                            Ok(filled_order.oid.to_string())
                        }
                        _ => Err(qerror!("{:?}", status)),
                    },
                    None => Err(qerror!("{:?}", data.statuses)),
                },
                None => Err(qerror!("{:?}", exchange_response)),
            },
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    async fn cancel_order(
        &self,
        symbol: quant_common::base::Symbol,
        order_id: quant_common::base::OrderId,
    ) -> quant_common::Result<()> {
        let asset = self
            .coin_to_asset
            .token_ind
            .get(&symbol.base)
            .ok_or(qerror!("{} AssetNotFound", self.name()))?;
        let action = match order_id {
            OrderId::Id(id) => Actions::Cancel(BulkCancel {
                cancels: vec![CancelRequest {
                    asset: *asset as u32,
                    oid: id.parse()?,
                }],
            }),
            OrderId::ClientOrderId(cid) => Actions::CancelByCloid(BulkCancelCloid {
                cancels: vec![CancelRequestCloid {
                    asset: *asset as u32,
                    cloid: cid,
                }],
            }),
        };
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        match info {
            ExchangeResponseStatus::Ok(_exchange_response) => Ok(()),
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    async fn batch_cancel_order(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<quant_common::base::BatchOrderRsp> {
        let orders = self.get_open_orders(symbol.clone()).await?;
        let mut transformed_cancels = Vec::new();
        for order in orders.clone() {
            if order.symbol.base == symbol.base {
                let asset = self
                    .coin_to_asset
                    .token_ind
                    .get(&symbol.base)
                    .ok_or(qerror!("{} AssetNotFound", self.name()))?;
                transformed_cancels.push(CancelRequest {
                    asset: *asset as u32,
                    oid: order.id.parse()?,
                });
            }
        }
        let action = Actions::Cancel(BulkCancel {
            cancels: transformed_cancels,
        });
        let info: ExchangeResponseStatus = self.post(define::PAYH_EXCHANGE, &action, true).await?;
        let mut result = quant_common::base::BatchOrderRsp::default();
        match info {
            ExchangeResponseStatus::Ok(exchange_response) => match exchange_response.data {
                Some(data) => {
                    for status in data.statuses {
                        match status {
                            define::action::ExchangeDataStatus::Resting(resting_order) => {
                                result.success_list.push(quant_common::base::SuccessOrder {
                                    id: Some(resting_order.oid.to_string()),
                                    cid: resting_order.cloid,
                                })
                            }
                            define::action::ExchangeDataStatus::Filled(filled_order) => {
                                result.success_list.push(quant_common::base::SuccessOrder {
                                    id: Some(filled_order.oid.to_string()),
                                    cid: filled_order.cloid,
                                })
                            }
                            _ => continue,
                        };
                    }
                    //进一步处理失败的订单
                    let orders_after = self.get_open_orders(symbol.clone()).await?;
                    if !orders_after.is_empty() {
                        orders_after.into_iter().for_each(|v| {
                            orders.iter().for_each(|item| {
                                if item.cid == v.cid {
                                    result.failure_list.push(FailOrder {
                                        id: Some(v.id.clone()),
                                        cid: v.cid.clone(),
                                        error_code: 0,
                                        error: "order failed".to_string(),
                                    });
                                }
                            })
                        });
                    } else {
                        orders.iter().for_each(|item| {
                            result.success_list.push(quant_common::base::SuccessOrder {
                                id: Some(item.id.clone()),
                                cid: item.cid.clone(),
                            })
                        })
                    }
                    Ok(result)
                }
                None => Err(qerror!("{:?}", exchange_response)),
            },
            ExchangeResponseStatus::Err(err) => Err(qerror!("{:?}", err)),
        }
    }

    async fn get_funding_rates(&self) -> quant_common::Result<Vec<quant_common::base::Funding>> {
        let input = InfoRequest::MetaAndAssetCtxs;
        let rsp: Value = self.post2(define::PATH_INFO, &input, false).await?;
        let mut market_data_info: Vec<MarketData2> = Vec::new();
        let mut universe_data_info: Vec<UniverseInfo2> = Vec::new();
        if let Some(universe) = rsp.get(0) {
            if let Some(universe_info) = universe.get("universe") {
                universe_data_info = sonic_rs::from_value(universe_info)?;
            }
        }
        if let Some(market_data) = rsp.get(1) {
            market_data_info = sonic_rs::from_value(market_data)?;
        }
        if market_data_info.len() != universe_data_info.len() {
            return Err(qerror!(
                "{} get_funding_rates error||data={}",
                self.name(),
                rsp
            ));
        }
        let mut result = Vec::new();
        for (index, item) in universe_data_info.iter().enumerate() {
            let mut quote = QuoteCcy::USDT;
            // the only USDC-denominated perpetual contracts are PURR-USD and HYPE-USD
            if item.name == "PURR" || item.name == "HYPE" {
                quote = QuoteCcy::USDC;
            }
            let symbol = Symbol::new_with_quote(item.name.as_str(), quote);
            result.push(quant_common::base::Funding {
                symbol,
                funding_rate: market_data_info[index].funding,
                next_funding_at: 0,
                funding_interval: Some(8),
            });
        }
        Ok(result)
    }

    async fn get_position(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<Vec<quant_common::base::Position>> {
        let rsp = self.get_positions().await?;
        Ok(rsp
            .into_iter()
            .filter(|item| item.symbol == symbol)
            .collect())
    }

    async fn get_positions(&self) -> quant_common::Result<Vec<quant_common::base::Position>> {
        let wallet: LocalWallet = self.config.secret.parse()?;
        let input = InfoRequest::UserState {
            user: wallet.address(),
        };
        let rsp: UserStateResponse = self.post(define::PATH_INFO, &input, false).await?;
        Ok(rsp
            .asset_positions
            .into_iter()
            .map(|item| item.into())
            .collect())
    }
    async fn get_bbo_ticker(
        &self,
        symbol: quant_common::base::Symbol,
    ) -> quant_common::Result<quant_common::base::BboTicker> {
        let info = self.get_depth(symbol.clone(), None).await?;
        if info.bids.is_empty() || info.asks.is_empty() {
            return Err(qerror!("{} get bbo ticker err", self.name()));
        }
        let item = quant_common::base::BboTicker {
            symbol,
            bid_price: info.bids[0].price,
            bid_qty: info.bids[0].amount,
            ask_price: info.asks[0].price,
            ask_qty: info.asks[0].amount,
            timestamp: info.timestamp,
        };
        Ok(item)
    }
    async fn get_depth(
        &self,
        symbol: quant_common::base::Symbol,
        limit: Option<u32>,
    ) -> quant_common::Result<quant_common::base::Depth> {
        if let Some(asset) = self.coin_to_asset.get_swap(&symbol) {
            let input = InfoRequest::L2Book { coin: asset };
            let rsp: L2SnapshotResponse = self.post(define::PATH_INFO, &input, false).await?;
            let mut result = quant_common::base::Depth {
                symbol,
                bids: vec![],
                asks: vec![],
                timestamp: rsp.time as i64,
            };
            if let Some(bids) = rsp.levels.first() {
                result.bids = bids.iter().map(|item| item.clone().into()).collect();
            }
            if let Some(asks) = rsp.levels.get(1) {
                result.asks = asks.iter().map(|item| item.clone().into()).collect();
            }
            return Ok(result.clone_limited(limit.unwrap_or(100) as usize));
        }
        Err(qerror!(
            "{} not support get symbol:{} depth",
            self.name(),
            symbol
        ))
    }

    async fn get_ticker(&self, symbol: Symbol) -> quant_common::Result<quant_common::base::Ticker> {
        if let Some(asset) = self.coin_to_asset.get_swap(&symbol) {
            let now_time = time_ms() as u64;
            let input = InfoRequest::CandleSnapshot {
                req: CandleSnapshotRequest {
                    coin: asset,
                    interval: "1d".to_string(),
                    start_time: now_time - 86400000,
                    end_time: now_time,
                },
            };
            let rsp: Vec<CandlesSnapshotResponse> =
                self.post(define::PATH_INFO, &input, false).await?;
            if let Some(item) = rsp.first() {
                let mut item = quant_common::base::Ticker {
                    symbol,
                    timestamp: item.time_close as i64,
                    high: item.high.parse()?,
                    low: item.low.parse()?,
                    open: item.open.parse()?,
                    close: item.close.parse()?,
                    volume: item.vlm.parse()?,
                    quote_volume: item.vlm.parse()?,
                    change: 0.,
                    change_percent: 0.,
                };
                item.change = item.close - item.open;
                item.change_percent = item.change / item.open;
                return Ok(item);
            }
        }
        Err(qerror!(
            "{} get_ticker error||symbol={}",
            self.name(),
            symbol
        ))
    }

    async fn get_bbo_tickers(&self) -> quant_common::Result<Vec<quant_common::base::BboTicker>> {
        Err(qerror!("{} not support get_bbo_tickers", self.name()))
    }
    async fn get_max_leverage(&self, symbol: Symbol) -> quant_common::Result<u8> {
        let params = InfoRequest::Meta;
        let metas: SwapMeta = self.post(define::PATH_INFO, &params, false).await?;
        for item_info in metas.universe {
            if item_info.name == symbol.base {
                return Ok(item_info.max_leverage as u8);
            }
        }
        Err(qerror!("{} not find symbol:{}", self.name(), symbol))
    }
    async fn get_max_position(
        &self,
        _symbol: Symbol,
        _leverage: u8,
    ) -> quant_common::Result<quant_common::base::MaxPosition> {
        Err(qerror!("{} not support get_max_position", self.name()))
    }
    async fn is_dual_side(&self) -> quant_common::Result<bool> {
        Ok(false)
    }

    async fn set_dual_side(&self, is_dual_side: bool) -> quant_common::Result<()> {
        Err(qerror!(
            "{} not support set dual side:{}",
            self.name(),
            is_dual_side
        ))
    }
    async fn get_tickers(&self) -> quant_common::Result<Vec<quant_common::base::Ticker>> {
        Err(qerror!("{} not support get_tickers", self.name()))
    }
    async fn get_mark_price(
        &self,
        symbol: Option<Symbol>,
    ) -> quant_common::Result<Vec<quant_common::base::MarkPrice>> {
        let input = InfoRequest::MetaAndAssetCtxs;
        let rsp: Value = self.post2(define::PATH_INFO, &input, false).await?;
        let mut market_data_info: Vec<MarketData2> = Vec::new();
        let mut universe_data_info: Vec<UniverseInfo2> = Vec::new();
        if let Some(universe) = rsp.get(0) {
            if let Some(universe_info) = universe.get("universe") {
                universe_data_info = sonic_rs::from_value(universe_info)?;
            }
        }
        if let Some(market_data) = rsp.get(1) {
            market_data_info = sonic_rs::from_value(market_data)?;
        }
        if market_data_info.len() != universe_data_info.len() {
            return Err(qerror!(
                "{} get_mark_price error||data={}",
                self.name(),
                rsp
            ));
        }
        let mut result = Vec::new();
        for (index, item) in universe_data_info.iter().enumerate() {
            let mut quote = QuoteCcy::USDT;
            // the only USDC-denominated perpetual contracts are PURR-USD and HYPE-USD
            if item.name == "PURR" || item.name == "HYPE" {
                quote = QuoteCcy::USDC;
            }
            let msymbol = Symbol::new_with_quote(item.name.as_str(), quote);
            if let Some(isymbol) = symbol.clone() {
                if msymbol == isymbol {
                    result.push(quant_common::base::MarkPrice {
                        symbol: msymbol,
                        price: market_data_info[index].mark_px,
                    });
                    break;
                }
            } else {
                result.push(quant_common::base::MarkPrice {
                    symbol: msymbol,
                    price: market_data_info[index].mark_px,
                });
            }
        }
        Ok(result)
    }
}

use std::str::FromStr;
use std::sync::Arc;
use std::time::Duration;

use quant_common::base::traits::PostStopOrderParams;
use quant_common::base::traits::StopOrderRsp;
use quant_common::base::*;
use quant_common::*;
use reqwest::Method;
use rustc_hash::FxHashMap;
use serde::de::DeserializeOwned;
// use serde_json::json;
use serde_json::Value as serdeValue;
use sha2::Digest;
use sha2::Sha512;
use sonic_rs::Value;
use tokio::sync::Mutex;
// use tokio_tungstenite::tungstenite::http::method;
use url::Url;

use crate::swap::model::*;

#[derive(Debug, Clone)]
pub struct GateSwap {
    pub url: Url,
    pub client: reqwest::Client,
    pub config: ExConfig,
    pub is_dual_side: Arc<Mutex<bool>>,
    pub funding_cache: Arc<Mutex<FxHashMap<String, f64>>>,
}

impl GateSwap {
    pub async fn new(config: base::model::ExConfig) -> Self {
        let client = new_reqwest_client();

        let host = config
            .host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(if config.is_testnet {
                REST_TEST_BASE_HOST.to_string()
            } else if config.is_colo {
                REST_COLO_BASE_HOST.to_string()
            } else {
                REST_BASE_HOST.to_string()
            });
        let url = if host == "fx-webws.gateio.live"
            || host == "webws.gateio.live"
            || host == "ws.gate.io"
            || host == "ws.gateio.io"
        {
            format!("https://{REST_BASE_HOST}")
        } else {
            format!("https://{host}")
        };

        // wss://fx-webws.gateio.live/v4/ws/usdt
        let gate_swap = Self {
            url: url.parse().unwrap(),
            client,
            config,
            funding_cache: Arc::new(Mutex::new(FxHashMap::default())),
            is_dual_side: Arc::new(Mutex::new(false)),
        };
        let is_dual_side = gate_swap.is_dual_side().await.unwrap_or_default();
        *gate_swap.is_dual_side.lock().await = is_dual_side;
        if MULTIPLIER_CACHE.get().is_none() {
            let mut map = FxHashMap::default();
            let contracts = retry_with_attempts_async(
                || async { gate_swap.contracts().await },
                5,
                Some(Duration::from_millis(200)),
            )
            .await
            .unwrap();
            for contract in contracts {
                map.insert(contract.name.0.base, contract.quanto_multiplier);
            }
            // 忽略设置失败的情况，可能是已经被其他线程设置了
            let _ = MULTIPLIER_CACHE.set(map);
        }

        gate_swap
    }
}

impl Rest for GateSwap {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, req: base::UserRequest) -> Result<Value> {
        let method = Method::from_str(&req.method)?;
        let query = req
            .query
            .map(|q| serde_urlencoded::to_string(q).unwrap_or_default())
            .unwrap_or_default();
        let body = req
            .body
            .map(|b| sonic_rs::to_string(&b).unwrap_or_default())
            .unwrap_or_default();
        let rsp: Value = self.req(method, &req.path, &query, &body, req.auth).await?;
        Ok(rsp)
    }

    async fn get_ticker(&self, symbol: base::model::Symbol) -> Result<base::model::Ticker> {
        let ser = serde_urlencoded::to_string(ContractReq::new(symbol))?;
        let ticker = self.get::<[GateTicker; 1]>(PATH_TICKER, &ser).await?;
        let [ticker] = ticker;
        Ok(ticker.into())
    }

    async fn get_tickers(&self) -> Result<Vec<base::model::Ticker>> {
        let tickers = self.get::<Vec<GateTicker>>(PATH_TICKER, "").await?;
        Ok(tickers.into_iter().map(Into::into).collect())
    }

    async fn get_bbo_ticker(&self, symbol: base::model::Symbol) -> Result<base::model::BboTicker> {
        let gate_symbol = GateSymbol::from(symbol);
        let depth = self.depth(gate_symbol.clone(), Some(1)).await?;
        Ok(depth.into_bbo_ticker(gate_symbol))
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<base::model::BboTicker>> {
        unimplemented!("GateSwap does not support getting all BBO tickers")
    }

    async fn get_depth(
        &self,
        symbol: base::model::Symbol,
        limit: Option<u32>,
    ) -> Result<base::model::Depth> {
        let gate_symbol = GateSymbol::from(symbol);
        let depth = self.depth(gate_symbol.clone(), limit).await?;
        let depth = depth
            .convert(gate_symbol)
            .truncate_limited(limit.unwrap_or(u32::MAX) as usize);
        Ok(depth)
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<base::model::Instrument> {
        let contract = self.contract(symbol.into()).await?;
        Ok(contract.into())
    }

    async fn get_instruments(&self) -> Result<Vec<base::model::Instrument>> {
        let contracts = self.contracts().await?;
        Ok(contracts.into_iter().map(Into::into).collect())
    }

    async fn get_funding_rates(&self) -> Result<Vec<base::model::Funding>> {
        Ok(self
            .contracts()
            .await?
            .into_iter()
            .map(Into::into)
            .collect())
    }

    async fn get_funding_rates_history_ext(
        &self,
        params: traits::GetFundingRateHistoryParams,
    ) -> Result<Vec<FundingHistory>> {
        // https://www.gate.com/docs/developers/apiv4/#funding-rate-history
        let symbol = match params.symbol {
            Some(ref symbol) => GateSymbol::from(symbol.clone()),
            None => return Err(qerror!("symbol 必须传递")),
        };
        let query = format!("contract={symbol}");
        let query = if params.limit > 0 {
            format!("{}&limit={}", query, params.limit)
        } else {
            query
        };
        let query = match params.since_secs {
            Some(since_secs) => format!("{query}&from={since_secs}"),
            None => query,
        };
        let rsp = self
            .get::<Vec<FundingRateRecord>>(PATH_FUNDING_RATE_HISTORY, &query)
            .await?;
        let mut result = vec![];
        let symbol = params.symbol.unwrap();
        for r in rsp.iter() {
            result.push(FundingHistory {
                symbol: symbol.clone(),
                funding_rate: r.r.parse::<f64>()?,
                funding_time: r.t as i64 * 1000, // seconds -> milliseconds
            });
        }
        Ok(result)
    }

    async fn get_orders(
        &self,
        _symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        let req = OrderReq {
            contract: Some(GateSymbol::from(_symbol)),
            status: None,
        };
        let orders = self.orders(req, PATH_TIME_ORDERS).await?;
        // filter by start_time and end_time
        let result = orders
            .into_iter()
            .filter(|o| o.timestamp >= start_time && o.timestamp <= end_time)
            .collect();
        Ok(result)
    }
    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        let req = OrderReq {
            contract: None,
            status: Some("open".to_string()),
        };

        let req = serde_urlencoded::to_string(&req)?;

        let mut orders: Vec<SpotGateOrder> = vec![];
        let rsp: Vec<SpotGateOrder> = self.get_signed(PATH_ORDER, &req).await?;
        orders.extend(rsp);

        Ok(orders.into_iter().map(SpotGateOrder::into_order).collect())
    }
    async fn get_open_orders(&self, _symbol: Symbol) -> Result<Vec<Order>> {
        let req = OrderReq {
            contract: Some(GateSymbol::from(_symbol)),
            status: Some("open".to_string()),
        };
        let req = serde_urlencoded::to_string(&req)?;

        let rsp: Vec<SpotGateOrder> = self.get_signed(PATH_ORDER, &req).await?;

        let mut orders: Vec<SpotGateOrder> = vec![];
        orders.extend(rsp);

        Ok(orders.into_iter().map(SpotGateOrder::into_order).collect())
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        if params.take_profit.is_some() || params.stop_loss.is_some() {
            warn!("take_profit and stop_loss need to be set through post_stop_order.");
        }
        let gate_order = GateOrderReq::new(order, params.is_dual_side)?;
        let body = sonic_rs::to_string(&gate_order)?;
        let rsp: OrderRsp = self.post(PATH_ORDER, &body).await?;
        Ok(rsp.id.to_string())
    }

    async fn post_stop_order(&self, params: PostStopOrderParams) -> Result<StopOrderRsp> {
        let mut ret = StopOrderRsp::default();
        let symbol = params.symbol;
        let is_dual_side = params.is_dual_side;
        if params.take_profit.is_some() {
            // long and take_profit: should be greater or equal
            let trigger_rule = match params.pos_side {
                PosSide::Long => FuturesTriggerRule::GreaterOrEqual,
                PosSide::Short => FuturesTriggerRule::LessOrEqual,
            };
            let price_type = match params.take_profit.as_ref().unwrap().trigger_price {
                TriggerPrice::ContractPrice(_) => FuturesPriceType::LastPrice,
                TriggerPrice::MarkPrice(_) => FuturesPriceType::MarkPrice,
            };
            let _ = match params.take_profit.as_ref().unwrap().trigger_action {
                TriggerAction::Limit(price) => {
                    warn!("GateSwap does not support limit price for take_profit");
                    Some(price)
                }
                TriggerAction::Market => None,
            };
            let gate_order = GateSwapPriceOrderReq::new(
                params.take_profit.as_ref().unwrap().trigger_price.price(),
                trigger_rule,
                Some(price_type),
                match params.pos_side {
                    PosSide::Long => FuturesOrderType::CloseLongPosition,
                    PosSide::Short => FuturesOrderType::CloseShortPosition,
                },
                // limit_price,
                symbol.clone().into(),
                is_dual_side,
            )?;
            let body = sonic_rs::to_string(&gate_order)?;
            let rsp: FuturePriceOrderRsp = self.post(PATH_PRICE_ORDERS, &body).await?;
            ret.take_profit = Some(rsp.id.to_string());
        }
        if params.stop_loss.is_some() {
            let trigger_rule = match params.pos_side {
                PosSide::Long => FuturesTriggerRule::LessOrEqual,
                PosSide::Short => FuturesTriggerRule::GreaterOrEqual,
            };
            let price_type = match params.stop_loss.as_ref().unwrap().trigger_price {
                TriggerPrice::ContractPrice(_) => FuturesPriceType::LastPrice,
                TriggerPrice::MarkPrice(_) => FuturesPriceType::MarkPrice,
            };
            let _ = match params.stop_loss.as_ref().unwrap().trigger_action {
                TriggerAction::Limit(price) => {
                    warn!("GateSwap does not support limit price for stop_loss");
                    Some(price)
                }
                TriggerAction::Market => None,
            };
            let gate_order = GateSwapPriceOrderReq::new(
                params.stop_loss.as_ref().unwrap().trigger_price.price(),
                trigger_rule,
                Some(price_type),
                match params.pos_side {
                    PosSide::Long => FuturesOrderType::CloseLongPosition,
                    PosSide::Short => FuturesOrderType::CloseShortPosition,
                },
                // limit_price,
                symbol.into(),
                is_dual_side,
            )?;
            let body = sonic_rs::to_string(&gate_order)?;
            let rsp: FuturePriceOrderRsp = self.post(PATH_PRICE_ORDERS, &body).await?;
            ret.stop_loss = Some(rsp.id.to_string());
        }
        Ok(ret)
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let gate_orders: Vec<GateOrderReq> = orders
            .into_iter()
            .map(|order| GateOrderReq::new(order, params.is_dual_side))
            .collect::<Result<Vec<_>>>()?;
        let body = sonic_rs::to_string(&gate_orders)?;

        let resp: serdeValue = self.post(PATH_BATCH_ORDER, &body).await?;
        let mut success_list = Vec::new();
        let mut failure_list = Vec::new();
        // 确保响应是一个数组
        if let Some(orders) = resp.as_array() {
            for order in orders {
                let succeeded = order["succeeded"].as_bool().unwrap_or(false);
                let id = order["id"].as_u64().map(|id| id.to_string());
                let text = order["text"].as_str().unwrap_or_default().to_string();

                if succeeded {
                    success_list.push(SuccessOrder {
                        id,
                        cid: Some(text),
                    });
                } else {
                    failure_list.push(FailOrder {
                        id,
                        cid: Some(text),
                        error_code: -1,
                        error: format!(
                            "Contract: {}, Status: {}",
                            order["contract"].as_str().unwrap_or_default(),
                            order["status"].as_str().unwrap_or_default()
                        ),
                    });
                }
            }
        }

        Ok(BatchOrderRsp {
            success_list,
            failure_list,
        })
        // unimplemented!()
    }

    async fn amend_order(&self, order: base::model::Order) -> Result<String> {
        let order_id = order.id.clone();

        let order_req = GateOrderReq::new(order, true)?;
        let req = AmendOrderReq {
            price: Some(order_req.price.to_string()),
            size: Some(order_req.size),
        };
        let body = sonic_rs::to_string(&req)?;
        let rsp: OrderRsp = self
            .req(
                Method::PUT,
                &format!("{PATH_ORDER}/{order_id}"),
                "",
                &body,
                true,
            )
            .await?;
        Ok(rsp.id.to_string())
    }

    async fn get_mark_price(&self, symbol: Option<Symbol>) -> Result<Vec<MarkPrice>> {
        if symbol.is_none() {
            let contracts = self.contracts().await?;
            let mut mark_prices = Vec::new();
            for contract in contracts {
                mark_prices.push(MarkPrice {
                    symbol: contract.name.into(),
                    price: contract.mark_price,
                });
            }
            return Ok(mark_prices);
        }
        let symbol = symbol.unwrap();
        let contract = self.contract(symbol.clone().into()).await?;
        Ok(vec![MarkPrice {
            symbol,
            price: contract.mark_price,
        }])
    }

    async fn cancel_order(
        &self,
        symbol: base::model::Symbol,
        order_id: base::model::OrderId,
    ) -> Result<()> {
        let order_id = match order_id {
            OrderId::Id(id) => id,
            OrderId::ClientOrderId(cid) => cid,
        };
        let query_string = serde_urlencoded::to_string(ContractReq::new(symbol))?;
        let _rsp = self
            .req::<OrderRsp>(
                Method::DELETE,
                &format!("{PATH_ORDER}/{order_id}"),
                &query_string,
                "",
                true,
            )
            .await?;
        Ok(())
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
        let query_string = serde_urlencoded::to_string(ContractReq::new(symbol))?;
        let rsp: Vec<OrderRsp> = self
            .req(Method::DELETE, PATH_ORDER, &query_string, "", true)
            .await?;
        let success_list = rsp
            .into_iter()
            .map(|order| SuccessOrder {
                id: Some(order.id.to_string()),
                cid: None,
            })
            .collect();

        Ok(BatchOrderRsp {
            success_list,
            failure_list: vec![],
        })
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        if self.config.is_testnet {
            return Ok(FeeRate {
                taker: 0.0005,
                maker: 0.0002,
                ..Default::default()
            });
        }
        let query_string = serde_urlencoded::to_string(CurrencyReq::new(symbol))?;
        let fee = self
            .get_signed::<AccountFee>(PATH_FEE, &query_string)
            .await?;
        Ok(fee.into())
    }

    /// 暂定仅支持USDT的余额查询，如需支持其他币种，需修改[`SETTLE`]
    async fn get_balances(&self) -> Result<Vec<base::model::Balance>> {
        if self.config.is_unified {
            return self.unified_balances().await;
        }
        Ok(vec![self.usdt_account().await?.into()])
    }

    async fn get_position(
        &self,
        symbol: base::model::Symbol,
    ) -> Result<Vec<base::model::Position>> {
        match self.is_dual_side().await? {
            true => {
                let positions = self
                    .get_signed::<Vec<GatePositionRest>>(
                        &format!("{PATH_SYMBOL_POSITIONS}/{symbol}"),
                        "",
                    )
                    .await?;
                Ok(positions.into_iter().map(Into::into).collect())
            }
            false => {
                let positions = self
                    .get_signed::<GatePositionRest>(
                        &format!("{PATH_SYMBOL_POSITIONS_SINGLE}/{symbol}"),
                        "",
                    )
                    .await?;
                Ok(vec![positions.into()])
            }
        }
    }

    async fn get_funding_fee(
        &self,
        symbol: Symbol,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Result<Vec<FundingFee>> {
        let req = FeeReq::new(symbol.clone(), start_time, end_time);
        let req = serde_urlencoded::to_string(&req)?;
        let positions = self
            .get_signed::<Vec<FeeRsp>>(PATH_ACCOUNT_BOOK, &req)
            .await?;
        let mut ret = vec![];
        for pos in positions {
            let funding_fee = FundingFee {
                symbol: symbol.clone(),
                funding_fee: pos.change.parse::<f64>()?,
                timestamp: pos.time,
            };

            // 检查缓存中是否已存在相同的数据
            let mut cache = self.funding_cache.lock().await;
            let cache = cache.entry(symbol.to_string()).or_insert_with(|| 0.0);
            *cache += funding_fee.funding_fee;
            ret.push(funding_fee);
            // if !cache.iter().any(|f| {
            //     f.funding_fee == funding_fee.funding_fee && f.timestamp == funding_fee.timestamp
            // }) {
            //     cache.push(funding_fee.clone());
            //     ret.push(funding_fee);
            // }
        }
        Ok(ret)
    }

    async fn get_positions(&self) -> Result<Vec<base::model::Position>> {
        let pos_list = self
            .get_signed::<Vec<GatePositionRest>>(PATH_POSITIONS, "")
            .await?;
        Ok(pos_list.into_iter().map(Into::into).collect())
    }

    async fn set_leverage(&self, symbol: base::model::Symbol, leverage: u8) -> Result<()> {
        let is_dual_side = self.is_dual_side().await?;
        match is_dual_side {
            true => {
                let risk_limit = self
                    .get_max_position(symbol.clone(), leverage)
                    .await?
                    .long_notional;
                self.set_risk_limit_tiers_dual_side(Some(symbol.clone()), risk_limit)
                    .await?;
                let symbol = GateSymbol::from(symbol);
                let is_isolated = false;
                let query = if is_isolated {
                    // 逐仓设置杠杠不能传入cross_leverage_limit参数
                    format!("leverage={leverage}")
                } else {
                    format!("leverage=0&cross_leverage_limit={leverage}")
                };
                let _rsp = self
                    .query_post::<Vec<GatePositionRest>>(
                        &format!("{PATH_SYMBOL_POSITIONS}/{symbol}/leverage"),
                        &query,
                    )
                    .await?;
                Ok(())
            }
            false => {
                let risk_limit = self
                    .get_max_position(symbol.clone(), leverage)
                    .await?
                    .long_notional;
                self.set_risk_limit_tiers(Some(symbol.clone()), risk_limit)
                    .await?;
                let symbol = GateSymbol::from(symbol);
                let is_isolated = false;
                let query = if is_isolated {
                    // 逐仓设置杠杠不能传入cross_leverage_limit参数
                    format!("leverage={leverage}")
                } else {
                    format!("leverage=0&cross_leverage_limit={leverage}")
                };
                let _rsp = self
                    .query_post::<GatePositionRest>(
                        &format!("{PATH_SYMBOL_POSITIONS_SINGLE}/{symbol}/leverage"),
                        &query,
                    )
                    .await?;
                Ok(())
            }
        }
    }

    async fn is_dual_side(&self) -> Result<bool> {
        Ok(self.usdt_account().await?.in_dual_mode)
    }

    async fn set_dual_side(&self, is_dual_side: bool) -> Result<()> {
        *self.is_dual_side.lock().await = is_dual_side;
        self.query_post::<GateAccount>(PATH_SET_DUAL_SIDE, &format!("dual_mode={is_dual_side}"))
            .await?;
        Ok(())
    }

    async fn get_max_leverage(&self, symbol: Symbol) -> Result<u8> {
        let resp = self.contract(symbol.into()).await?;
        Ok(resp.leverage_max)
    }

    async fn get_max_position(&self, symbol: Symbol, leverage: u8) -> Result<MaxPosition> {
        // 获取风险限额等级
        let risk_tiers = self.risk_limit_tiers(Some(symbol.clone())).await?;

        if risk_tiers.is_empty() {
            return Err(qerror!("No risk limit tiers found for symbol: {}", symbol));
        }

        // 找到适用于当前杠杆的最高等级
        let applicable_tier = risk_tiers
            .iter()
            .filter(|tier| leverage as f64 <= tier.leverage_max)
            .max_by(|a, b| a.tier.cmp(&b.tier));
        let contract = self.contract(symbol.clone().into()).await?;
        match applicable_tier {
            Some(tier) => {
                // 获取标记价格
                let max_notional = tier.risk_limit;
                let qty = max_notional / contract.mark_price;
                Ok(MaxPosition {
                    long_notional: max_notional,
                    short_notional: max_notional,
                    long_quantity: qty,
                    short_quantity: qty,
                })
            }
            None => {
                // 如果没有找到适用的等级，说明杠杆超出了最大限制
                let max_leverage = risk_tiers
                    .iter()
                    .map(|tier| tier.leverage_max)
                    .fold(0.0, f64::max) as u8;

                Err(qerror!(
                    "Leverage {} exceeds maximum allowed leverage {} for symbol {}",
                    leverage,
                    max_leverage,
                    symbol
                ))
            }
        }
    }

    async fn batch_cancel_order_by_ids(
        &self,
        _symbol: Option<Symbol>,
        _ids: Option<Vec<String>>,
        _cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        let ids_json = serde_json::to_string(&_ids.unwrap_or_default())?;
        // let params = json!({
        //     "body": ids_json
        // });
        // let body = serde_json::to_string(&params).unwrap();
        let resp: serdeValue = self.post(PATH_BATCH_CANCEL_OIDS, &ids_json).await?;

        let mut success_list = Vec::new();
        let mut failure_list = Vec::new();

        if let Some(orders) = resp.as_array() {
            for order in orders {
                let succeeded = order["succeeded"].as_bool().unwrap_or(false);
                let id = order["id"].as_str().unwrap_or_default().to_string();

                if succeeded {
                    success_list.push(SuccessOrder {
                        id: Some(id),
                        cid: None,
                    });
                } else {
                    let message = order["message"].as_str().unwrap_or_default();
                    failure_list.push(FailOrder {
                        id: Some(id),
                        cid: None,
                        error_code: -1,
                        error: message.to_string(),
                    });
                }
            }
        }

        Ok(BatchOrderRsp {
            success_list,
            failure_list,
        })
    }

    async fn get_order_by_id(&self, _symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let oid_req = match order_id {
            OrderId::Id(id) => id,
            OrderId::ClientOrderId(cid) => cid,
        };
        let path_get_order = format!("/api/v4/futures/{SETTLE}/orders/{oid_req}");
        let _resp: SpotGateOrder = self.req(Method::GET, &path_get_order, "", "", true).await?;
        Ok(_resp.into_order())
        // println!("{:?}",_resp);
        // unimplemented!()
    }
}

impl GateSwap {
    pub(crate) async fn req<RSP: DeserializeOwned>(
        &self,
        method: Method,
        path: &str,
        query: &str,
        body: &str,
        required_auth: bool,
    ) -> Result<RSP> {
        let mut url = self.url.join(path).unwrap();
        if !query.is_empty() {
            url.set_query(Some(query));
        }

        // println!("url:{}",url);
        let mut req = self.client.request(method.clone(), url);
        if required_auth {
            let time = time().to_string();
            let sign = rest_gen_sign(
                method.as_str(),
                path,
                query,
                body,
                &time,
                &self.config.secret,
            );
            req = req
                .header("Accept", "application/json")
                .header("Content-Type", "application/json")
                .header("Timestamp", &time)
                .header("SIGN", &sign)
                .header("KEY", &self.config.key);
        };
        if !body.is_empty() {
            let body = body.to_owned();
            req = req.body(body);
        }

        let rsp = req.send().await?;
        let status = rsp.status();

        if !status.is_success() {
            let rsp = rsp.text().await?;
            let error = format!("{method} {path} {query} {body} {status} {rsp}",);
            let error = match sonic_rs::from_str::<GateErr>(&rsp) {
                Ok(r) => mapping_error(r, &error),
                Err(e) => {
                    if status == reqwest::StatusCode::TOO_MANY_REQUESTS {
                        QuantError::network_error(error)
                    } else {
                        qerror!("{e}, {error}")
                    }
                }
            };

            return Err(error);
        }

        let rsp = rsp.bytes().await?;
        debug!("rsp: {rsp:?}");
        sonic_rs::from_slice::<RSP>(&rsp).map_err(|err| {
            let rsp = std::str::from_utf8(&rsp[0..rsp.len().min(1024)]).unwrap_or_default();
            qerror!("{method} {path} {query} {err} {rsp}")
        })
    }

    #[inline]
    async fn get<RSP: DeserializeOwned>(&self, path: &str, query: &str) -> Result<RSP> {
        self.req(Method::GET, path, query, "", false).await
    }

    #[inline]
    async fn get_signed<RSP: DeserializeOwned>(&self, path: &str, query: &str) -> Result<RSP> {
        self.req(Method::GET, path, query, "", true).await
    }

    #[inline]
    async fn query_post<RSP: DeserializeOwned>(&self, path: &str, body: &str) -> Result<RSP> {
        self.req(Method::POST, path, body, "", true).await
    }

    #[inline]
    async fn post<RSP: DeserializeOwned>(&self, path: &str, body: &str) -> Result<RSP> {
        self.req(Method::POST, path, "", body, true).await
    }

    pub async fn usdt_account(&self) -> Result<GateAccount> {
        self.get_signed(PATH_ACCOUNT, "").await
    }

    pub async fn contract(&self, symbol: GateSymbol) -> Result<Contract> {
        let r: Contract = self.get(&format!("{PATH_CONTRACTS}/{symbol}"), "").await?;
        Ok(r)
    }

    pub async fn contracts(&self) -> Result<Vec<Contract>> {
        let r: Vec<Contract> = self.get(PATH_CONTRACTS, "").await?;
        Ok(r.into_iter().filter(|x| x.is_open()).collect())
    }

    pub async fn risk_limit_tiers(&self, symbol: Option<Symbol>) -> Result<Vec<PosTier>> {
        let req = RiskLimitTierReq::new(symbol, None, None);
        let query_string = serde_urlencoded::to_string(&req)?;
        let r: Vec<PosTier> = self.get(PATH_MAX_POSITION, &query_string).await?;
        Ok(r)
    }

    pub async fn set_risk_limit_tiers_dual_side(
        &self,
        symbol: Option<Symbol>,
        tier: f64,
    ) -> Result<()> {
        // 双仓模式下设置风险限额，使用dual_comp API路径
        if let Some(symbol) = symbol {
            let path = format!("/api/v4/futures/{SETTLE}/dual_comp/positions/{symbol}/risk_limit");
            let query = format!("risk_limit={tier}");
            let _: Vec<GatePositionRest> = self.query_post(&path, &query).await?;
        } else {
            return Err(qerror!("Symbol is required for setting risk limit tiers"));
        }
        Ok(())
    }
    pub async fn set_risk_limit_tiers(&self, symbol: Option<Symbol>, tier: f64) -> Result<()> {
        // 根据Python示例，需要使用POST方法和正确的路径
        if let Some(symbol) = symbol {
            let path = format!("/api/v4/futures/{SETTLE}/positions/{symbol}/risk_limit");
            let query = format!("risk_limit={tier}");
            let _: GatePositionRest = self.query_post(&path, &query).await?;
        } else {
            return Err(qerror!("Symbol is required for setting risk limit tiers"));
        }
        Ok(())
    }
    pub async fn unified_balances(&self) -> Result<Vec<Balance>> {
        let r: GateAccountInfo = self.get_signed(PATH_UNI_ACCOUNT, "").await?;
        Ok(r.get_balances())
    }

    pub(crate) async fn depth(&self, symbol: GateSymbol, limit: Option<u32>) -> Result<DepthRest> {
        let query_string = serde_urlencoded::to_string(OrderBookReq {
            contract: symbol,
            with_id: true,
            limit,
        })?;
        self.get::<DepthRest>(PATH_DEPTH, &query_string).await
    }

    /// 查询vip等级,是否统一账户等信息
    pub async fn account_detail(&self) -> Result<AccountDetail> {
        let r: AccountDetail = self.get_signed(PATH_ACCOUNT_DETAIL, "").await?;
        Ok(r)
    }

    // pub async fn risk_limit_tiers(&self, symbol: Option<Symbol>) -> Result<Vec<PosTier>> {
    //     let req = RiskLimitTierReq::new(symbol, None, None);
    //     let query_string = serde_urlencoded::to_string(&req)?;
    //     let r: Vec<PosTier> = self.get(PATH_MAX_POSITION, &query_string).await?;
    //     Ok(r)
    // }

    pub async fn get_user_id(&self) -> Result<String> {
        if self.config.is_testnet || self.config.key.is_empty() {
            return Ok("********".to_string());
        }
        Ok(self.account_detail().await?.user_id.to_string())
    }

    pub async fn create_sub_account(
        &self,
        name: String,
        password: Option<String>,
        email: Option<String>,
    ) -> Result<()> {
        let body = sonic_rs::to_string(&SubAccountReq::new(name, password, email))?;
        let _: Value = self.post(PATH_CREATE_SUB_ACCOUNT, &body).await?;
        Ok(())
    }

    pub async fn get_sub_accounts(&self) -> Result<Vec<SubAccount>> {
        self.get_signed(PATH_GET_ACCOUNTS, "").await
    }

    pub async fn create_sub_account_api_key(
        &self,
        uid: String,
        ips: Option<Vec<String>>,
    ) -> Result<SubAccountKeyInfo> {
        let body = sonic_rs::to_string(&SubAccountKey::new_all_perm(ips))?;
        self.post(&format!("/api/v4/sub_accounts/{uid}/keys"), &body)
            .await
    }

    #[allow(dead_code)]
    async fn sub_transfer(
        &self,
        sub_account: String,
        currency: String,
        amount: f64,
        in_sub: bool,
        sub_type: String,
    ) -> Result<()> {
        let body = sonic_rs::to_string(&SubTransferReq::new(
            currency,
            sub_account,
            in_sub,
            amount,
            sub_type,
        ))?;
        let _: RawValue = self.post(PATH_SUB_TRANSFER, &body).await?;
        Ok(())
    }

    pub async fn get_account(&self) -> Result<Account> {
        self.get_signed(PATH_GET_ACCOUNT, "").await
    }

    async fn orders(&self, req: OrderReq, path: &'static str) -> Result<Vec<base::Order>, Error> {
        let mut orders: Vec<SpotGateOrder> = vec![];
        let req = serde_urlencoded::to_string(req)?;
        let rsp: Vec<SpotGateOrder> = self.get_signed(path, &req).await?;
        orders.extend(rsp);

        Ok(orders.into_iter().map(SpotGateOrder::into_order).collect())
    }
}

fn mapping_error(r: GateErr, error: &String) -> QuantError {
    match r.label.as_str() {
        "INSUFFICIENT_AVAILABLE" => QuantError::insufficient_balance(error),
        "ORDER_NOT_FOUND" => QuantError::order_not_found(error),
        "ORDER_POC_IMMEDIATE" => QuantError::maker_only_rejected(error),
        _ => qerror!("{error}"),
    }
}

fn rest_gen_sign(
    method: &str,
    path: &str,
    query_string: &str,
    body: &str,
    time: &str,
    secret: &str,
) -> String {
    use hmac::Mac;

    let mut hasher = Sha512::new();
    hasher.update(body.as_bytes());
    let digest = hasher.finalize();
    let body_hexdigest = format!("{digest:02x}");

    let s = format!("{method}\n{path}\n{query_string}\n{body_hexdigest}\n{time}");
    let mut mac = hmac::Hmac::<Sha512>::new_from_slice(secret.as_bytes()).unwrap();
    mac.update(s.as_bytes());
    let digest = mac.finalize().into_bytes();
    format!("{digest:02x}")
}

#[cfg(test)]
mod tests {
    use super::*;
    use quant_common::base::model::ExConfig;

    #[tokio::test]
    #[ignore] // 忽略这个测试，因为需要网络请求
    async fn test_get_max_position_with_leverage() {
        let config = ExConfig::default();
        let gate_swap = GateSwap::new(config).await;

        let symbol = Symbol::new("BTC_USDT");

        // 测试正常杠杆
        let result = gate_swap.get_max_position(symbol.clone(), 10).await;
        println!("Max position for leverage 10: {result:?}");
        assert!(result.is_ok());

        // 测试过高杠杆
        let result = gate_swap.get_max_position(symbol.clone(), 200).await;
        println!("Max position for leverage 200: {result:?}");
        // 这应该返回错误，因为杠杆过高
        if result.is_err() {
            println!("Expected error: {}", result.unwrap_err());
        }
    }

    #[tokio::test]
    #[ignore] // 忽略这个测试，因为需要网络请求
    async fn test_risk_limit_tiers() {
        let config = ExConfig::default();
        let gate_swap = GateSwap::new(config).await;

        let symbol = Symbol::new("BTC_USDT");
        let result = gate_swap.risk_limit_tiers(Some(symbol)).await;
        println!("Risk limit tiers: {result:?}");
        assert!(result.is_ok());

        let tiers = result.unwrap();
        assert!(!tiers.is_empty());

        // 验证风险限额等级是按tier排序的
        for i in 1..tiers.len() {
            assert!(tiers[i].tier > tiers[i - 1].tier);
        }
    }
}

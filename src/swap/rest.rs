use quant_common::{
    base::{model::*, traits::rest::Rest},
    new_reqwest_client, q<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result,
};
use reqwest::Method;
use rustc_hash::FxHashMap;
use serde::Serialize;
use sonic_rs::Value;
use std::fmt;
use std::fmt::Formatter;
use std::future::Future;
use std::time::Duration;
use url::Url;

use crate::swap::model::*;

#[derive(Debug, Clone)]
pub struct OkxSwap {
    pub url: Url,
    pub client: reqwest::Client,
    pub config: ExConfig,
}

impl Default for OkxSwap {
    fn default() -> Self {
        let url = format!("{}{}{}", REST_BASE_URL, API_PREFIX, VERSION);
        OkxSwap {
            url: url
                .parse()
                .unwrap_or_else(|_| panic!("Failed to parse URL: {}", url)),
            client: new_reqwest_client(),
            config: ExConfig::default(),
        }
    }
}

impl Rest for OkxSwap {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    /// 发送自定义请求
    async fn request(&self, user_request: UserRequest) -> Result<Value> {
        let req_url = self.url.join(&user_request.path)?;
        let method = Method::from_bytes(user_request.method.as_bytes())?;
        let mut req = self.client.request(method.clone(), req_url);

        let (mut body, mut query) = (None, None);
        if let Some(params) = user_request.query {
            req = req.query(&params);
            query = Some(params);
        }

        if let Some(params) = user_request.body {
            req = req.body(params.to_string());
            body = Some(params);
        }

        if user_request.auth {
            req = self.sign(method, &user_request.path, query, body, req)?;
        }
        if self.config.is_testnet {
            req = req.header(HEADER_TESTNET, "1");
        }

        let resp = req.send().await.map_err(|e| Error::network_error(e))?;
        let status = resp.status();
        if !status.is_success() {
            let err = format!("{:?}", resp.text().await?);
            return Err(qerror!("{err}"));
        }
        let resp_str = resp.text().await?;
        let resp = sonic_rs::from_str::<OkxResp>(&resp_str).map_err(|_| qerror!("{}", resp_str))?;
        if resp.code != 0 {
            let err = format!("{:?}", resp);
            if matches!(resp.code, 50011) {
                return Err(Error::exchange_rate_limit(err));
            }
            return Err(qerror!("{err}"));
        }
        Ok(resp.data)
    }

    // --------------------------------------------------公共方法--------------------------------------------------
    /// 获取给定交易对的行情信息
    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        let mut req = OkxTickerReq::default();
        req.set_symbol(symbol);
        let result = self.get(PATH_TICKER, false, req.into()).await?;
        let tickers = sonic_rs::from_value::<Vec<OkxTickerResp>>(&result)?;
        let ticker = tickers
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("Okx swap不存在该交易对"))?;
        Ok(ticker.into())
    }

    /// 获取所有交易对的行情信息
    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        let req = OkxTickersReq::default();
        let result = self.get(PATH_TICKERS, false, req.into()).await?;
        let tickers = sonic_rs::from_value::<Vec<OkxTickerResp>>(&result)?;
        Ok(tickers.into_iter().map(|t| t.into()).collect())
    }

    async fn get_mark_price(&self, symbol: Option<Symbol>) -> Result<Vec<MarkPrice>> {
        let req = OkxMarkPriceReq::new(symbol);
        let result = self.get(PATH_MARK_PRICE, false, req.into()).await?;
        let mark_prices = sonic_rs::from_value::<Vec<OkxMarkPriceResp>>(&result)?;
        Ok(mark_prices.into_iter().map(|m| m.into()).collect())
    }

    /// 获取给定交易对的最佳报价
    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        let mut req = OkxTickerReq::default();
        req.set_symbol(symbol);
        let result = self.get(PATH_TICKER, false, req.into()).await?;
        let tickers = sonic_rs::from_value::<Vec<OkxTickerResp>>(&result)?;
        let ticker = tickers
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("Okx swap 不存在该交易对"))?;
        Ok(ticker.into())
    }

    /// 获取所有交易对的最佳报价
    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        let req = OkxTickersReq::default();
        let result = self.get(PATH_TICKERS, false, req.into()).await?;
        let tickers = sonic_rs::from_value::<Vec<OkxTickerResp>>(&result)?;
        Ok(tickers.into_iter().map(|t| t.into()).collect())
    }

    /// 获取给定交易对的深度信息
    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        let req = OkxDepthReq::new(symbol.clone(), limit);
        let result = self.get(PATH_DEPTH, false, req.into()).await?;
        let depths = sonic_rs::from_value::<Vec<OkxDepthResp>>(&result)?;
        let mut depth_resp = depths
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No depth"))?;
        depth_resp.set_symbol(symbol);
        let depth = depth_resp.convert_to_depth();
        Ok(depth)
    }

    /// 获取给定交易对的合约信息
    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        let result = self.instruments(Some(symbol)).await?;
        let instrument = result
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No instrument"))?;
        Ok(instrument)
    }

    /// 获取所有交易对的合约信息
    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        self.instruments(None).await
    }

    /// 获取给定交易对的资金费率
    async fn get_funding_rate(&self, symbol: Symbol) -> Result<Funding> {
        let req = OkxFundingRateReq::new(symbol);
        let result = self.get(PATH_FUNDING_RATE, false, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxFundingRateResp>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("Okx swap 不存在该交易对"))?;
        Ok(resp.into())
    }

    /// 获取所有交易对的资金费率
    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        let req = OkxFundingRateReq::default();
        let result = self.get(PATH_FUNDING_RATE, false, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxFundingRateResp>>(&result)?;
        Ok(resp.into_iter().map(|r| r.into()).collect())
    }

    // --------------------------------------------------私有方法--------------------------------------------------

    // ***************************************************账户*****************************************************
    /// 设置给定交易对的杠杆倍数
    async fn set_leverage(&self, symbol: Symbol, leverage: u8) -> Result<()> {
        let margin_mode = MarginMode::Cross;
        let req = OkxSetLeverageReq::new(symbol, leverage, margin_mode);
        let _ = self.post(PATH_SET_LEVERAGE, true, req.into()).await?;
        Ok(())
    }

    /// 是否为双向持仓
    async fn is_dual_side(&self) -> Result<bool> {
        let result = self.get::<()>(PATH_ACCOUNT_CONFIG, true, None).await?;
        let config = sonic_rs::from_value::<Vec<OkxAccountConfig>>(&result)?;
        let config = config
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No account config"))?;
        Ok(config.is_dual_side())
    }

    /// 设置双向持仓
    async fn set_dual_side(&self, is_dual_side: bool) -> Result<()> {
        let mut req = OkxAccountConfig::default();
        req.set_position_mode(is_dual_side);
        let _ = self.post(PATH_SET_POSITION_MODE, true, req.into()).await?;
        Ok(())
    }

    /// 获取账户指定交易对的手续费率
    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        let req = OkxTradeFeeReq::new(symbol);
        let result = self.get(PATH_TRADE_FEE, true, req.into()).await?;
        let fees = sonic_rs::from_value::<Vec<OkxTradeFeeResp>>(&result)?;
        let fee = fees
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No fee rate"))?;
        Ok(fee.into())
    }

    /// 获取USDT的余额信息
    async fn get_usdt_balance(&self) -> Result<Balance> {
        self.get_balances()
            .await?
            .into_iter()
            .find(|b| b.asset == "USDT")
            .ok_or_else(|| qerror!("USDT balance not found"))
    }

    /// 获取全部的余额信息
    async fn get_balances(&self) -> Result<Vec<Balance>> {
        let result = self.get::<()>(PATH_ACCOUNT_BALANCE, true, None).await?;
        let mut account_balance = vec![];
        let balances = sonic_rs::from_value::<Vec<OkxBalancesResp>>(&result)?;
        if let Some(account) = balances.into_iter().next() {
            for balance in account.details {
                account_balance.push(balance.into());
            }
        }
        Ok(account_balance)
    }

    //// 查询交易所的折扣信息
    async fn get_fee_discount_info(&self) -> Result<Option<Discount>> {
        unimplemented!("Okx get fee discount info is not supported")
    }

    /// 查询账号是否开启原生币手续费折扣
    async fn is_fee_discount_enabled(&self) -> Result<bool> {
        unimplemented!("Okx is fee discount enabled is not supported")
    }

    /// 开关账号原生币手续费的折扣
    async fn set_fee_discount_enabled(&self, _enable: bool) -> Result<()> {
        unimplemented!("Okx set fee discount enabled is not supported")
    }

    /// 获取最大杠杆
    async fn get_max_leverage(&self, symbol: Symbol) -> Result<u8> {
        let req = OkxMaxLeverageReq::new(symbol);
        let result = self.get(PATH_MAX_LEVERAGE, true, req.into()).await?;

        let resp = sonic_rs::from_value::<Vec<OkxMaxLeverageResp>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No max leverage"))?;
        Ok(resp.max_lever)
    }

    /// 获取充值地址
    async fn get_deposit_address(
        &self,
        ccy: String,
        chain: Option<Chain>,
        _amount: Option<f64>,
    ) -> Result<Vec<DepositAddress>> {
        let req = OkxDepositAddressReq::new(ccy);
        let result = self.get(PATH_DEPOSIT_ADDRESS, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxDepositAddressResp>>(&result)?;
        let mut deposit_addresses: Vec<DepositAddress> =
            resp.into_iter().map(|r| r.into()).collect();
        if let Some(chain) = chain {
            deposit_addresses.retain(|d| d.chain == Some(chain.clone()));
        }
        Ok(deposit_addresses)
    }

    /// 提币
    async fn withdrawal(&self, withdrwl_params: WithDrawlParams) -> Result<()> {
        let req = OkxWithdrawalReq::new(withdrwl_params);
        let _ = self.post(PATH_WITHDRAWAL, true, req.into()).await?;
        Ok(())
    }

    /// 获取用户id
    async fn get_user_id(&self) -> Result<String> {
        let result = self.get::<()>(PATH_ACCOUNT_CONFIG, true, None).await?;
        let resp = sonic_rs::from_value::<Vec<OkxAccountConfig>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No user id"))?;
        Ok(resp.get_uid())
    }

    /// 用户万向划转
    async fn transfer(&self, _transfer: Transfer) -> Result<()> {
        unimplemented!("Okx transfer is not supported")
    }

    /// 子账户划转
    async fn sub_transfer(&self, _transfer: SubTransfer) -> Result<()> {
        unimplemented!("Okx sub transfer is not supported")
    }

    // ***************************************************订单*****************************************************
    /// 下单
    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        if order.amount.is_none() {
            return Err(qerror!("Amount is required"));
        }
        let req = OkxPostOrderReq::new(order, params);

        let result = self.post(PATH_POST_ORDER, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxPostOrderResp>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No order id"))?;
        match resp.s_code {
            0 => Ok(resp.ord_id),
            _ => Err(qerror!("{:?}", resp)),
        }
    }

    /// 批量下单
    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let reqs = orders
            .into_iter()
            .map(|o| OkxPostOrderReq::new(o, params.clone()))
            .collect::<Vec<_>>();
        let result = self.post(PATH_POST_ORDER_BATCH, true, reqs.into()).await?;
        let resp = sonic_rs::from_value::<BatchOrderRespWrapper>(&result)?;
        Ok(resp.into())
    }

    /// 根据symbol查询当前挂单
    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let req = OkxGetOrderByIdReq::new(symbol, order_id);
        let result = self.get(PATH_ORDERS, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxOrderResp>>(&result)?;
        let resp = resp.into_iter().next().ok_or_else(|| qerror!("No order"))?;
        Ok(resp.into())
    }

    /// 根据symbol查询当前挂单
    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        self.orders(PATH_ORDERS, Some(symbol), None, None).await
    }

    /// 查询当前所有挂单
    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        self.orders(PATH_ORDERS, None, None, None).await
    }

    /// 根据symbol查询历史订单
    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        self.orders(
            PATH_ORDERS_HISTORY,
            Some(symbol),
            Some(start_time),
            Some(end_time),
        )
        .await
    }

    /// 修改订单
    async fn amend_order(&self, order: Order) -> Result<String> {
        if order.amount.is_none() {
            return Err(qerror!("Amount is required"));
        }
        let req = OkxPostAmendOrderReq::new(order);
        let result = self.post(PATH_POST_ORDER_AMEND, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxPostOrderResp>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No order id"))?;
        match resp.s_code {
            0 => Ok(resp.ord_id),
            _ => Err(qerror!("{:?}", resp)),
        }
    }

    /// 取消订单
    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        let (order_id, client_id) = match order_id {
            OrderId::Id(id) => (Some(id), None),
            OrderId::ClientOrderId(cid) => (None, Some(cid)),
        };
        let req = OkxPostCancelOrderReq::new(symbol, order_id, client_id);
        let _ = self.post(PATH_POST_ORDER_CANCEL, true, req.into()).await?;
        Ok(())
    }

    /// 批量取消订单
    async fn batch_cancel_order(&self, _symbol: Symbol) -> Result<BatchOrderRsp> {
        unimplemented!("Okx batch cancel order is not supported.Because it needs OrderId which is not supplied by trait")
    }

    /// 根据订单id批量取消订单
    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        if symbol.is_none() || (ids.is_none() && cids.is_none()) {
            // symbol is required, and at least one of ids or cids is required
            return Err(qerror!("symbol or ids and cids are required"));
        }
        let reqs = OkxBatchCancelOrderReq::new(symbol.unwrap(), ids, cids);
        let result = self
            .post(PATH_POST_ORDER_CANCEL_BATCH, true, reqs.into())
            .await?;
        let resp = sonic_rs::from_value::<BatchOrderRespWrapper>(&result)?;
        Ok(resp.into())
    }

    // **************************************************仓位**************************************************
    /// 获取给定交易对的持仓信息
    async fn get_position(&self, symbol: Symbol) -> Result<Vec<Position>> {
        let req = OkxPositionReq::new(InstType::Swap, Some(symbol));
        self.positions(req).await
    }

    /// 获取所有仓位信息
    async fn get_positions(&self) -> Result<Vec<Position>> {
        let req = OkxPositionReq::new(InstType::Swap, None);
        self.positions(req).await
    }

    /// 获取最大持仓
    async fn get_max_position(&self, symbol: Symbol, leverage: u8) -> Result<MaxPosition> {
        let req = OkxMaxPositionReq::new(symbol, leverage);
        let result = self.get(PATH_MAX_POSITION, true, req.into()).await?;
        let resp = sonic_rs::from_value::<Vec<OkxMaxPositionResp>>(&result)?;
        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No max position"))?;
        Ok(resp.into())
    }
}

impl OkxSwap {
    pub async fn new(config: ExConfig) -> Self {
        let default_host = if config
            .params
            .get("aws")
            .map(|v| v == "true")
            .unwrap_or(false)
        {
            REST_AWS_BASE_URL
        } else {
            REST_BASE_URL
        }
        .to_string();
        let host = config
            .host
            .as_ref()
            .map(|h| h.to_string())
            .unwrap_or(default_host);
        let url = format!("{}{}{}", host, API_PREFIX, VERSION);
        // 如果config.params下的aws为true，则使用aws的rest接口
        // if config
        //     .params
        //     .get("aws")
        //     .map(|v| v == "true")
        //     .unwrap_or(false)
        // {
        //     url = format!("{}{}{}", REST_AWS_BASE_URL, API_PREFIX, VERSION);
        // }
        // 填充需要的数据，在其他地方可能用到
        let swap = OkxSwap {
            url: url
                .parse()
                .unwrap_or_else(|_| panic!("Failed to parse URL: {}", url)),
            client: new_reqwest_client(),
            config,
        };
        swap.fill_cache().await;
        swap
    }

    async fn fill_cache(&self) {
        if MULTIPLIER_CACHE.get().is_none() {
            let mut map = FxHashMap::default();
            let instruments = retry_with_attempts_async(
                || async { self.get_instruments().await },
                5,
                Some(Duration::from_millis(200)),
            )
            .await
            .unwrap();
            for instrument in instruments {
                map.insert(instrument.symbol.clone(), instrument.amount_multiplier);
            }
            MULTIPLIER_CACHE.set(map).unwrap();
        }
    }

    fn sign(
        &self,
        method: Method,
        req_path: &str,
        query: Option<Value>,
        body: Option<Value>,
        mut req: reqwest::RequestBuilder,
    ) -> Result<reqwest::RequestBuilder, Error> {
        let timestamp = quant_common::time_iso();
        let mut sign_msg = format!(
            "{}{}{}{}{}",
            timestamp, method, API_PREFIX, VERSION, req_path
        );
        if let Some(query) = query {
            sign_msg.push('?');
            let query = serde_urlencoded::to_string(&query)?;
            sign_msg.push_str(&query);
        }
        if let Some(body) = body {
            sign_msg.push_str(&body.to_string());
        }
        req = req.header(HEADER_CONTENT_TYPE, HEADER_JSON);
        req = req.header(HEADER_KEY, &self.config.key);
        req = req.header(HEADER_SIGN, crypto(&self.config.secret, sign_msg));
        req = req.header(HEADER_TIMESTAMP, timestamp);
        req = req.header(HEADER_PASSPHRASE, &self.config.passphrase);

        Ok(req)
    }

    async fn get<T: Serialize>(
        &self,
        path: &str,
        is_auth: bool,
        params: Option<T>,
    ) -> Result<Value> {
        let params = params.map_or(Ok(None), |p| serialized_to_value(p).map(Some))?;
        let uer_request = UserRequest {
            method: "GET".to_owned(),
            path: path.to_owned(),
            auth: is_auth,
            query: params,
            body: None,
            url: None,
            headers: None,
        };
        self.request(uer_request).await
    }

    async fn post<T: Serialize>(
        &self,
        path: &str,
        is_auth: bool,
        params: Option<T>,
    ) -> Result<Value> {
        let params = params.map_or(Ok(None), |p| serialized_to_value(p).map(Some))?;
        let uer_request = UserRequest {
            method: "POST".to_owned(),
            path: path.to_owned(),
            auth: is_auth,
            query: None,
            body: params,
            url: None,
            headers: None,
        };
        self.request(uer_request).await
    }

    async fn instruments(&self, symbol: Option<Symbol>) -> Result<Vec<Instrument>> {
        let mut req = OkxInstrumentReq::default();
        if let Some(symbol) = symbol {
            req.set_symbol(symbol);
        }
        let result = self.get(PATH_INSTRUMENTS, false, req.into()).await?;
        let instruments = sonic_rs::from_value::<Vec<OkxInstrumentResp>>(&result)?;
        Ok(instruments.into_iter().map(|i| i.into()).collect())
    }

    async fn orders(
        &self,
        path: &str,
        symbol: Option<Symbol>,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Result<Vec<Order>> {
        let mut req = OkxOrdersReq::default();
        if let Some(symbol) = symbol {
            req.set_symbol(symbol);
        }
        if let Some(start_time) = start_time {
            req.set_start_time(start_time);
        }
        if let Some(end_time) = end_time {
            req.set_end_time(end_time);
        }
        let result = self.get(path, true, req.into()).await?;
        let orders = sonic_rs::from_value::<Vec<OkxOrderResp>>(&result)?;
        Ok(orders.into_iter().map(|o| o.into()).collect())
    }

    pub(crate) async fn positions(&self, req: OkxPositionReq) -> Result<Vec<Position>> {
        let result = self.get(PATH_POSITIONS, true, req.into()).await?;
        let positions = sonic_rs::from_value::<Vec<OkxPositionResp>>(&result)?;
        Ok(positions.into_iter().map(|p| p.into()).collect())
    }
}

#[derive(serde::Deserialize)]
pub struct OkxResp {
    #[serde(deserialize_with = "quant_common::de_from_num_or_str")]
    pub code: i32,
    #[serde(default)]
    pub msg: String,
    #[serde(default)]
    pub data: Value,
}

impl fmt::Debug for OkxResp {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "OkxResp {{ code: {}, msg: {}, data: {} }}",
            self.code, self.msg, self.data
        )
    }
}

#[inline]
pub(crate) fn crypto(secret: &str, msg: String) -> String {
    use hmac::Mac;

    let mut mac = hmac::Hmac::<sha2::Sha256>::new_from_slice(secret.as_bytes()).unwrap();
    mac.update(msg.as_bytes());
    let bytes = mac.finalize().into_bytes();
    base64::Engine::encode(&base64::prelude::BASE64_STANDARD, bytes)
}

#[inline]
pub(crate) fn serialized_to_value<T: Serialize>(t: T) -> Result<Value> {
    sonic_rs::to_value(&t).map_err(|e| qerror!("{:?}", e))
}

pub async fn retry_with_attempts_async<T, F, Fut>(
    mut action: F,
    attempts: u32,
    interval: Option<Duration>,
) -> Result<T>
where
    F: FnMut() -> Fut,
    Fut: Future<Output = Result<T>>,
{
    for attempt in 1..=attempts {
        match action().await {
            Ok(result) => return Ok(result),
            Err(e) => {
                if attempt == attempts {
                    return Err(qerror!("Failed after {} attempts: {:?}", attempts, e));
                }
                error!("Attempt {} failed: {:?}", attempt, e);
                if let Some(interval) = interval {
                    tokio::time::sleep(interval).await;
                }
            }
        }
    }
    Err(qerror!("Failed after {} attempts", attempts))
}

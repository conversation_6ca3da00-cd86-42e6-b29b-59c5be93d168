use std::sync::Arc;

use quant_common::client_pool::{ClientPool, new_reqwest_client};
use reqwest::Method;
use rustc_hash::FxHashMap;
use serde::{Serialize, de::DeserializeOwned};
use sonic_rs::Value;
use url::Url;

use quant_common::base::{
    model::*,
    traits::{rest::Rest, *},
};
use quant_common::{Error, QuantError, Result, qerror};

use crate::swap::model::*;
use crate::util::{BnOrderItemRsp, gen_sign};

#[derive(Clone)]
pub struct BinanceSwap {
    pub url: Url,
    pub client: Arc<ClientPool>,
    pub client_gzip: Arc<ClientPool>,
    pub config: ExConfig,
    discount: Discount,
    swap: bool, // swap or future
}

impl BinanceSwap {
    pub async fn new(config: ExConfig) -> Self {
        Self::init(config, true).await
    }

    pub async fn new_future(config: ExConfig) -> Self {
        Self::init(config, false).await
    }

    pub async fn init(config: ExConfig, swap: bool) -> Self {
        let client = Arc::new(new_reqwest_client(config.multi_ip, false).await.unwrap());
        let client_gzip = Arc::new(new_reqwest_client(config.multi_ip, true).await.unwrap());
        let url = match &config.host {
            Some(host) => host.as_str(),
            None => match config.is_testnet {
                true => REST_TEST_BASE_URL,
                false => match config.is_colo {
                    true => REST_COLO_BASE_URL,
                    false => REST_BASE_URL,
                },
            },
        };
        let url = format!("https://{url}");
        let url = Url::parse(&url).unwrap();
        let discount = config
            .params
            .get("discount")
            .map(|v| sonic_rs::from_str(v).unwrap())
            .unwrap_or_else(|| Discount::new("BNB".to_string(), 0.9));
        let rest = Self {
            url,
            client,
            client_gzip,
            config,
            discount,
            swap,
        };

        if SPECIAL_SYMBOLS.get().is_none() {
            let symbols = init_special_symbols(&rest)
                .await
                .expect("init special symbols failed");
            SPECIAL_SYMBOLS.set(symbols).unwrap();
        }

        rest
    }

    #[inline(always)]
    pub async fn req<T: DeserializeOwned, P: Serialize>(
        &self,
        method: Method,
        path: &str,
        query: &P,
        with_sign: bool,
        timestamp: i64,
        gzip: bool,
    ) -> Result<T> {
        let mut url = self.url.clone().join(path)?;
        let query = serde_urlencoded::to_string(query)?;
        let query = if with_sign {
            gen_sign(&query, &self.config.secret, timestamp)?
        } else {
            query
        };
        if !query.is_empty() {
            url.set_query(Some(&query));
        }
        let mut req = match gzip {
            true => self.client_gzip.request(method.clone(), url.clone()),
            false => self.client.request(method.clone(), url.clone()),
        };
        if with_sign {
            req = req.header(HEADER_KEY, &self.config.key);
        }
        let rsp = req.send().await.map_err(QuantError::network_error)?;
        let status = rsp.status();
        let rsp = rsp.bytes().await?;
        if !status.is_success() {
            let err = sonic_rs::from_slice::<BinanceErr>(&rsp).map_err(|err| {
                let rsp = String::from_utf8_lossy(&rsp[..rsp.len().min(1024)]);
                qerror!("{method} {url} {err:?} {rsp}")
            })?;
            let msg = format!("{method} {url}\n => {} {}", err.code, err.msg);
            let error = match err.code {
                -5021 => Error::fok_rejected(msg),
                -5022 => Error::maker_only_rejected(msg),
                -2019 | -5013 => Error::insufficient_balance(msg),
                -2011 | -2013 => Error::order_not_found(msg),
                -1111 => Error::parameter_error(msg),
                -1008 => Error::exchange_busy(msg),
                _ => Error::exchange_error(msg),
            };
            return Err(error);
        }
        sonic_rs::from_slice(&rsp).map_err(|err| {
            let rsp = String::from_utf8_lossy(&rsp[..rsp.len().min(1024)]);
            qerror!("{} {}?{} {err} {rsp}", method, url, query)
        })
    }

    #[inline(always)]
    pub async fn get<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        self.req(Method::GET, path, params, false, 0, false).await
    }

    pub async fn get_gzip<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        self.req(Method::GET, path, params, false, 0, true).await
    }

    #[inline(always)]
    pub async fn get_signed<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
    ) -> Result<T> {
        self.req(Method::GET, path, params, true, 0, false).await
    }

    #[inline(always)]
    pub async fn post<T: DeserializeOwned, P: Serialize>(
        &self,
        path: &'static str,
        params: &P,
        ts: i64,
    ) -> Result<T> {
        self.req(Method::POST, path, params, true, ts, false).await
    }

    pub async fn set_bn_leverage(&self, symbol: Symbol, leverage: u8) -> Result<SetLeverageRsp> {
        let params = SetLeverageReq::new(symbol, leverage);
        self.post(PATH_LEVERAGE, &params, 0).await
    }
}

impl BinanceSwap {
    pub async fn get_funding_interval(&self) -> Result<Vec<BnFundingInfo>> {
        self.get(PATH_FUNDING_INFO, &()).await
    }

    async fn listen_key_req(&self, method: Method, query: Option<&str>) -> Result<String> {
        let mut url = self.url.join(PATH_LISTEN_KEY)?;
        if let Some(query) = query {
            url.set_query(Some(&format!("listenKey={query}")));
        }
        let r = self
            .client
            .request(method, url)
            .header(HEADER_KEY, &self.config.key)
            .send()
            .await?;
        let status = r.status();
        let body = r.text().await?;
        if !status.is_success() {
            return Err(qerror!("{}", body));
        }
        Ok(body)
    }

    #[inline]
    pub async fn create_listen_key(&self) -> Result<String> {
        let r = self.listen_key_req(Method::POST, None).await?;
        let r: RspListenKey = sonic_rs::from_str(&r)?;
        Ok(r.listen_key)
    }

    pub async fn refresh_listen_key(&self, listen_key: &str) -> Result<()> {
        let _r = self.listen_key_req(Method::PUT, Some(listen_key)).await?;
        Ok(())
    }

    pub async fn pos_risk(&self, symbol: Symbol) -> Result<Vec<PosRisk>> {
        let params: SymbolArg = symbol.into();
        self.get_signed(PATH_POS_RISK, &params).await
    }

    pub(crate) async fn positions(&self) -> Result<Vec<PosRisk>> {
        self.get_signed(PATH_POS_RISK, &()).await
    }

    pub(crate) async fn depth(&self, symbol: BnSymbol, limit: Option<u32>) -> Result<BnDepth> {
        let params = DepthReq::new(symbol, limit);
        self.get(PATH_DEPTH, &params).await
    }

    pub(crate) async fn symbol_infos(&self) -> Result<Vec<SymbolInfo>> {
        let exchange_info: ExchangeInfo = self.get(PATH_EXCHANGE_INFO, &()).await?;
        Ok(exchange_info
            .symbols
            .into_iter()
            .filter(|x| x.is_open())
            .filter(|x| x.is_delivery(self.swap))
            .collect())
    }

    pub(crate) async fn income(
        &self,
        symbol: Symbol,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Result<Vec<BnIncome>> {
        let req = BnIncomeReq::funding_fee(symbol, start_time, end_time);
        self.get_signed(PATH_INCOME, &req).await
    }

    pub(crate) async fn klines(&self, req: KlineReq) -> Result<Vec<Candle>> {
        let prefix = req.pair.prefix_f64();
        let candles: Vec<BnCandle> = self.get_gzip(PATH_KLINE, &req).await?;
        let candles = candles
            .into_iter()
            .map(|v| v.into_base(prefix))
            .collect::<Vec<Candle>>();
        Ok(candles)
    }

    pub(crate) async fn bbo_tickers(&self) -> Result<Vec<RestBookTicker>> {
        self.get(PATH_BOOKTICKER, &()).await
    }

    pub(crate) async fn all_open_orders(&self) -> Result<Vec<OpenOrdersResp>> {
        self.get_signed(PATH_OPEN_ORDERS, &()).await
    }
}

impl Rest for BinanceSwap {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, req: UserRequest) -> Result<Value> {
        let path = req.path;
        let params = req.query;
        let method = match req.method.as_str() {
            "GET" => Method::GET,
            "POST" => Method::POST,
            _ => return Err(qerror!("Unsupported method {}", req.method)),
        };
        self.req(method, &path, &params, req.auth, 0, false).await
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        let params: SymbolArg = symbol.into();
        let ticker: BnTicker = self.get(PATH_TICKER_24H, &params).await?;
        Ok(ticker.into())
    }

    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        let tickers_response: Vec<BnTicker> = self.get(PATH_TICKER_24H, &()).await?;
        Ok(tickers_response
            .into_iter()
            .filter(|t| match t.symbol.inner.contract_type {
                ContractType::Normal => self.swap,
                ContractType::Delivery { .. } => !self.swap,
                ContractType::Option { .. } => false,
            })
            .map(Into::into)
            .collect())
    }

    async fn get_mark_price(&self, symbol: Option<Symbol>) -> Result<Vec<MarkPrice>> {
        match symbol {
            Some(symbol) => {
                let params: SymbolArg = symbol.into();
                let resp: BnFunding = self.get(PATH_PREMIUM_INDEX, &params).await?;
                Ok(vec![resp.into()])
            }
            None => {
                let resp: Vec<BnFunding> = self.get(PATH_PREMIUM_INDEX, &()).await?;
                Ok(resp.into_iter().map(Into::into).collect())
            }
        }
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        let params: SymbolArg = symbol.into();
        let resp: RestBookTicker = self.get(PATH_BOOKTICKER, &params).await?;
        Ok(resp.into())
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        let resp = self.bbo_tickers().await?;
        Ok(resp
            .into_iter()
            .filter_map(|x| if !x.is_future() { Some(x.into()) } else { None })
            .collect())
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        let resp: BnDepth = self.depth(symbol.clone().into(), limit).await?;
        Ok(resp.into_depth(symbol))
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        self.get_instruments()
            .await?
            .into_iter()
            .find(|x| x.symbol == symbol)
            .ok_or_else(|| qerror!("get_instrument {} not found", symbol))
    }

    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        Ok(self
            .symbol_infos()
            .await?
            .into_iter()
            .map(Instrument::from)
            .collect())
    }

    async fn get_klines_ext(&self, params: GetKlineParams) -> Result<Kline> {
        let symbol = params.symbol.clone();
        let interval = params.interval.clone();
        let req = KlineReq::swap_from_params(params);
        let candles = self.klines(req).await?;
        Ok(Kline::new(symbol, interval, candles))
    }

    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        let r: Vec<BnFunding> = self.get(PATH_PREMIUM_INDEX, &()).await?;
        let funding_interval = self
            .get_funding_interval()
            .await?
            .into_iter()
            .map(|x| (x.symbol.clone(), x))
            .collect::<FxHashMap<_, _>>();
        Ok(r.into_iter()
            // 过滤掉交割合约
            .filter(|x| !x.is_future())
            .map(|f| {
                let interval = funding_interval.get(&f.symbol);
                f.into_fund(interval)
            })
            .collect())
    }

    async fn get_funding_fee(
        &self,
        symbol: Symbol,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Result<Vec<FundingFee>> {
        let rsp = self.income(symbol, start_time, end_time).await?;
        Ok(rsp.into_iter().map(|x| x.into()).collect())
    }

    async fn get_funding_rates_history_ext(
        &self,
        params: GetFundingRateHistoryParams,
    ) -> Result<Vec<FundingHistory>> {
        let req: HistoryFundingReq = params.into();
        let rsp: Vec<HistoryFunding> = self.get_signed(PATH_FUNDING_RATE, &req).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        let ts = order.timestamp;
        let order_request = NewOrderRequest::new(order, params.is_dual_side);
        let rsp: OrderResponse = self.post(PATH_ORDER, &order_request, ts).await?;
        Ok(rsp.order_id.to_string())
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let cids = orders.iter().map(|o| o.cid.clone()).collect::<Vec<_>>();
        let new_orders: Vec<NewOrderRequest> = orders
            .into_iter()
            .map(|order| NewOrderRequest::new(order, params.is_dual_side))
            .collect();
        let request = BatchOrders::new(new_orders);

        let rsp: Vec<PostBatchOrderRsp> = self.post(PATH_BATCH_ORDER, &request, 0).await?;
        let rsp = rsp
            .into_iter()
            .enumerate()
            .fold(BatchOrderRsp::default(), |mut r, (i, o)| {
                match o.into_base(cids.get(i)) {
                    BnOrderItemRsp::Succ(succ) => r.success_list.push(succ),
                    BnOrderItemRsp::Fail(fail) => r.failure_list.push(fail),
                }
                r
            });
        Ok(rsp)
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let req = GetOrderReq::new(symbol, order_id)?;
        let rsp: OpenOrdersResp = self.get_signed(PATH_ORDER, &req).await?;
        Ok(rsp.into())
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        let order_req = OpenOrdersReq::new(Some(symbol), None, None);
        let rsp: Vec<OpenOrdersResp> = self.get_signed(PATH_OPEN_ORDERS, &order_req).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        let rsp = self.all_open_orders().await?;
        Ok(rsp
            .into_iter()
            .filter_map(|x| if !x.is_future() { Some(x.into()) } else { None })
            .collect())
    }

    async fn get_orders(
        &self,
        symbol: Symbol,
        mut start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        // 查询时间范围最大不得超过7天
        // 默认查询最近7天内的数据
        let max_interval = 7 * 86_400_000;
        if end_time - start_time > max_interval {
            start_time = end_time - max_interval;
        }

        let order_req = OpenOrdersReq::new(Some(symbol), Some(start_time), Some(end_time));
        let rsp: Vec<UserTrades> = self.get_signed(PATH_ALL_ORDERS, &order_req).await?;
        Ok(rsp.into_iter().map(Into::into).collect())
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        let ts = order.timestamp;
        let amend_request: AmendOrderRequest = order.into();
        let rsp: OrderResponse = self
            .req(Method::PUT, PATH_ORDER, &amend_request, true, ts, false)
            .await?;

        Ok(rsp.order_id.to_string())
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        let cancel_request = SymbolOrderId::new(symbol, order_id);

        self.req::<TrivialResponse, _>(Method::DELETE, PATH_ORDER, &cancel_request, true, 0, false)
            .await?;

        Ok(())
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        let params: SymbolArg = symbol.into();
        let resp: TradeFee = self.get_signed(PATH_COMMISSION_RATE, &params).await?;
        Ok(resp.into())
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        let r: Vec<AccountBalance> = self.get_signed(PATH_BALANCE, &()).await?;
        Ok(r.into_iter().map(Into::into).collect())
    }

    async fn get_position(&self, symbol: Symbol) -> Result<Vec<Position>> {
        let resp = self.pos_risk(symbol).await?;
        Ok(resp.into_iter().map(Into::into).collect())
    }

    async fn get_positions(&self) -> Result<Vec<Position>> {
        let resp = self.positions().await?;
        Ok(resp
            .into_iter()
            .filter_map(|x| match x.symbol.is_future() {
                true => None,
                false => Some(x.into()),
            })
            .collect())
    }

    async fn get_max_leverage(&self, symbol: Symbol) -> Result<u8> {
        let params: SymbolArg = symbol.into();
        let resp: Vec<Bracket> = self.get_signed(PATH_LEVERAGE_BRACKET, &params).await?;
        let leverage = resp.iter().filter_map(|b| b.get_max_leverage()).max();
        leverage.ok_or_else(|| qerror!("{} 返回空数组", self.name()))
    }

    async fn set_leverage(&self, symbol: Symbol, leverage: u8) -> Result<()> {
        let _r: SetLeverageRsp = self.set_bn_leverage(symbol, leverage).await?;
        Ok(())
    }

    async fn is_dual_side(&self) -> Result<bool> {
        #[derive(serde::Deserialize)]
        struct Rsp {
            #[serde(rename = "dualSidePosition")]
            dual_side_position: bool,
        }
        let resp: Rsp = self.get_signed(PATH_DUAL_SIDE, &()).await?;

        Ok(resp.dual_side_position)
    }

    async fn set_dual_side(&self, dual_side: bool) -> Result<()> {
        let req = RequestDualSidePosition {
            dual_side_position: dual_side,
        };
        let _: RawValue = self.post(PATH_DUAL_SIDE, &req, 0).await?;

        Ok(())
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
        let params: SymbolArg = symbol.into();
        let rsp: BinanceErr = self
            .req(
                Method::DELETE,
                PATH_BATCH_CANCEL_ORDER,
                &params,
                true,
                0,
                false,
            )
            .await?;
        match rsp.code == 200 {
            true => Ok(Default::default()),
            false => Err(qerror!("{rsp:?}")),
        }
    }

    async fn batch_cancel_order_by_ids(
        &self,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<BatchOrderRsp> {
        let symbol = symbol.ok_or_else(|| qerror!("{} 的symbol是必填项", self.name()))?;
        let req = BatchCancelOrderByIdsReq::new(symbol, ids, cids)?;
        let rsp: Vec<BatchCancelOrderByIdsRsp> = self
            .req(Method::DELETE, PATH_BATCH_ORDER, &req, true, 0, false)
            .await?;
        let rsp = rsp.into_iter().fold(BatchOrderRsp::default(), |mut r, i| {
            match i.into() {
                BnOrderItemRsp::Succ(success_order) => r.success_list.push(success_order),
                BnOrderItemRsp::Fail(fail_order) => r.failure_list.push(fail_order),
            }
            r
        });
        Ok(rsp)
    }

    async fn get_usdt_balance(&self) -> Result<Balance> {
        get_usdt_balance(self.get_balances().await?)
            .ok_or_else(|| qerror!("USDT balance not found"))
    }

    async fn get_fee_discount_info(&self) -> Result<Option<Discount>> {
        Ok(Some(self.discount.clone()))
    }

    async fn is_fee_discount_enabled(&self) -> Result<bool> {
        let resp: FeeBurn = self.get_signed(PATH_FEE_BURN, &()).await?;
        Ok(resp.into())
    }

    async fn set_fee_discount_enabled(&self, _enable: bool) -> Result<()> {
        let req: FeeBurn = _enable.into();
        let resp: SetFeeBurnRsp = self.post(PATH_FEE_BURN, &req, 0).await?;
        match resp.is_succ() {
            true => Ok(()),
            false => Err(qerror!("设置失败: {:?}", resp)),
        }
    }

    async fn get_max_position(&self, symbol: Symbol, leverage: u8) -> Result<MaxPosition> {
        let params = GetMarkPriceParams::new(Some(symbol.clone()));
        let mark_price = self.get_mark_price_ext(params).await?;
        let mark_price = mark_price.first().unwrap().price;

        let rsp: SetLeverageRsp = self.set_bn_leverage(symbol, leverage).await?;
        let long_notional = rsp.max_notional_value;
        let short_notional = long_notional;

        let long_quantity = long_notional / mark_price;
        let short_quantity = long_quantity;

        Ok(MaxPosition {
            long_notional,
            short_notional,
            long_quantity,
            short_quantity,
        })
    }

    async fn get_margin_mode(&self, symbol: Symbol, _margin_coin: String) -> Result<MarginMode> {
        let req = GetMarginModeReq::new(symbol);
        let rsp: Vec<BnMarginModeRsp> = self.get_signed(PATH_SYMBOL_CONFIG, &req).await?;
        rsp.into_iter()
            .next()
            .map(|m| m.into())
            .ok_or_else(|| qerror!("{} 返回空数组", self.name()))
    }

    async fn set_margin_mode(
        &self,
        symbol: Symbol,
        _margin_coin: String,
        margin_mode: MarginMode,
    ) -> Result<()> {
        let req = SetMarginModeReq::new(symbol, margin_mode);
        let _rsp: SetMarginModeRsq = self.post(PATH_MARGIN_TYPE, &req, 0).await?;
        Ok(())
    }

    async fn get_account_info(&self) -> Result<AccountInfo> {
        let rsp: AccountRsp = self.get_signed(PATH_ACCOUNT, &()).await?;
        Ok(rsp.into())
    }
}

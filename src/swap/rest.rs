use crate::common::symbol::{BbSymbol, SPECIAL_SYMBOLS, swap_prefix_mul_by_base};
use crate::swap::model::*;
use quant_common::base::traits::{GetFundingRateHistoryParams, GetKlineParams};
use quant_common::{Result, new_reqwest_client, qerror, retry_with_attempts_async, time_ms};
use reqwest::Method;
use reqwest::header::{CONTENT_TYPE, HeaderMap, HeaderValue};
use rustc_hash::FxHashMap;
use serde::Serialize;
use serde::de::DeserializeOwned;
use sonic_rs::{JsonContainerTrait, JsonValueTrait, Value};
use std::collections::HashMap;
use std::str::FromStr;
use std::sync::atomic::{AtomicU8, Ordering};
use std::time::Duration;
use tokio::join;
use tracing::{debug, info};
use url::Url;

use quant_common::base::{
    AccountInfo, AccountMode, Balance, BatchOrderRsp, BboTicker, Borrow, Candle, Chain,
    DepositAddress, Depth, Discount, ExConfig, FeeRate, Funding, FundingFee, FundingHistory,
    Instrument, InternalTransferParams, Kline, MarginMode, MarkPrice, MaxPosition, Order, OrderId,
    OrderParams, OrderSide, OrderType, PosSide, Position, PrefixMulExt, RawValue, Rest,
    SubTransfer, SuccessOrder, Symbol, Ticker, TimeInForce, Transfer, UserRequest, WithDrawlParams,
    WithdrawalAddr,
};

#[derive(Debug)]
pub struct BybitSwap {
    pub url: Url,
    pub client: reqwest::Client,
    pub config: ExConfig,
    pub dual_side: AtomicU8,
}

impl Clone for BybitSwap {
    fn clone(&self) -> Self {
        BybitSwap {
            url: self.url.clone(),
            client: self.client.clone(),
            config: self.config.clone(),
            dual_side: AtomicU8::new(self.dual_side.load(Ordering::Acquire)),
        }
    }
}

impl Default for BybitSwap {
    fn default() -> Self {
        let config = ExConfig::default();
        let client = new_reqwest_client();
        let url = "".parse().expect("parse rest base url err.");
        Self {
            url,
            client,
            config,
            dual_side: AtomicU8::new(0),
        }
    }
}

impl BybitSwap {
    async fn new_uninitialized(config: ExConfig) -> Self {
        let client = new_reqwest_client();
        let host: String =
            config
                .host
                .as_ref()
                .map(|h| h.to_string())
                .unwrap_or(if config.is_testnet {
                    REST_TEST_BASE_HOST.to_string()
                } else if config.is_colo {
                    REST_COLONY_BASE_HOST.to_string()
                } else {
                    REST_BASE_HOST.to_string()
                });
        let url = Url::parse(&format!("https://{host}")).expect("parse url err.");
        Self {
            url,
            client,
            config,
            dual_side: AtomicU8::new(0),
        }
    }

    pub async fn new(config: ExConfig) -> Self {
        let rest = BybitSwap::new_uninitialized(config).await;
        if SPECIAL_SYMBOLS.get().is_none() {
            let contracts = retry_with_attempts_async(
                || async { rest.contracts().await },
                5,
                Some(Duration::from_millis(200)),
            )
            .await
            .unwrap();

            let mut special_symbols = FxHashMap::default();
            for contract in contracts {
                let symbol = contract.symbol;
                if symbol.prefix_mul != 1 {
                    special_symbols.insert(symbol.inner.base, symbol.prefix_mul);
                }
            }

            if SPECIAL_SYMBOLS.get().is_none() {
                SPECIAL_SYMBOLS.set(special_symbols).unwrap();
            }
        }

        rest
    }

    async fn contracts(&self) -> Result<Vec<BbInstrumentInner>> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("limit", 1000.to_string()),
        ]);
        self.req::<BbInstrument, _>(Method::GET, "/v5/market/instruments-info", &map, true)
            .await
            .map(|r| r.list)
    }

    #[inline(always)]
    pub async fn req<T: DeserializeOwned, P: Serialize>(
        &self,
        method: Method,
        path: &str,
        query: &P,
        with_sign: bool,
    ) -> Result<T> {
        // 定义请求窗口时间
        let recv_window = 5000;
        let host = self.url.to_string();

        // 根据请求方法来确定请求参数的拼接模式
        let request_param_str = if Method::GET == method {
            serde_urlencoded::to_string(query).expect("url encode err")
        } else {
            sonic_rs::to_string(query).expect("json encode err")
        };

        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        headers.insert("X-BAPI-API-KEY", HeaderValue::from_str(&self.config.key)?);
        headers.insert(
            "X-BAPI-RECV-WINDOW",
            HeaderValue::from_str(&recv_window.to_string())?,
        );

        let timestamp = time_ms();
        if with_sign {
            // 构造待签名的字符串
            let params = format!(
                "{}{}{}{}",
                timestamp, &self.config.key, recv_window, request_param_str
            );

            // 生成 HMAC-SHA256 签名
            let sign = quant_common::hmac_sha256_hex(self.config.secret.as_ref(), &params)?;
            headers.insert(
                "X-BAPI-TIMESTAMP",
                HeaderValue::from_str(&timestamp.to_string())?,
            );
            headers.insert("X-BAPI-SIGN", HeaderValue::from_str(&sign)?);
        }

        // 构建请求
        let request_builder = if method == Method::GET {
            debug!("req url:{host}{path}{request_param_str}");
            self.client
                .request(method, format!("{host}{path}"))
                .query(query)
        } else {
            debug!("request_param_str{}", request_param_str);
            self.client
                .request(method, format!("{host}{path}"))
                .body(request_param_str)
        }
        .headers(headers);

        let response = request_builder.send().await?;

        // 检查响应状态并打印响应体
        if !response.status().is_success() {
            let status = response.status().to_string();
            let body = response.text().await.expect("get text err");
            return Err(qerror!("status:{status},body:{body}"));
        }
        let body = response.bytes().await?;

        // 先反序列化为 ApiResponse<RawValue> 来检查返回码
        let api_response: ApiResponse<RawValue> = sonic_rs::from_slice(&body)
            .map_err(|err| qerror!("{err} {}", std::str::from_utf8(&body).unwrap()))?;

        debug!("{api_response:#?}");

        // 检查返回码
        if !matches!(api_response.ret_code, 0 | 110043) {
            return Err(qerror!("请求错误：{}", api_response.ret_msg));
        }

        // 如果成功，反序列化 result 字段
        match api_response.result {
            Some(raw_result) => sonic_rs::from_str(raw_result.get())
                .map_err(|err| qerror!("{err} {}", raw_result.get())),
            None => Err(qerror!("get result err")),
        }
    }

    #[inline(always)]
    pub async fn req_raw<T: DeserializeOwned, P: Serialize>(
        &self,
        method: Method,
        path: &str,
        query: &P,
        with_sign: bool,
    ) -> Result<ApiResponse<T>> {
        // 定义请求窗口时间
        let recv_window = 5000;
        let host = self.url.to_string();

        // 根据请求方法来确定请求参数的拼接模式
        let request_param_str = if Method::GET == method {
            serde_urlencoded::to_string(query).expect("url encode err")
        } else {
            sonic_rs::to_string(query).expect("json encode err")
        };

        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        headers.insert("X-BAPI-API-KEY", HeaderValue::from_str(&self.config.key)?);
        headers.insert(
            "X-BAPI-RECV-WINDOW",
            HeaderValue::from_str(&recv_window.to_string())?,
        );

        let timestamp = time_ms();
        if with_sign {
            // 构造待签名的字符串
            let params = format!(
                "{}{}{}{}",
                timestamp, &self.config.key, recv_window, request_param_str
            );

            // 生成 HMAC-SHA256 签名
            let sign = quant_common::hmac_sha256_hex(self.config.secret.as_ref(), &params)?;
            headers.insert(
                "X-BAPI-TIMESTAMP",
                HeaderValue::from_str(&timestamp.to_string())?,
            );
            headers.insert("X-BAPI-SIGN", HeaderValue::from_str(&sign)?);
        }

        // 构建请求
        let request_builder = if method == Method::GET {
            debug!("req url:{host}{path}{request_param_str}");
            self.client
                .request(method, format!("{host}{path}"))
                .query(query)
        } else {
            debug!("request_param_str{}", request_param_str);
            self.client
                .request(method, format!("{host}{path}"))
                .body(request_param_str)
        }
        .headers(headers);

        let response = request_builder.send().await?;

        // 检查响应状态并打印响应体
        if !response.status().is_success() {
            let status = response.status().to_string();
            let body = response.text().await.expect("get text err");
            return Err(qerror!("status:{status},body:{body}"));
        }
        let body = response.bytes().await?;

        // 直接反序列化为 ApiResponse<T>
        sonic_rs::from_slice(&body)
            .map_err(|err| qerror!("{err} {}", std::str::from_utf8(&body).unwrap()))
    }

    pub async fn get_coin_chain(&self, coin: &str) -> Result<Vec<ChainConfig>> {
        let map = HashMap::from([("coin", coin.to_string()), ("limit", "35".to_string())]);
        let result = self
            .req::<ChainConfigList, _>(
                Method::GET,
                "/v5/asset/deposit/query-allowed-list",
                &map,
                true,
            )
            .await?;
        Ok(result.config_list)
    }

    pub fn chain_rule(&self, chain: &Chain) -> String {
        match chain {
            Chain::Erc20 => "ETH",
            Chain::Trc20 => "TRX",
            Chain::Bep20 => "BSC",
            Chain::Sol => "SOL",
            Chain::Polygon => "MATIC",
            Chain::ArbitrumOne => "ARBI",
            Chain::Optimism => "OP",
            Chain::Ton => "TON",
            Chain::AVAXC => "CAVAX",
        }
        .to_string()
    }

    pub async fn check_coin_chain(&self, coin: &str, chain: &Chain) -> Result<bool> {
        let chain = self.chain_rule(chain);
        let vec = self
            .get_coin_chain(coin)
            .await?
            .into_iter()
            .find(|chain_config| chain_config.chain == chain);
        Ok(vec.is_some())
    }

    async fn get_common_tickers(&self, symbol: Option<Symbol>) -> Result<Vec<BbTickerInner>> {
        let mut map = HashMap::from([("category", "linear".to_owned())]);
        if let Some(symbol) = symbol {
            map.insert("symbol", BbSymbol::from(symbol).to_string());
        }
        let response = self
            .req::<BbCList<BbTickerInner>, _>(Method::GET, "/v5/market/tickers", &map, false)
            .await?;
        Ok(response.list)
    }

    pub async fn get_market_price_kline(&self, symbol: Symbol) -> Result<Vec<MarkPriceKline>> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
            ("interval", "1".to_owned()),
            ("limit", "1".to_owned()),
        ]);

        let result = self
            .req::<MarkPriceKlineResponse, _>(Method::GET, "/v5/market/kline", &map, false)
            .await?;
        Ok(result.list)
    }
}

impl Rest for BybitSwap {
    fn name(&self) -> &'static str {
        EXCHANGE
    }

    async fn request(&self, req: UserRequest) -> Result<Value> {
        let method = Method::from_str(req.method.as_str())?;
        let p = if method == Method::GET {
            req.query
        } else {
            req.body
        };
        self.req(method, &req.path, &p, req.auth).await
    }

    async fn get_ticker(&self, symbol: Symbol) -> Result<Ticker> {
        let mut resp = self.get_common_tickers(Some(symbol)).await?;
        Ok(resp
            .pop()
            .ok_or(qerror!("BybitSwap ticker list is empty"))?
            .into())
    }

    async fn get_tickers(&self) -> Result<Vec<Ticker>> {
        let resp = self.get_common_tickers(None).await?;
        let tickers = resp.into_iter().map(|t| t.into()).collect::<Vec<Ticker>>();
        Ok(tickers)
    }

    async fn get_bbo_ticker(&self, symbol: Symbol) -> Result<BboTicker> {
        let resp = self.get_common_tickers(Some(symbol)).await?;
        let ticker = resp
            .into_iter()
            .next()
            .map(|t| t.into())
            .ok_or(qerror!("BybitSwap bbo ticker list is empty"))?;
        Ok(ticker)
    }

    async fn get_bbo_tickers(&self) -> Result<Vec<BboTicker>> {
        let resp = self.get_common_tickers(None).await?;
        let tickers = resp.into_iter().map(|t| t.into()).collect();
        Ok(tickers)
    }

    async fn get_depth(&self, symbol: Symbol, limit: Option<u32>) -> Result<Depth> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
            ("limit", limit.unwrap_or(10).to_string()),
        ]);
        let result = self
            .req::<BbDepth, _>(Method::GET, "/v5/market/orderbook", &map, true)
            .await?;
        Ok(result.into())
    }

    async fn get_instrument(&self, symbol: Symbol) -> Result<Instrument> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
        ]);

        let result = self
            .req::<BbInstrument, _>(Method::GET, "/v5/market/instruments-info", &map, true)
            .await?;
        result
            .list
            .into_iter()
            .next()
            .map(|i| i.into())
            .ok_or(qerror!("BybitSwap instrument list is empty"))
    }

    async fn get_instruments(&self) -> Result<Vec<Instrument>> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("limit", 1000.to_string()),
        ]);
        let result = self
            .req::<BbInstrument, _>(Method::GET, "/v5/market/instruments-info", &map, true)
            .await?;

        Ok(result.list.into_iter().map(|i| i.into()).collect())
    }

    async fn get_funding_rates(&self) -> Result<Vec<Funding>> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("limit", 1000.to_string()),
        ]);

        // 并发获取交易对信息和资金费率
        let (instruments_result, tickers_result) = tokio::join!(
            self.req::<BbInstrument, _>(Method::GET, "/v5/market/instruments-info", &map, true,),
            self.get_common_tickers(None)
        );

        let instruments = instruments_result?;
        let tickers = tickers_result?;

        let instruments: HashMap<Symbol, BbInstrumentInner> = instruments
            .list
            .into_iter()
            .map(|inner| (inner.symbol.inner.clone(), inner))
            .collect();

        // 将 tickers 转换为 Funding 并设置 funding_interval
        let fundings = tickers
            .into_iter()
            .filter_map(|ticker| {
                let instrument = instruments.get(&ticker.symbol.inner)?;

                let mut funding: Funding = ticker.into();
                funding.funding_interval = Some((instrument.funding_interval / 60) as u8);
                funding.min_funding_rate = instrument.lower_funding_rate;
                funding.max_funding_rate = instrument.upper_funding_rate;

                Some(funding)
            })
            .collect();

        Ok(fundings)
    }

    async fn get_max_position(&self, symbol: Symbol, leverage: u8) -> Result<MaxPosition> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol.clone()).to_string()),
        ]);
        let mut result = self
            .req::<BbCList<RiskResp>, _>(Method::GET, "/v5/market/risk-limit", &map, true)
            .await?;

        // 按照杠杆值从小到大排序
        result.list.sort_by(|a, b| {
            let a_lev = a.max_leverage;
            let b_lev = b.max_leverage;
            a_lev
                .partial_cmp(&b_lev)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        // 找到第一个大于等于请求杠杆值的风险限制
        let risk = result
            .list
            .into_iter()
            .find(|r| {
                let max_lev = r.max_leverage;
                max_lev >= leverage as f64
            })
            .ok_or_else(|| qerror!("找不到合适的杠杆值：{}", leverage))?;

        let value = risk.risk_limit_value;

        let mark_price = self.get_mark_price(Some(symbol)).await?;

        // 计算最大持仓数量
        let max_quantity = value / mark_price.first().ok_or(qerror!("无法获取标记价格"))?.price;

        Ok(MaxPosition {
            long_notional: value,
            short_notional: value,
            long_quantity: max_quantity,
            short_quantity: max_quantity,
        })
    }

    async fn set_leverage(&self, symbol: Symbol, leverage: u8) -> Result<()> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
            ("buyLeverage", leverage.to_string()),
            ("sellLeverage", leverage.to_string()),
        ]);
        self.req::<BbResult, _>(Method::POST, "/v5/position/set-leverage", &map, true)
            .await?;
        Ok(())
    }

    async fn is_dual_side(&self) -> Result<bool> {
        match self.dual_side.load(Ordering::Acquire) {
            0 => {
                self.set_dual_side(true).await?;
                self.dual_side.store(1, Ordering::Release);
                Ok(true)
            }
            1 => Ok(true),
            2 => Ok(false),
            _ => Err(qerror!("is_dual_side err")),
        }
    }

    async fn set_dual_side(&self, is_dual_side: bool) -> Result<()> {
        let mode = if is_dual_side { 3 } else { 0 };
        let map = HashMap::from([
            ("category", "linear".to_string()),
            ("mode", mode.to_string()),
            ("coin", "USDT".to_string()),
        ]);
        self.req::<BbResult, _>(Method::POST, "/v5/position/switch-mode", &map, true)
            .await?;
        match is_dual_side {
            true => {
                self.dual_side.store(1, Ordering::Release);
            }
            false => {
                self.dual_side.store(2, Ordering::Release);
            }
        }
        Ok(())
    }

    async fn get_fee_rate(&self, symbol: Symbol) -> Result<FeeRate> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
        ]);
        let result = self
            .req::<BbList<BbFeeRate>, _>(Method::GET, "/v5/account/fee-rate", &map, true)
            .await?;
        let fee: Option<FeeRate> = result.list.into_iter().map(FeeRate::from).next();
        fee.ok_or(qerror!("get fee err"))
    }

    async fn get_usdt_balance(&self) -> Result<Balance> {
        self.get_balance("USDT").await
    }

    async fn get_balance(&self, asset: &str) -> Result<Balance> {
        let account_type = if self.config.is_unified {
            "UNIFIED".to_string()
        } else {
            "CONTRACT".to_string()
        };
        let map = HashMap::from([("accountType", account_type), ("coin", asset.to_string())]);
        let result = if asset == "USDT" {
            let (usdt, all_balances) = join!(
                self.req::<BbAccountBalance, _>(
                    Method::GET,
                    "/v5/asset/transfer/query-account-coin-balance",
                    &map,
                    true,
                ),
                self.get_balances()
            );
            let all_balances = all_balances?
                .into_iter()
                .find(|b| b.asset == asset)
                .ok_or(qerror!("get balance err"))?;
            (usdt?, Some(all_balances))
        } else {
            (
                self.req::<BbAccountBalance, _>(
                    Method::GET,
                    "/v5/asset/transfer/query-account-coin-balance",
                    &map,
                    true,
                )
                .await?,
                None,
            )
        };

        let balance: Balance = result.0.balance.into();
        if let Some(mut all_balances) = result.1 {
            all_balances.available_balance = balance.available_balance;
            Ok(all_balances)
        } else {
            Ok(balance)
        }
    }

    async fn get_balances(&self) -> Result<Vec<Balance>> {
        let map = HashMap::from([("accountType", "UNIFIED".to_owned())]);
        let mut result = self
            .req::<BbList<BbBalance>, _>(Method::GET, "/v5/account/wallet-balance", &map, true)
            .await?;

        let account = result.list.pop().ok_or(qerror!("get balance list err"))?;
        let mut balance_list: Vec<Balance> = account.coin.into_iter().map(Balance::from).collect();
        for balance in balance_list.iter_mut() {
            let asset = balance.asset.clone();
            let map = HashMap::from([("coinName", asset)]);
            let result = self
                .req::<BbAvailableWithdrawal, _>(Method::GET, "/v5/account/withdrawal", &map, true)
                .await?;
            balance.available_balance = result.available_withdrawal;
        }
        Ok(balance_list)
    }

    async fn get_fee_discount_info(&self) -> Result<Option<Discount>> {
        unimplemented!("bybit 不支持 get_fee_discount_info 接口")
    }

    async fn is_fee_discount_enabled(&self) -> Result<bool> {
        unimplemented!("bybit 不支持 is_fee_discount_enabled 接口")
    }

    async fn set_fee_discount_enabled(&self, _enable: bool) -> Result<()> {
        unimplemented!("bybit 不支持 set_fee_discount_enabled 接口")
    }

    async fn get_max_leverage(&self, symbol: Symbol) -> Result<u8> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol.clone()).to_string()),
        ]);
        let result = self
            .req::<BbCList<RiskResp>, _>(Method::GET, "/v5/market/risk-limit", &map, true)
            .await?;
        Ok(result
            .list
            .first()
            .ok_or(qerror!("Cannot find max leverage, symbol: {}", symbol))?
            .max_leverage as u8)
    }

    async fn get_deposit_address(
        &self,
        ccy: String,
        chain: Option<Chain>,
        _amount: Option<f64>,
    ) -> Result<Vec<DepositAddress>> {
        let mut map = HashMap::from([("coin", ccy.to_uppercase())]);

        // 如果指定了链，添加 chainType 参数
        if let Some(chain) = &chain {
            map.insert("chainType", self.chain_rule(chain));
        }

        let result = self
            .req::<DepositAddressResponse, _>(
                Method::GET,
                "/v5/asset/deposit/query-address",
                &map,
                true,
            )
            .await?;

        // 转换响应数据为 DepositAddress 列表
        let addresses = result
            .chains
            .into_iter()
            .filter_map(|chain_info| {
                if chain_info.chain.is_some() {
                    Some(DepositAddress {
                        asset: result.coin.clone(),
                        address: chain_info.address_deposit,
                        chain: chain_info.chain,
                        tag: if chain_info.tag_deposit.is_empty() {
                            None
                        } else {
                            Some(chain_info.tag_deposit)
                        },
                        url: None,
                    })
                } else {
                    None
                }
            })
            .collect();

        Ok(addresses)
    }

    async fn withdrawal(&self, withdrwl_params: WithDrawlParams) -> Result<()> {
        info!("请保证將地址添加到了网页地址簿中");

        let mut map = HashMap::from([
            ("coin", withdrwl_params.asset.to_string()),
            ("amount", withdrwl_params.amt.to_string()),
            ("timestamp", time_ms().to_string()),
            ("feeType", "1".to_string()),
        ]);
        withdrwl_params.cid.map(|c| map.insert("requestId", c));
        match withdrwl_params.addr {
            WithdrawalAddr::InternalTransfer(i) => {
                if let InternalTransferParams::Uid(uid) = i {
                    map.insert("forceChain", "2".to_string());
                    map.insert("address", uid);
                } else {
                    return Err(qerror!("bybit 只支持 uid 内部划转"));
                }
            }
            WithdrawalAddr::OnChain(c) => {
                if !self
                    .check_coin_chain(&withdrwl_params.asset, &c.chain)
                    .await?
                {
                    return Err(qerror!(
                        "bybit 不支持 {} 在 {} 链上提币",
                        withdrwl_params.asset,
                        self.chain_rule(&c.chain)
                    ));
                }

                map.insert("forceChain", "1".to_string());
                map.insert("chain", self.chain_rule(&c.chain));
                map.insert("address", c.address);
            }
        }

        let _result = self
            .req::<BbResult, _>(Method::POST, "/v5/asset/withdraw/create", &map, true)
            .await?;
        Ok(())
    }

    async fn get_user_id(&self) -> Result<String> {
        let map: HashMap<String, String> = HashMap::new();
        let result = self
            .req::<Value, _>(Method::GET, "/v5/user/query-api", &map, true)
            .await?;
        let user_id = result.get("userID").unwrap().as_i64().unwrap();
        Ok(user_id.to_string())
    }

    async fn transfer(&self, _transfer: Transfer) -> Result<()> {
        Err(qerror!("已经实现统一账号，不用万象划转"))
    }

    async fn sub_transfer(&self, transfer: SubTransfer) -> Result<()> {
        if transfer.from_account == transfer.to_account {
            return Err(qerror!("不支持同一个 uid 进行转账，请使用 transfer"));
        }
        let transfer_id = uuid::Uuid::new_v4().to_string();
        let map = HashMap::from([
            ("transferId", transfer_id),
            ("coin", transfer.asset.to_uppercase()),
            ("amount", transfer.amount.to_string()),
            (
                "fromMemberId",
                transfer
                    .from_account
                    .ok_or(qerror!("from_account 不能为空"))?,
            ),
            (
                "toMemberId",
                transfer.to_account.ok_or(qerror!("to_account 不能为空"))?,
            ),
            ("fromAccountType", "UNIFIED".to_string()),
            ("toAccountType", "UNIFIED".to_string()),
        ]);
        let result = self
            .req::<Value, _>(
                Method::POST,
                "/v5/asset/transfer/universal-transfer",
                &map,
                true,
            )
            .await?;
        let status = result
            .get("status")
            .ok_or(qerror!("{}", "get status err"))?
            .as_str()
            .unwrap();
        if status == "FAILED" {
            Err(qerror!("{}", status))
        } else {
            Ok(())
        }
    }

    async fn get_margin_mode(&self, _symbol: Symbol, _margin_coin: String) -> Result<MarginMode> {
        unimplemented!(
            "bybit 不支持 get_margin_mode 接口, 请使用 set_margin_mode 接口设置保证金模式"
        )
    }

    async fn set_margin_mode(
        &self,
        symbol: Symbol,
        _margin_coin: String,
        margin_mode: MarginMode,
    ) -> Result<()> {
        let m = match margin_mode {
            MarginMode::Isolated => "1".to_string(),
            MarginMode::Cross => "0".to_string(),
        };
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
            ("tradeMode", m),
            ("buyLeverage", "1".to_string()),
            ("sellLeverage", "1".to_string()),
        ]);
        let result = self
            .req_raw::<BbResult, _>(Method::POST, "/v5/position/switch-isolated", &map, true)
            .await?;
        match result.ret_code {
            0 => Ok(()),
            100028 => Err(qerror!("当前是统一账号，无法设置保证金模式")),
            _ => Err(qerror!("set margin mode err：{}", result.ret_msg)),
        }
    }

    async fn get_funding_fee(
        &self,
        symbol: Symbol,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Result<Vec<FundingFee>> {
        let new_symbol = symbol.clone();
        let mut map = HashMap::from([
            ("category", "linear".to_owned()),
            ("execType", "Funding".to_owned()),
            ("limit", "100".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
        ]);

        start_time.map(|start| map.insert("startTime", start.to_string()));
        end_time.map(|end| map.insert("endTime", end.to_string()));

        let result = self
            .req::<BbList<BbExecution>, _>(Method::GET, "/v5/execution/list", &map, true)
            .await?;
        let fees: Vec<FundingFee> = result
            .list
            .into_iter()
            .map(|f| {
                let mut funding_fee = FundingFee::from(f);
                funding_fee.symbol = new_symbol.clone();
                funding_fee
            })
            .collect();
        Ok(fees)
    }

    async fn get_account_info(&self) -> Result<AccountInfo> {
        let account_type = match self.config.is_unified {
            true => "UNIFIED".to_string(),
            false => "CONTRACT".to_string(),
        };
        let map = HashMap::from([("accountType", account_type)]);
        let result = self
            .req::<BbList<BbBalance>, _>(Method::GET, "/v5/account/wallet-balance", &map, true)
            .await?;
        let orders: Vec<AccountInfo> = result.list.into_iter().map(AccountInfo::from).collect();
        Ok(orders.into_iter().next().unwrap())
    }

    async fn post_order(&self, order: Order, params: OrderParams) -> Result<String> {
        let position_idx = if params.is_dual_side {
            match order.pos_side {
                Some(PosSide::Long) => Some(1),
                Some(PosSide::Short) => Some(2),
                _ => None,
            }
        } else {
            Some(0)
        };
        let side = if let OrderSide::Buy = order.side {
            "Buy"
        } else {
            "Sell"
        };
        let o_type = if let OrderType::Market = order.order_type {
            "Market"
        } else {
            "Limit"
        };
        let request = NewOrderRequest {
            category: "linear".into(),
            symbol: BbSymbol::from(order.symbol).to_string(),
            side: side.to_string(),
            order_type: o_type.to_string(),
            qty: order.amount.unwrap_or_default().to_string(),
            price: order.price.map(|p| p.to_string()),
            time_in_force: BbTimeInForce::from(order.time_in_force),
            position_idx,
            order_link_id: order.cid,
        };
        let result = self
            .req::<BbOrderResult, _>(Method::POST, "/v5/order/create", &request, true)
            .await?;
        Ok(result.order_id)
    }

    async fn post_batch_order(
        &self,
        orders: Vec<Order>,
        params: OrderParams,
    ) -> Result<BatchOrderRsp> {
        let order_list: Vec<_> = orders
            .into_iter()
            .map(|order| {
                let position_idx = if params.is_dual_side {
                    match order.pos_side {
                        Some(PosSide::Long) => Some(1),
                        Some(PosSide::Short) => Some(2),
                        _ => None,
                    }
                } else {
                    None
                };
                let time_in_force = match order.time_in_force {
                    TimeInForce::IOC => "IOC",
                    TimeInForce::FOK => "FOK",
                    TimeInForce::GTC => "GTC",
                    TimeInForce::PostOnly => "IOC",
                };
                let side = if let OrderSide::Buy = order.side {
                    "Buy"
                } else {
                    "Sell"
                };
                let o_type = if let OrderType::Market = order.order_type {
                    "Market"
                } else {
                    "Limit"
                };
                let symbol = BbSymbol::from(order.symbol).to_string();
                NewBatchOrderRequest {
                    symbol,
                    side: side.to_string(),
                    order_type: o_type.to_string(),
                    qty: order.amount.unwrap_or_default().to_string(),
                    price: order.price.map(|p| p.to_string()),
                    time_in_force: Some(time_in_force.to_string()),
                    position_idx,
                    order_link_id: order.cid,
                }
            })
            .collect();
        let order_list_request = NewBatchOrderListRequest {
            category: "linear".to_string(),
            request: order_list,
        };
        let result: BbList<BbOrderListResultInner> = self
            .req(
                Method::POST,
                "/v5/order/create-batch",
                &order_list_request,
                true,
            )
            .await?;

        let mut data = BatchOrderRsp {
            success_list: vec![],
            failure_list: vec![],
        };

        let mut orders = result.list;
        for _ in 0..orders.len() {
            let order = orders.pop().unwrap();
            data.success_list.push(SuccessOrder {
                id: Some(order.order_id),
                cid: Some(order.order_link_id),
            })
        }
        Ok(data)
    }

    async fn get_order_by_id(&self, symbol: Symbol, order_id: OrderId) -> Result<Order> {
        let mut map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
        ]);
        match order_id {
            OrderId::Id(id) => {
                map.insert("orderId", id);
            }
            OrderId::ClientOrderId(cid) => {
                map.insert("orderLinkId", cid);
            }
        };
        let result = self
            .req::<BbList<BbOrderData>, _>(Method::GET, "/v5/order/realtime", &map, true)
            .await?;

        result
            .list
            .into_iter()
            .next()
            .map(|o| o.into())
            .ok_or(qerror!("get_order_by_id err"))
    }

    async fn get_open_orders(&self, symbol: Symbol) -> Result<Vec<Order>> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("symbol", BbSymbol::from(symbol).to_string()),
        ]);
        let result = self
            .req::<BbList<BbOrderData>, _>(Method::GET, "/v5/order/realtime", &map, true)
            .await?;

        let orders: Vec<Order> = result.list.into_iter().map(|o| o.into()).collect();
        Ok(orders)
    }

    async fn get_all_open_orders(&self) -> Result<Vec<Order>> {
        unimplemented!("bybit 不支持 get_all_open_orders 接口, 请使用 get_open_orders 接口")
    }

    async fn get_orders(
        &self,
        symbol: Symbol,
        start_time: i64,
        end_time: i64,
    ) -> Result<Vec<Order>> {
        let map = HashMap::from([
            ("category", "linear".to_string()),
            ("startTime", start_time.to_string()),
            ("endTime", end_time.to_string()),
            ("symbol", BbSymbol::from(symbol).to_string()),
        ]);
        let result = self
            .req::<BbList<BbOrderData>, _>(Method::GET, "/v5/order/history", &map, true)
            .await?;
        let orders: Vec<Order> = result.list.into_iter().map(|o| o.into()).collect();
        Ok(orders)
    }

    async fn amend_order(&self, order: Order) -> Result<String> {
        let mut map = HashMap::from([("category", "linear".to_owned())]);
        let qty = order.amount.unwrap_or_default().to_string();
        map.insert("symbol", BbSymbol::from(order.symbol).to_string());
        map.insert("qty", qty);
        if order.price.is_some() {
            map.insert("price", order.price.unwrap().to_string());
        }
        if order.cid.is_some() {
            map.insert("orderLinkId", order.cid.unwrap());
        } else {
            map.insert("orderId", order.id);
        }
        let result = self
            .req::<BbOrderResult, _>(Method::POST, "/v5/order/amend", &map, true)
            .await?;
        Ok(result.order_id)
    }

    async fn cancel_order(&self, symbol: Symbol, order_id: OrderId) -> Result<()> {
        let mut map = HashMap::from([("category", "linear".to_owned())]);
        map.insert("symbol", BbSymbol::from(symbol).to_string());
        match order_id {
            OrderId::Id(id) => map.insert("orderId", id),
            OrderId::ClientOrderId(cid) => map.insert("orderLinkId", cid),
        };

        let _result = self
            .req::<BbOrderResult, _>(Method::POST, "/v5/order/cancel", &map, true)
            .await?;
        Ok(())
    }

    async fn batch_cancel_order(&self, symbol: Symbol) -> Result<BatchOrderRsp> {
        let request = BatchCancelOrderRequest {
            category: "linear".to_string(),
            symbol: Some(BbSymbol::from(symbol).to_string()),
            base_coin: None,
            settle_coin: None,
            order_filter: None,
            stop_order_type: None,
        };

        let result = self
            .req::<BatchCancelOrderResult, _>(Method::POST, "/v5/order/cancel-all", &request, true)
            .await?;

        let mut success_list = Vec::new();
        let failure_list = Vec::new();

        for item in result.list {
            success_list.push(SuccessOrder {
                id: Some(item.order_id),
                cid: Some(item.order_link_id),
            });
        }

        Ok(BatchOrderRsp {
            success_list,
            failure_list,
        })
    }

    async fn get_position(&self, symbol: Symbol) -> Result<Vec<Position>> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("limit", "100".to_string()),
            ("symbol", BbSymbol::from(symbol).to_string()),
        ]);
        // let mut m_mode = None;
        // if self.config.is_unified {
        //     let map: HashMap<&str, &str> = HashMap::new();
        //     let result = self
        //         .req::<AccountStatus, _>(Method::GET, "/v5/account/info", &map, true)
        //         .await?;
        //     if result.margin_mode == "REGULAR_MARGIN" {
        //         m_mode = Some(MarginMode::Cross)
        //     } else {
        //         m_mode = Some(MarginMode::Isolated)
        //     }
        // }
        let result = self
            .req::<BbCList<BbPosition>, _>(Method::GET, "/v5/position/list", &map, true)
            .await?;
        let poslist: Vec<Position> = result
            .list
            .into_iter()
            .filter(|pos| pos.seq != -1)
            .map(|p| p.into())
            .collect();
        Ok(poslist)
    }

    async fn get_positions(&self) -> Result<Vec<Position>> {
        let map = HashMap::from([
            ("category", "linear".to_owned()),
            ("limit", "100".to_string()),
            ("settleCoin", "USDT".to_string()),
        ]);
        // let mut m_mode = None;
        // if self.config.is_unified {
        //     let map: HashMap<&str, &str> = HashMap::new();
        //     let result = self
        //         .req::<AccountStatus, _>(Method::GET, "/v5/account/info", &map, true)
        //         .await?;
        //     if result.margin_mode == "REGULAR_MARGIN" {
        //         m_mode = Some(MarginMode::Cross)
        //     } else {
        //         m_mode = Some(MarginMode::Isolated)
        //     }
        // }
        let result = self
            .req::<BbCList<BbPosition>, _>(Method::GET, "/v5/position/list", &map, true)
            .await?;
        let poslist: Vec<Position> = result.list.into_iter().map(|p| p.into()).collect();
        Ok(poslist)
    }

    async fn borrow_coin(&self, _coin: String, _amount: f64) -> Result<()> {
        unimplemented!("这是现货 api，请调用杠杆 api")
    }

    async fn get_borrow(&self, _coin: Option<String>) -> Result<Vec<Borrow>> {
        unimplemented!("这是现货 api，请调用杠杆 api")
    }

    async fn repay_coin(&self, _coin: String, _amount: f64) -> Result<()> {
        unimplemented!("这是现货 api，请调用杠杆 api")
    }

    async fn get_account_mode(&self) -> Result<AccountMode> {
        let map: HashMap<&str, &str> = HashMap::new();
        let result = self
            .req::<AccountStatus, _>(Method::GET, "/v5/account/info", &map, true)
            .await?;
        Ok(AccountMode::from(result))
    }

    async fn set_account_mode(&self, _mode: AccountMode) -> Result<()> {
        unimplemented!("不支持这个 api")
    }

    async fn get_klines_ext(&self, params: GetKlineParams) -> Result<Kline> {
        let prefix_mul = swap_prefix_mul_by_base(&params.symbol.base) as f64;
        // 构建请求参数
        let mut map = HashMap::from([
            ("category", "linear".to_string()),
            ("symbol", BbSymbol::from(params.symbol.clone()).to_string()),
            ("interval", convert_interval_to_bybit(&params.interval)?),
        ]);

        // 添加可选参数
        if let Some(start_time) = params.start_time {
            map.insert("start", start_time.to_string());
        }
        if let Some(end_time) = params.end_time {
            map.insert("end", end_time.to_string());
        }
        if let Some(limit) = params.limit {
            map.insert("limit", limit.to_string());
        }

        // 发送请求
        let response = self
            .req::<BbKlineResponse, _>(Method::GET, "/v5/market/kline", &map, false)
            .await?;

        // 转换K线数据
        let candles: Vec<Candle> = response
            .list
            .into_iter()
            .filter_map(|kline_array| {
                if kline_array.len() >= 6 {
                    // 解析K线数据数组
                    // [0]: startTime, [1]: openPrice, [2]: highPrice, [3]: lowPrice, [4]: closePrice, [5]: volume, [6]: turnover
                    let timestamp = kline_array[0].parse::<i64>().ok()?;
                    let open = kline_array[1].parse::<f64>().ok()?;
                    let high = kline_array[2].parse::<f64>().ok()?;
                    let low = kline_array[3].parse::<f64>().ok()?;
                    let close = kline_array[4].parse::<f64>().ok()?;
                    let volume = kline_array[5].parse::<f64>().ok()?;
                    let quote_volume = kline_array
                        .get(6)
                        .and_then(|v| v.parse::<f64>().ok())
                        .unwrap_or(0.0);

                    let adjusted_open = prefix_mul.price2base(open);
                    let adjusted_high = prefix_mul.price2base(high);
                    let adjusted_low = prefix_mul.price2base(low);
                    let adjusted_close = prefix_mul.price2base(close);
                    let adjusted_volume = prefix_mul.qty2base(volume);

                    Some(Candle {
                        timestamp,
                        open: adjusted_open,
                        high: adjusted_high,
                        low: adjusted_low,
                        close: adjusted_close,
                        volume: adjusted_volume,
                        quote_volume,
                        trades: None,
                        taker_buy_volume: None,
                        taker_buy_quote_volume: None,
                        confirm: true, // Bybit返回的K线数据通常是已确认的
                    })
                } else {
                    None
                }
            })
            .collect();

        Ok(Kline {
            symbol: params.symbol,
            interval: params.interval,
            candles,
        })
    }

    async fn get_funding_rates_history_ext(
        &self,
        params: GetFundingRateHistoryParams,
    ) -> Result<Vec<FundingHistory>> {
        use std::collections::HashMap;
        let mut map = HashMap::new();
        map.insert("category", "linear".to_string());
        if let Some(symbol) = &params.symbol {
            map.insert("symbol", BbSymbol::from(symbol.clone()).to_string());
        } else {
            return Err(qerror!("symbol 必须传递"));
        }
        // Bybit 只传 endTime 返回最近200条，不传返回当前时间往前200条
        if let Some(since_secs) = params.since_secs {
            map.insert("startTime", (since_secs * 1000).to_string());
        }
        if params.limit > 0 {
            map.insert("limit", params.limit.to_string());
        }
        // 发送请求
        let result = self
            .req::<Value, _>(Method::GET, "/v5/market/funding/history", &map, false)
            .await?;
        let list = result["list"].as_array().ok_or(qerror!("list 字段为空"))?;
        let mut fundings: Vec<FundingHistory> = Vec::new();
        for item in list {
            let symbol_str = item["symbol"].as_str().unwrap_or("");
            let funding_rate = item["fundingRate"]
                .as_str()
                .unwrap_or("0")
                .parse()
                .unwrap_or(0.0);
            let timestamp = item["fundingRateTimestamp"]
                .as_str()
                .unwrap_or("0")
                .parse()
                .unwrap_or(0);
            let symbol = BbSymbol::from_str(symbol_str)?.inner;
            fundings.push(FundingHistory {
                symbol,
                funding_rate,
                funding_time: timestamp,
            });
        }
        Ok(fundings)
    }

    async fn get_mark_price(&self, symbol: Option<Symbol>) -> Result<Vec<MarkPrice>> {
        let tickers = self.get_common_tickers(symbol).await?;
        Ok(tickers.into_iter().map(|t| t.into()).collect())
    }
}

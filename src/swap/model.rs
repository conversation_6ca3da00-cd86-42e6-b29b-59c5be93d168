use chrono::Datelike;
use quant_common::base::traits::{GetFundingRateHistoryParams, GetKlineParams};
use std::str::FromStr;
use std::time::Duration;

use once_cell::sync::OnceCell;
use quant_common::base::{
    AccountInfo, Candle, ContractType, FundingFee, FundingHistory, Kline, KlineInterval,
    OrderSource, PlaceOrderCmd,
};
use quant_common::{
    Error, de_from_str, de_from_str_or_default, deserialize_f64_pairs, separate_prefix_numbers,
    time_ms,
};
use quant_common::{
    Result,
    base::{
        FailOrder, FeeRate, MarkPrice, SuccessOrder, Trade, Transfer, WalletType,
        model::{
            Balance, BboTicker, Depth, Funding, InsState, Instrument, MarginMode, Order, OrderId,
            OrderSide, OrderStatus, OrderType, PosSide, Position, PrefixMulExt, RawValue, Symbol,
            Ticker, <PERSON>In<PERSON><PERSON><PERSON>,
        },
    },
    qerror,
};
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::spot::model::BnQuote;
use crate::util::{BnOrderItemRsp, PrefixFormat, is_ed25519_secret, sign_ws_api_params};

use super::rest::BinanceSwap;

pub(crate) const EXCHANGE: &str = "BinanceSwap";

pub(crate) const WS_URL: &str = "fstream.binance.com";
pub(crate) const WS_TEST_URL: &str = "fstream.binancefuture.com";
pub(crate) const WS_COLO_URL: &str = "fstream-mm.binance.com";

pub(crate) const WS_API_URL: &str = "ws-fapi.binance.com";
pub(crate) const WS_API_TEST_URL: &str = "testnet.binancefuture.com";
pub(crate) const WS_API_COLO_URL: &str = "ws-fapi-mm.binance.com";

pub(crate) const MARK_PRICE_CHANNEL: &str = "@markPrice";
pub(crate) const BOOK_TICKER_CHANNEL: &str = "@bookTicker";
pub(crate) const KLINE_CHANNEL: &str = "continuousKline";
pub(crate) const TRADE_CHANNEL: &str = "@trade";
pub(crate) const DEPTH_UPDATE: &str = "@depth@100ms";

pub(crate) const REST_BASE_URL: &str = "fapi.binance.com";
pub(crate) const REST_TEST_BASE_URL: &str = "testnet.binancefuture.com";
pub(crate) const REST_COLO_BASE_URL: &str = "fapi-mm.binance.com";

pub(crate) const HEADER_KEY: &str = "X-MBX-APIKEY";
/// public api
pub(crate) const PATH_BOOKTICKER: &str = "/fapi/v1/ticker/bookTicker";
pub(crate) const PATH_DEPTH: &str = "/fapi/v1/depth";
pub const PATH_TIME: &str = "/fapi/v1/time";
pub(crate) const PATH_EXCHANGE_INFO: &str = "/fapi/v1/exchangeInfo";
pub(crate) const PATH_TICKER_24H: &str = "/fapi/v1/ticker/24hr";
pub(crate) const PATH_PREMIUM_INDEX: &str = "/fapi/v1/premiumIndex";
pub(crate) const PATH_FUNDING_INFO: &str = "/fapi/v1/fundingInfo";
pub(crate) const PATH_FUNDING_RATE: &str = "/fapi/v1/fundingRate";
pub(crate) const PATH_KLINE: &str = "/fapi/v1/continuousKlines";

/// private api
pub(crate) const PATH_ORDER: &str = "/fapi/v1/order";
pub(crate) const PATH_BATCH_ORDER: &str = "/fapi/v1/batchOrders";
pub(crate) const PATH_OPEN_ORDERS: &str = "/fapi/v1/openOrders";
pub(crate) const PATH_ALL_ORDERS: &str = "/fapi/v1/userTrades";
pub(crate) const PATH_BALANCE: &str = "/fapi/v2/balance";
pub(crate) const PATH_COMMISSION_RATE: &str = "/fapi/v1/commissionRate";
pub(crate) const PATH_DUAL_SIDE: &str = "/fapi/v1/positionSide/dual";
pub(crate) const PATH_LEVERAGE: &str = "/fapi/v1/leverage";
pub(crate) const PATH_POS_RISK: &str = "/fapi/v2/positionRisk";
pub(crate) const PATH_LISTEN_KEY: &str = "/fapi/v1/listenKey";
pub(crate) const PATH_BATCH_CANCEL_ORDER: &str = "/fapi/v1/allOpenOrders";
pub(crate) const PATH_FEE_BURN: &str = "/fapi/v1/feeBurn";
pub(crate) const PATH_LEVERAGE_BRACKET: &str = "/fapi/v1/leverageBracket";
pub(crate) const PATH_SYMBOL_CONFIG: &str = "/fapi/v1/symbolConfig";
pub(crate) const PATH_MARGIN_TYPE: &str = "/fapi/v1/marginType";
pub(crate) const PATH_INCOME: &str = "/fapi/v1/income";
pub(crate) const PATH_ACCOUNT: &str = "/fapi/v3/account";

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnPosSide {
    Both,
    Long,
    Short,
}

impl BnPosSide {
    pub(crate) fn to_base(&self, pos_amt_is_positive: bool) -> PosSide {
        match self {
            BnPosSide::Both => {
                if pos_amt_is_positive {
                    PosSide::Long
                } else {
                    PosSide::Short
                }
            }
            BnPosSide::Long => PosSide::Long,
            BnPosSide::Short => PosSide::Short,
        }
    }
}

#[derive(Debug, Serialize, Default, Deserialize, Clone, Copy)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnOrderSide {
    #[default]
    Buy,
    Sell,
}

pub static SPECIAL_SYMBOLS: OnceCell<FxHashMap<Symbol, PrefixFormat>> = OnceCell::new();

pub(crate) async fn init_special_symbols(
    rest: &BinanceSwap,
) -> Result<FxHashMap<Symbol, PrefixFormat>> {
    for i in (0..5).rev() {
        match rest.symbol_infos().await {
            Ok(info) => {
                let map = info
                    .into_iter()
                    .map(|s| (s.symbol.inner, s.symbol.prefix_mul))
                    .collect::<FxHashMap<_, _>>();
                return Ok(map);
            }
            Err(e) if i == 0 => {
                return Err(e);
            }
            Err(e) => {
                warn!("symbol_infos failed: {e}");
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        }
    }
    Err(qerror!("get exchange info failed"))
}

#[inline]
fn swap_prefix_mul_by_symbol(symbol: &Symbol) -> PrefixFormat {
    SPECIAL_SYMBOLS
        .get()
        .unwrap()
        .get(symbol)
        .copied()
        .unwrap_or_default()
}

#[derive(Clone, PartialEq, Default, Eq, Hash, Debug)]
pub struct BnSymbol {
    pub inner: Symbol,
    pub prefix_mul: PrefixFormat,
}

impl BnSymbol {
    pub fn prefix(&self) -> u32 {
        self.prefix_mul.prefix()
    }

    pub fn prefix_f64(&self) -> f64 {
        self.prefix_mul.prefix() as f64
    }

    pub fn is_future(&self) -> bool {
        matches!(self.inner.contract_type, ContractType::Delivery { .. })
    }
}

impl From<Symbol> for BnSymbol {
    fn from(inner: Symbol) -> Self {
        let prefix_mul = swap_prefix_mul_by_symbol(&inner);
        Self { inner, prefix_mul }
    }
}

impl From<BnSymbol> for Symbol {
    fn from(value: BnSymbol) -> Self {
        value.inner
    }
}

impl std::fmt::Display for BnSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let symbol = match self.prefix_mul {
            PrefixFormat::Numeric(1) => format!("{}{}", self.inner.base, self.inner.quote),
            PrefixFormat::MillionPrefix => format!("1M{}{}", self.inner.base, self.inner.quote),
            PrefixFormat::Numeric(prefix_mul) => {
                format!("{prefix_mul}{}{}", self.inner.base, self.inner.quote)
            }
        };

        match &self.inner.contract_type {
            ContractType::Normal => write!(f, "{symbol}"),
            ContractType::Delivery { expiry } => write!(f, "{symbol}_{expiry}"),
            ContractType::Option { .. } => Err(std::fmt::Error),
        }
    }
}

impl FromStr for BnSymbol {
    type Err = Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(serde_plain::from_str(s)?)
    }
}

impl Serialize for BnSymbol {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let symbol_str = self.to_string();
        serializer.serialize_str(&symbol_str)
    }
}

impl<'de> Deserialize<'de> for BnSymbol {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let symbol = String::deserialize(deserializer)?.to_uppercase();
        let (prefix_symbol, expiry) = symbol.split_once('_').unwrap_or((&symbol, ""));

        let (prefix_mul, symbol) = match prefix_symbol.starts_with("1M") {
            true => (PrefixFormat::MillionPrefix, prefix_symbol[2..].to_string()),
            false => {
                let (prefix_mul, try_symbol) = separate_prefix_numbers(prefix_symbol);
                match prefix_mul.as_str() {
                    "1" | "" => (PrefixFormat::Numeric(1), prefix_symbol.to_string()),
                    _ => {
                        let prefix_mul = prefix_mul
                            .parse::<u32>()
                            .map_err(serde::de::Error::custom)?;
                        (PrefixFormat::Numeric(prefix_mul), try_symbol)
                    }
                }
            }
        };

        let inner = Symbol::split_no_separator_symbol::<BnQuote>(&symbol)
            .map_err(serde::de::Error::custom)?;

        let inner = match expiry.is_empty() {
            true => inner,
            false => Symbol::new_delivery(&inner.base, inner.quote, expiry),
        };

        Ok(Self { inner, prefix_mul })
    }
}

#[cfg(test)]
mod tests {
    use tracing_subscriber::fmt;

    use super::*;

    #[test]
    fn test_get_prefix_multiplier() {
        let _ = fmt().try_init();

        for (origin, prefix_mul, base) in [
            ("1000000MOGUSDT", PrefixFormat::Numeric(1_000_000), "MOG"),
            ("1000BONKUSDT", PrefixFormat::Numeric(1_000), "BONK"),
            ("10SETUSDT", PrefixFormat::Numeric(10), "SET"),
            ("1INCHUSDT", PrefixFormat::Numeric(1), "1INCH"),
            ("1MBABYDOGEUSDT", PrefixFormat::MillionPrefix, "BABYDOGE"),
            ("1MDOGEBABYUSDT", PrefixFormat::MillionPrefix, "DOGEBABY"),
            ("BTCUSDT", PrefixFormat::Numeric(1), "BTC"),
            ("BTCUSDT_250926", PrefixFormat::Numeric(1), "BTC"),
        ] {
            let symbol: BnSymbol = origin.parse().unwrap();
            info!("{origin:?}, symbol: {symbol:?}");
            assert_eq!(symbol.inner.base, base);
            assert_eq!(symbol.prefix_mul, prefix_mul);
            assert_eq!(symbol.to_string(), origin);
        }

        const BN_SWAP_1000_SYMBOLS: [&str; 10] = [
            "BONK", "CAT", "FLOKI", "LUNC", "PEPE", "RATS", "SATS", "SHIB", "X", "XEC",
        ];

        for base in BN_SWAP_1000_SYMBOLS {
            let str = format!("1000{base}USDT");
            let symbol: BnSymbol = str.parse().unwrap();
            info!("{:?}", symbol);

            assert_eq!(symbol.prefix_mul, PrefixFormat::Numeric(1_000));
            assert_eq!(symbol.inner.base, base);
            assert_eq!(symbol.to_string(), str);
        }

        const BN_SWAP_1000_000_SYMBOLS: [&str; 1] = ["MOG"];
        for base in BN_SWAP_1000_000_SYMBOLS {
            let str: String = format!("1000000{base}USDT");
            let symbol: BnSymbol = str.parse().unwrap();
            info!("{:?}", symbol);

            assert_eq!(symbol.prefix_mul, PrefixFormat::Numeric(1_000_000));
            assert_eq!(symbol.inner.base, base);
            assert_eq!(symbol.to_string(), str);
        }
    }
}

impl From<OrderSide> for BnOrderSide {
    fn from(value: OrderSide) -> Self {
        match value {
            OrderSide::Sell => Self::Sell,
            OrderSide::Buy => Self::Buy,
        }
    }
}
impl From<BnOrderSide> for OrderSide {
    fn from(value: BnOrderSide) -> Self {
        match value {
            BnOrderSide::Sell => Self::Sell,
            BnOrderSide::Buy => Self::Buy,
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnOrderType {
    #[default]
    Limit,
    Market,
}

impl From<OrderType> for BnOrderType {
    fn from(value: OrderType) -> Self {
        match value {
            OrderType::Limit => Self::Limit,
            OrderType::Market => Self::Market,
        }
    }
}

impl From<BnOrderType> for OrderType {
    fn from(value: BnOrderType) -> Self {
        match value {
            BnOrderType::Limit => Self::Limit,
            BnOrderType::Market => Self::Market,
        }
    }
}

// MARKET 市价单
// LIMIT 限价单
// STOP 止损单
// TAKE_PROFIT 止盈单
// LIQUIDATION 强平单
#[derive(Debug, Serialize, Default, Deserialize, Clone)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnWsOrderType {
    #[default]
    Limit,
    Market,
    Stop,
    TakeProfit,
    Liquidation,
}

impl From<BnWsOrderType> for OrderType {
    fn from(value: BnWsOrderType) -> Self {
        match value {
            BnWsOrderType::Limit => OrderType::Limit,
            _ => OrderType::Market,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Default, PartialEq, Clone)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnTimeInForce {
    #[default]
    Gtc,
    Ioc,
    Fok,
    Gtx,
}

impl From<TimeInForce> for BnTimeInForce {
    fn from(value: TimeInForce) -> Self {
        match value {
            TimeInForce::GTC => Self::Gtc,
            TimeInForce::IOC => Self::Ioc,
            TimeInForce::FOK => Self::Fok,
            TimeInForce::PostOnly => Self::Gtx,
        }
    }
}

impl From<BnTimeInForce> for TimeInForce {
    fn from(value: BnTimeInForce) -> Self {
        match value {
            BnTimeInForce::Gtc => Self::GTC,
            BnTimeInForce::Ioc => Self::IOC,
            BnTimeInForce::Fok => Self::FOK,
            BnTimeInForce::Gtx => Self::PostOnly,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BnTicker {
    pub(crate) symbol: BnSymbol,
    #[serde(deserialize_with = "de_from_str")]
    price_change: f64,
    #[serde(deserialize_with = "de_from_str")]
    price_change_percent: f64,
    // weighted_avg_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) last_price: f64,
    // last_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    open_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    high_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    low_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    volume: f64,
    #[serde(deserialize_with = "de_from_str")]
    quote_volume: f64,
    // open_time: u64,
    close_time: i64,
}

impl From<BnTicker> for Ticker {
    fn from(value: BnTicker) -> Self {
        let prefix_mul = f64::from(value.symbol.prefix());
        Self {
            symbol: value.symbol.into(),
            timestamp: value.close_time,
            high: prefix_mul.price2base(value.high_price),
            low: prefix_mul.price2base(value.low_price),
            open: prefix_mul.price2base(value.open_price),
            close: prefix_mul.price2base(value.last_price),
            volume: prefix_mul.qty2base(value.volume),
            quote_volume: value.quote_volume,
            change: value.price_change,
            change_percent: value.price_change_percent,
        }
    }
}

#[derive(Debug, Deserialize)]
pub(crate) struct StreamData {
    pub(crate) stream: String,
    pub(crate) data: RawValue,
}

/// websocket bookticker
#[derive(Debug, Deserialize)]
pub(crate) struct BookTickerPayload {
    #[serde(rename = "E")]
    pub(crate) timestamp: i64,
    #[serde(rename = "s")]
    symbol: BnSymbol,
    #[serde(rename = "b")]
    #[serde(deserialize_with = "de_from_str")]
    bid_price: f64,
    #[serde(rename = "B")]
    #[serde(deserialize_with = "de_from_str")]
    bid_qty: f64,
    #[serde(rename = "a")]
    #[serde(deserialize_with = "de_from_str")]
    ask_price: f64,
    #[serde(rename = "A")]
    #[serde(deserialize_with = "de_from_str")]
    ask_qty: f64,
}

impl From<BookTickerPayload> for BboTicker {
    fn from(v: BookTickerPayload) -> Self {
        let bid_price;
        let bid_qty;
        let ask_price;
        let ask_qty;
        let mul = v.symbol.prefix();
        if mul == 1 {
            bid_price = v.bid_price;
            bid_qty = v.bid_qty;
            ask_price = v.ask_price;
            ask_qty = v.ask_qty;
        } else {
            let mul = f64::from(mul);
            bid_price = v.bid_price / mul;
            bid_qty = v.bid_qty * mul;
            ask_price = v.ask_price / mul;
            ask_qty = v.ask_qty * mul;
        }
        BboTicker {
            symbol: v.symbol.into(),
            timestamp: v.timestamp,
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
        }
    }
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub(crate) struct MarkPricePayload {
    #[serde(rename = "E")]
    event_time_ms: i64,

    #[serde(rename = "s")]
    symbol: BnSymbol,

    #[serde(rename = "p")]
    #[serde(deserialize_with = "de_from_str")]
    mark_price: f64,
    /// 现货指数价格(用于资金费率计算公式)
    #[serde(rename = "i")]
    #[serde(deserialize_with = "de_from_str")]
    index_price: f64,

    #[serde(rename = "r")]
    #[serde(deserialize_with = "de_from_str")]
    funding_rate: f64,
    #[serde(rename = "T")]
    next_fund_time_ms: i64,
}

impl From<&MarkPricePayload> for Funding {
    fn from(value: &MarkPricePayload) -> Self {
        Self {
            symbol: value.symbol.clone().into(),
            funding_rate: value.funding_rate,
            next_funding_at: value.next_fund_time_ms,
            funding_interval: None,
            max_funding_rate: 0.0,
            min_funding_rate: 0.0,
        }
    }
}

impl From<MarkPricePayload> for MarkPrice {
    fn from(value: MarkPricePayload) -> Self {
        let bn_symbol = value.symbol;
        let prefix_mul = bn_symbol.prefix() as f64;
        let price = prefix_mul.price2base(value.mark_price);
        Self {
            symbol: bn_symbol.into(),
            price,
        }
    }
}

#[derive(Debug, Serialize)]
pub(crate) struct SubscriptionRequest {
    pub(crate) id: i64,
    pub(crate) method: SubReqMethod,
    pub(crate) params: Vec<String>,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum SubReqMethod {
    Subscribe,
    // Unsubscribe,
}

#[derive(Debug, Deserialize)]
pub(crate) struct WsResponse {
    // pub(crate) id: i64,
    // pub(crate)result: Option<bool>,
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct OrderResponse {
    pub(crate) order_id: u64,
    // pub(crate)client_order_id: String,
}

#[derive(Serialize, Debug)]
pub(crate) struct NewOrderRequest {
    symbol: BnSymbol,
    side: BnOrderSide,
    #[serde(skip_serializing_if = "Option::is_none")]
    price: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    quantity: Option<String>,

    #[serde(rename = "positionSide")]
    #[serde(skip_serializing_if = "Option::is_none")]
    position_side: Option<BnPosSide>,

    #[serde(rename = "type")]
    order_type: BnOrderType,

    #[serde(rename = "timeInForce")]
    #[serde(skip_serializing_if = "Option::is_none")]
    time_in_force: Option<BnTimeInForce>,

    #[serde(rename = "newClientOrderId")]
    #[serde(skip_serializing_if = "Option::is_none")]
    client_order_id: Option<String>,

    /// true, false; 非双开模式下默认false 双开模式下不接受此参数。
    #[serde(rename = "reduceOnly")]
    #[serde(skip_serializing_if = "Option::is_none")]
    reduce_only: Option<String>,
}

impl NewOrderRequest {
    pub(crate) fn new(order: Order, is_dual_side: bool) -> Self {
        let symbol: BnSymbol = order.symbol.clone().into();
        let reduce_only = match (order.reduce_only(), !is_dual_side) {
            (true, true) => Some("true".to_string()),
            _ => None,
        };
        let time_in_force = match order.order_type {
            OrderType::Market => None,
            OrderType::Limit => Some(order.time_in_force.into()),
        };
        let price = match order.order_type {
            OrderType::Limit => order.price.map(|p| p.to_string()),
            OrderType::Market => None,
        };
        Self {
            symbol,
            side: order.side.into(),
            order_type: order.order_type.into(),
            price,
            quantity: order.amount.map(|a| a.to_string()),
            position_side: if is_dual_side {
                Some(match order.pos_side.as_ref().unwrap() {
                    PosSide::Long => BnPosSide::Long,
                    PosSide::Short => BnPosSide::Short,
                })
            } else {
                None
            },
            time_in_force,
            client_order_id: order.cid,
            reduce_only,
        }
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BatchOrders {
    batch_orders: String,
}

impl BatchOrders {
    pub(crate) fn new(batch_orders: Vec<NewOrderRequest>) -> Self {
        Self {
            batch_orders: sonic_rs::to_string(&batch_orders).unwrap(),
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct PostBatchOrderRsp {
    order_id: Option<u64>,
    code: Option<i32>,
    msg: Option<String>,
    client_order_id: Option<String>,
}

impl PostBatchOrderRsp {
    pub(crate) fn into_base(self, cid: Option<&Option<String>>) -> BnOrderItemRsp {
        match self.code {
            Some(error_code) => BnOrderItemRsp::Fail(FailOrder {
                id: None,
                cid: cid.and_then(|x| x.clone()),
                error_code,
                error: self.msg.unwrap_or_default(),
            }),
            None => BnOrderItemRsp::Succ(SuccessOrder {
                id: self.order_id.map(|id| id.to_string()),
                cid: self.client_order_id,
            }),
        }
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct OpenOrdersReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    symbol: Option<BnSymbol>,
    #[serde(skip_serializing_if = "Option::is_none")]
    start_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    end_time: Option<i64>,
}

impl OpenOrdersReq {
    pub(crate) fn new(
        symbol: Option<Symbol>,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Self {
        Self {
            symbol: symbol.map(|s| s.into()),
            start_time,
            end_time,
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct UserTrades {
    order_id: u64,
    time: i64,
    symbol: BnSymbol,
    side: BnOrderSide,
    position_side: BnPosSide,
    #[serde(deserialize_with = "de_from_str_or_default")]
    price: f64, // 成交价格
    #[serde(deserialize_with = "de_from_str")]
    qty: f64, // 成交数量
}

impl From<UserTrades> for Order {
    fn from(v: UserTrades) -> Self {
        let prefix_mul = f64::from(v.symbol.prefix());
        let pos_side = v.position_side.to_base(false);

        Self {
            id: v.order_id.to_string(),
            timestamp: v.time,
            status: OrderStatus::Filled,
            symbol: v.symbol.into(),
            side: v.side.into(),
            pos_side: Some(pos_side),
            price: if v.price == 0. {
                None
            } else {
                Some(prefix_mul.price2base(v.price))
            },
            amount: Some(prefix_mul.qty2base(v.qty)),
            filled: prefix_mul.qty2base(v.qty),
            filled_avg_price: prefix_mul.price2base(v.price),
            ..Default::default()
        }
    }
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct GetOrderReq {
    symbol: BnSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    order_id: Option<u64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    orig_client_order_id: Option<String>,
}

impl GetOrderReq {
    pub(crate) fn new(symbol: Symbol, order_id: OrderId) -> Result<Self> {
        let symbol = symbol.into();
        Ok(match order_id {
            OrderId::Id(id) => Self {
                symbol,
                order_id: Some(id.parse()?),
                orig_client_order_id: None,
            },
            OrderId::ClientOrderId(cid) => Self {
                symbol,
                order_id: None,
                orig_client_order_id: Some(cid),
            },
        })
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct OpenOrdersResp {
    order_id: u64,
    client_order_id: String,
    time: i64,
    status: BnOrderStatus,
    symbol: BnSymbol,
    r#type: BnOrderType,
    side: BnOrderSide,
    position_side: BnPosSide,
    time_in_force: BnTimeInForce,
    #[serde(deserialize_with = "de_from_str_or_default")]
    price: f64,
    #[serde(deserialize_with = "de_from_str")]
    orig_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    executed_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    avg_price: f64,
}

impl OpenOrdersResp {
    pub(crate) fn is_future(&self) -> bool {
        self.symbol.is_future()
    }
}

impl From<OpenOrdersResp> for Order {
    fn from(v: OpenOrdersResp) -> Self {
        let prefix_mul = f64::from(v.symbol.prefix());
        let pos_side = v.position_side.to_base(false);
        Self {
            id: v.order_id.to_string(),
            cid: Some(v.client_order_id),
            timestamp: v.time,
            status: v.status.into(),
            symbol: v.symbol.into(),
            order_type: v.r#type.into(),
            side: v.side.into(),
            pos_side: Some(pos_side),
            time_in_force: v.time_in_force.into(),
            price: if v.price == 0. {
                None
            } else {
                Some(prefix_mul.price2base(v.price))
            },
            amount: Some(prefix_mul.qty2base(v.orig_qty)),
            filled: prefix_mul.qty2base(v.executed_qty),
            filled_avg_price: prefix_mul.price2base(v.avg_price),
            quote_amount: None,
            ..Default::default()
        }
    }
}

#[derive(Deserialize)]
// SCREAMING_SNAKE_CASE means that all the field names in the serialized form will be in uppercase letters with underscores separating words.
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
enum BnOrderStatus {
    // 新建订单
    New,
    // 部分成交
    PartiallyFilled,
    // 全部成交
    Filled,
    // 已撤销
    Canceled,
    // 订单被拒绝
    Rejected,
    // 订单过期(根据timeInForce参数规则)
    Expired,
    // 订单被STP过期
    ExpiredInMatch,
}

// #[derive(Deserialize)]
// #[serde(rename_all = "SCREAMING_SNAKE_CASE")]
// enum BnOrderType {
//     Market,
//     Limit,
//     Stop,
//     StopMarket,
//     TakeProfit,
//     TakeProfitMarket,
//     TrailingStopMarket,
// }

impl From<BnOrderStatus> for OrderStatus {
    fn from(v: BnOrderStatus) -> Self {
        match v {
            BnOrderStatus::New => Self::Open,
            BnOrderStatus::PartiallyFilled => Self::PartiallyFilled,
            BnOrderStatus::Filled => Self::Filled,
            BnOrderStatus::Canceled => Self::Canceled,
            _ => Self::Canceled,
        }
    }
}

#[derive(Deserialize)]
pub(crate) struct TrivialResponse {}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct RestBookTicker {
    symbol: BnSymbol,
    #[serde(deserialize_with = "de_from_str")]
    bid_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    bid_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    ask_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    ask_qty: f64,
    #[serde(default = "time_ms")]
    time: i64,
}

impl RestBookTicker {
    pub(crate) fn is_future(&self) -> bool {
        self.symbol.is_future()
    }
}

impl From<RestBookTicker> for BboTicker {
    fn from(v: RestBookTicker) -> Self {
        let prefix_mul = v.symbol.prefix() as f64;
        Self {
            symbol: v.symbol.into(),
            bid_price: prefix_mul.price2base(v.bid_price),
            bid_qty: prefix_mul.qty2base(v.bid_qty),
            ask_price: prefix_mul.price2base(v.ask_price),
            ask_qty: prefix_mul.qty2base(v.ask_qty),
            timestamp: v.time,
        }
    }
}

#[derive(Serialize)]
pub struct SymbolArg {
    symbol: BnSymbol,
}

impl From<Symbol> for SymbolArg {
    fn from(value: Symbol) -> Self {
        Self {
            symbol: value.into(),
        }
    }
}

/// 账户余额
#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AccountBalance {
    pub(crate) asset: String,
    #[serde(deserialize_with = "de_from_str")]
    balance: f64,
    #[serde(deserialize_with = "de_from_str")]
    cross_un_pnl: f64,
    /// 统一账户下avail可能会大于max_withdraw_amount
    #[serde(deserialize_with = "de_from_str")]
    max_withdraw_amount: f64,
}

impl From<AccountBalance> for Balance {
    fn from(v: AccountBalance) -> Self {
        Self {
            asset: v.asset,
            balance: v.balance + v.cross_un_pnl,
            available_balance: v.max_withdraw_amount,
            unrealized_pnl: v.cross_un_pnl,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct QueryPosition {
    pub(crate) symbol: BnSymbol,
    pub(crate) position_side: BnPosSide,
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) leverage: u8,
    pub(crate) entry_price: f64,
    pub(crate) position_amt: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) unrealized_profit: f64,
    // pub(crate) initial_margin: f64,
    // pub(crate) maint_margin: f64,
    pub(crate) isolated: bool,
    pub(crate) update_time: i64,
}

impl From<QueryPosition> for Position {
    fn from(v: QueryPosition) -> Self {
        let prefix_mul = v.symbol.prefix() as f64;
        let side = v.position_side.to_base(v.position_amt.is_sign_positive());
        Self {
            symbol: v.symbol.into(),
            side,
            leverage: v.leverage,
            entry_price: prefix_mul.price2base(v.entry_price),
            amount: prefix_mul.qty2base(v.position_amt).abs(),
            unrealized_pnl: v.unrealized_profit,
            margin_mode: MarginMode::is_isolated(v.isolated),
            timestamp: v.update_time,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PosRisk {
    pub symbol: BnSymbol,
    pub(crate) position_side: BnPosSide,
    #[serde(deserialize_with = "de_from_str")]
    pub leverage: u8,
    #[serde(deserialize_with = "de_from_str")]
    pub max_notional_value: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) entry_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) position_amt: f64,
    margin_type: String,

    #[serde(deserialize_with = "de_from_str")]
    pub(crate) un_realized_profit: f64,
    // #[serde(deserialize_with = "de_from_str")]
    // pub(crate) mark_price: f64,
    #[allow(dead_code)]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) liquidation_price: f64,
    update_time: i64,
}

impl From<PosRisk> for Position {
    fn from(v: PosRisk) -> Self {
        let prefix_mul_f64 = f64::from(v.symbol.prefix());
        let side = v.position_side.to_base(v.position_amt.is_sign_positive());
        Self {
            symbol: v.symbol.into(),
            side,
            leverage: v.leverage,
            entry_price: prefix_mul_f64.price2base(v.entry_price),
            amount: prefix_mul_f64.qty2base(v.position_amt.abs()),
            unrealized_pnl: v.un_realized_profit,
            margin_mode: MarginMode::is_isolated(v.margin_type != "cross"),
            timestamp: v.update_time,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct SetLeverageReq {
    symbol: BnSymbol,
    leverage: u8,
}

impl SetLeverageReq {
    pub(crate) fn new(symbol: Symbol, leverage: u8) -> Self {
        Self {
            symbol: symbol.into(),
            leverage,
        }
    }
}

#[allow(dead_code)]
#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SetLeverageRsp {
    #[serde(deserialize_with = "de_from_str")]
    pub max_notional_value: f64,
}

#[derive(Serialize, Debug)]
pub struct GetMarginModeReq {
    symbol: BnSymbol,
}

impl GetMarginModeReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            symbol: symbol.into(),
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "UPPERCASE")]
enum BnMarginMode {
    Crossed,
    Isolated,
}

impl From<BnMarginMode> for MarginMode {
    fn from(value: BnMarginMode) -> Self {
        match value {
            BnMarginMode::Crossed => Self::Cross,
            BnMarginMode::Isolated => Self::Isolated,
        }
    }
}

impl From<MarginMode> for BnMarginMode {
    fn from(value: MarginMode) -> Self {
        match value {
            MarginMode::Cross => Self::Crossed,
            MarginMode::Isolated => Self::Isolated,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BnMarginModeRsp {
    // "symbol": "BTCUSDT",
    margin_type: BnMarginMode,
    // "isAutoAddMargin": "false",
    // "leverage": 21,
    // "maxNotionalValue": "1000000",
}

impl From<BnMarginModeRsp> for MarginMode {
    fn from(value: BnMarginModeRsp) -> Self {
        value.margin_type.into()
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct SetMarginModeReq {
    symbol: BnSymbol,
    margin_type: BnMarginMode,
}

impl SetMarginModeReq {
    pub fn new(symbol: Symbol, margin_mode: MarginMode) -> Self {
        Self {
            symbol: symbol.into(),
            margin_type: margin_mode.into(),
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct SetMarginModeRsq {}

#[derive(Serialize, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub(crate) struct RequestDualSidePosition {
    pub(crate) dual_side_position: bool,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct ExchangeInfo {
    pub(crate) symbols: Vec<SymbolInfo>,
}

/// 合约状态 (contractStatus, status):
#[derive(Deserialize, Debug, Eq, PartialEq)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum ContractStatus {
    /// 待上市
    PendingTrading,
    /// 交易中
    Trading,
    /// 预交割
    PreDelivering,
    /// 交割中
    Delivering,
    /// 已交割
    Delivered,
    /// 预结算
    PreSettle,
    /// 结算中
    Settling,
    /// 已下架
    Close,
}

impl From<ContractStatus> for InsState {
    fn from(value: ContractStatus) -> Self {
        match value {
            ContractStatus::Trading => Self::Normal,
            _ => Self::Close,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct SymbolInfo {
    // base_asset: String,
    // quote_asset: String,
    contract_type: BnContractType,
    status: ContractStatus,
    symbol: BnSymbol,
    // price_precision: i32,
    // quantity_precision: i32,
    filters: Vec<Value>,
}

impl SymbolInfo {
    pub(crate) fn is_open(&self) -> bool {
        self.status == ContractStatus::Trading
    }

    pub(crate) fn is_delivery(&self, swap: bool) -> bool {
        match self.contract_type {
            BnContractType::Perpetual => swap,
            BnContractType::CurrentQuarter | BnContractType::NextQuarter => !swap,
            BnContractType::Other => false,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(
    tag = "filterType",
    rename_all = "SCREAMING_SNAKE_CASE",
    rename_all_fields = "camelCase"
)]
pub(crate) enum SymbolFilter {
    PriceFilter {
        // #[serde(deserialize_with = "de_from_str")]
        // min_price: f64,
        // #[serde(deserialize_with = "de_from_str")]
        // max_price: f64,
        #[serde(deserialize_with = "de_from_str")]
        tick_size: f64,
    },
    LotSize {
        #[serde(deserialize_with = "de_from_str")]
        min_qty: f64,
        // pub(crate)max_qty: f64,
        #[serde(deserialize_with = "de_from_str")]
        step_size: f64,
    },
    MarketLotSize {
        // pub(crate)min_qty: f64,
        // pub(crate)max_qty: f64,
        // pub(crate)step_size: f64,
    },
    MaxNumOrders {
        // limit: usize,
    },
    MaxNumAlgoOrders {
        // limit: usize,
    },
    MinNotional {
        #[serde(deserialize_with = "de_from_str")]
        notional: f64,
    },
    PercentPrice {
        // multiplier_up: f64,
        // multiplier_down: f64,
        // multiplier_decimal: f64,
    },
    // POSITION_RISK_CONTROL
    PositionRiskControl {},
}

impl From<SymbolInfo> for Instrument {
    fn from(value: SymbolInfo) -> Self {
        let filters = value
            .filters
            .iter()
            .filter_map(|v| {
                let filter = serde_json::from_value(v.clone()).ok();
                if filter.is_none() {
                    let v = serde_json::to_string(v).unwrap();
                    warn!("symbol filter error: {}", v);
                }
                filter
            })
            .collect::<Vec<SymbolFilter>>();

        let (min_qty, step_size) = filters
            .iter()
            .find_map(|f| match f {
                SymbolFilter::LotSize {
                    min_qty, step_size, ..
                } => Some((*min_qty, step_size)),
                _ => None,
            })
            .unwrap();
        let prefix_mul_f64 = f64::from(value.symbol.prefix());

        let tick_size = filters
            .iter()
            .find_map(|f| match f {
                SymbolFilter::PriceFilter { tick_size, .. } => Some(tick_size),
                _ => None,
            })
            .unwrap();
        let min_notional = filters
            .iter()
            .find_map(|f| match f {
                SymbolFilter::MinNotional { notional } => Some(*notional),
                _ => None,
            })
            .unwrap();
        Self {
            symbol: value.symbol.into(),
            state: value.status.into(),
            min_qty,
            min_notional,
            price_precision: -(tick_size.log10() as i32),
            amount_precision: -(step_size.log10() as i32),
            price_tick: *tick_size,
            amount_tick: *step_size,
            price_multiplier: prefix_mul_f64,
            amount_multiplier: 1.0 / prefix_mul_f64,
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct RspTime {
    server_time: i64,
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct RspListenKey {
    pub(crate) listen_key: String,
}

#[derive(Debug, Deserialize)]
#[serde(tag = "e", rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum AccountPayload {
    ListenKeyExpired,
    MarginCall,
    AccountConfigUpdate,
    StrategyUpdate,
    GridUpdate,
    ConditionalOrderTriggerReject,
    AccountUpdate(AccountUpdate),
    OrderTradeUpdate(OrderTradeUpdate),
    TradeLite(TradeLite),
}

// {
//     "e":"TRADE_LITE",             // 事件类型
//     "E":*************,            // 事件时间
//     "T":*************,            // 交易时间
//     "s":"BTCUSDT",                // 交易对
//     "q":"0.001",                  // 订单原始数量
//     "p":"0",                      // 订单原始价格
//     "m":false,                    // 该成交是作为挂单成交吗？
//     "c":"z8hcUoOsqEdKMeKPSABslD", // 客户端自定订单ID
//         // 特殊的自定义订单ID:
//         // "autoclose-"开头的字符串: 系统强平订单
//         // "adl_autoclose": ADL自动减仓订单
//         // "settlement_autoclose-": 下架或交割的结算订单
//     "S":"BUY",                    // 订单方向
//     "L":"64089.20",               // 订单末次成交价格
//     "l":"0.040",                  // 订单末次成交量
//     "t":*********,                // 成交ID
//     "i":8886774,                  // 订单ID
// }
#[derive(Debug, Deserialize)]
pub(crate) struct TradeLite {
    // /// 事件时间
    // #[serde(rename = "E")]
    // pub(crate) event_time: i64,
    /// 交易时间
    #[serde(rename = "T")]
    pub(crate) timestamp: i64,
    /// 交易对
    #[serde(rename = "s")]
    pub(crate) symbol: BnSymbol,
    /// 订单原始数量
    #[serde(rename = "q")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) amount: f64,
    /// 订单原始价格
    #[serde(rename = "p")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) price: f64,
    // /// 该成交是作为挂单成交吗？
    // #[serde(rename = "m")]
    // pub(crate) is_maker: bool,
    /// 客户端自定订单ID
    #[serde(rename = "c")]
    pub(crate) client_order_id: Option<String>,
    /// 订单方向
    #[serde(rename = "S")]
    pub(crate) side: BnOrderSide,
    /// 订单末次成交价格
    #[serde(rename = "L")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) last_price: f64,
    /// 订单末次成交量
    #[serde(rename = "l")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) last_amount: f64,
    // /// 成交ID
    // #[serde(rename = "t")]
    // pub(crate) trade_id: u64,
    /// 订单ID
    #[serde(rename = "i")]
    pub(crate) order_id: u64,
}

impl TradeLite {
    pub(crate) fn get_symbol(&self) -> Symbol {
        self.symbol.clone().into()
    }
}

impl From<TradeLite> for Order {
    fn from(value: TradeLite) -> Self {
        let v = value;
        let prefix_mul = v.symbol.prefix();
        let prefix_mul_f64 = f64::from(prefix_mul);

        Self {
            symbol: v.symbol.into(),
            status: OrderStatus::PartiallyFilled,
            pos_side: None,
            side: v.side.into(),
            id: v.order_id.to_string(),
            filled: prefix_mul_f64.qty2base(v.last_amount),
            filled_avg_price: prefix_mul_f64.price2base(v.last_price),
            cid: v.client_order_id,
            price: Some(prefix_mul_f64.price2base(v.price)),
            amount: Some(prefix_mul_f64.qty2base(v.amount)),
            time_in_force: TimeInForce::GTC,
            timestamp: v.timestamp,
            order_type: OrderType::Limit,
            quote_amount: None,
            source: OrderSource::UserTrade,
        }
    }
}

#[derive(Debug, Deserialize)]
pub(crate) struct AccountUpdate {
    /// 撮合时间
    #[serde(rename = "T")]
    pub(crate) timestamp: i64,
    #[serde(rename = "a")]
    pub(crate) data: AccountUpdateData,
}

#[allow(dead_code)]
#[derive(Deserialize, Debug)]
pub(crate) struct AccountUpdateData {
    #[serde(rename = "B")]
    pub(crate) balance: Vec<BalanceItem>,

    #[serde(rename = "P")]
    pub(crate) position: Option<Vec<WsPosition>>,

    #[serde(rename = "m")]
    pub(crate) reason: AccountUpdateReason,
}

#[derive(Deserialize, Debug)]
pub(crate) struct WsPosition {
    #[serde(rename = "s")]
    pub(crate) symbol: BnSymbol,

    /// 仓位
    #[serde(rename = "pa")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) position_amount: f64,

    /// 平均持仓价格
    #[serde(rename = "ep")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) entry_price: f64,

    /// 未实现盈亏
    #[serde(rename = "up")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) unrealized_profit: f64,

    /// 持仓方向
    #[serde(rename = "ps")]
    pub(crate) position_side: BnPosSide,

    /// 若为逐仓，仓位保证金
    #[serde(rename = "iw")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) isolated_wallet: f64,
}

impl WsPosition {
    pub(crate) fn into_base(self, timestamp: i64) -> Position {
        let prefix_mul = f64::from(self.symbol.prefix());
        Position {
            symbol: self.symbol.into(),
            side: self
                .position_side
                .to_base(self.position_amount.is_sign_positive()),
            entry_price: prefix_mul.price2base(self.entry_price),
            amount: prefix_mul.qty2base(self.position_amount).abs(),
            unrealized_pnl: self.unrealized_profit,
            margin_mode: if self.isolated_wallet > 0.0 {
                MarginMode::Isolated
            } else {
                MarginMode::Cross
            },
            timestamp,
            leverage: 1,
        }
    }
}

#[derive(Deserialize, Clone, Debug)]
pub(crate) struct OrderTradeUpdate {
    #[serde(rename = "E")]
    event_time: i64,
    #[serde(rename = "o")]
    data: OrderTradeUpdateData,
    // #[serde(rename = "T")]
    // pub(crate) trans_time: i64,
}

impl OrderTradeUpdate {
    pub(crate) fn get_symbol(&self) -> Symbol {
        self.data.symbol.clone().into()
    }
}

impl From<OrderTradeUpdate> for Order {
    fn from(value: OrderTradeUpdate) -> Self {
        let v = value.data;
        let prefix_mul = v.symbol.prefix();
        let prefix_mul_f64 = f64::from(prefix_mul);
        Self {
            symbol: v.symbol.into(),
            status: v.order_state.into(),
            pos_side: Some(v.position_side.to_base(v.last_amount.is_sign_positive())),
            side: v.side.into(),
            id: v.order_id.to_string(),
            filled: prefix_mul_f64.qty2base(v.z),
            filled_avg_price: prefix_mul_f64.price2base(v.average_price),
            cid: v.client_order_id,
            price: Some(prefix_mul_f64.price2base(v.price)),
            amount: Some(prefix_mul_f64.qty2base(v.amount)),
            time_in_force: v.time_in_force.into(),
            timestamp: value.event_time,
            order_type: v.order_type.into(),
            quote_amount: None,
            ..Default::default()
        }
    }
}

#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum BnOrderState {
    New,
    PartiallyFilled,
    Filled,
    Expired,
    Canceled,
    ExpiredInMatch,
}

impl From<BnOrderState> for OrderStatus {
    fn from(value: BnOrderState) -> Self {
        match value {
            BnOrderState::New => Self::Open,
            BnOrderState::PartiallyFilled => Self::PartiallyFilled,
            BnOrderState::Filled => Self::Filled,
            _ => Self::Canceled,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BatchCancelOrderSucc {
    client_order_id: Option<String>,
    order_id: Option<i64>,
}

impl From<BatchCancelOrderSucc> for SuccessOrder {
    fn from(value: BatchCancelOrderSucc) -> Self {
        Self {
            id: value.order_id.map(|id| id.to_string()),
            cid: value.client_order_id,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(untagged)]
pub(crate) enum BatchCancelOrderByIdsRsp {
    Fail(BinanceErr),
    Succ(BatchCancelOrderSucc),
}

impl From<BatchCancelOrderByIdsRsp> for BnOrderItemRsp {
    fn from(value: BatchCancelOrderByIdsRsp) -> BnOrderItemRsp {
        match value {
            BatchCancelOrderByIdsRsp::Fail(err) => BnOrderItemRsp::Fail(FailOrder {
                id: Default::default(),
                cid: Default::default(),
                error_code: err.code,
                error: err.msg,
            }),
            BatchCancelOrderByIdsRsp::Succ(succ) => BnOrderItemRsp::Succ(succ.into()),
        }
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BatchCancelOrderByIdsReq {
    symbol: BnSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    order_id_list: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    orig_client_order_id_list: Option<String>,
}

impl BatchCancelOrderByIdsReq {
    pub(crate) fn new(
        symbol: Symbol,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<Self> {
        let cids = match ids.is_some() {
            true => None,
            false => cids,
        };
        let ids = ids
            .map(|ids| {
                ids.into_iter()
                    .try_fold(vec![], |mut acc, s| match s.parse::<i64>() {
                        Ok(n) => {
                            acc.push(n);
                            Ok(acc)
                        }
                        Err(e) => Err(e),
                    })
            })
            .transpose()?;
        Ok(Self {
            symbol: symbol.into(),
            order_id_list: ids.map(|ids| sonic_rs::to_string(&ids)).transpose()?,
            orig_client_order_id_list: cids.map(|cids| sonic_rs::to_string(&cids)).transpose()?,
        })
    }
}

#[derive(Debug, Default, Clone, Serialize, Deserialize)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum ExecutionType {
    #[default]
    New,
    /// IOC部分成交后撤单
    Canceled,
    /// 强平 ADL或爆仓
    Calculated,
    /// IOC部分成交后撤单
    Expired,
    Trade,
    Amendment,
}
#[derive(Deserialize, Debug, Clone)]
pub(crate) struct OrderTradeUpdateData {
    #[serde(rename = "s")]
    pub(crate) symbol: BnSymbol,

    #[serde(rename = "S")]
    pub(crate) side: BnOrderSide,
    #[serde(rename = "ps")]
    pub(crate) position_side: BnPosSide,

    #[serde(rename = "X")]
    pub(crate) order_state: BnOrderState,
    // #[serde(rename = "x")]
    // pub(crate) execution_type: ExecutionType,
    #[serde(rename = "o")]
    pub(crate) order_type: BnWsOrderType,

    /// 订单末次成交量
    #[serde(rename = "l")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) last_amount: f64,
    #[serde(rename = "p")]
    #[serde(deserialize_with = "de_from_str")]
    price: f64,
    #[serde(rename = "q")]
    #[serde(deserialize_with = "de_from_str")]
    amount: f64,

    #[serde(rename = "i")]
    pub(crate) order_id: u64,
    #[serde(rename = "c")]
    pub(crate) client_order_id: Option<String>,
    /// 累计成交数量
    #[serde(deserialize_with = "de_from_str")]
    z: f64,
    #[serde(rename = "ap")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) average_price: f64,
    /// 手续费资产类型
    // #[serde(rename = "N")]
    // pub(crate) fee_ccy: String,
    /// 最新一笔成交的手续费
    /*
    taker: 0.0004
    filled_amount: 0.025, avg_price: 3533.96, fill_price: 3533.96, fill_amount: 0.025, fill_fee: 0.0353396
    filled_amount: 0.075, avg_price: 3529.24, fill_price: 3526.88, fill_amount: 0.05,  fill_fee: 0.0705376
    filled_amount: 0.15,  avg_price: 3515.12, fill_price: 3501.0,  fill_amount: 0.075, fill_fee: 0.10503
    */
    // #[serde(rename = "n")]
    // #[serde(deserialize_with = "de_from_str")]
    // pub(crate) fee: f64,
    // #[serde(rename = "R")]
    // pub(crate) reduce_only: bool,
    #[serde(rename = "f")]
    pub(crate) time_in_force: BnTimeInForce,
    // #[serde(rename = "m")]
    // pub(crate) is_maker: bool,
}

#[derive(Deserialize, Debug, PartialEq, Eq)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum AccountUpdateReason {
    Deposit,
    Withdraw,
    Order,
    FundingFee,
    WithdrawReject,
    Adjustment,
    InsuranceClear,
    AdminDeposit,
    AdminWithdraw,
    MarginTransfer,
    MarginTypeChange,
    AssetTransfer,
    OptionsPremiumFee,
    OptionsSettleProfit,
    AutoExchange,
    CoinSwapDeposit,
    CoinSwapWithdraw,
}

#[allow(dead_code)]
#[derive(Deserialize, Debug)]
pub(crate) struct BalanceItem {
    #[serde(rename = "a")]
    pub(crate) asset: String,

    #[serde(rename = "wb")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) wallet_balance: f64,
    // 除去逐仓仓位保证金的钱包余额
    // 全仓模式下，current_wallet_balance跟wallet_balance一样
    // #[serde(rename = "cw")]
    // pub(crate)current_wallet_balance: f64,
    #[serde(rename = "bc")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) balance_change: f64,
}

impl BalanceItem {
    pub(crate) fn get_funding_fee(&self, timestamp: i64) -> FundingFee {
        FundingFee {
            symbol: Symbol::new(&self.asset),
            timestamp,
            funding_fee: self.balance_change,
        }
    }
}

#[derive(Serialize)]
pub(crate) struct BnKlineParams {
    pub(crate) symbol: BnSymbol,
    pub(crate) start_time: Option<u64>,
    pub(crate) end_time: Option<u64>,
    pub(crate) interval: &'static str,
    pub(crate) limit: u32,
}

impl Default for BnKlineParams {
    fn default() -> Self {
        Self {
            symbol: BnSymbol::default(),
            start_time: None,
            end_time: None,
            interval: "1m",
            limit: 500,
        }
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct BnDepth {
    #[serde(rename = "E")]
    #[serde(default = "time_ms")]
    event_time: i64,

    #[serde(deserialize_with = "deserialize_f64_pairs")]
    bids: Vec<(f64, f64)>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    asks: Vec<(f64, f64)>,

    #[serde(rename = "lastUpdateId")]
    last_update_id: i64,
}

impl BnDepth {
    pub(crate) fn into_depth(self, symbol: Symbol) -> Depth {
        let dep = self;
        let prefix_mul = f64::from(swap_prefix_mul_by_symbol(&symbol).prefix());
        Depth {
            symbol,
            timestamp: dep.event_time,
            asks: dep
                .asks
                .into_iter()
                .map(|(p, a)| (prefix_mul.price2base(p), prefix_mul.qty2base(a)).into())
                .collect(),
            bids: dep
                .bids
                .into_iter()
                .map(|(p, a)| (prefix_mul.price2base(p), prefix_mul.qty2base(a)).into())
                .collect(),
        }
    }
}

#[test]
fn test_bn_order_book_deserialize() {
    let json = r#"{"lastUpdateId":4434779856223,"E":1713336239788,"T":1713336239766,"bids":[["0.002251","353881"],["0.002250","1224858"],["0.002249","2577780"],["0.002248","3151525"],["0.002247","4904603"],["0.002246","5153037"],["0.002245","7577393"],["0.002244","4585309"],["0.002243","6824191"],["0.002242","8175640"]],"asks":[["0.002252","1030920"],["0.002253","1358538"],["0.002254","4173110"],["0.002255","7443342"],["0.002256","7975991"],["0.002257","6697209"],["0.002258","7099825"],["0.002259","4696988"],["0.002260","6445838"],["0.002261","8048879"]]}"#;
    let _book: BnDepth = sonic_rs::from_str(json).unwrap();
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct SymbolOrderId {
    pub(crate) symbol: BnSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub(crate) order_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub(crate) orig_client_order_id: Option<String>,
}

impl SymbolOrderId {
    pub(crate) fn new(symbol: Symbol, order_id: OrderId) -> Self {
        let (order_id, orig_client_order_id) = match order_id {
            OrderId::Id(order_id) => (Some(order_id), None),
            OrderId::ClientOrderId(client_order_id) => (None, Some(client_order_id)),
        };
        Self {
            symbol: symbol.into(),
            order_id,
            orig_client_order_id,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BnFunding {
    pub(crate) symbol: BnSymbol,
    #[serde(deserialize_with = "de_from_str")]
    mark_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    last_funding_rate: f64,
    // 资金费率周期: ok/gate/bn 8小时, dydx_v3 1小时
    // 币安资金费率有的交易对不是默认的8小时结算周期要去 /fundingInfo 查询结算周期
    // funding_interval_hours: u8,
    pub(crate) next_funding_time: i64,
    // ms
    // time: u64,
}

impl BnFunding {
    pub(crate) fn into_fund(self, adjust: Option<&BnFundingInfo>) -> Funding {
        let def = BnFundingInfo::default();
        let adjust = adjust.unwrap_or(&def);
        Funding {
            symbol: self.symbol.into(),
            funding_rate: self.last_funding_rate,
            funding_interval: Some(adjust.funding_interval_hours),
            next_funding_at: self.next_funding_time,
            max_funding_rate: adjust.adjusted_funding_rate_cap,
            min_funding_rate: adjust.adjusted_funding_rate_floor,
        }
    }

    pub(crate) fn is_future(&self) -> bool {
        self.symbol.is_future()
    }
}

impl From<BnFunding> for MarkPrice {
    fn from(value: BnFunding) -> Self {
        let prefix_mul = f64::from(value.symbol.prefix());
        MarkPrice {
            symbol: value.symbol.into(),
            price: prefix_mul.price2base(value.mark_price),
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BnFundingInfo {
    pub symbol: BnSymbol,
    pub funding_interval_hours: u8,
    // 资金费率上限的系数
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) adjusted_funding_rate_cap: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) adjusted_funding_rate_floor: f64,
}

impl Default for BnFundingInfo {
    fn default() -> Self {
        Self {
            symbol: BnSymbol::default(),
            funding_interval_hours: 8,
            adjusted_funding_rate_cap: 0.02,
            adjusted_funding_rate_floor: -0.02,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct Bracket {
    // pub(crate) symbol: BnSymbol,
    // // #[serde(deserialize_with = "common::serde_aux::de_from_option_str")]
    // pub(crate) notional_coef: Option<f64>,
    pub(crate) brackets: Vec<BracketItem>,
}

impl Bracket {
    pub(crate) fn get_max_leverage(&self) -> Option<u8> {
        self.brackets.iter().map(|b| b.initial_leverage).max()
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BracketItem {
    // pub(crate) notional_cap: f64,
    pub(crate) initial_leverage: u8,
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AdlQuantileItem {
    // pub(crate) symbol: BnSymbol,
    // pub(crate) adl_quantile: AdlQuantile,
}

#[derive(Deserialize)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) struct AdlQuantile {
    // pub(crate) long: u8,
    // pub(crate) short: u8,
    // #[serde(default)]
    // pub(crate) both: u8,
}

#[derive(Deserialize, Debug)]
pub(crate) struct BinanceErr {
    pub(crate) code: i32,
    pub(crate) msg: String,
}

#[derive(Debug, Default, Serialize, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AmendOrderRequest {
    pub(crate) order_id: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    #[serde(rename = "origClientOrderId")]
    pub(crate) cid: Option<String>,
    pub(crate) symbol: BnSymbol,
    pub(crate) side: BnOrderSide,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub(crate) quantity: Option<f64>,
    pub(crate) price: f64,
}

impl From<Order> for AmendOrderRequest {
    fn from(ord: Order) -> Self {
        let symbol: BnSymbol = ord.symbol.into();
        Self {
            order_id: ord.id,
            cid: ord.cid,
            symbol,
            side: ord.side.into(),
            quantity: ord.amount,
            price: ord.price.unwrap_or_default(),
        }
    }
}

#[derive(Serialize)]
pub(crate) struct DepthReq {
    pub(crate) symbol: BnSymbol,
    pub(crate) limit: Option<u32>,
}

impl DepthReq {
    pub(crate) fn new(symbol: BnSymbol, limit: Option<u32>) -> Self {
        Self { symbol, limit }
    }
}

/// 增量更新深度
#[derive(Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub(crate) struct DepthUpdate {
    #[serde(rename = "E")]
    pub(crate) timestamp: i64,
    #[serde(rename = "s")]
    pub(crate) symbol: BnSymbol,
    #[serde(rename = "U")]
    pub(crate) begin_update_id: i64,
    #[serde(rename = "u")]
    pub(crate) end_update_id: i64,
    #[serde(rename = "pu")]
    pub(crate) prev_update_id: i64,
    #[serde(deserialize_with = "deserialize_f64_pairs", rename = "b")]
    pub(crate) bids: Vec<(f64, f64)>,
    #[serde(deserialize_with = "deserialize_f64_pairs", rename = "a")]
    pub(crate) asks: Vec<(f64, f64)>,
}

impl DepthUpdate {
    pub(crate) fn depth(self) -> Depth {
        let mut v = self;
        if v.symbol.prefix() != 1 {
            let prefix_mul = f64::from(v.symbol.prefix());
            v.asks.iter_mut().for_each(|(p, q)| {
                *p = prefix_mul.price2base(*p);
                *q = prefix_mul.qty2base(*q);
            });
            v.bids.iter_mut().for_each(|(p, q)| {
                *p = prefix_mul.price2base(*p);
                *q = prefix_mul.qty2base(*q);
            });
        }
        Depth {
            timestamp: v.timestamp,
            symbol: v.symbol.into(),
            asks: v.asks.into_iter().map(|(p, q)| (p, q).into()).collect(),
            bids: v.bids.into_iter().map(|(p, q)| (p, q).into()).collect(),
        }
    }
}

#[derive(Deserialize)]
pub(crate) struct WsId {
    // pub(crate) id: i64,
}

// {
//   "e": "trade",     // 事件类型
//   "E": 1672515782136,   // 事件时间
//   "s": "BNBBTC",    // 交易对
//   "t": 12345,       // 交易ID
//   "p": "0.001",     // 成交价格
//   "q": "100",       // 成交数量
//   "T": 1672515782136,   // 成交时间
//   "m": true,        // 买方是否是做市方。如true，则此次成交是一个主动卖出单，否则是一个主动买入单。
//   "M": true         // 请忽略该字段
// }
#[derive(Deserialize)]
pub(crate) struct BnTrade {
    // pub(crate) E: i64,
    #[serde(rename = "s")]
    symbol: BnSymbol,
    #[serde(rename = "t")]
    trade_id: u64,
    #[serde(rename = "p")]
    #[serde(deserialize_with = "de_from_str")]
    price: f64,
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "q")]
    quantity: f64,
    #[serde(rename = "T")]
    timestamp: i64,
    #[serde(rename = "m")]
    buyer_is_maker: bool,
}

impl From<BnTrade> for Trade {
    fn from(value: BnTrade) -> Self {
        let prefix_multiplier = value.symbol.prefix() as f64;
        Trade {
            id: value.trade_id.to_string(),
            symbol: value.symbol.into(),
            timestamp: value.timestamp,
            price: prefix_multiplier.price2base(value.price),
            amount: prefix_multiplier.qty2base(value.quantity),
            side: if value.buyer_is_maker {
                OrderSide::Sell
            } else {
                OrderSide::Buy
            },
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct TradeFee {
    // pub(crate)symbol: BnSymbol,
    /// 币安现货叫 maker_commission
    #[serde(alias = "makerCommission")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) maker_commission_rate: f64,
    #[serde(alias = "takerCommission")]
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) taker_commission_rate: f64,
}
impl From<TradeFee> for FeeRate {
    fn from(val: TradeFee) -> Self {
        FeeRate {
            maker: val.maker_commission_rate,
            taker: val.taker_commission_rate,
            ..Default::default()
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct FeeBurn {
    fee_burn: bool,
}

impl From<FeeBurn> for bool {
    fn from(value: FeeBurn) -> Self {
        value.fee_burn
    }
}

impl From<bool> for FeeBurn {
    fn from(fee_burn: bool) -> Self {
        Self { fee_burn }
    }
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub(crate) struct SetFeeBurnRsp {
    code: i64,
    msg: String,
}

impl SetFeeBurnRsp {
    pub(crate) fn is_succ(&self) -> bool {
        self.code == 200
    }
}

#[derive(Serialize, Clone, Copy, Debug)]
#[serde(rename_all = "UPPERCASE")]
pub(crate) enum BnWaleetType {
    /// 现货钱包
    Main,
    /// U本位合约钱包
    Umfuture,
    /// 币本位合约钱包
    Cmfuture,
    /// 杠杆全仓钱包
    Margin,
    /// 杠杆逐仓钱包
    Isolatedmargin,
}

impl From<WalletType> for BnWaleetType {
    fn from(val: WalletType) -> Self {
        match val {
            WalletType::Spot => Self::Main,
            WalletType::UsdtFuture => Self::Umfuture,
            WalletType::CoinFuture => Self::Cmfuture,
            WalletType::Margin => Self::Margin,
            WalletType::IsolatedMargin => Self::Isolatedmargin,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct TransferRequest {
    pub(crate) r#type: String,
    pub(crate) asset: String,
    pub(crate) amount: f64,
    pub(crate) from_symbol: Option<BnWaleetType>,
    pub(crate) to_symbol: Option<BnWaleetType>,
}

impl From<Transfer> for TransferRequest {
    fn from(val: Transfer) -> Self {
        let from = val.from.into();
        let to = val.to.into();
        Self {
            asset: val.asset,
            amount: val.amount,
            from_symbol: Some(from),
            to_symbol: Some(to),
            r#type: format!("{:?}_{:?}", from, to),
        }
    }
}

// 如何正确在本地维护一个orderbook副本
//   https://developers.binance.com/docs/zh-CN/derivatives/usds-margined-futures/websocket-market-streams/How-to-manage-a-local-order-book-correctly
#[derive(Debug)]
pub(crate) struct LocalDepth {
    pub(crate) depth: Depth,
    pub(crate) end_update_id: i64,
}

impl LocalDepth {
    pub(crate) fn new(
        symbol: BnSymbol,
        depth: BnDepth,
        pendings: Vec<DepthUpdate>,
    ) -> Result<Self> {
        let last_update_id = depth.last_update_id;

        let mut pendings = pendings.into_iter();

        while let Some(update) = pendings.next() {
            if update.begin_update_id <= last_update_id && last_update_id <= update.end_update_id {
                let end_update_id = update.end_update_id;
                let mut depth = depth.into_depth(symbol.into());
                depth.update_depth(update.depth());

                let mut local_depth = Self {
                    depth,
                    end_update_id,
                };

                for update in pendings.by_ref() {
                    local_depth.update(update)?;
                }

                return Ok(local_depth);
            }
        }

        Err(qerror!("{symbol} 创建local_depth时，pending找不到第一个包"))
    }

    // 初始化和update的包识别规则有区别
    pub(crate) fn update(&mut self, update: DepthUpdate) -> Result<()> {
        if update.prev_update_id != -1 && update.prev_update_id != self.end_update_id {
            let local = self.end_update_id;
            let need = update.prev_update_id;
            return Err(qerror!("depth 增量更新丢包, 需要: {need}, 本地: {local}"));
        }

        self.end_update_id = update.end_update_id;
        self.depth.update_depth(update.depth());

        Ok(())
    }
}

#[derive(Serialize, Deserialize, Clone, Copy, Debug)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum BnIncomeType {
    /// 收益类型：
    /// TRANSFER 转账,
    /// WELCOME_BONUS 欢迎奖金,
    /// REALIZED_PNL 已实现盈亏,
    /// FUNDING_FEE 资金费用,
    /// COMMISSION 佣金,
    /// INSURANCE_CLEAR 强平,
    /// REFERRAL_KICKBACK 推荐人返佣,
    /// COMMISSION_REBATE 被推荐人返佣,
    /// API_REBATE API佣金回扣,
    /// CONTEST_REWARD 交易大赛奖金,
    /// CROSS_COLLATERAL_TRANSFER cc转账,
    /// OPTIONS_PREMIUM_FEE 期权购置手续费,
    /// OPTIONS_SETTLE_PROFIT 期权行权收益,
    /// INTERNAL_TRANSFER 内部账户，给普通用户划转,
    /// AUTO_EXCHANGE 自动兑换,
    /// DELIVERED_SETTELMENT 下架结算,
    /// COIN_SWAP_DEPOSIT 闪兑转入,
    /// COIN_SWAP_WITHDRAW 闪兑转出,
    /// POSITION_LIMIT_INCREASE_FEE 仓位限制上调费用，
    /// STRATEGY_UMFUTURES_TRANSFER UM策略子账户划转，
    /// FEE_RETURN 策略交易手续费退还，
    /// BFUSD_REWARD BFUSD每日奖励
    Transfer,
    WelcomeBonus,
    RealizedPnl,
    FundingFee,
    Commission,
    InsuranceClear,
    ReferralKickback,
    CommissionRebate,
    ApiRebate,
    ContestReward,
    CrossCollateralTransfer,
    OptionsPremiumFee,
    OptionsSettleProfit,
    InternalTransfer,
    AutoExchange,
    DeliveredSettelment,
    CoinSwapDeposit,
    CoinSwapWithdraw,
    PositionLimitIncreaseFee,
    StrategyUmfuturesTransfer,
    FeeReturn,
    BfusdReward,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct BnIncomeReq {
    income_type: Option<BnIncomeType>,
    symbol: Option<BnSymbol>,
    start_time: Option<i64>,
    end_time: Option<i64>,
    page: Option<i32>,
    limit: Option<i32>,
}

impl BnIncomeReq {
    pub(crate) fn funding_fee(
        symbol: Symbol,
        start_time: Option<i64>,
        end_time: Option<i64>,
    ) -> Self {
        Self {
            symbol: Some(symbol.into()),
            income_type: Some(BnIncomeType::FundingFee),
            start_time,
            end_time,
            page: None,
            limit: None,
        }
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct BnIncome {
    symbol: Option<BnSymbol>,
    #[serde(deserialize_with = "de_from_str")]
    income: f64,
    time: i64,
    // income_type: BnIncomeType,
    // asset: String,
    // info: String,
    // tran_id: i64,
    // trade_id: String,
}

impl From<BnIncome> for FundingFee {
    fn from(value: BnIncome) -> Self {
        Self {
            symbol: value.symbol.map(|s| s.into()).unwrap_or_default(),
            timestamp: value.time,
            funding_fee: value.income,
        }
    }
}

#[derive(Deserialize, Serialize, Debug, Clone, Copy, PartialEq, Eq)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub(crate) enum BnContractType {
    Perpetual,      // 永续合约
    CurrentQuarter, // 当季交割合约
    NextQuarter,    // 次季交割合约
    #[serde(rename = "")]
    Other,
}

impl BnContractType {
    pub(crate) fn from_symbol(symbol: &Symbol) -> Result<BnContractType> {
        let expiry = match &symbol.contract_type {
            ContractType::Delivery { expiry, .. } => expiry,
            ContractType::Normal => return Ok(BnContractType::Perpetual),
            _ => {
                let err = format!("symbol {symbol} not supported");
                return Err(Error::parameter_error(err));
            }
        };

        if expiry.len() != 6 {
            let err = format!("{symbol} 交割日期格式不对");
            return Err(Error::parameter_error(err));
        }

        let year = expiry[..2].parse::<i32>()?;
        let month = expiry[2..].parse::<u32>()?;

        let dt = chrono::Utc::now();
        if year != dt.year() % 100 {
            let err = format!("{symbol} 年份不对");
            return Err(Error::parameter_error(err));
        }

        let contract_type = match month / 4 == dt.month() / 4 {
            true => BnContractType::CurrentQuarter,
            false => BnContractType::NextQuarter,
        };
        Ok(contract_type)
    }
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub(crate) struct KlineReq {
    pub(crate) pair: BnSymbol,
    interval: KlineInterval,
    #[serde(skip_serializing_if = "Option::is_none")]
    contract_type: Option<BnContractType>,
    #[serde(skip_serializing_if = "Option::is_none")]
    start_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    end_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    limit: Option<u32>,
}

impl KlineReq {
    pub(crate) fn swap_from_params(params: GetKlineParams) -> KlineReq {
        KlineReq {
            pair: params.symbol.into(),
            interval: params.interval,
            start_time: params.start_time,
            end_time: params.end_time,
            limit: Some(params.limit.unwrap_or(1000)),
            contract_type: Some(BnContractType::Perpetual),
        }
    }

    pub(crate) fn future_from_params(params: GetKlineParams) -> Result<KlineReq> {
        let symbol = params.symbol.clone();
        let contract_type = BnContractType::from_symbol(&symbol)?;
        let pair = Symbol::new_with_quote(&symbol.base, symbol.quote).into();

        Ok(KlineReq {
            pair,
            interval: params.interval,
            start_time: params.start_time,
            end_time: params.end_time,
            limit: Some(params.limit.unwrap_or(1000)),
            contract_type: Some(contract_type),
        })
    }
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub(crate) struct BnCandle {
    open_timestamp: i64, // 开盘时间
    #[serde(deserialize_with = "de_from_str")]
    open: f64, // 开盘价
    #[serde(deserialize_with = "de_from_str")]
    high: f64, // 最高价
    #[serde(deserialize_with = "de_from_str")]
    low: f64, // 最低价
    #[serde(deserialize_with = "de_from_str")]
    close: f64, // 收盘价(当前K线未结束的即为最新价)
    #[serde(deserialize_with = "de_from_str")]
    volume: f64, // 成交量
    close_time: i64,     // 收盘时间
    #[serde(deserialize_with = "de_from_str")]
    quote_volume: f64, // 成交额
    trades: u32,         // 成交笔数
    #[serde(deserialize_with = "de_from_str")]
    taker_buy_volume: f64, // 主动买入成交量
    #[serde(deserialize_with = "de_from_str")]
    taker_buy_quote_volume: f64, // 主动买入成交额
    _ignore: String,     // 忽略字段
}

impl BnCandle {
    pub(crate) fn into_base(self, prefix: f64) -> Candle {
        let confirm = time_ms() - self.close_time > 1000;
        Candle::new(
            self.open_timestamp,
            prefix.price2base(self.open),
            prefix.price2base(self.high),
            prefix.price2base(self.low),
            prefix.price2base(self.close),
            prefix.qty2base(self.volume),
            self.quote_volume,
            Some(self.trades),
            Some(prefix.qty2base(self.taker_buy_volume)),
            Some(self.taker_buy_quote_volume),
            confirm,
        )
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AccountRsp {
    #[serde(deserialize_with = "de_from_str")]
    total_maint_margin: f64, // 维持保证金总额, 仅计算usdt资产
    #[serde(deserialize_with = "de_from_str")]
    total_cross_wallet_balance: f64, // 全仓账户余额, 仅计算usdt资产
    #[serde(deserialize_with = "de_from_str")]
    available_balance: f64, // 可用余额, 仅计算usdt资产

                            // #[serde(deserialize_with = "de_from_str")]
                            // total_initial_margin: f64, // 当前所需起始保证金总额(存在逐仓请忽略), 仅计算usdt资产positions), only for USDT asset
                            // #[serde(deserialize_with = "de_from_str")]
                            // total_wallet_balance: f64, // 账户总余额, 仅计算usdt资产
                            // #[serde(deserialize_with = "de_from_str")]
                            // total_unrealized_profit: f64, // 持仓未实现盈亏总额, 仅计算usdt资产
                            // #[serde(deserialize_with = "de_from_str")]
                            // total_margin_balance: f64, // 保证金总余额, 仅计算usdt资产
                            // #[serde(deserialize_with = "de_from_str")]
                            // total_position_initial_margin: f64, // 持仓所需起始保证金(基于最新标记价格), 仅计算usdt资产
                            // #[serde(deserialize_with = "de_from_str")]
                            // total_open_order_initial_margin: f64, // 当前挂单所需起始保证金(基于最新标记价格), 仅计算usdt资产
                            // #[serde(deserialize_with = "de_from_str")]
                            // total_cross_un_pnl: f64, // 全仓持仓未实现盈亏总额, 仅计算usdt资产
                            // #[serde(deserialize_with = "de_from_str")]
                            // max_withdraw_amount: f64, // 最大可转出余额, 仅计算usdt资产
                            // "assets": [
                            //     {
                            //         "asset": "USDT",			            // 资产
                            //         "walletBalance": "23.72469206",         // 余额
                            //         "unrealizedProfit": "0.00000000",       // 未实现盈亏
                            //         "marginBalance": "23.72469206",         // 保证金余额
                            //         "maintMargin": "0.00000000",	        // 维持保证金
                            //         "initialMargin": "0.00000000",          // 当前所需起始保证金
                            //         "positionInitialMargin": "0.00000000",  // 持仓所需起始保证金(基于最新标记价格)
                            //         "openOrderInitialMargin": "0.00000000", // 当前挂单所需起始保证金(基于最新标记价格)
                            //         "crossWalletBalance": "23.72469206",    // 全仓账户余额
                            //         "crossUnPnl": "0.00000000"              // 全仓持仓未实现盈亏
                            //         "availableBalance": "23.72469206",      // 可用余额
                            //         "maxWithdrawAmount": "23.72469206",     // 最大可转出余额
                            //         "updateTime": 1625474304765             // 更新时间
                            //     },
                            //     {
                            //         "asset": "USDC",
                            //         "walletBalance": "103.12345678",
                            //         "unrealizedProfit": "0.00000000",
                            //         "marginBalance": "103.12345678",
                            //         "maintMargin": "0.00000000",
                            //         "initialMargin": "0.00000000",
                            //         "positionInitialMargin": "0.00000000",
                            //         "openOrderInitialMargin": "0.00000000",
                            //         "crossWalletBalance": "103.12345678",
                            //         "crossUnPnl": "0.00000000"
                            //         "availableBalance": "126.72469206",
                            //         "maxWithdrawAmount": "103.12345678",
                            //         "updateTime": 1625474304765
                            //     }
                            // ],
                            // "positions": [  // 仅有仓位或挂单的交易对会被返回
                            //                 // 根据用户持仓模式展示持仓方向，即单向模式下只返回BOTH持仓情况，双向模式下只返回 LONG 和 SHORT 持仓情况
                            //      {
                            //        "symbol": "BTCUSDT",               // 交易对
                            //        "positionSide": "BOTH",            // 持仓方向
                            //        "positionAmt": "1.000",            // 持仓数量
                            //        "unrealizedProfit": "0.00000000",  // 持仓未实现盈亏
                            //        "isolatedMargin": "0.00000000",
                            //        "notional": "0",
                            //        "isolatedWallet": "0",
                            //        "initialMargin": "0",              // 持仓所需起始保证金(基于最新标记价格)
                            //        "maintMargin": "0",                // 当前杠杆下用户可用的最大名义价值
                            //        "updateTime": 0                    // 更新时间
                            //     }
                            // ]
}

impl From<AccountRsp> for AccountInfo {
    fn from(value: AccountRsp) -> Self {
        Self {
            total_mmr: value.total_maint_margin / value.total_cross_wallet_balance,
            total_equity: value.total_cross_wallet_balance,
            total_available: value.available_balance,
        }
    }
}

#[derive(Serialize, Debug)]
pub(crate) struct HistoryFundingReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    symbol: Option<BnSymbol>,
    #[serde(skip_serializing_if = "Option::is_none")]
    start_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    end_time: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    limit: Option<u32>,
}

impl From<GetFundingRateHistoryParams> for HistoryFundingReq {
    fn from(params: GetFundingRateHistoryParams) -> Self {
        Self {
            symbol: params.symbol.map(|x| x.into()),
            start_time: params.since_secs.map(|x| x * 1000),
            end_time: None,
            limit: Some(params.limit),
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub(crate) struct HistoryFunding {
    symbol: BnSymbol, // 交易对
    #[serde(deserialize_with = "de_from_str")]
    funding_rate: f64, // 资金费率
    funding_time: i64, // 资金费时间
                      // #[serde(deserialize_with = "de_from_str")]
                      // mark_price: f64, // 资金费对应标记价格
}

impl From<HistoryFunding> for FundingHistory {
    fn from(value: HistoryFunding) -> Self {
        Self {
            symbol: value.symbol.into(),
            funding_rate: value.funding_rate,
            funding_time: value.funding_time,
        }
    }
}

///////////////////////// ws api /////////////////////////

#[derive(Serialize, Debug)]
pub struct WsApiReq {
    pub(crate) id: u64,
    pub(crate) method: WsApiMethod,
    pub(crate) params: serde_json::Value,
}

impl WsApiReq {
    fn sign(
        id: u64,
        api_key: String,
        method: WsApiMethod,
        secret: &str,
        params: impl Serialize,
    ) -> Result<Self> {
        let mut params = serde_json::to_value(params)?;
        params["timestamp"] = time_ms().into();
        if !is_ed25519_secret(secret) || method == WsApiMethod::Logon {
            params["apiKey"] = api_key.into();
            let sign = sign_ws_api_params(secret, &params)?;
            params
                .as_object_mut()
                .ok_or(qerror!("params is not an object"))?
                .insert("signature".to_string(), serde_json::Value::String(sign));
        }

        Ok(Self { id, method, params })
    }

    pub(crate) fn logon(id: u64, api_key: String, secret: &str) -> Result<Self> {
        let params = serde_json::to_value(WsApiLogonParams)?;
        Self::sign(id, api_key, WsApiMethod::Logon, secret, params)
    }

    pub(crate) fn post_order(
        id: u64,
        api_key: String,
        secret: &str,
        cmd: PlaceOrderCmd,
    ) -> Result<Self> {
        let params = WsApiPostOrderParams::new(cmd);
        Self::sign(id, api_key, WsApiMethod::Post, secret, params)
    }

    pub(crate) fn amend_order(
        id: u64,
        api_key: String,
        secret: &str,
        order: Order,
    ) -> Result<Self> {
        let params = WsApiAmendOrderParams::new(order)?;
        Self::sign(id, api_key, WsApiMethod::Amend, secret, params)
    }

    pub(crate) fn cancel_order(
        req_id: u64,
        api_key: String,
        secret: &str,
        symbol: Symbol,
        order_id: OrderId,
    ) -> Result<Self> {
        let params = WsApiCancelOrderParams::new(symbol, order_id);
        Self::sign(req_id, api_key, WsApiMethod::Cancel, secret, params)
    }
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct WsApiRsp {
    pub(crate) id: u64,
    pub(crate) status: i64,
    pub(crate) error: Option<WsApiError>,
    pub(crate) result: Option<serde_json::Value>,
}

impl WsApiRsp {
    pub(crate) fn id(&self) -> u64 {
        self.id
    }

    pub(crate) fn into_logon_rsp(self) -> Result<()> {
        if let Some(error) = self.error.as_ref() {
            return Err(qerror!("logon error: {error:?}"));
        }
        Ok(())
    }

    pub(crate) fn into_post_order_rsp(self) -> Result<String> {
        if let Some(error) = self.error.as_ref() {
            return Err(qerror!("post order error: {error:?}"));
        }

        let rsp: PostOrderRsp = match self.result {
            Some(result) => serde_json::from_value(result)?,
            None => return Err(qerror!("result is null")),
        };
        Ok(rsp.order_id.to_string())
    }

    pub(crate) fn into_cancel_order_rsp(self) -> Result<String> {
        if let Some(error) = self.error.as_ref() {
            return Err(qerror!("post order error: {error:?}"));
        }

        let rsp: CancelOrderRsp = match self.result {
            Some(result) => serde_json::from_value(result)?,
            None => return Err(qerror!("result is null")),
        };
        Ok(rsp.order_id.to_string())
    }

    pub(crate) fn into_amend_order_rsp(self) -> Result<String> {
        if let Some(error) = self.error.as_ref() {
            return Err(qerror!("post order error: {error:?}"));
        }

        let rsp: AmendOrderRsp = match self.result {
            Some(result) => serde_json::from_value(result)?,
            None => return Err(qerror!("result is null")),
        };
        Ok(rsp.order_id.to_string())
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct WsApiError {
    code: Option<i64>,
    msg: String,
}

#[derive(Serialize, Debug, PartialEq, Eq)]
pub(crate) enum WsApiMethod {
    #[serde(rename = "session.logon")]
    Logon,
    #[serde(rename = "order.place")]
    Post,
    #[serde(rename = "order.cancel")]
    Cancel,
    #[serde(rename = "order.modify")]
    Amend,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct WsApiLogonParams;

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct WsApiPostOrderParams {
    symbol: BnSymbol,
    side: BnOrderSide,
    r#type: BnOrderType,
    #[serde(skip_serializing_if = "Option::is_none")]
    time_in_force: Option<BnTimeInForce>,
    #[serde(skip_serializing_if = "Option::is_none")]
    price: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    quantity: Option<String>,
    new_client_order_id: Option<String>,

    /// true, false; 非双开模式下默认false 双开模式下不接受此参数。
    #[serde(rename = "reduceOnly")]
    #[serde(skip_serializing_if = "Option::is_none")]
    reduce_only: Option<bool>,
    position_side: Option<BnPosSide>,
}

impl WsApiPostOrderParams {
    pub(crate) fn new(cmd: PlaceOrderCmd) -> Self {
        let order: Order = cmd.order;
        let reduce_only = Some(order.reduce_only());
        let (time_in_force, price) = match order.order_type {
            OrderType::Limit => (
                Some(order.time_in_force.into()),
                order.price.map(|p| p.to_string()),
            ),
            OrderType::Market => (None, None),
        };
        let position_side = match order.pos_side {
            Some(PosSide::Long) => Some(BnPosSide::Long),
            Some(PosSide::Short) => Some(BnPosSide::Short),
            _ => None,
        };
        Self {
            symbol: order.symbol.into(),
            side: order.side.into(),
            r#type: order.order_type.into(),
            time_in_force,
            price,
            quantity: order.amount.map(|a| a.to_string()),
            new_client_order_id: order.cid,
            reduce_only,
            position_side,
        }
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct WsApiCancelOrderParams {
    symbol: BnSymbol,
    #[serde(skip_serializing_if = "Option::is_none")]
    order_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    orig_client_order_id: Option<String>,
}

impl WsApiCancelOrderParams {
    pub(crate) fn new(symbol: Symbol, order_id: OrderId) -> Self {
        let (order_id, client_order_id) = match order_id {
            OrderId::Id(oid) => (Some(oid), None),
            OrderId::ClientOrderId(cid) => (None, Some(cid)),
        };
        Self {
            symbol: symbol.into(),
            order_id,
            orig_client_order_id: client_order_id,
        }
    }
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct WsApiAmendOrderParams {
    symbol: BnSymbol,
    order_id: Option<String>,
    orig_client_order_id: Option<String>,
    price: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    quantity: Option<String>,
    side: BnOrderSide,
}

impl WsApiAmendOrderParams {
    pub(crate) fn new(order: Order) -> Result<Self> {
        let price = order.price.ok_or(qerror!("price is null"))?;
        Ok(Self {
            symbol: order.symbol.into(),
            order_id: Some(order.id),
            orig_client_order_id: order.cid,
            price: price.to_string(),
            quantity: order.amount.map(|a| a.to_string()),
            side: order.side.into(),
        })
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
struct PostOrderRsp {
    order_id: u64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
struct CancelOrderRsp {
    order_id: u64,
    // symbol: BnSymbol,
    // orig_client_order_id: String,
    // order_list_id: i64,
    // client_order_id: String,
    // transact_time: i64,
    // price: String,
    // orig_qty: String,
    // executed_qty: String,
    // cummulative_quote_qty: String,
    // status: String,
    // time_in_force: String,
    // r#type: String,
    // side: String,
    // stop_price: String,
    // trailing_delta: i64,
    // iceberg_qty: String,
    // strategy_id: i64,
    // strategy_type: i64,
    // self_trade_prevention_mode: String,
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
struct AmendOrderRsp {
    order_id: u64,
}

#[derive(Deserialize, Debug)]
struct WsCandle {
    #[serde(rename = "t")]
    start_time: i64, // 这根K线的起始时间
    #[serde(rename = "i")]
    interval: KlineInterval, // K线间隔

    // #[serde(rename = "T")]
    // end_time: i64, // 这根K线的结束时间
    // #[serde(rename = "f")]
    // first_update_id: i64, // 这根K线期间第一笔更新ID
    // #[serde(rename = "L")]
    // last_update_id: i64, // 这根K线期间末一笔更新ID
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "o")]
    open: f64, // 这根K线期间第一笔成交价
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "c")]
    close: f64, // 这根K线期间末一笔成交价
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "h")]
    high: f64, // 这根K线期间最高成交价
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "l")]
    low: f64, // 这根K线期间最低成交价
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "v")]
    volume: f64, // 这根K线期间成交量
    #[serde(rename = "n")]
    trades: u32, // 这根K线期间成交笔数
    #[serde(rename = "x")]
    is_closed: bool, // 这根K线是否完结(是否已经开始下一根K线)
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "q")]
    quote_volume: f64, // 这根K线期间成交额
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "V")]
    taker_buy_volume: f64, // 主动买入的成交量
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "Q")]
    taker_buy_quote_volume: f64, // 主动买入的成交额
                                 // #[serde(deserialize_with = "de_from_str")]
                                 // #[serde(rename = "B")]
                                 // taker_buy_base_volume: f64, // 忽略此参数
}

impl WsCandle {
    pub(crate) fn into_base(self, prefix: f64) -> Candle {
        Candle::new(
            self.start_time,
            prefix.price2base(self.open),
            prefix.price2base(self.high),
            prefix.price2base(self.low),
            prefix.price2base(self.close),
            prefix.qty2base(self.volume),
            self.quote_volume,
            Some(self.trades),
            Some(prefix.qty2base(self.taker_buy_volume)),
            Some(self.taker_buy_quote_volume),
            self.is_closed,
        )
    }
}

#[derive(Deserialize, Debug)]
pub(crate) struct WsKline {
    // #[serde(rename = "e")]
    // event: String, // 事件类型 (continuous_kline)
    // #[serde(rename = "E")]
    // event_time: i64, // 事件时间
    #[serde(rename = "ps")]
    symbol: BnSymbol, // 标的交易对
    // #[serde(rename = "ct")]
    // contract_type: BnContractType, // 合约类型
    #[serde(rename = "k")]
    candle: WsCandle,
}

impl From<WsKline> for Kline {
    fn from(value: WsKline) -> Self {
        let interval = value.candle.interval.clone();
        let prefix = value.symbol.prefix_f64();
        let candle = value.candle.into_base(prefix);
        Kline::new(value.symbol.into(), interval, vec![candle])
    }
}

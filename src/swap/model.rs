use std::fmt;

use once_cell::sync::OnceCell;
use quant_common::base::{
    <PERSON>lance, BatchOrderRsp, BboTicker, Chain, DepositAddress, Depth, FailOrder, FeeRate, Funding,
    FundingFee, InsState, Instrument, InternalTransferParams, MarginMode, MarkPrice, MaxPosition,
    Order, OrderId, OrderParams, OrderSide, OrderSideLower, OrderStatus, OrderType, PosSide,
    PosSideLower, Position, PositionValue, QuoteCcy, SuccessOrder, Symbol, Ticker, TimeInForce,
    Trade, TriggerAction, TriggerPrice, WithDrawlParams, WithdrawalAddr,
};
use quant_common::{de_empty_str_to_default, de_from_str, deserialize_f64_pairs};
use quant_common::{qerror, Result};
use rustc_hash::FxHashMap;
use serde::ser::{SerializeMap, SerializeStruct};
use serde::{Deserialize, Deserializer, Serialize, Serializer};
use sonic_rs::Value;

pub const API_PREFIX: &str = "/api";
pub const EXCHANGE: &str = "OkxSwap";
pub const HEADER_CONTENT_TYPE: &str = "Content-Type";
pub const HEADER_JSON: &str = "application/json";
pub const HEADER_KEY: &str = "OK-ACCESS-KEY";
pub const HEADER_SIGN: &str = "OK-ACCESS-SIGN";
pub const HEADER_TIMESTAMP: &str = "OK-ACCESS-TIMESTAMP";
pub const HEADER_PASSPHRASE: &str = "OK-ACCESS-PASSPHRASE";
pub const HEADER_TESTNET: &str = "x-simulated-trading";
pub const PATH_ACCOUNT_CONFIG: &str = "account/config";
pub const PATH_ACCOUNT_BALANCE: &str = "account/balance";
pub const PATH_DEPTH: &str = "market/books";
pub const PATH_DEPOSIT_ADDRESS: &str = "asset/deposit-address";
pub const PATH_FUNDING_RATE: &str = "public/funding-rate";
pub const PATH_INSTRUMENTS: &str = "public/instruments";
pub const PATH_ORDERS: &str = "trade/orders-pending";
pub const PATH_ORDERS_HISTORY: &str = "trade/orders-history-archive";
pub const PATH_POST_ORDER: &str = "trade/order";
pub const PATH_POST_ORDER_BATCH: &str = "trade/batch-orders";
pub const PATH_POST_ORDER_CANCEL: &str = "trade/cancel-order";
pub const PATH_POST_ORDER_CANCEL_BATCH: &str = "trade/cancel-batch-orders";
pub const PATH_POST_ORDER_AMEND: &str = "trade/amend-order";
pub const PATH_POSITIONS: &str = "account/positions";
pub const PATH_MAX_LEVERAGE: &str = "account/adjust-leverage-info";
pub const PATH_MAX_POSITION: &str = "account/max-size";
pub const PATH_SET_LEVERAGE: &str = "account/set-leverage";
pub const PATH_SET_POSITION_MODE: &str = "account/set-position-mode";
pub const PATH_TICKER: &str = "market/ticker";
pub const PATH_TICKERS: &str = "market/tickers";
pub const PATH_MARK_PRICE: &str = "public/mark-price";
pub const PATH_TRADE_FEE: &str = "account/trade-fee";
pub const PATH_TRANSFER: &str = "asset/transfer";
pub const PATH_WITHDRAWAL: &str = "asset/withdrawal";
pub const REST_BASE_URL: &str = "https://www.okx.com";
pub const REST_AWS_BASE_URL: &str = "https://aws.okx.com";
pub const VERSION: &str = "/v5/";
// pub const WS_URL_PUBLIC: &str = "wss://ws.okx.com:8443/ws/v5/public";
// pub const WS_URL_PRIVATE: &str = "wss://ws.okx.com:8443/ws/v5/private";
// pub const WS_URL_BUSINESS: &str = "wss://ws.okx.com:8443/ws/v5/business";
// pub const WS_AWS_URL_PUBLIC: &str = "wss://wsaws.okx.com:8443/ws/v5/public";
// pub const WS_AWS_URL_PRIVATE: &str = "wss://wsaws.okx.com:8443/ws/v5/private";
// pub const WS_AWS_URL_BUSINESS: &str = "wss://wsaws.okx.com:8443/ws/v5/business";
// pub const WS_TEST_URL_PUBLIC: &str = "wss://wspap.okx.com:8443/ws/v5/public";
// pub const WS_TEST_URL_PRIVATE: &str = "wss://wspap.okx.com:8443/ws/v5/private";
// pub const WS_TEST_URL_BUSINESS: &str = "wss://wspap.okx.com:8443/ws/v5/business";

pub const WS_AWS_URL: &str = "wsaws.okx.com:8443";
pub const WS_TEST_URL: &str = "wspap.okx.com:8443";
pub const WS_URL: &str = "ws.okx.com:8443";

pub static MULTIPLIER_CACHE: OnceCell<FxHashMap<Symbol, f64>> = OnceCell::new();

#[derive(Clone, PartialEq, Default, Eq, Hash)]
pub struct OkxSymbol(pub Symbol);

impl OkxSymbol {
    #[inline]
    pub(crate) fn multiplier(&self) -> f64 {
        self.multiplier_f64()
    }
    #[inline]
    pub(crate) fn multiplier_f64(&self) -> f64 {
        // contracts 中不存在的合约都是已经下线的
        MULTIPLIER_CACHE
            .get()
            .and_then(|cache| cache.get(&self.0))
            .copied()
            .unwrap_or(1.)
    }
}

impl From<Symbol> for OkxSymbol {
    fn from(symbol: Symbol) -> Self {
        Self(symbol)
    }
}

impl From<OkxSymbol> for Symbol {
    fn from(symbol: OkxSymbol) -> Self {
        symbol.0
    }
}

impl std::fmt::Debug for OkxSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}-{}", self.0.base, self.0.quote)
    }
}

impl<'de> Deserialize<'de> for OkxSymbol {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        let parts: Vec<&str> = s.split('-').collect();
        if parts.len() < 2 {
            return Err(serde::de::Error::custom("Invalid format"));
        }

        let base = parts[0].to_string();
        let quote = parts[1];
        let quote = serde_plain::from_str(quote).unwrap_or(QuoteCcy::OTHER);

        Ok(OkxSymbol(Symbol { base, quote }))
    }
}

impl Serialize for OkxSymbol {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        format!("{}-{}", self.0.base, self.0.quote).serialize(serializer)
    }
}

#[derive(Debug, Default, Deserialize, Serialize)]
#[serde(rename_all = "UPPERCASE")]
pub enum InstType {
    // 币币
    Spot,
    // 永续合约
    #[default]
    Swap,
    // 交割合约
    Futures,
    // 期权
    Option,
    // 杠杆
    Margin,
}

impl fmt::Display for InstType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            InstType::Spot => write!(f, "SPOT"),
            InstType::Swap => write!(f, "SWAP"),
            InstType::Futures => write!(f, "FUTURES"),
            InstType::Option => write!(f, "OPTION"),
            InstType::Margin => write!(f, "MARGIN"),
        }
    }
}

#[derive(Default)]
pub struct OkxTickerReq {
    pub symbol: OkxSymbol,
    pub inst_type: InstType,
}

impl OkxTickerReq {
    pub fn set_symbol(&mut self, symbol: Symbol) {
        self.symbol = symbol.into();
    }
}

impl Serialize for OkxTickerReq {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        //  "instId": "BTC-USDT-SWAP",
        let inst_id = format!(
            "{}-{}-{}",
            self.symbol.0.base, self.symbol.0.quote, self.inst_type
        );
        let mut map = serializer.serialize_map(Some(1))?;
        map.serialize_entry("instId", &inst_id)?;
        map.end()
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTickersReq {
    pub inst_type: InstType,
    // 标的指数，仅适用于交割/永续/期权，期权必填
    #[serde(skip_serializing_if = "Option::is_none")]
    pub uly: Option<String>,
    // 交易品种，仅适用于交割/永续/期权
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inst_family: Option<String>,
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTickerResp {
    pub inst_type: InstType,
    pub inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    pub last: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub last_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub ask_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub ask_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub bid_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub bid_sz: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub open_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub high_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub low_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub vol_ccy_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub vol_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub sod_utc_0: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub sod_utc_8: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub ts: i64,
}

impl From<OkxTickerResp> for Ticker {
    fn from(value: OkxTickerResp) -> Self {
        let symbol = value.inst_id;
        let multiplier = symbol.multiplier();
        Ticker {
            symbol: symbol.into(),
            timestamp: value.ts,
            high: value.high_24h,
            low: value.low_24h,
            open: value.open_24h,
            close: value.last,
            volume: value.vol_24h / multiplier,
            quote_volume: value.vol_ccy_24h,
            change: value.last - value.open_24h,
            change_percent: (value.last - value.open_24h) / value.open_24h,
        }
    }
}

impl From<OkxTickerResp> for BboTicker {
    fn from(value: OkxTickerResp) -> Self {
        let symbol = value.inst_id;
        let multiplier = symbol.multiplier();
        BboTicker {
            symbol: symbol.into(),
            bid_price: value.bid_px,
            bid_qty: value.bid_sz / multiplier,
            ask_price: value.ask_px,
            ask_qty: value.ask_sz / multiplier,
            timestamp: value.ts,
        }
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxMarkPriceReq {
    inst_type: InstType,
    #[serde(skip_serializing_if = "Option::is_none")]
    inst_id: Option<String>,
}

impl OkxMarkPriceReq {
    pub fn new(inst_id: Option<Symbol>) -> Self {
        Self {
            inst_type: InstType::Swap,
            inst_id: inst_id.map(|s| format!("{}-{}-SWAP", s.base, s.quote)),
        }
    }
}

#[derive(Default, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxMarkPriceResp {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    mark_px: f64,
}

impl From<OkxMarkPriceResp> for MarkPrice {
    fn from(value: OkxMarkPriceResp) -> Self {
        MarkPrice {
            symbol: value.inst_id.into(),
            price: value.mark_px,
        }
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxDepthReq {
    pub inst_id: OkxSymbol,
    // 深度档位数量，最大值可传400，即买卖深度共800条
    // 不填写此参数，默认返回1档深度数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sz: Option<String>,
}

// impl Serialize for OkxDepthReq {
//     fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
//     where
//         S: Serializer,
//     {
//         //  "instId": "BTC-USDT",
//         let inst_id = format!("{}-{}", self.inst_id.0.base, self.inst_id.0.quote);
//         let mut map = serializer.serialize_map(Some(1))?;
//         map.serialize_entry("instId", &inst_id)?;
//         if let Some(sz) = &self.sz {
//             map.serialize_entry("sz", sz)?;
//         }
//         map.end()
//     }
// }

impl OkxDepthReq {
    pub fn new(symbol: Symbol, sz: Option<u32>) -> Self {
        let mut req = Self::default();
        req.set_symbol(symbol);
        if let Some(sz) = sz {
            req.set_sz(sz);
        }
        req
    }

    pub fn set_symbol(&mut self, symbol: Symbol) {
        self.inst_id = symbol.into();
    }

    pub fn set_sz(&mut self, sz: u32) {
        if sz > 400 {
            self.sz = Some("400".to_string());
        }
        self.sz = Some(sz.to_string());
    }
}

#[derive(Debug, Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxInstrumentReq {
    // 产品类型
    // SPOT：币币
    // MARGIN：币币杠杆
    // SWAP：永续合约
    // FUTURES：交割合约
    // OPTION：期权
    pub inst_type: InstType,
    // 标的指数，仅适用于交割/永续/期权，期权必填
    #[serde(skip_serializing_if = "Option::is_none")]
    pub uly: Option<String>,
    // 交易品种，仅适用于交割/永续/期权
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inst_family: Option<String>,
    // 产品ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub inst_id: Option<String>,
}

impl OkxInstrumentReq {
    pub fn set_symbol(&mut self, symbol: Symbol) {
        self.inst_id = Some(format!(
            "{}-{}-{}",
            symbol.base, symbol.quote, self.inst_type
        ));
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxInstrumentResp {
    pub inst_type: InstType,
    pub inst_id: OkxSymbol,
    pub state: String,
    pub settle_ccy: String,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub ct_val: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub tick_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub lot_sz: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub min_sz: f64,
}

impl From<OkxInstrumentResp> for Instrument {
    fn from(value: OkxInstrumentResp) -> Self {
        let amount_precision = -if matches!(value.inst_type, InstType::Swap | InstType::Futures) {
            value.min_sz
        } else {
            value.lot_sz
        }
        .log10() as i32;
        let state = match value.state.as_str() {
            "live" => InsState::Normal,
            "suspended" => InsState::Maintenance,
            _ => InsState::Close,
        };

        let mut inst = Instrument {
            symbol: value.inst_id.into(),
            state,
            price_tick: value.tick_sz,
            amount_tick: value.lot_sz,
            price_precision: -value.tick_sz.log10() as i32,
            amount_precision,
            min_qty: value.min_sz,
            min_notional: 0.,
            price_multiplier: 1.,
            amount_multiplier: 1.,
        };
        if matches!(
            value.inst_type,
            InstType::Swap | InstType::Futures | InstType::Option
        ) {
            inst.amount_multiplier = 1. / value.ct_val;
        }
        inst
    }
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxFundingRateReq {
    pub inst_id: String,
}

impl Default for OkxFundingRateReq {
    fn default() -> Self {
        Self {
            inst_id: "ANY".to_string(),
        }
    }
}

impl OkxFundingRateReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            inst_id: format!("{}-{}-SWAP", symbol.base, symbol.quote),
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxFundingRateResp {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    funding_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    funding_time: i64,
    #[serde(deserialize_with = "de_from_str")]
    next_funding_time: i64,
}

impl From<OkxFundingRateResp> for Funding {
    fn from(value: OkxFundingRateResp) -> Self {
        // 计算资金费率时间间隔，毫秒级转小时
        let funding_interval = (value.next_funding_time - value.funding_time) / 1000 / 60 / 60;
        Self {
            symbol: value.inst_id.into(),
            funding_rate: value.funding_rate,
            next_funding_at: value.funding_time,
            funding_interval: Some(funding_interval as u8),
        }
    }
}
#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxPostOrderReq {
    inst_id: OkxSymbolSwap,
    td_mode: OkxTdMode,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
    side: OrderSideLower,
    #[serde(skip_serializing_if = "Option::is_none")]
    pos_side: Option<PosSideLower>,
    ord_type: OkxOrderType,
    sz: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    attach_algo_ords: Option<Vec<OkxPostAttach>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    reduce_only: Option<bool>,
}

impl OkxPostOrderReq {
    pub fn new(order: Order, order_params: OrderParams) -> Self {
        let order_type = match (order.order_type, order.time_in_force) {
            (OrderType::Market, _) => OkxOrderType::Market,
            (OrderType::Limit, TimeInForce::GTC) => OkxOrderType::Limit,
            (OrderType::Limit, TimeInForce::FOK) => OkxOrderType::Fok,
            (OrderType::Limit, TimeInForce::IOC) => OkxOrderType::Ioc,
            (OrderType::Limit, TimeInForce::PostOnly) => OkxOrderType::PostOnly,
        };

        let mut req = OkxPostOrderReq {
            inst_id: order.symbol.into(),
            td_mode: OkxTdMode::default(),
            cl_ord_id: order.cid,
            side: order.side.into(),
            pos_side: None,
            ord_type: order_type,
            sz: order.amount.unwrap().to_string(),
            px: order.price.map(|p| p.to_string()),
            attach_algo_ords: None,
            reduce_only: None,
        };
        if let Some(pos_side) = order.pos_side {
            if (pos_side == PosSide::Long && order.side == OrderSide::Sell)
                || (pos_side == PosSide::Short && order.side == OrderSide::Buy)
                    && !order_params.is_dual_side
            {
                req.reduce_only = Some(true);
            }
            if order_params.is_dual_side {
                req.pos_side = Some(pos_side.into());
            }
        }
        if let Some(margin_mode) = order_params.margin_mode.clone() {
            req.td_mode = margin_mode.into();
        }

        if order_params.stop_loss.is_some() || order_params.take_profit.is_some() {
            req.attach_algo_ords = Some(vec![order_params.into()]);
        }

        req
    }

    fn new_ws_req(order: Order, order_params: OrderParams) -> Self {
        let order_type = match (order.order_type, order.time_in_force) {
            (OrderType::Market, _) => OkxOrderType::Market,
            (OrderType::Limit, TimeInForce::GTC) => OkxOrderType::Limit,
            (OrderType::Limit, TimeInForce::FOK) => OkxOrderType::Fok,
            (OrderType::Limit, TimeInForce::IOC) => OkxOrderType::Ioc,
            (OrderType::Limit, TimeInForce::PostOnly) => OkxOrderType::PostOnly,
        };

        let mut req = OkxPostOrderReq {
            inst_id: order.symbol.into(),
            td_mode: OkxTdMode::default(),
            cl_ord_id: order.cid,
            side: order.side.into(),
            pos_side: None,
            ord_type: order_type,
            sz: order.amount.unwrap().to_string(),
            px: order.price.map(|p| p.to_string()),
            attach_algo_ords: None,
            reduce_only: None,
        };
        if let Some(pos_side) = order.pos_side {
            if (pos_side == PosSide::Long && order.side == OrderSide::Sell)
                || (pos_side == PosSide::Short && order.side == OrderSide::Buy)
                    && !order_params.is_dual_side
            {
                req.reduce_only = Some(true);
            }
            if order_params.is_dual_side {
                req.pos_side = Some(pos_side.into());
            }
        }
        if let Some(margin_mode) = order_params.margin_mode.clone() {
            req.td_mode = margin_mode.into();
        }
        req
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
struct OkxPostAttach {
    #[serde(skip_serializing_if = "Option::is_none")]
    tp_ord_px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tp_trigger_px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tp_trigger_px_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    sl_ord_px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    sl_trigger_px: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    sl_trigger_px_type: Option<String>,
}

impl From<OrderParams> for OkxPostAttach {
    fn from(value: OrderParams) -> Self {
        let mut attach = OkxPostAttach::default();
        if let Some(profit_trigger) = value.take_profit {
            match profit_trigger.trigger_price {
                TriggerPrice::MarkPrice(price) => {
                    attach.tp_trigger_px = Some(price.to_string());
                    attach.tp_trigger_px_type = Some("mark".to_string());
                }
                TriggerPrice::ContractPrice(price) => {
                    attach.tp_trigger_px = Some(price.to_string());
                    attach.tp_trigger_px_type = Some("last".to_string());
                }
            }

            match profit_trigger.trigger_action {
                TriggerAction::Limit(price) => {
                    attach.tp_ord_px = Some(price.to_string());
                }
                TriggerAction::Market => {
                    attach.tp_ord_px = Some((-1).to_string());
                }
            }
        }

        if let Some(stop_loss) = value.stop_loss {
            match stop_loss.trigger_price {
                TriggerPrice::MarkPrice(price) => {
                    attach.sl_trigger_px = Some(price.to_string());
                    attach.sl_trigger_px_type = Some("mark".to_string());
                }
                TriggerPrice::ContractPrice(price) => {
                    attach.sl_trigger_px = Some(price.to_string());
                    attach.sl_trigger_px_type = Some("last".to_string());
                }
            }

            match stop_loss.trigger_action {
                TriggerAction::Limit(price) => {
                    attach.sl_ord_px = Some(price.to_string());
                }
                TriggerAction::Market => {
                    attach.sl_ord_px = Some((-1).to_string());
                }
            }
        }
        attach
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxPostCancelOrderReq {
    inst_id: OkxSymbolSwap,
    #[serde(skip_serializing_if = "Option::is_none")]
    ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
}

impl OkxPostCancelOrderReq {
    pub fn new(symbol: Symbol, order_id: Option<String>, c_id: Option<String>) -> Self {
        Self {
            inst_id: symbol.into(),
            ord_id: order_id,
            cl_ord_id: c_id,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxBatchCancelOrderReq {
    inst_id: OkxSymbolSwap,
    #[serde(skip_serializing_if = "Option::is_none")]
    ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
}

impl OkxBatchCancelOrderReq {
    pub fn new(symbol: Symbol, ids: Option<Vec<String>>, cids: Option<Vec<String>>) -> Vec<Self> {
        let mut reqs = vec![];
        if let Some(ids) = ids {
            reqs.extend(ids.into_iter().map(|id| Self {
                inst_id: symbol.clone().into(),
                ord_id: Some(id),
                cl_ord_id: None,
            }));
        }
        if let Some(cids) = cids {
            reqs.extend(cids.into_iter().map(|cid| Self {
                inst_id: symbol.clone().into(),
                ord_id: None,
                cl_ord_id: Some(cid),
            }));
        }
        reqs
    }
}

#[derive(Deserialize)]
pub struct BatchOrderRespWrapper(Vec<OkxBatchOrderResp>);

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxBatchOrderResp {
    ord_id: String,
    cl_ord_id: String,
    #[serde(deserialize_with = "de_from_str")]
    s_code: i32,
    s_msg: String,
}

impl From<BatchOrderRespWrapper> for BatchOrderRsp {
    fn from(value: BatchOrderRespWrapper) -> Self {
        let (success_list, failure_list): (Vec<_>, Vec<_>) =
            value.0.into_iter().partition(|r| r.s_code == 0);

        BatchOrderRsp {
            success_list: success_list
                .into_iter()
                .map(|r| SuccessOrder {
                    id: Some(r.ord_id),
                    cid: Some(r.cl_ord_id),
                })
                .collect(),
            failure_list: failure_list
                .into_iter()
                .map(|r| FailOrder {
                    id: Some(r.ord_id),
                    cid: Some(r.cl_ord_id),
                    error_code: r.s_code,
                    error: r.s_msg,
                })
                .collect(),
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxPostAmendOrderReq {
    inst_id: OkxSymbolSwap,
    #[serde(skip_serializing_if = "Option::is_none")]
    ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    new_sz: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    new_px: Option<String>,
    // #[serde(skip_serializing_if = "Option::is_none")]
    // attach_algo_ords: Option<Vec<OkxPostNewAttach>>,
}

impl OkxPostAmendOrderReq {
    pub fn new(order: Order) -> Self {
        Self {
            inst_id: order.symbol.into(),
            ord_id: Some(order.id),
            cl_ord_id: order.cid,
            new_sz: Some(order.amount.unwrap().to_string()),
            new_px: order.price.map(|p| p.to_string()),
            // attach_algo_ords: None,
        }
    }
}

// #[derive(Serialize)]
// #[serde(rename_all = "camelCase")]
// struct OkxPostNewAttach {
//     #[serde(skip_serializing_if = "Option::is_none")]
//     new_tp_trigger_px: Option<String>,
//     #[serde(skip_serializing_if = "Option::is_none")]
//     new_tp_ord_px: Option<String>,
//     #[serde(skip_serializing_if = "Option::is_none")]
//     new_tp_ord_px_type: Option<String>,
//     #[serde(skip_serializing_if = "Option::is_none")]
//     new_sl_trigger_px: Option<String>,
//     #[serde(skip_serializing_if = "Option::is_none")]
//     new_sl_ord_px: Option<String>,
//     #[serde(skip_serializing_if = "Option::is_none")]
//     new_sl_ord_px_type: Option<String>,
// }

struct OkxSymbolSwap(OkxSymbol, String);

impl From<Symbol> for OkxSymbolSwap {
    fn from(symbol: Symbol) -> Self {
        OkxSymbolSwap(symbol.into(), "SWAP".to_string())
    }
}

impl Serialize for OkxSymbolSwap {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        format!("{}-{}-{}", self.0 .0.base, self.0 .0.quote, self.1).serialize(serializer)
    }
}

#[derive(Default, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum OkxTdMode {
    Isolated,
    #[default]
    Cross,
}

impl From<MarginMode> for OkxTdMode {
    fn from(value: MarginMode) -> Self {
        match value {
            MarginMode::Isolated => OkxTdMode::Isolated,
            MarginMode::Cross => OkxTdMode::Cross,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxPostOrderResp {
    pub ord_id: String,
    #[serde(deserialize_with = "de_from_str")]
    pub s_code: i32,
    pub s_msg: String,
}

#[derive(Default)]
pub struct OkxOrdersReq {
    pub inst_type: InstType,
    pub inst_id: Option<OkxSymbol>,
    pub start_time: Option<i64>,
    pub end_time: Option<i64>,
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxGetOrderByIdReq {
    inst_id: OkxSymbolSwap,
    #[serde(skip_serializing_if = "Option::is_none")]
    ord_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cl_ord_id: Option<String>,
}

impl OkxGetOrderByIdReq {
    pub fn new(symbol: Symbol, order_id: OrderId) -> Self {
        let (ord_id, cl_ord_id) = match order_id {
            OrderId::Id(id) => (Some(id), None),
            OrderId::ClientOrderId(cid) => (None, Some(cid)),
        };
        Self {
            inst_id: symbol.into(),
            ord_id,
            cl_ord_id,
        }
    }
}

impl OkxOrdersReq {
    pub fn set_symbol(&mut self, symbol: Symbol) {
        self.inst_id = Some(symbol.into());
    }

    pub fn set_start_time(&mut self, start_time: i64) {
        self.start_time = Some(start_time);
    }

    pub fn set_end_time(&mut self, end_time: i64) {
        self.end_time = Some(end_time);
    }
}

impl Serialize for OkxOrdersReq {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let mut map = serializer.serialize_map(Some(4))?;
        map.serialize_entry("instType", &self.inst_type)?;
        if let Some(inst_id) = &self.inst_id {
            //  "instId": "BTC-USDT-SWAP", 仅支持永续合约
            let inst_id = format!("{}-{}-{}", inst_id.0.base, inst_id.0.quote, self.inst_type);
            map.serialize_entry("instId", &inst_id)?;
        }
        if let Some(start_time) = &self.start_time {
            map.serialize_entry("startTime", &start_time.to_string())?;
        }
        if let Some(end_time) = &self.end_time {
            map.serialize_entry("endTime", &end_time.to_string())?;
        }
        map.end()
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxOrderResp {
    ord_id: String,
    cl_ord_id: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    c_time: i64,
    state: OkxOrderState,
    inst_id: OkxSymbol,
    ord_type: OkxOrderType,
    // 订单方向 buy：买 sell：卖
    side: OrderSideLower,
    // 持仓方向 long：多 short：空 买卖模式返回 net
    pos_side: PosSideLower,
    px: String,
    #[serde(deserialize_with = "de_from_str")]
    sz: f64,
    #[serde(deserialize_with = "de_from_str")]
    acc_fill_sz: f64,
    avg_px: String,
}

impl From<OkxOrderResp> for Order {
    fn from(value: OkxOrderResp) -> Self {
        let avg_px = value.avg_px.parse().unwrap_or(0.);
        let px = value.px.parse().ok();
        let amount_multiplier = value.inst_id.multiplier();

        Self {
            id: value.ord_id,
            cid: value.cl_ord_id,
            symbol: value.inst_id.into(),
            order_type: value.ord_type.clone().into(),
            side: value.side.into(),
            timestamp: value.c_time,
            status: value.state.into(),
            time_in_force: value.ord_type.into(),
            price: px,
            amount: Some(value.sz / amount_multiplier),
            quote_amount: None,
            filled: value.acc_fill_sz / amount_multiplier,
            filled_avg_price: avg_px,
            pos_side: value.pos_side.into(),
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum OkxOrderState {
    Canceled,
    Live,
    PartiallyFilled,
    Filled,
    MmpCanceled,
}

impl From<OkxOrderState> for OrderStatus {
    fn from(value: OkxOrderState) -> Self {
        match value {
            OkxOrderState::Live => OrderStatus::Open,
            OkxOrderState::PartiallyFilled => OrderStatus::PartiallyFilled,
            OkxOrderState::Filled => OrderStatus::Filled,
            OkxOrderState::Canceled => OrderStatus::Canceled,
            OkxOrderState::MmpCanceled => OrderStatus::Canceled,
        }
    }
}

// #[derive(Deserialize)]
// #[serde(rename_all = "snake_case")]
// pub enum OkxOrderState {
//     Live,
//     PartiallyFilled,
// }
//
// impl From<OkxOrderState> for OrderStatus {
//     fn from(value: OkxOrderState) -> Self {
//         match value {
//             OkxOrderState::Live => OrderStatus::Open,
//             OkxOrderState::PartiallyFilled => OrderStatus::PartiallyFilled,
//         }
//     }
// }

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum OkxOrderType {
    // 市价单
    Market,
    // 限价单
    Limit,
    // 做maker单
    PostOnly,
    // 全部成交或立即取消
    Fok,
    // 立即成交并取消剩余
    Ioc,
    // 市价委托立即成交并取消剩余（仅适用交割、永续）
    OptimalLimitIoc,
    // 做市商保护(仅适用于组合保证金账户模式下的期权订单)
    Mmp,
    // 做市商保护且只做maker单(仅适用于组合保证金账户模式下的期权订单)
    MmpAndPostOnly,
    // 期权简选（全部成交或立即取消）
    OpFok,
}

impl From<OkxOrderType> for OrderType {
    fn from(value: OkxOrderType) -> Self {
        match value {
            OkxOrderType::Market => OrderType::Market,
            _ => OrderType::Limit,
        }
    }
}

// impl From<OrderType> for OkxOrderType {
//     fn from(value: OrderType) -> Self {
//         match value {
//             OrderType::Market => OkxOrderType::Market,
//             OrderType::Limit => OkxOrderType::Limit,
//         }
//     }
// }

impl From<OkxOrderType> for TimeInForce {
    fn from(value: OkxOrderType) -> Self {
        match value {
            OkxOrderType::Fok => TimeInForce::FOK,
            OkxOrderType::Ioc => TimeInForce::IOC,
            OkxOrderType::PostOnly => TimeInForce::PostOnly,
            _ => TimeInForce::GTC,
        }
    }
}

// #[derive(Serialize)]
// #[serde(rename_all = "camelCase")]
pub struct OkxPositionReq {
    inst_type: InstType,
    // #[serde(skip_serializing_if = "Option::is_none")]
    inst_id: Option<OkxSymbol>,
}

impl Serialize for OkxPositionReq {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let mut map = serializer.serialize_map(Some(2))?;
        map.serialize_entry("instType", &self.inst_type)?;
        if let Some(inst_id) = &self.inst_id {
            //  "instId": "BTC-USDT-SWAP", 仅支持永续合约
            let inst_id = format!("{}-{}-{}", inst_id.0.base, inst_id.0.quote, self.inst_type);
            map.serialize_entry("instId", &inst_id)?;
        }
        map.end()
    }
}

impl OkxPositionReq {
    pub fn new(inst_type: InstType, symbol: Option<Symbol>) -> Self {
        let inst_id = symbol.map(Into::into);
        Self { inst_type, inst_id }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxPositionResp {
    pub inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub c_time: i64,
    pub mgn_mode: String,
    pub pos_side: OkxPositionSide,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub pos: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub lever: u8,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub avg_px: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub upl: f64,
}

impl From<OkxPositionResp> for Position {
    fn from(value: OkxPositionResp) -> Self {
        let symbol = value.inst_id;
        let multiplier = symbol.multiplier();
        let margin_mode = match value.mgn_mode.as_str() {
            "cross" => MarginMode::Cross,
            "isolated" => MarginMode::Isolated,
            _ => MarginMode::Cross,
        };
        let side = match value.pos_side {
            OkxPositionSide::Long => PosSide::Long,
            OkxPositionSide::Short => PosSide::Short,
            OkxPositionSide::Net => {
                if value.pos > 0. {
                    PosSide::Long
                } else {
                    PosSide::Short
                }
            }
        };
        Position {
            symbol: symbol.into(),
            timestamp: value.c_time,
            margin_mode,
            side,
            leverage: value.lever,
            amount: value.pos.abs() / multiplier,
            entry_price: value.avg_px,
            unrealized_pnl: value.upl,
        }
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "lowercase")]
pub enum OkxPositionSide {
    Long,
    Short,
    Net,
}

#[derive(Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTradeFeeReq {
    inst_type: InstType,
    inst_family: OkxSymbol,
}

impl OkxTradeFeeReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            inst_type: InstType::Swap,
            inst_family: symbol.into(),
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTradeFeeResp {
    #[serde(deserialize_with = "de_from_str")]
    pub taker_u: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub maker_u: f64,
}

impl From<OkxTradeFeeResp> for FeeRate {
    fn from(value: OkxTradeFeeResp) -> Self {
        Self {
            taker: value.taker_u.abs(),
            maker: value.maker_u.abs(),
            ..Default::default()
        }
    }
}

pub struct OkxMaxLeverageReq {
    inst_type: InstType,
    inst_id: OkxSymbol,
    mgn_mode: OkxMarginMode,
    lever: String,
}

impl OkxMaxLeverageReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            inst_type: InstType::Swap,
            inst_id: symbol.into(),
            mgn_mode: OkxMarginMode::Cross,
            lever: "1".to_string(),
        }
    }
}

impl Serialize for OkxMaxLeverageReq {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let inst_id = format!("{}-{}-SWAP", self.inst_id.0.base, self.inst_id.0.quote);
        let mut map = serializer.serialize_map(Some(4))?;
        map.serialize_entry("instType", &self.inst_type)?;
        map.serialize_entry("instId", &inst_id)?;
        map.serialize_entry("mgnMode", &self.mgn_mode)?;
        map.serialize_entry("lever", &self.lever)?;
        map.end()
    }
}

#[derive(Default, Serialize)]
pub struct OkxDepositAddressReq {
    ccy: String,
}

impl OkxDepositAddressReq {
    pub fn new(ccy: String) -> Self {
        Self {
            ccy: ccy.to_uppercase(),
        }
    }
}

pub enum OkxChain {
    TRC20,
    ERC20,
    Aptos,
    ArbitrumOne,
    AvalancheC,
    OKTC,
    Optimism,
    Polygon,
    Solana,
    TON,
    XLayer,
}

#[derive(Deserialize)]
pub struct OkxDepositAddressResp {
    ccy: String,
    chain: String,
    // to: String,
    addr: String,
    tag: Option<String>,
}

impl From<OkxDepositAddressResp> for DepositAddress {
    fn from(value: OkxDepositAddressResp) -> Self {
        // like "USDT-TRC20" => "TRC20"
        let chain_str = value
            .chain
            .split('-')
            .nth(1)
            .unwrap_or_default()
            .to_string();
        let chain = match chain_str.as_str() {
            "TRC20" => Chain::Trc20,
            "ERC20" => Chain::Erc20,
            "Solana" => Chain::Sol,
            "Polygon" => Chain::Polygon,
            "Arbitrum One" => Chain::ArbitrumOne,
            "Optimism" => Chain::Optimism,
            "TON" => Chain::Ton,
            "Avalanche C-Chain" => Chain::AVAXC,
            _ => Chain::Erc20, // 默认fallback
        };
        Self {
            asset: value.ccy,
            chain: Some(chain),
            address: value.addr,
            tag: value.tag,
            url: None,
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxWithdrawalReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    client_id: Option<String>,
    ccy: String,
    amt: String,
    // 提币方式, 3-内部转账, 4-链上提币
    dest: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    chain: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    area_code: Option<String>,
    to_addr: String,
}

impl OkxWithdrawalReq {
    pub fn new(withdrawal: WithDrawlParams) -> Self {
        let base = Self {
            client_id: withdrawal.cid,
            ccy: withdrawal.asset.clone(),
            amt: withdrawal.amt.to_string(),
            to_addr: String::new(), // Will be overwritten
            dest: String::new(),    // Will be overwritten
            chain: None,
            area_code: None,
        };

        match &withdrawal.addr {
            WithdrawalAddr::OnChain(params) => Self {
                to_addr: params.tag.as_ref().map_or_else(
                    || params.address.clone(),
                    |tag| format!("{}:{}", params.address, tag),
                ),
                dest: "4".to_owned(),
                chain: Some(chain_to_str(withdrawal.asset, params.chain.clone())),
                ..base
            },
            WithdrawalAddr::InternalTransfer(params) => Self {
                to_addr: match params {
                    InternalTransferParams::Email(email) => email.to_owned(),
                    InternalTransferParams::Phone(phone) => phone.1.to_owned(),
                    InternalTransferParams::Uid(uid) => uid.to_owned(),
                },
                dest: "3".to_owned(),
                area_code: if let InternalTransferParams::Phone(phone) = params {
                    Some(phone.0.to_owned())
                } else {
                    None
                },
                ..base
            },
        }
    }
}

fn chain_to_str(ccy: String, chain: Chain) -> String {
    match chain {
        Chain::Erc20 => format!("{}-ERC20", ccy),
        Chain::Trc20 => format!("{}-TRC20", ccy),
        Chain::Bep20 => format!("{}-BEP20", ccy),
        Chain::Sol => format!("{}-Solana", ccy),
        Chain::Polygon => format!("{}-Polygon", ccy),
        Chain::ArbitrumOne => format!("{}-Arbitrum One", ccy),
        Chain::Optimism => format!("{}-Optimism", ccy),
        Chain::Ton => format!("{}-TON", ccy),
        Chain::AVAXC => format!("{}-Avalanche C-Chain", ccy),
    }
}

pub struct OkxMaxPositionReq {
    inst_id: OkxSymbol,
    td_mode: OkxMarginMode,
    lever: String,
}

impl OkxMaxPositionReq {
    pub fn new(symbol: Symbol, leverage: u8) -> Self {
        Self {
            inst_id: symbol.into(),
            td_mode: OkxMarginMode::Cross,
            lever: leverage.to_string(),
        }
    }
}

impl Serialize for OkxMaxPositionReq {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let inst_id = format!("{}-{}-SWAP", self.inst_id.0.base, self.inst_id.0.quote);
        let mut map = serializer.serialize_map(Some(3))?;
        map.serialize_entry("instId", &inst_id)?;
        map.serialize_entry("tdMode", &self.td_mode)?;
        map.serialize_entry("lever", &self.lever)?;
        map.end()
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxMaxPositionResp {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    pub max_sell: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub max_buy: f64,
}

impl From<OkxMaxPositionResp> for MaxPosition {
    fn from(value: OkxMaxPositionResp) -> Self {
        let multiplier = value.inst_id.multiplier();
        MaxPosition {
            long: PositionValue::Quantity(value.max_sell / multiplier),
            short: PositionValue::Quantity(value.max_buy / multiplier),
        }
    }
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxMaxLeverageResp {
    #[serde(deserialize_with = "de_from_str")]
    pub max_lever: u8,
}

pub struct OkxSetLeverageReq {
    inst_id: OkxSymbol,
    lever: String,
    mgn_mode: OkxMarginMode,
}

impl OkxSetLeverageReq {
    pub fn new(symbol: Symbol, lever: u8, mgn_mode: MarginMode) -> Self {
        Self {
            inst_id: symbol.into(),
            lever: lever.to_string(),
            mgn_mode: mgn_mode.into(),
        }
    }
}

impl Serialize for OkxSetLeverageReq {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        //  "instId": "BTC-USDT-SWAP", 仅永续合约
        let inst_id = format!("{}-{}-SWAP", self.inst_id.0.base, self.inst_id.0.quote);
        let mut map = serializer.serialize_map(Some(3))?;
        map.serialize_entry("instId", &inst_id)?;
        map.serialize_entry("lever", &self.lever)?;
        map.serialize_entry("mgnMode", &self.mgn_mode)?;
        map.end()
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum OkxMarginMode {
    Cross,
    Isolated,
}

impl From<MarginMode> for OkxMarginMode {
    fn from(value: MarginMode) -> Self {
        match value {
            MarginMode::Cross => OkxMarginMode::Cross,
            MarginMode::Isolated => OkxMarginMode::Isolated,
        }
    }
}

impl From<OkxMarginMode> for MarginMode {
    fn from(value: OkxMarginMode) -> Self {
        match value {
            OkxMarginMode::Cross => MarginMode::Cross,
            OkxMarginMode::Isolated => MarginMode::Isolated,
        }
    }
}

#[derive(Default, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxAccountConfig {
    uid: String,
    pub pos_mode: OkxPostMode,
}

impl OkxAccountConfig {
    pub fn is_dual_side(&self) -> bool {
        match self.pos_mode {
            OkxPostMode::LongShortMode => true,
            OkxPostMode::NetMode => false,
        }
    }

    pub fn set_position_mode(&mut self, is_dual_side: bool) {
        if is_dual_side {
            self.pos_mode = OkxPostMode::LongShortMode;
        } else {
            self.pos_mode = OkxPostMode::NetMode;
        }
    }

    pub fn get_uid(&self) -> String {
        self.uid.clone()
    }
}

#[derive(Default, Deserialize, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum OkxPostMode {
    #[default]
    LongShortMode,
    NetMode,
}

#[derive(Serialize, Deserialize, Clone, Copy, PartialEq, Debug)]
pub enum Channel {
    /// public
    #[serde(rename = "mark-price")]
    MarkPrice,
    // #[serde(rename = "tickers")]
    #[serde(rename = "bbo-tbt")]
    BookTicker,
    #[serde(rename = "funding-rate")]
    FundingRate,
    #[serde(rename = "books5")]
    Depth,
    #[serde(rename = "trades")]
    Trade,
    #[serde(rename = "instruments")]
    Instruments,

    // login
    #[serde(rename = "login")]
    Login,
    // private
    #[serde(rename = "orders")]
    Orders,
    #[serde(rename = "positions")]
    Positions,
    #[serde(rename = "account")]
    Balances,
}

#[derive(Serialize)]
pub struct SubscriptionRequest {
    #[serde(rename = "op")]
    pub operation: Operation,
    pub args: Vec<RequestArgs>,
}

impl SubscriptionRequest {
    pub fn new(is_private: bool, channel: Channel, symbol: String) -> Self {
        if is_private {
            Self {
                operation: Operation::Subscribe,
                args: vec![RequestArgs::Private(PrivateArgs::new(channel, symbol))],
            }
        } else {
            Self {
                operation: Operation::Subscribe,
                args: vec![RequestArgs::Public(PublicArgs::new(
                    channel,
                    symbol,
                    InstIdFormat::Default,
                ))],
            }
        }
    }
}

#[derive(Serialize)]
// 序列化的时候不会不会包含 Public 和 Private 标签。
#[serde(untagged)]
pub enum RequestArgs {
    Public(PublicArgs),
    Private(PrivateArgs),
}

#[derive(Default, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum Operation {
    #[default]
    Subscribe,
    Unsubscribe,
}

pub struct PublicArgs {
    pub channel: Channel,
    pub inst_id: String,
    pub format: InstIdFormat,
}

impl PublicArgs {
    pub fn new(channel: Channel, symbol: String, format: InstIdFormat) -> Self {
        PublicArgs {
            channel,
            inst_id: symbol,
            format,
        }
    }
}

#[derive(Clone, Copy)]
pub enum InstIdFormat {
    Default,
    Swap,
}

impl Serialize for PublicArgs {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let mut state = serializer.serialize_struct("args", 2)?;
        state.serialize_field("channel", &self.channel)?;
        let inst_id = match self.format {
            InstIdFormat::Default => self.inst_id.to_string(),
            InstIdFormat::Swap => format!("{}-SWAP", self.inst_id),
        };
        state.serialize_field("instId", &inst_id)?;
        state.end()
    }
}

#[derive(Debug, Deserialize)]
pub struct OkxStreamData {
    pub arg: OkxStreamArg,
    pub data: Value,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxStreamArg {
    pub channel: Channel,
    pub inst_id: Option<OkxSymbol>,
    pub inst_type: Option<InstType>,
    pub inst_family: Option<String>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxStreamEventSuccess {
    pub event: String,
    pub arg: OkxStreamArg,
    pub conn_id: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxStreamEventError {
    pub event: String,
    #[serde(deserialize_with = "de_from_str")]
    pub code: i32,
    pub msg: String,
    pub conn_id: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxStreamChannelConnCount {
    pub event: String,
    pub channel: String,
    #[serde(deserialize_with = "de_from_str")]
    pub conn_count: i32,
    pub conn_id: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsMarkPrice {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    mark_px: f64,
}

impl From<WsMarkPrice> for MarkPrice {
    fn from(value: WsMarkPrice) -> Self {
        MarkPrice {
            symbol: value.inst_id.into(),
            price: value.mark_px,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsBbo {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    bid_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    bid_sz: f64,
    #[serde(deserialize_with = "de_from_str")]
    ask_px: f64,
    #[serde(deserialize_with = "de_from_str")]
    ask_sz: f64,
    #[serde(deserialize_with = "de_from_str")]
    ts: i64,
}

impl From<WsBbo> for BboTicker {
    fn from(value: WsBbo) -> Self {
        let multiplier = value.inst_id.multiplier();
        BboTicker {
            symbol: value.inst_id.into(),
            bid_price: value.bid_px,
            bid_qty: value.bid_sz / multiplier,
            ask_price: value.ask_px,
            ask_qty: value.ask_sz / multiplier,
            timestamp: value.ts,
        }
    }
}

impl From<OkxDepthResp> for BboTicker {
    fn from(value: OkxDepthResp) -> Self {
        let symbol = value.symbol.unwrap();
        let multiplier = symbol.multiplier();
        BboTicker {
            symbol: symbol.into(),
            bid_price: value.bids.first().map(|(price, _)| *price).unwrap_or(0.),
            bid_qty: value
                .bids
                .first()
                .map(|(_, qty)| qty / multiplier)
                .unwrap_or(0.),
            ask_price: value.asks.first().map(|(price, _)| *price).unwrap_or(0.),
            ask_qty: value
                .asks
                .first()
                .map(|(_, qty)| qty / multiplier)
                .unwrap_or(0.),
            timestamp: value.ts,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxDepthResp {
    symbol: Option<OkxSymbol>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    bids: Vec<(f64, f64)>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    asks: Vec<(f64, f64)>,
    #[serde(deserialize_with = "de_from_str")]
    ts: i64,
}

impl OkxDepthResp {
    pub fn set_symbol(&mut self, symbol: Symbol) {
        self.symbol = Some(symbol.into());
    }

    pub fn convert_to_depth(self) -> Depth {
        let symbol = self.symbol.unwrap();
        let multiplier = symbol.multiplier();
        Depth {
            symbol: symbol.into(),
            timestamp: self.ts,
            asks: self
                .asks
                .into_iter()
                .map(|(price, amount)| (price, amount / multiplier).into())
                .collect(),
            bids: self
                .bids
                .into_iter()
                .map(|(price, amount)| (price, amount / multiplier).into())
                .collect(),
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsFundingRate {
    inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_from_str")]
    funding_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    funding_time: i64,
    #[serde(deserialize_with = "de_from_str")]
    next_funding_time: i64,
}

impl From<WsFundingRate> for Funding {
    fn from(value: WsFundingRate) -> Self {
        let interval = (value.next_funding_time - value.funding_time) / 1000 / 3600;
        Funding {
            symbol: value.inst_id.into(),
            funding_rate: value.funding_rate,
            next_funding_at: value.funding_time,
            funding_interval: Some(interval as u8),
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsTrade {
    inst_id: OkxSymbol,
    trade_id: String,
    #[serde(deserialize_with = "de_from_str")]
    ts: i64,
    #[serde(deserialize_with = "de_from_str")]
    px: f64,
    #[serde(deserialize_with = "de_from_str")]
    sz: f64,
    side: OrderSideLower,
}

impl From<WsTrade> for Trade {
    fn from(value: WsTrade) -> Self {
        let multiplier = value.inst_id.multiplier();
        Trade {
            id: value.trade_id,
            symbol: value.inst_id.into(),
            timestamp: value.ts,
            price: value.px,
            amount: value.sz / multiplier,
            side: value.side.into(),
        }
    }
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct WsLogin {
    pub op: Channel,
    pub args: Vec<WsLoginInner>,
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct WsLoginInner {
    api_key: String,
    passphrase: String,
    timestamp: String,
    sign: String,
}

impl WsLogin {
    pub fn new(api_key: &str, passphrase: &str, timestamp: String, sign: String) -> Self {
        Self {
            op: Channel::Login,
            args: vec![WsLoginInner {
                api_key: api_key.to_owned(),
                passphrase: passphrase.to_owned(),
                timestamp,
                sign,
            }],
        }
    }
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PrivateArgs {
    channel: Channel,
    #[serde(skip_serializing_if = "Option::is_none")]
    inst_type: Option<InstType>,
    #[serde(skip_serializing_if = "Option::is_none")]
    inst_family: Option<String>,
}

impl PrivateArgs {
    pub fn new(channel: Channel, symbol: String) -> Self {
        match channel {
            Channel::Balances => PrivateArgs {
                channel,
                inst_type: None,
                inst_family: None,
            },
            _ => PrivateArgs {
                channel,
                inst_type: Some(InstType::Swap),
                inst_family: Some(symbol),
            },
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxBalancesResp {
    pub details: Vec<OkxBalanceDetails>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxBalanceDetails {
    ccy: String,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    eq: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    avail_bal: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    upl: f64,
}

impl From<OkxBalanceDetails> for Balance {
    fn from(value: OkxBalanceDetails) -> Self {
        Balance {
            asset: value.ccy,
            balance: value.eq,
            available_balance: value.avail_bal,
            unrealized_pnl: value.upl,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WsPosition {
    pub inst_id: OkxSymbol,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    c_time: i64,
    mgn_mode: OkxMarginMode,
    pos_side: PosSideLower,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    lever: u8,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pos: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    avg_px: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    upl: f64,
    #[serde(deserialize_with = "de_empty_str_to_default")]
    pub funding_fee: f64,
}

impl From<WsPosition> for Position {
    fn from(value: WsPosition) -> Self {
        let symbol = value.inst_id;
        let multiplier = symbol.multiplier();
        let side = match value.pos_side {
            PosSideLower::Long => PosSide::Long,
            PosSideLower::Short => PosSide::Short,
            PosSideLower::Net => PosSide::Long,
        };
        let margin_mode = value.mgn_mode.into();
        Position {
            symbol: symbol.into(),
            timestamp: value.c_time,
            margin_mode,
            side,
            leverage: value.lever,
            amount: value.pos.abs() / multiplier,
            entry_price: value.avg_px,
            unrealized_pnl: value.upl,
        }
    }
}

impl From<&WsPosition> for FundingFee {
    fn from(value: &WsPosition) -> Self {
        FundingFee {
            symbol: value.inst_id.clone().into(),
            funding_fee: value.funding_fee,
            timestamp: value.c_time,
        }
    }
}

#[derive(Debug)]
pub struct FundingFeeCache {
    pub funding_fee: f64,
    pub timestamp: i64,
}

impl PartialEq for FundingFeeCache {
    fn eq(&self, other: &Self) -> bool {
        (self.funding_fee - other.funding_fee).abs() < 1e-10 && self.timestamp == other.timestamp
    }
}

impl Eq for FundingFeeCache {}

#[derive(Serialize, Debug)]
pub struct WsApiReq {
    pub(crate) id: u64,
    pub(crate) op: WsApiMethod,
    pub(crate) args: serde_json::Value,
}

impl WsApiReq {
    pub fn post_order(id: u64, order: Order, params: OrderParams) -> Result<Self> {
        if order.amount.is_none() {
            return Err(qerror!("Amount is required"));
        }
        let req = OkxPostOrderReq::new_ws_req(order, params);
        let args = serde_json::to_value(vec![req])?;
        debug!("args: {args:?}");
        Ok(Self {
            id,
            op: WsApiMethod::Post,
            args,
        })
    }

    pub fn batch_post_order(id: u64, orders: Vec<Order>, params: OrderParams) -> Result<Self> {
        // let req = OkxPostBatchOrderReq::new(orders, params);
        let reqs = orders
            .into_iter()
            .map(|order| OkxPostOrderReq::new_ws_req(order, params.clone()))
            .collect::<Vec<_>>();
        let args = serde_json::to_value(reqs)?;
        Ok(Self {
            id,
            op: WsApiMethod::BatchPost,
            args,
        })
    }

    pub fn cancel_order(
        id: u64,
        symbol: Symbol,
        order_id: Option<String>,
        client_id: Option<String>,
    ) -> Result<Self> {
        let req = OkxPostCancelOrderReq::new(symbol, order_id, client_id);
        let args = serde_json::to_value(vec![req])?;
        Ok(Self {
            id,
            op: WsApiMethod::Cancel,
            args,
        })
    }

    pub fn batch_cancel_order_by_ids(
        id: u64,
        symbol: Option<Symbol>,
        ids: Option<Vec<String>>,
        cids: Option<Vec<String>>,
    ) -> Result<Self> {
        if symbol.is_none() || (ids.is_none() && cids.is_none()) {
            // symbol is required, and at least one of ids or cids is required
            return Err(qerror!("symbol or ids and cids are required"));
        }

        let reqs = OkxBatchCancelOrderReq::new(symbol.unwrap(), ids, cids);
        let args = serde_json::to_value(reqs)?;
        Ok(Self {
            id,
            op: WsApiMethod::BatchCancel,
            args,
        })
    }

    pub fn amend_order(id: u64, order: Order) -> Result<Self> {
        let req = OkxPostAmendOrderReq::new(order);
        let args = serde_json::to_value(vec![req])?;
        Ok(Self {
            id,
            op: WsApiMethod::Amend,
            args,
        })
    }
}

#[derive(Serialize, Debug)]
pub enum WsApiMethod {
    #[serde(rename = "order")]
    Post,
    #[serde(rename = "batch-orders")]
    BatchPost,
    #[serde(rename = "cancel-order")]
    Cancel,
    #[serde(rename = "batch-cancel-orders")]
    BatchCancel,
    #[serde(rename = "amend-order")]
    Amend,
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct WsApiRsp {
    pub(crate) id: String,
    pub(crate) op: String,
    pub(crate) code: String,
    pub(crate) msg: String,
    pub(crate) data: Option<serde_json::Value>,
}

impl WsApiRsp {
    pub(crate) fn id(&self) -> u64 {
        self.id.parse::<u64>().unwrap()
    }

    pub(crate) fn into_common_order_rsp(self) -> Result<String> {
        let resp: Vec<OkxPostOrderResp> = match self.data {
            Some(result) => serde_json::from_value(result)?,
            None => return Err(qerror!("result is null")),
        };

        let resp = resp
            .into_iter()
            .next()
            .ok_or_else(|| qerror!("No order rsp"))?;
        match resp.s_code {
            0 => Ok(resp.ord_id.to_string()),
            _ => Err(qerror!("{:?}", resp)),
        }
    }

    pub(crate) fn into_batch_common_order_rsp(self) -> Result<BatchOrderRsp> {
        let resp: BatchOrderRespWrapper = match self.data {
            Some(result) => serde_json::from_value(result)?,
            None => return Err(qerror!("result is null")),
        };
        Ok(resp.into())
    }
}

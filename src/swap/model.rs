use std::str::FromStr;

use const_str::format as cfmt;
use hmac::{Hmac, Mac};
use once_cell::sync::OnceCell;
use quant_common::base::*;
use quant_common::*;
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};
use sha2::Sha512;

use crate::swap::rest::GateSwap;

// 自定义反序列化器，处理 id 字段可能是 u64、i64 或字符串的情况
fn de_id_from_any<'de, D>(deserializer: D) -> Result<u64, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::de::{self, Visitor};
    use std::fmt;

    struct IdVisitor;

    impl Visitor<'_> for IdVisitor {
        type Value = u64;

        fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
            formatter.write_str("a u64, i64, or string representing an id")
        }

        fn visit_u64<E>(self, value: u64) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            Ok(value)
        }

        fn visit_i64<E>(self, value: i64) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            if value < 0 {
                Err(E::custom(format!("id cannot be negative: {value}")))
            } else {
                Ok(value as u64)
            }
        }

        fn visit_str<E>(self, value: &str) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            value.parse::<u64>().map_err(E::custom)
        }

        fn visit_string<E>(self, value: String) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            value.parse::<u64>().map_err(E::custom)
        }
    }

    deserializer.deserialize_any(IdVisitor)
}

pub const EXCHANGE: &str = "GateSwap";

pub const WS_HOST: &str = "fx-ws.gateio.ws";
pub const WS_COLO_HOST: &str = "fxws-private.gateapi.io";
pub const WS_TEST_HOST: &str = "ws-testnet.gate.io";

pub const REST_BASE_HOST: &str = "api.gateio.ws";
pub const REST_COLO_BASE_HOST: &str = "apiv4-private.gateapi.io";
pub const REST_TEST_BASE_HOST: &str = "api-testnet.gateapi.io";

pub const SETTLE: &str = "usdt";
pub const PATH_ORDERS: &str = cfmt!("/api/v4/futures/{SETTLE}/orders");
pub const PATH_PRICE_ORDERS: &str = cfmt!("/api/v4/futures/{SETTLE}/price_orders");
pub const PATH_TIME_ORDERS: &str = cfmt!("/api/v4/futures/{SETTLE}/orders_timerange");
pub const PATH_ACCOUNT_DETAIL: &str = "/api/v4/account/detail";
pub const PATH_ACCOUNT: &str = cfmt!("/api/v4/futures/{SETTLE}/accounts");
pub const PATH_POSITIONS: &str = cfmt!("/api/v4/futures/{SETTLE}/positions");
pub const PATH_CONTRACTS: &str = cfmt!("/api/v4/futures/{SETTLE}/contracts");
pub const PATH_DEPTH: &str = cfmt!("/api/v4/futures/{SETTLE}/order_book");
pub const PATH_TICKER: &str = cfmt!("/api/v4/futures/{SETTLE}/tickers");
pub const PATH_ORDER: &str = cfmt!("/api/v4/futures/{SETTLE}/orders");
pub const PATH_BATCH_ORDER: &str = cfmt!("/api/v4/futures/{SETTLE}/batch_orders");
pub const PATH_UNI_ACCOUNT: &str = "/api/v4/unified/accounts";
pub const PATH_FEE: &str = "/api/v4/wallet/fee";
pub const PATH_SYMBOL_POSITIONS_SINGLE: &str = cfmt!("/api/v4/futures/{SETTLE}/positions");
pub const PATH_SYMBOL_POSITIONS: &str = cfmt!("/api/v4/futures/{SETTLE}/dual_comp/positions");
pub const PATH_SET_DUAL_SIDE: &str = cfmt!("/api/v4/futures/{SETTLE}/dual_mode");
pub const PATH_CREATE_SUB_ACCOUNT: &str = "/api/v4/sub_accounts/sub_accounts";
pub const PATH_GET_ACCOUNTS: &str = "/api/v4/sub_accounts/sub_accounts";
pub const PATH_SUB_TRANSFER: &str = "/api/v4/wallet/sub_account_transfers";
pub const PATH_GET_ACCOUNT: &str = "/api/v4/account/detail";
pub const PATH_BATCH_CANCEL_OIDS: &str = cfmt!("/api/v4/futures/{SETTLE}/batch_cancel_orders");
pub const PATH_ACCOUNT_BOOK: &str = cfmt!("/api/v4/futures/{SETTLE}/account_book");
pub const PATH_MAX_POSITION: &str = cfmt!("/api/v4/futures/{SETTLE}/risk_limit_tiers");
pub const PATH_FUNDING_RATE_HISTORY: &str = cfmt!("/api/v4/futures/{SETTLE}/funding_rate");

pub const WSAPI_URL: &str = "wss://fx-ws.gateio.ws/v4/ws/usdt";
pub const WSAPI_TEST_URL: &str = "wss://fx-ws-testnet.gateio.ws/v4/ws/usdt";

pub static MULTIPLIER_CACHE: OnceCell<FxHashMap<String, f64>> = OnceCell::new();

#[derive(Serialize, Deserialize, Clone, Copy, PartialEq, Debug, PartialOrd, Eq, Hash)]
pub enum Channel {
    /// Pong frame
    #[serde(rename = "")]
    Empty,
    #[serde(rename = "futures.ping")]
    Ping,
    #[serde(rename = "futures.pong")]
    Pong,

    /// public
    #[serde(rename = "futures.book_ticker")]
    BookTicker,
    #[serde(rename = "futures.mini_ob")]
    MiniOrderBook,
    #[serde(rename = "futures.order_book")]
    OrderBook,
    #[serde(rename = "futures.order_book_update")]
    OrderBookUpdate,
    #[serde(rename = "futures.trades")]
    Trades,
    #[serde(rename = "futures.tickers")]
    Tickers,
    #[serde(rename = "futures.candlesticks")]
    Candlesticks,

    /// private
    #[serde(rename = "futures.balances")]
    Balance,
    #[serde(rename = "futures.orders")]
    Orders,
    #[serde(rename = "futures.usertrades")]
    UserTrades,
    #[serde(rename = "futures.positions")]
    Position,

    /// api
    #[serde(rename = "futures.login")]
    Login,
    #[serde(rename = "futures.order_place")]
    PlaceOrder,
}
impl Channel {
    fn is_public(self) -> bool {
        matches!(
            self,
            Self::BookTicker
                | Self::OrderBook
                | Self::OrderBookUpdate
                | Self::Trades
                | Self::Tickers
                | Self::Candlesticks
        )
    }
}
impl std::fmt::Display for Channel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        std::fmt::Display::fmt(&serde_plain::to_string(self).unwrap(), f)
    }
}

#[derive(Serialize)]
pub struct WsReq<T: Serialize> {
    time: i64,
    pub channel: Channel,
    event: &'static str,
    payload: T,
    #[serde(skip_serializing_if = "Option::is_none")]
    auth: Option<WsReqAuth>,
}

/// event: subscribe or unsubscribe
pub(crate) fn new_ws_subscribe_req<T: Serialize>(
    channel: Channel,
    event: &'static str,
    payload: T,
    time: i64,
    key: &str,
    secret: &str,
) -> WsReq<T> {
    // ws 下单用的另一种鉴权方法
    if channel.is_public() {
        return WsReq {
            time,
            channel,
            event,
            payload,
            auth: None,
        };
    }

    let s = format!("channel={channel}&event={event}&time={time}");

    let mut mac = Hmac::<Sha512>::new_from_slice(secret.as_bytes()).unwrap();
    mac.update(s.as_bytes());
    let digest = mac.finalize().into_bytes();
    let sign = format!("{digest:02x}");

    let auth = WsReqAuth {
        method: "api_key",
        key: key.to_string(),
        sign,
    };

    WsReq {
        time,
        channel,
        event,
        payload,
        auth: Some(auth),
    }
}

#[derive(Serialize)]
struct WsReqAuth {
    /// api_key
    method: &'static str,
    #[serde(rename = "KEY")]
    key: String,
    #[serde(rename = "SIGN")]
    sign: String,
}

#[derive(Deserialize, Debug)]
pub struct GateEvent {
    pub channel: Channel,
    pub event: String,
    // #[serde(borrow)]
    pub result: RawValue,
    pub error: Option<SubscribeError>,
    // time_ms: u64
}

#[derive(Deserialize, Debug)]
pub struct SubscribeError {
    pub code: i32,
    pub message: String,
}

#[derive(Deserialize, Debug)]
pub struct SubscribeStatus {
    pub status: String,
}

#[derive(Deserialize)]
pub struct GateErr {
    pub label: String,
}

#[derive(Clone, Serialize, Deserialize, Default, PartialEq)]
pub struct GateSymbol(pub Symbol);

impl std::fmt::Debug for GateSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        self.0.fmt(f)
    }
}

impl std::fmt::Display for GateSymbol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        self.0.fmt(f)
    }
}

impl From<Symbol> for GateSymbol {
    fn from(value: Symbol) -> Self {
        Self(value)
    }
}
impl From<GateSymbol> for Symbol {
    fn from(value: GateSymbol) -> Self {
        value.0
    }
}

impl GateSymbol {
    #[inline]
    pub(crate) fn multiplier(&self) -> f64 {
        self.multiplier_f64()
    }
    #[inline]
    pub(crate) fn multiplier_f64(&self) -> f64 {
        let base = &self.0.base;
        // contracts 中不存在的合约都是已经下线的
        MULTIPLIER_CACHE
            .get()
            .unwrap()
            .get(base)
            .copied()
            .unwrap_or(1.0)
    }
}

#[allow(dead_code)]
#[derive(Deserialize, Debug)]
pub struct AccountDetail {
    ip_whitelist: Vec<String>,
    /// 交易对白名单
    currency_pairs: Vec<GateSymbol>,
    pub user_id: u32,
    /// 用户vip等级
    pub tier: u8,
    pub key: AccountDetailKey,
}

#[derive(Deserialize, Debug)]
pub struct AccountDetailKey {
    /// 1 - 经典模式 2 - 统一账户unified
    pub mode: u8,
}

/*
rest only fields: {'pending_orders', 'unrealised_pnl', 'adl_ranking', 'close_order', 'update_time', 'maintenance_margin', 'open_time', 'mark_price', 'initial_margin', 'value'}
ws   only fields: {'update_id', 'time_ms', 'time'}
*/
#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct GatePositionWs {
    pub contract: GateSymbol,
    /// 开仓价格
    #[serde(deserialize_with = "de_from_num_or_str")]
    entry_price: f64,
    pub size: i32,
    pub mode: String,
    #[serde(default)]
    pub adl_ranking: u8,
    #[serde(deserialize_with = "de_from_num_or_str")]
    margin: f64,
    unrealised_pnl: Option<String>,
    #[serde(deserialize_with = "de_from_num_or_str")]
    realised_pnl: f64,
    pub pnl_fund: Option<String>,

    #[serde(deserialize_with = "de_from_num_or_str")]
    pub risk_limit: f64,
    #[serde(deserialize_with = "de_from_num_or_str")]
    pub leverage_max: f64,

    /// 0代表全仓，正数代表逐仓
    #[serde(deserialize_with = "de_from_num_or_str")]
    pub leverage: u8,
    /// 全仓杠杆倍率
    #[serde(deserialize_with = "de_from_num_or_str")]
    pub cross_leverage_limit: f64,
    #[serde(deserialize_with = "de_from_num_or_str")]
    last_close_pnl: f64,
    #[serde(alias = "time")]
    update_time: i64,
    /// rest only field
    open_time: Option<i64>,

    update_id: Option<i64>,
    mark_price: Option<String>,
    liq_price: Option<f64>,
    maintenance_rate: Option<f64>,
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct GatePositionRest {
    pub contract: GateSymbol,
    /// 开仓价格
    #[serde(deserialize_with = "de_from_num_or_str")]
    entry_price: f64,
    pub size: i32,
    pub mode: String,
    #[serde(default)]
    pub adl_ranking: u8,
    #[serde(deserialize_with = "de_from_num_or_str")]
    margin: f64,
    unrealised_pnl: Option<String>,
    #[serde(deserialize_with = "de_from_num_or_str")]
    realised_pnl: f64,
    pub pnl_fund: Option<String>,

    #[serde(deserialize_with = "de_from_num_or_str")]
    pub risk_limit: f64,
    #[serde(deserialize_with = "de_from_num_or_str")]
    pub leverage_max: f64,

    /// 0代表全仓，正数代表逐仓
    #[serde(deserialize_with = "de_from_num_or_str")]
    pub leverage: u8,
    /// 全仓杠杆倍率
    #[serde(deserialize_with = "de_from_num_or_str")]
    pub cross_leverage_limit: f64,
    #[serde(deserialize_with = "de_from_num_or_str")]
    last_close_pnl: f64,
    #[serde(alias = "time")]
    update_time: i64,
    /// rest only field
    open_time: Option<i64>,

    update_id: Option<i64>,
    mark_price: Option<String>,
    liq_price: Option<String>,
    maintenance_rate: Option<String>,
}

#[derive(Deserialize, Debug, Clone)]
pub struct GateBalance {
    balance: f64,
    change: f64,
    pub r#type: String,
    currency: String, // lowercase
    text: String,
    // user: String,
    // time: i64,
    time_ms: i64,
}

impl From<GateBalance> for Option<FundingFee> {
    fn from(v: GateBalance) -> Self {
        // test: BTC_USD:3914424
        let symbols_str = v.text.split(':').next()?.to_uppercase();
        let symbol = serde_plain::from_str(&symbols_str).ok()?;
        Some(FundingFee {
            symbol,
            funding_fee: v.change,
            timestamp: v.time_ms,
        })
    }
}

impl From<GateBalance> for Balance {
    fn from(v: GateBalance) -> Self {
        Self {
            asset: v.currency.to_uppercase(),
            balance: v.balance,
            available_balance: v.balance,
            unrealized_pnl: 0.0,
        }
    }
}

impl GatePositionWs {
    #[inline]
    pub fn is_isolated(&self) -> bool {
        self.leverage > 0
    }
    pub fn leverage(&self) -> u8 {
        if self.is_isolated() {
            self.leverage
        } else {
            self.cross_leverage_limit.round() as u8
        }
    }
}

impl From<GatePositionWs> for Position {
    fn from(v: GatePositionWs) -> Self {
        let side = if v.mode == "single" {
            if v.size > 0 {
                PosSide::Long
            } else {
                PosSide::Short
            }
        } else if v.mode == "dual_short" {
            PosSide::Short
        } else if v.mode == "dual_long" {
            PosSide::Long
        } else {
            unreachable!("{}", v.mode)
        };
        let is_isolated = v.is_isolated();
        let leverage = v.leverage();
        let multiplier = v.contract.multiplier();
        Self {
            symbol: v.contract.into(),
            leverage,
            amount: f64::from(v.size.abs()) * multiplier,
            entry_price: v.entry_price,
            unrealized_pnl: v
                .unrealised_pnl
                .map(|x| x.parse().unwrap())
                .unwrap_or_default(),
            side,
            margin_mode: MarginMode::is_isolated(is_isolated),
            timestamp: v
                .open_time
                .and_then(|x| if x == 0 { None } else { Some(x * 1000) })
                .unwrap_or_default(),
        }
    }
}
impl GatePositionRest {
    #[inline]
    pub fn is_isolated(&self) -> bool {
        self.leverage > 0
    }
    pub fn leverage(&self) -> u8 {
        if self.is_isolated() {
            self.leverage
        } else {
            self.cross_leverage_limit.round() as u8
        }
    }
}

impl From<GatePositionRest> for Position {
    fn from(v: GatePositionRest) -> Self {
        let side = if v.mode == "single" {
            if v.size > 0 {
                PosSide::Long
            } else {
                PosSide::Short
            }
        } else if v.mode == "dual_short" {
            PosSide::Short
        } else if v.mode == "dual_long" {
            PosSide::Long
        } else {
            unreachable!("{}", v.mode)
        };
        let is_isolated = v.is_isolated();
        let leverage = v.leverage();
        let multiplier = v.contract.multiplier();
        Self {
            symbol: v.contract.into(),
            leverage,
            amount: f64::from(v.size.abs()) * multiplier,
            entry_price: v.entry_price,
            unrealized_pnl: v
                .unrealised_pnl
                .map(|x| x.parse().unwrap())
                .unwrap_or_default(),
            side,
            margin_mode: MarginMode::is_isolated(is_isolated),
            timestamp: v
                .open_time
                .and_then(|x| if x == 0 { None } else { Some(x * 1000) })
                .unwrap_or_default(),
        }
    }
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct GateAccount {
    currency: String,
    #[serde(deserialize_with = "de_from_str")]
    pub total: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub available: f64,
    #[serde(deserialize_with = "de_from_str")]
    /// 未完全成交订单的保证金
    pub order_margin: f64,
    #[serde(deserialize_with = "de_from_str")]
    /// 头寸保证金
    pub position_margin: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub unrealised_pnl: f64,
    /// 点卡数额
    #[serde(deserialize_with = "de_from_str")]
    point: f64,
    // 是否开启统一账户
    // pub enable_credit: bool,
    pub in_dual_mode: bool,
    /// user_id
    pub user: u32,
}

impl From<GateAccount> for Balance {
    fn from(v: GateAccount) -> Self {
        debug!("GateAccount: {v:?}");
        Self {
            asset: v.currency,
            balance: v.total + v.unrealised_pnl,
            available_balance: v.available,
            unrealized_pnl: v.unrealised_pnl,
        }
    }
}

/// websocket book_ticker
#[derive(Deserialize)]
pub struct GateBookTiker {
    #[serde(rename = "s")]
    pub symbol: GateSymbol,
    #[serde(rename = "b")]
    #[serde(deserialize_with = "de_from_str")]
    bid_price: f64,
    #[serde(rename = "B")]
    bid_qty: f64,
    #[serde(rename = "a")]
    #[serde(deserialize_with = "de_from_str")]
    ask_price: f64,
    #[serde(rename = "A")]
    ask_qty: f64,
    #[serde(rename = "t")]
    pub timestamp_ms: i64,
}

impl From<GateBookTiker> for BboTicker {
    fn from(v: GateBookTiker) -> Self {
        let multiplier = v.symbol.multiplier_f64();
        Self {
            symbol: v.symbol.into(),
            timestamp: v.timestamp_ms,
            // local_time_ns: common::time_ns(),
            bid_price: v.bid_price,
            bid_qty: v.bid_qty * multiplier,
            ask_price: v.ask_price,
            ask_qty: v.ask_qty * multiplier,
        }
    }
}

#[derive(Deserialize)]
pub struct OrderBookWs {
    // id: u64,
    contract: GateSymbol,
    #[serde(rename = "t")]
    pub time_ms: i64,
    pub asks: Vec<OrderBookItem>,
    pub bids: Vec<OrderBookItem>,
}
impl From<OrderBookWs> for Depth {
    fn from(v: OrderBookWs) -> Self {
        let multiplier = v.contract.multiplier();
        Self {
            symbol: v.contract.0,
            timestamp: v.time_ms,
            bids: v
                .bids
                .into_iter()
                .map(|x| x.convert(multiplier).into())
                .collect(),
            asks: v
                .asks
                .into_iter()
                .map(|x| x.convert(multiplier).into())
                .collect(),
        }
    }
}

#[derive(Serialize)]
pub struct ContractReq {
    contract: GateSymbol,
}

impl ContractReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            contract: symbol.into(),
        }
    }
}

// #[derive(Serialize)]
// pub struct RiskLimitTierReq {
//     #[serde(skip_serializing_if = "Option::is_none")]
//     pub contract: Option<GateSymbol>,
//     #[serde(skip_serializing_if = "Option::is_none")]
//     pub limit: Option<u32>,
//     #[serde(skip_serializing_if = "Option::is_none")]
//     pub offset: Option<u32>,
// }

// impl RiskLimitTierReq {
//     pub fn new(symbol: Option<Symbol>, limit: Option<u32>, offset: Option<u32>) -> Self {
//         Self {
//             contract: symbol.map(|s| s.into()),
//             limit,
//             offset,
//         }
//     }
// }

#[derive(Serialize)]
pub struct OrderBookReq {
    pub contract: GateSymbol,
    pub with_id: bool,
    pub limit: Option<u32>,
}
#[derive(Deserialize)]
pub(crate) struct DepthRest {
    pub id: u64,
    pub update: f64,
    pub asks: Vec<OrderBookItem>,
    pub bids: Vec<OrderBookItem>,
}
impl DepthRest {
    pub fn convert(self, symbol: GateSymbol) -> Depth {
        let multiplier = symbol.multiplier();
        Depth {
            symbol: symbol.into(),
            timestamp: (self.update * 1000.0) as _,
            bids: self
                .bids
                .into_iter()
                .map(|x| x.convert(multiplier).into())
                .collect(),
            asks: self
                .asks
                .into_iter()
                .map(|x| x.convert(multiplier).into())
                .collect(),
        }
    }

    pub fn into_bbo_ticker(self, symbol: GateSymbol) -> BboTicker {
        let multiplier = symbol.multiplier_f64();
        let ask1 = self.asks.first().cloned().unwrap_or_default();
        let bid1 = self.bids.first().cloned().unwrap_or_default();
        BboTicker {
            symbol: symbol.into(),
            bid_price: bid1.p,
            ask_price: ask1.p,
            timestamp: (self.update * 1000.0) as i64,
            bid_qty: bid1.s * multiplier,
            ask_qty: ask1.s * multiplier,
        }
    }
}

#[derive(Deserialize, Clone, Default)]
#[cfg_attr(test, derive(PartialEq, Debug))]
pub struct OrderBookItem {
    /// price
    #[serde(deserialize_with = "de_from_str")]
    pub(crate) p: f64,
    // size
    pub(crate) s: f64,
}

impl OrderBookItem {
    pub fn convert(self, multiplier: f64) -> (f64, f64) {
        (self.p, self.s * multiplier)
    }

    pub fn covert_to_depth_entry(self, multiplier: f64) -> DepthEntry {
        DepthEntry {
            price: self.p,
            amount: self.s * multiplier,
        }
    }
}

impl From<OrderBookItem> for DepthEntry {
    fn from(v: OrderBookItem) -> Self {
        Self {
            price: v.p,
            amount: v.s,
        }
    }
}

#[derive(Deserialize, Clone)]
pub struct OrderBooks {
    pub contract: GateSymbol,
    #[serde(rename = "t")]
    pub timestamp: i64,
    pub asks: Vec<OrderBookItem>,
    pub bids: Vec<OrderBookItem>,
}

impl From<OrderBooks> for BboTicker {
    fn from(value: OrderBooks) -> Self {
        BboTicker {
            symbol: value.contract.into(),
            bid_price: value.bids.first().cloned().unwrap_or_default().p,
            ask_price: value.asks.first().cloned().unwrap_or_default().p,
            timestamp: value.timestamp,
            bid_qty: value.bids.first().cloned().unwrap_or_default().s,
            ask_qty: value.asks.first().cloned().unwrap_or_default().s,
        }
    }
}

impl From<OrderBooks> for Depth {
    fn from(value: OrderBooks) -> Self {
        let mul = value.contract.multiplier();
        Self {
            symbol: value.contract.0,
            bids: value
                .bids
                .into_iter()
                .map(|o| o.covert_to_depth_entry(mul))
                .collect(),
            asks: value
                .asks
                .into_iter()
                .map(|o| o.covert_to_depth_entry(mul))
                .collect(),
            timestamp: value.timestamp,
        }
    }
}

#[derive(Deserialize)]
pub struct OrderBookUpdate {
    #[serde(rename = "s")]
    pub symbol: GateSymbol,
    // #[serde(rename = "t")]
    // time_ms: u64,
    #[serde(rename = "U")]
    pub first_id: u64,
    #[serde(rename = "u")]
    pub last_id: u64,
    pub a: Vec<OrderBookItem>,
    pub b: Vec<OrderBookItem>,
}

#[derive(Deserialize, Debug)]
pub struct Contract {
    pub name: GateSymbol,
    #[serde(deserialize_with = "de_from_str")]
    pub quanto_multiplier: f64,
    /// inverse 反向合约, direct 正向合约
    /// 目前gate查询settle固定传入USDT只返回u本位正向合约所以代码没有过滤
    pub r#type: String,
    pub position_size: Option<i64>,
    #[serde(deserialize_with = "de_from_str")]
    pub maintenance_rate: f64,
    order_size_min: u32,
    // order_size_max: u32,
    #[serde(deserialize_with = "de_from_str")]
    pub order_price_round: f64,
    // last_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub mark_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub funding_rate: f64,
    /// second
    pub funding_interval: u32,
    /// second
    pub funding_next_apply: f64,

    // 最大价格偏离比例，类似币安 marketTakeBound 字段
    // pub order_price_deviate: f64,

    // 在没有调用 更新仓位风险限额/更新双仓模式下的风险限额 接口前，仓位的默认最大下单+持仓价值就是 risk_limit_base
    // 通常 risk_limit_base < risk_limit_max < 10倍率杠杆的risk_limit_tiers
    #[serde(deserialize_with = "de_from_str")]
    pub risk_limit_base: f64,
    // pub risk_limit_step: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub risk_limit_max: f64,
    /// 合约已下线
    pub in_delisting: bool,

    #[serde(deserialize_with = "de_from_str")]
    pub leverage_max: u8,

    #[serde(deserialize_with = "de_from_str")]
    pub funding_cap_ratio: f64,
}
impl From<Contract> for Instrument {
    fn from(v: Contract) -> Self {
        Self {
            symbol: v.name.into(),
            price_tick: v.order_price_round,
            amount_tick: 1.,
            price_precision: -v.order_price_round.log10().trunc() as i32,
            amount_precision: (-v.quanto_multiplier.log10().trunc() as i32).max(0),
            min_qty: v.order_size_min as f64,
            min_notional: 0.,
            price_multiplier: 1.,
            amount_multiplier: 1. / v.quanto_multiplier,
            state: InsState::Normal, // unopen instrument had been filtered
        }
    }
}

impl From<Contract> for Funding {
    fn from(v: Contract) -> Self {
        // funding_cap_ratio	string	false	none
        // 资金费率上限的系数。资金费率上限 = (1/市场最大杠杆 - 维持保证金率) * funding_cap_ratio
        let funding_max = 1. / (v.leverage_max as f64 - v.maintenance_rate) * v.funding_cap_ratio;
        Funding {
            symbol: v.name.into(),
            min_funding_rate: -funding_max,
            max_funding_rate: funding_max,
            funding_rate: v.funding_rate,
            next_funding_at: (v.funding_next_apply * 1000.0).trunc() as i64,
            funding_interval: Some((v.funding_interval / 3600) as u8),
        }
    }
}

// impl From<Contract> for MaxPosition {
//     fn from(value: Contract) -> Self {
//         Self {
//             max_leverage: value.leverage_max,
//             max_sz: MaxSz::Notional(value.risk_limit_base),
//         }
//     }
// }
// in_delisting=true
// 并且
// position_size>0时候表示该合约
// 处于下线过渡期
// `in_delisting=true" #且
// position_size=0时候表示该合约
// 处于下线状态

impl Contract {
    pub fn is_open(&self) -> bool {
        !self.in_delisting || self.position_size.unwrap_or(0) > 0
    }
}

#[derive(Deserialize)]
#[allow(dead_code)]
pub struct GateTicker {
    pub contract: GateSymbol,
    #[serde(deserialize_with = "de_from_str")]
    high_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    low_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    last: f64,
    #[serde(deserialize_with = "de_from_str")]
    volume_24h_base: f64,
    #[serde(deserialize_with = "de_from_str")]
    volume_24h_quote: f64,
    #[serde(deserialize_with = "de_from_str")]
    change_percentage: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub mark_price: f64,
    /// rest only 字段，ws 无该字段
    #[serde(default)]
    #[serde(deserialize_with = "de_from_str")]
    pub highest_bid: f64,
    #[serde(default)]
    #[serde(deserialize_with = "de_from_str")]
    pub lowest_ask: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub funding_rate: f64,
    /// 下一周期预测资金费率
    // #[serde(deserialize_with = "de_from_str")]
    // funding_rate_indicative: f64,
    #[serde(deserialize_with = "de_from_str")]
    index_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    total_size: f64,
}
impl From<GateTicker> for Ticker {
    fn from(v: GateTicker) -> Self {
        let price_change = (v.last * v.change_percentage / 100.0).trunc();
        Self {
            symbol: v.contract.into(),
            high: v.high_24h,
            low: v.low_24h,
            open: v.last - price_change,
            close: v.last,
            volume: v.volume_24h_base,
            quote_volume: v.volume_24h_quote,
            change: price_change,
            change_percent: v.change_percentage,
            timestamp: 0,
        }
    }
}

impl From<GateTicker> for Funding {
    fn from(value: GateTicker) -> Self {
        Self {
            symbol: value.contract.clone().into(),
            funding_rate: value.funding_rate,
            ..Default::default()
        }
    }
}

impl From<&GateTicker> for MarkPrice {
    fn from(value: &GateTicker) -> Self {
        Self {
            symbol: value.contract.clone().into(),
            price: value.mark_price,
        }
    }
}

#[derive(Serialize, Deserialize, Default, Debug, Clone, Copy, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum GateTimeInForce {
    /// GoodTillCancelled 直到全部成交, 合约市价单不能用 Gtc
    Gtc,
    /// ImmediateOrCancelled 立即成交或者取消，只吃单不挂单，无法立即成交的部分就撤销
    #[default]
    Ioc,
    /// PendingOrCancelled，被动委托，只挂单不吃单
    Poc,
    // 合约市价下单不能用, 市价下单时能够全部撮合成交，否则整个订单将被取消
    Fok,
}

impl From<TimeInForce> for GateTimeInForce {
    fn from(value: TimeInForce) -> Self {
        match value {
            TimeInForce::GTC => Self::Gtc,
            TimeInForce::IOC => Self::Ioc,
            TimeInForce::FOK => Self::Fok,
            TimeInForce::PostOnly => Self::Poc,
        }
    }
}

impl From<GateTimeInForce> for TimeInForce {
    fn from(value: GateTimeInForce) -> Self {
        match value {
            GateTimeInForce::Gtc => Self::GTC,
            GateTimeInForce::Ioc => Self::IOC,
            GateTimeInForce::Fok => Self::FOK,
            GateTimeInForce::Poc => Self::PostOnly,
        }
    }
}

#[derive(Deserialize, Default, Clone, Debug, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum FinishAs {
    /// rest接口当前委托没有finish_as字段，给一个默认值
    #[default]
    Open,

    #[serde(rename = "_new")]
    New,
    #[serde(rename = "_update")]
    Update,

    Filled,
    Cancelled,
    Liquidated,
    Ioc,
    AutoDeleveraged,
    ReduceOnly,
    PositionClosed,
    ReduceOut,
    Stp,
}

#[derive(Deserialize, Clone, Debug, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum GateOrderStatus {
    Open,
    Finished,
}

#[derive(Deserialize, Debug)]
pub struct GateFill {
    #[serde(deserialize_with = "de_id_from_any")]
    pub id: u64,
    pub create_time_ms: i64,
    pub contract: GateSymbol,
    pub order_id: String,
    pub size: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub price: f64,
    #[serde(rename = "text")]
    pub client_order_id: String,
    pub fee: f64,
}

impl From<GateFill> for Order {
    fn from(value: GateFill) -> Self {
        let v = value;
        let order_type = OrderType::Limit;
        let side = if v.size < 0.0 {
            OrderSide::Sell
        } else {
            OrderSide::Buy
        };
        let pos_side = if v.size < 0.0 {
            PosSide::Short
        } else {
            PosSide::Long
        };
        let amount = v.size.abs() * v.contract.multiplier();
        let filled = v.size.abs() * v.contract.multiplier_f64();
        Order {
            symbol: v.contract.into(),
            id: v.id.to_string(),
            cid: Some(v.client_order_id),
            amount: Some(amount),
            filled: filled.abs(),
            side,
            pos_side: Some(pos_side),
            time_in_force: TimeInForce::IOC,
            source: OrderSource::UserTrade,
            timestamp: v.create_time_ms,
            order_type,
            price: Some(v.price),
            filled_avg_price: v.price,
            quote_amount: None,
            status: OrderStatus::PartiallyFilled,
        }
    }
}

/*
rest only fields: {'biz_info', 'stp_id', 'auto_size', 'amend_text', 'reduce_only', 'stp_act', 'close'}
ws only fields:   {'finish_time_ms', 'create_time_ms', 'refr'}
*/
#[derive(Deserialize, Clone, Debug)]
pub struct GateOrder {
    pub contract: GateSymbol,
    /// 单位为多少张合约，目前一张合约等于万分之一个 btc。正数为买入，负数为卖出。平仓委托则设置为0
    pub size: i32,
    pub price: f64,
    pub tif: GateTimeInForce,

    #[serde(rename = "text")]
    pub client_order_id: Option<String>,
    #[serde(deserialize_with = "de_id_from_any")]
    pub id: u64,
    pub left: i32,
    /// 订单完成原因
    #[serde(default)]
    pub finish_as: FinishAs,
    pub status: GateOrderStatus,
    pub finish_time_ms: i64,
    // pub finish_time: f64,
    // ws only
    // #[serde(default)]
    // pub finish_time_ms: u64,
    pub is_liq: bool,
    pub is_close: bool,
    pub is_reduce_only: bool,
    pub fill_price: f64,
    // pub user: String,
}
impl GateOrder {
    #[inline]
    fn is_close(&self) -> bool {
        self.is_close || self.is_reduce_only
    }
    #[inline]
    pub fn filled_size(&self) -> i32 {
        (self.size.abs() - self.left.abs()).abs()
    }
    #[must_use]
    pub fn into_order(self) -> Order {
        let v = self;

        let order_type = if v.price == 0.0 && v.tif == GateTimeInForce::Ioc {
            OrderType::Market
        } else {
            OrderType::Limit
        };
        let mul = v.contract.multiplier();
        let mul_f64 = v.contract.multiplier_f64();
        let amount = v.size.abs() as f64 * mul;
        let filled = f64::from((v.size.abs() - v.left.abs()).abs()) * mul_f64;
        let (side, pos_side) = if v.size < 0 {
            if v.is_close() {
                (OrderSide::Sell, PosSide::Long)
            } else {
                (OrderSide::Sell, PosSide::Short)
            }
        } else if v.is_close() {
            (OrderSide::Buy, PosSide::Short)
        } else {
            (OrderSide::Buy, PosSide::Long)
        };
        let status = if v.status == GateOrderStatus::Open {
            if v.left == v.size {
                OrderStatus::Open
            } else {
                OrderStatus::PartiallyFilled
            }
        } else {
            match v.finish_as {
                FinishAs::Cancelled => OrderStatus::Canceled,
                FinishAs::Ioc => {
                    if v.left != 0 {
                        OrderStatus::Canceled
                    } else {
                        OrderStatus::Filled
                    }
                }
                _ => {
                    if v.left != 0 {
                        OrderStatus::Canceled
                    } else {
                        OrderStatus::Filled
                    }
                }
            }
        };

        Order {
            symbol: v.contract.into(),
            id: v.id.to_string(),
            price: Some(v.price),
            filled_avg_price: v.fill_price,
            amount: Some(amount),
            quote_amount: None,
            filled: filled.abs(),
            side,
            pos_side: Some(pos_side),
            time_in_force: v.tif.into(),
            status,
            cid: v.client_order_id,
            timestamp: v.finish_time_ms,
            order_type,
            source: OrderSource::Order,
        }
    }
}

#[derive(Serialize, Debug)]
pub struct GateOrderReq {
    pub contract: GateSymbol,
    /// 单位为多少张合约，目前一张合约等于万分之一个 btc。正数为买入，负数为卖出。平仓委托则设置为0
    pub size: i32,
    pub price: f64,
    pub tif: GateTimeInForce,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reduce_only: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    #[serde(rename = "text")]
    pub client_order_id: Option<String>,
}
/*
例子
开空: size=-4, reduce_only=false
平空: size=6 , reduce_only=true
开多: {"contract":"ETH_USDT","price":0,"size":"6","reduce_only":false}:
平多: {"contract":"ETH_USDT","price":0,"size":"-9","reduce_only":true}:

market order without IOC or FOK
*/
impl GateOrderReq {
    pub(crate) fn new(v: Order, is_dual_side: bool) -> Result<Self, Error> {
        debug_assert!(
            v.amount.is_some() && v.amount.unwrap() > 0.,
            "Order.amount should > 0"
        );
        if is_dual_side && v.pos_side.is_none() {
            return Err(QuantError::parameter_error(
                "pos_side is required in dual side mode.",
            ));
        }

        let symbol = GateSymbol::from(v.symbol.clone());
        let size = if is_dual_side && v.pos_side.as_ref().unwrap() == &PosSide::Short
            || (!is_dual_side && v.side == OrderSide::Sell)
        {
            -v.amount.unwrap().round()
        } else {
            (v.amount.unwrap()).round()
        };

        let reduce_only = v.reduce_only();

        let mut self_ = Self {
            contract: symbol,
            size: size as i32,
            price: v.price.unwrap_or_default(),
            client_order_id: v.cid,
            tif: if v.order_type.is_market() {
                GateTimeInForce::Ioc
            } else {
                v.time_in_force.into()
            },
            reduce_only: None,
        };

        if reduce_only {
            self_.reduce_only = Some(true);
            // 平空仓要传正的张数，平多要传负的张数
            if (v.side == OrderSide::Buy && self_.size < 0)
                || (v.side == OrderSide::Sell && self_.size > 0)
            {
                self_.size *= -1;
            }
        }

        Ok(self_)
    }
}

#[derive(Serialize)]
pub struct AmendOrderReq {
    pub price: Option<String>,
    pub size: Option<i32>,
}

// 期货价格触发订单相关结构体（根据官方API文档重新设计）

#[derive(Serialize, Deserialize, Debug, Clone, Copy, PartialEq)]
#[repr(u8)]
pub enum FuturesTriggerStrategyType {
    PriceTriggered = 0,
    SpreadTriggered = 1,
}

#[derive(Serialize, Deserialize, Debug, Clone, Copy, PartialEq)]
#[repr(u8)]
pub enum FuturesPriceType {
    LastPrice = 0,
    MarkPrice = 1,
    IndexPrice = 2,
}

#[derive(Serialize, Deserialize, Debug, Clone, Copy, PartialEq)]
#[repr(u8)]
pub enum FuturesTriggerRule {
    GreaterOrEqual = 1,
    LessOrEqual = 2,
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "kebab-case")]
pub enum FuturesOrderType {
    CloseLongOrder,
    CloseShortOrder,
    CloseLongPosition,
    CloseShortPosition,
    PlanCloseLongPosition,
    PlanCloseShortPosition,
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum AutoSizeType {
    CloseLong,
    CloseShort,
}

// initial对象
#[derive(Serialize, Debug)]
pub struct FuturesInitialOrder {
    pub contract: GateSymbol,
    pub size: i64,
    pub price: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tif: Option<GateTimeInForce>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub text: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reduce_only: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub close: Option<bool>,
}

// trigger对象
#[derive(Serialize, Debug)]
pub struct FuturesTrigger {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub strategy_type: Option<i8>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub price_type: Option<i8>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub price: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rule: Option<i8>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub expiration: Option<u64>,
}

// 完整的期货价格触发订单请求
#[derive(Serialize, Debug)]
pub struct GateSwapPriceOrderReq {
    pub initial: FuturesInitialOrder,
    pub trigger: FuturesTrigger,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub order_type: Option<FuturesOrderType>,
}

// 响应结构体
#[derive(Deserialize, Debug)]
pub struct FuturePriceOrderRsp {
    pub id: i64,
}

impl GateSwapPriceOrderReq {
    pub fn new(
        trigger_price: f64,
        trigger_rule: FuturesTriggerRule,
        price_type: Option<FuturesPriceType>,
        order_type: FuturesOrderType,
        symbol: GateSymbol,
        is_dual_side: bool,
    ) -> Result<Self, Error> {
        // 当price = 0， 使用市价
        let close = if !is_dual_side { Some(true) } else { None };
        Ok(GateSwapPriceOrderReq {
            initial: FuturesInitialOrder {
                contract: symbol,
                size: 0,                // 全平
                price: "0".to_string(), // limit_price.unwrap_or(0.0).to_string(), // 限价止盈
                text: None,
                tif: Some(GateTimeInForce::Ioc),
                reduce_only: None,
                close,
            },
            trigger: FuturesTrigger {
                strategy_type: Some(FuturesTriggerStrategyType::PriceTriggered as i8),
                price_type: price_type.map(|p| p as i8),
                price: Some(trigger_price.to_string()),
                rule: Some(trigger_rule as i8),
                expiration: None,
            },
            order_type: Some(order_type),
        })
    }
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
pub struct CloseOrderHistory {
    pub contract: GateSymbol,
    side: String,
    #[serde(default)]
    long_price: f64,
    #[serde(default)]
    short_price: f64,
    #[serde(default)]
    #[serde(deserialize_with = "de_from_str")]
    accum_size: i32,
    /// 包含开仓和平仓手续费
    pnl: f64,
    /// seconds
    pub time: i64,
}

#[derive(Serialize)]
pub struct TimeRangeReq {
    pub from: Option<u64>,
    pub to: Option<u64>,
    pub limit: Option<u32>,
}

#[derive(Deserialize, Debug, Clone)]
pub struct PosTier {
    pub tier: u8,
    #[serde(deserialize_with = "de_from_str")]
    pub risk_limit: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub initial_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub maintenance_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub leverage_max: f64,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub contract: Option<GateSymbol>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct GateLiqOrder {
    pub time: i64,
    pub contract: GateSymbol,
    pub order_id: u64,

    /// 仓位大小
    pub size: f64,

    pub leverage: f64,
    pub margin: f64,

    /// 开仓价
    pub entry_price: f64,
    /// 标记价格
    pub mark_price: f64,

    /// 平均吃单成交价
    pub fill_price: f64,

    /// 强平价格
    pub liq_price: f64,

    pub left: i64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct GateAdlOrder {
    pub time: i32,
    pub contract: GateSymbol,
    pub order_id: u64,
    pub entry_price: f64,
    pub fill_price: f64,
    pub position_size: f64,
    pub trade_size: f64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct GateAccountInfo {
    balances: FxHashMap<String, UnifiedBalance>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct UnifiedBalance {
    #[serde(deserialize_with = "de_from_str")]
    available: f64,
    #[serde(deserialize_with = "de_from_str")]
    equity: f64,
    // total_freeze: f64,
}

impl GateAccountInfo {
    pub fn get_balances(self) -> Vec<Balance> {
        self.balances
            .into_iter()
            .map(|(k, v)| Balance {
                asset: k,
                balance: v.equity,
                available_balance: v.available,
                unrealized_pnl: 0.0,
            })
            .collect()
    }
}

#[derive(Default, Debug)]
pub struct LocalDepth {
    pub depth: Depth,
    pub order_book_is_initialized: bool,
    pub is_initialized: bool,
    pub last_update_id: u64,
}

impl LocalDepth {
    pub async fn update(&mut self, update: OrderBookUpdate) -> Result<Option<Depth>> {
        if !self.order_book_is_initialized {
            let depth: DepthRest = GateSwap::new(ExConfig::default())
                .await
                .depth(update.symbol.clone(), Some(50))
                .await?;
            self.last_update_id = depth.id;
            self.depth = depth.convert(update.symbol.clone());
            self.order_book_is_initialized = true;
        }
        if !self.is_initialized {
            if update.last_id <= self.last_update_id {
                return Ok(None);
            } else {
                self.is_initialized = true;
            }
        }
        self.order_book_update(update)
    }

    fn order_book_update(&mut self, update: OrderBookUpdate) -> Result<Option<Depth>> {
        let first_id = update.first_id;
        if self.last_update_id != 0 && first_id > self.last_update_id + 1 {
            self.is_initialized = false;
            self.order_book_is_initialized = false;
            return Ok(None);
        }
        self.last_update_id = update.last_id;
        let multiplier = update.symbol.multiplier();

        let asks = update
            .a
            .into_iter()
            .map(|x| x.convert(multiplier))
            .collect();
        let bids = update
            .b
            .into_iter()
            .map(|x| x.convert(multiplier))
            .collect();
        self.depth.update(asks, bids);

        Ok(Some(self.depth.clone()))
    }
}

#[derive(Deserialize, Debug)]
pub struct OrderRsp {
    #[serde(deserialize_with = "de_id_from_any")]
    pub id: u64,
}

#[derive(Deserialize)]
pub struct GateTrade {
    pub contract: GateSymbol,
    pub size: f64,
    #[serde(deserialize_with = "de_id_from_any")]
    pub id: u64,
    pub create_time: i64,
    pub create_time_ms: i64,
    pub price: String,
}

impl From<GateTrade> for Trade {
    fn from(v: GateTrade) -> Self {
        let multiplier = v.contract.multiplier();

        let symbol: Symbol = v.contract.into();

        Self {
            symbol,
            id: v.id.to_string(),
            amount: v.size.abs() * multiplier,
            price: v.price.parse().unwrap(),
            timestamp: v.create_time_ms,
            side: if v.size > 0. {
                OrderSide::Buy
            } else {
                OrderSide::Sell
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_round() {
        let mul = 0.01;
        assert_eq!((0.01_f64 / mul).round(), 1.0);
        assert_eq!((0.02_f64 / mul).round(), 2.0);
        assert_eq!((0.9_f64 / mul).round(), 90.0);
        assert_eq!((1.0_f64 / mul).round(), 100.0);
        assert_eq!((1.01_f64 / mul).round(), 101.0);
        assert_eq!((0.56_f64 / mul).round(), 56.0);
    }

    #[test]
    fn test_gate_fill_deserialize() {
        let json_data = r#"[{"id":"149350846","order_id":"56013521796172560", "contract":"DOGE_USDT", "create_time":1748329834, "create_time_ms":1748329834461,"size":-3,"role":"taker","price":"0.22599", "text": "api","fee":0.**********,"point_fee":0, "amend_text":"-","bizinfo":"-"}]"#;

        let fills: Result<Vec<GateFill>, _> = serde_json::from_str(json_data);

        match fills {
            Ok(fills) => {
                assert_eq!(fills.len(), 1);
                let fill = &fills[0];

                assert_eq!(fill.id, 149350846);
                assert_eq!(fill.order_id, "56013521796172560");
                assert_eq!(fill.contract.to_string(), "DOGE_USDT");
                assert_eq!(fill.create_time_ms, 1748329834461);
                assert_eq!(fill.size, -3.0);
                assert_eq!(fill.price, 0.22599);
                assert_eq!(fill.client_order_id, "api");
                assert_eq!(fill.fee, 0.**********);

                println!("反序列化成功: {fill:?}");
            }
            Err(e) => {
                panic!("反序列化失败: {e}");
            }
        }
    }
}

#[derive(Serialize)]
pub struct CurrencyReq {
    currency_pair: GateSymbol,
}

impl CurrencyReq {
    pub fn new(symbol: Symbol) -> Self {
        Self {
            currency_pair: symbol.into(),
        }
    }
}

#[derive(Deserialize)]
pub struct AccountFee {
    #[serde(deserialize_with = "de_from_str")]
    futures_maker_fee: f64,
    #[serde(deserialize_with = "de_from_str")]
    futures_taker_fee: f64,
}

impl From<AccountFee> for FeeRate {
    fn from(v: AccountFee) -> Self {
        Self {
            maker: v.futures_maker_fee,
            taker: v.futures_taker_fee,
            ..Default::default()
        }
    }
}

#[derive(Serialize)]
pub struct SubAccountReq {
    pub login_name: String,
    pub password: Option<String>,
    pub email: Option<String>,
}

impl SubAccountReq {
    pub fn new(login_name: String, password: Option<String>, email: Option<String>) -> Self {
        Self {
            login_name,
            password,
            email,
        }
    }
}

#[derive(Deserialize, Debug)]
pub struct SubAccount {
    pub login_name: String,
    pub password: Option<String>,
    pub email: Option<String>,
    pub state: u8,
    pub r#type: u8,
    pub user_id: u32,
    pub create_time: i64,
}

#[derive(Serialize)]
pub struct SubAccountKey {
    pub mode: u8,
    pub perms: Option<Vec<SubAccountPerm>>,
    pub ip_whitelist: Option<Vec<String>>,
}

impl SubAccountKey {
    pub fn new_all_perm(ip_whitelist: Option<Vec<String>>) -> Self {
        // wallet: 钱包
        // spot: 现货/杠杆
        // futures: 永续合约
        // delivery: 交割合约
        // earn: 理财
        // options: 期权
        // account: 账户信息
        // unified: 统一账户
        // loan: 借贷
        Self {
            mode: 1,
            perms: Some(vec![
                SubAccountPerm {
                    name: "wallet".to_string(),
                    read_only: None,
                },
                SubAccountPerm {
                    name: "spot".to_string(),
                    read_only: None,
                },
                SubAccountPerm {
                    name: "futures".to_string(),
                    read_only: None,
                },
                SubAccountPerm {
                    name: "delivery".to_string(),
                    read_only: None,
                },
                SubAccountPerm {
                    name: "earn".to_string(),
                    read_only: None,
                },
                SubAccountPerm {
                    name: "options".to_string(),
                    read_only: None,
                },
                SubAccountPerm {
                    name: "account".to_string(),
                    read_only: None,
                },
                SubAccountPerm {
                    name: "unified".to_string(),
                    read_only: None,
                },
                SubAccountPerm {
                    name: "loan".to_string(),
                    read_only: None,
                },
            ]),
            ip_whitelist,
        }
    }
}

#[derive(Serialize)]
pub struct SubAccountPerm {
    pub name: String,
    pub read_only: Option<bool>,
}

// 返回格式
// 状态码 200

// 名称	类型	描述
// » user_id	string	用户ID
// » mode	integer(int32)	模式 1 - 经典帐户 2 - 统一账户
// » name	string	API Key名称
// » perms	array
// »» name	string	权限功能名称（不传值即为清空）
// - wallet: 钱包
// - spot: 现货/杠杆
// - futures: 永续合约
// - delivery: 交割合约
// - earn: 理财
// - options: 期权
// - account: 账户信息
// - unified: 统一账户
// - loan: 借贷
// »» read_only	boolean	该功能是否只读
// » ip_whitelist	array	IP白名单列表（不传值即为清空）
// » key	string	API Key
// » state	integer(int32)	状态 1 - 正常 2 - 冻结 3 - 锁定
// » created_at	integer(int64)	创建时间
// » updated_at	integer(int64)	最近更新时间
// » last_access	integer(int64)	最近使用时间
#[derive(Deserialize, Debug)]
pub struct SubAccountKeyInfo {
    pub user_id: i64,
    pub mode: u8,
    pub name: String,
    #[serde(default, deserialize_with = "de_default_on_null")]
    pub ip_whitelist: Vec<String>,
    pub key: String,
    pub secret: String,
    pub state: u8,
    pub created_at: i64,
    pub updated_at: i64,
}

// » currency	body	string	是	转账货币名称
// » sub_account	body	string	是	子账号用户 ID
// » direction	body	string	是	资金流向，to - 转入子账号, from - 转出子账号
// » amount	body	string	是	转账额度
// » client_order_id	body	string	否	客户自定义ID，防止重复划转，字母（区分大小写）、数字、连字符'-'和下划线'_'的组合，可以是纯字母、纯数字且长度要在1-64位之间
// » sub_account_type	body	string	否	操作的子账号交易账户， spot - 现货账户， futures - 永续合约账户， delivery - 交割合约账户
#[derive(Serialize)]
pub struct SubTransferReq {
    pub currency: String,
    pub sub_account: String,
    pub direction: String,
    pub amount: f64,
    pub sub_account_type: String,
}

impl SubTransferReq {
    pub fn new(
        currency: String,
        sub_account: String,
        in_sub: bool,
        amount: f64,
        sub_account_type: String,
    ) -> Self {
        let direction = if in_sub { "to" } else { "from" };
        Self {
            currency,
            sub_account,
            direction: direction.to_string(),
            amount,
            sub_account_type,
        }
    }
}

#[derive(Deserialize)]
pub struct Account {
    pub user_id: i64,
}

#[derive(Serialize, Default)]
pub struct OrderReq {
    // 市场名称
    #[serde(skip_serializing_if = "Option::is_none")]
    pub contract: Option<GateSymbol>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub status: Option<String>,
}

#[derive(Deserialize, Debug)]
pub struct FundingRateRecord {
    pub r: String,
    pub t: u32,
}

#[derive(Deserialize, Clone, Debug)]
pub struct SpotGateOrder {
    pub contract: GateSymbol,
    /// 单位为多少张合约，目前一张合约等于万分之一个 btc。正数为买入，负数为卖出。平仓委托则设置为0
    pub size: i32,
    #[serde(deserialize_with = "de_from_str")]
    pub price: f64,
    pub tif: GateTimeInForce,

    #[serde(rename = "text")]
    pub client_order_id: Option<String>,
    pub id: i64,
    pub left: i32,
    /// 订单完成原因
    #[serde(default)]
    pub finish_as: FinishAs,
    pub status: GateOrderStatus,

    pub create_time: f64,
    // pub finish_time: f64,
    // ws only
    // #[serde(default)]
    // pub finish_time_ms: u64,
    pub is_liq: bool,
    pub is_close: bool,
    pub is_reduce_only: bool,
    #[serde(deserialize_with = "de_from_str")]
    pub fill_price: f64,
    // pub user: String,
}
impl SpotGateOrder {
    #[inline]
    fn is_close(&self) -> bool {
        self.is_close || self.is_reduce_only
    }
    #[inline]
    pub fn filled_size(&self) -> i32 {
        self.size - self.left
    }
    #[must_use]
    pub fn into_order(self) -> Order {
        let v = self;

        let order_type = if v.price == 0.0 && v.tif == GateTimeInForce::Ioc {
            OrderType::Market
        } else {
            OrderType::Limit
        };
        let mul = v.contract.multiplier();
        let mul_f64 = v.contract.multiplier_f64();
        let amount = v.size.abs() as f64 * mul;
        let filled = f64::from((v.size - v.left).abs()) * mul_f64;
        let (side, pos_side) = if v.size < 0 {
            if v.is_close() {
                (OrderSide::Sell, PosSide::Long)
            } else {
                (OrderSide::Sell, PosSide::Short)
            }
        } else if v.is_close() {
            (OrderSide::Buy, PosSide::Short)
        } else {
            (OrderSide::Buy, PosSide::Long)
        };
        let status = if v.status == GateOrderStatus::Open {
            if v.left == v.size {
                OrderStatus::Open
            } else {
                OrderStatus::PartiallyFilled
            }
        } else {
            match v.finish_as {
                FinishAs::Cancelled => OrderStatus::Canceled,
                FinishAs::Ioc => {
                    if v.left != 0 {
                        OrderStatus::Canceled
                    } else {
                        OrderStatus::Filled
                    }
                }
                _ => {
                    if v.left != 0 {
                        OrderStatus::Canceled
                    } else {
                        OrderStatus::Filled
                    }
                }
            }
        };

        Order {
            symbol: v.contract.into(),
            id: v.id.to_string(),
            price: Some(v.price),
            filled_avg_price: v.fill_price,
            amount: Some(amount),
            quote_amount: None,
            filled: filled.abs(),
            side,
            pos_side: Some(pos_side),
            time_in_force: v.tif.into(),
            status,
            cid: v.client_order_id,
            timestamp: v.create_time as i64 * 1000,
            order_type,
            source: OrderSource::Order,
        }
    }
}

#[derive(Deserialize, Debug)]
pub struct FundingChange {
    pub time: i64,
    pub change: String,
    pub balnace: String,
    pub text: String,
    pub contract: String, // symbol
    pub r#type: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct AccountReq {
    pub from: i64,
    pub to: i64,
    pub r#type: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct FeeReq {
    pub contract: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub from: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub to: Option<i64>,
    pub r#type: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct FeeRsp {
    pub time: i64,
    pub change: String,
    pub balance: String,
    pub text: String,
    pub contract: String, // symbol
    pub r#type: String,
}

impl FeeReq {
    pub fn new(symbol: Symbol, from: Option<i64>, to: Option<i64>) -> Self {
        Self {
            contract: symbol.to_string(),
            from,
            to,
            r#type: "fund".to_string(),
        }
    }
}

#[derive(Serialize)]
pub struct RiskLimitTierReq {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub contract: Option<GateSymbol>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub limit: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub offset: Option<u32>,
}

impl RiskLimitTierReq {
    pub fn new(symbol: Option<Symbol>, limit: Option<u32>, offset: Option<u32>) -> Self {
        Self {
            contract: symbol.map(|s| s.into()),
            limit,
            offset,
        }
    }
}

#[derive(Deserialize, Debug, Clone)]
pub struct GateKline {
    /// 时间戳
    #[serde(rename = "t")]
    pub timestamp: i64,
    /// 成交量
    #[serde(rename = "v")]
    pub volume: i64,
    /// 收盘价格
    #[serde(rename = "c", deserialize_with = "de_from_str")]
    pub close: f64,
    /// 最高价格
    #[serde(rename = "h", deserialize_with = "de_from_str")]
    pub high: f64,
    /// 最低价格
    #[serde(rename = "l", deserialize_with = "de_from_str")]
    pub low: f64,
    /// 开盘价格
    #[serde(rename = "o", deserialize_with = "de_from_str")]
    pub open: f64,
    /// 合约名称（包含时间间隔前缀，如 "1m_BTC_USD"）
    #[serde(rename = "n")]
    pub name: String,
    /// 成交原始币种数量
    #[serde(rename = "a", deserialize_with = "de_from_str")]
    pub amount: f64,
}

impl From<GateKline> for Kline {
    fn from(v: GateKline) -> Self {
        // 从名称中提取symbol，去掉时间间隔前缀
        let mut interval = KlineInterval::default();
        let symbol_str = if let Some(index) = v.name.find('_') {
            interval = KlineInterval::from_str(&v.name[..index]).unwrap_or_default();
            &v.name[index + 1..]
        } else {
            &v.name
        };

        let symbol: Symbol = serde_plain::from_str(symbol_str).unwrap_or_default();
        let mul = GateSymbol::from(symbol.clone()).multiplier_f64();
        let quote_volume = v.amount * v.close;
        Self {
            symbol,
            interval,
            candles: vec![Candle {
                close: v.close,
                timestamp: v.timestamp * 1000,
                open: v.open * mul,
                high: v.high * mul,
                low: v.low * mul,
                quote_volume,
                volume: v.amount * mul,
                ..Default::default()
            }],
        }
    }
}

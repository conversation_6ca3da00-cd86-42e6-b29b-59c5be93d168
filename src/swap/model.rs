use quant_common::base::{
    AccountInfo, AccountMode, Balance, BboTicker, Chain, Depth, DepthEntry, FeeRate, Funding,
    FundingFee, InsState, Instrument, KlineInterval, MarginMode, MarkPrice, Order, OrderSide,
    OrderSource, OrderStatus, OrderType, PosSide, Position, PrefixMulExt, RawValue, Symbol, Ticker,
    TimeInForce, Trade,
};
use quant_common::{Result, de_from_str, deserialize_f64_pairs, qerror, time_ms};

use serde::{Deserialize, Deserializer, Serialize};
use sonic_rs::Value;
use std::borrow::Cow;
use std::fmt::Display;
use std::str::FromStr;
use std::sync::Arc;

use crate::common::symbol::BbSymbol;

pub const EXCHANGE: &str = "BybitSwap";

pub const REST_BASE_HOST: &str = "api.bybit.com";
pub const REST_COLONY_BASE_HOST: &str = "api-colo.bybit.com"; // 假的
pub const REST_TEST_BASE_HOST: &str = "api-demo.bybit.com";

pub const WS_HOST: &str = "stream.bybit.com";
pub const WS_COLO_HOST: &str = "stream-colo.bybit.com"; // 假的
pub const WS_TEST_HOST: &str = "stream-testnet.bybit.com";

// pub const WS_URL: &str = "wss://stream.bybit.com/v5/public/linear";
// pub const WS_PRIVATE_URL: &str = "wss://stream.bybit.com/v5/private";
// pub const WS_TEST_URL: &str = "wss://stream-testnet.bybit.com/v5/public/linear";
// pub const WS_PRIVATE_TEST_URL: &str = "wss://stream-testnet.bybit.com/v5/private";

pub fn de_from_str_or_default<'de, T, D>(deserializer: D) -> Result<T, D::Error>
where
    D: Deserializer<'de>,
    T: FromStr + Default,
    <T as FromStr>::Err: Display,
{
    let s = String::deserialize(deserializer)?;
    Ok(s.parse::<T>().unwrap_or_default())
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbExecData {
    pub category: String,
    pub symbol: BbSymbol,
    #[serde(default)]
    pub is_leverage: String,
    pub order_id: String,
    pub order_link_id: String,
    pub side: OrderSide,
    #[serde(default)]
    pub order_price: String,
    #[serde(default)]
    pub order_qty: String,
    #[serde(default)]
    pub leaves_qty: String,
    #[serde(default)]
    pub create_type: String,
    #[serde(default)]
    pub order_type: String,
    #[serde(default)]
    pub stop_order_type: String,
    #[serde(default)]
    #[serde(deserialize_with = "de_from_str")]
    pub exec_fee: f64,
    pub exec_id: String,
    #[serde(deserialize_with = "de_from_str")]
    pub exec_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub exec_qty: f64,
    #[serde(default)]
    pub exec_type: String,
    #[serde(default)]
    pub exec_value: String,
    #[serde(deserialize_with = "de_from_str")]
    pub exec_time: i64,
    pub is_maker: bool,
    #[serde(default)]
    pub fee_rate: String,
    #[serde(default)]
    pub trade_iv: String,
    #[serde(default)]
    pub mark_iv: String,
    #[serde(default)]
    pub mark_price: String,
    #[serde(default)]
    pub index_price: String,
    #[serde(default)]
    pub underlying_price: String,
    #[serde(default)]
    pub block_trade_id: String,
    #[serde(default)]
    pub closed_size: String,
    pub seq: i64,
}

impl From<BbExecData> for FundingFee {
    fn from(exec: BbExecData) -> Self {
        Self {
            symbol: exec.symbol.inner,
            funding_fee: exec.exec_fee,
            timestamp: exec.exec_time,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ApiResponse<T> {
    pub ret_code: i32,
    pub ret_msg: String,
    pub result: Option<T>,
    pub ret_ext_info: Option<RawValue>,
    pub time: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbResult {}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbCList<T> {
    pub category: String,
    pub list: Vec<T>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbTickerInner {
    pub symbol: BbSymbol,
    #[serde(deserialize_with = "de_from_str")]
    pub last_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub prev_price_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub price_24h_pcnt: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub high_price_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub low_price_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub turnover_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub volume_24h: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub ask_1_size: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub bid_1_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub ask_1_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub bid_1_size: f64,

    #[serde(deserialize_with = "de_from_str")]
    pub mark_price: f64,

    #[serde(deserialize_with = "de_from_str")]
    pub funding_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub next_funding_time: i64,
}

impl From<BbTickerInner> for Ticker {
    fn from(bb: BbTickerInner) -> Self {
        let prefix_mul = bb.symbol.prefix_mul as f64;

        Ticker {
            symbol: bb.symbol.inner,
            timestamp: time_ms(),
            high: prefix_mul.price2base(bb.high_price_24h),
            low: prefix_mul.price2base(bb.low_price_24h),
            open: prefix_mul.price2base(bb.prev_price_24h),
            close: prefix_mul.price2base(bb.last_price),
            volume: prefix_mul.qty2base(bb.volume_24h),
            quote_volume: bb.turnover_24h,
            change: prefix_mul.price2base(bb.last_price - bb.prev_price_24h),
            change_percent: bb.price_24h_pcnt,
        }
    }
}

impl From<BbTickerInner> for BboTicker {
    fn from(bbticker: BbTickerInner) -> Self {
        let symbol = bbticker.symbol.inner;

        let prefix_mul = bbticker.symbol.prefix_mul as f64;

        BboTicker {
            symbol,
            bid_price: prefix_mul.price2base(bbticker.bid_1_price),
            bid_qty: prefix_mul.qty2base(bbticker.bid_1_size),
            ask_price: prefix_mul.price2base(bbticker.ask_1_price),
            ask_qty: prefix_mul.qty2base(bbticker.ask_1_size),
            timestamp: time_ms(),
        }
    }
}

impl From<BbTickerInner> for MarkPrice {
    fn from(bbticker: BbTickerInner) -> Self {
        let prefix_mul = bbticker.symbol.prefix_mul as f64;
        MarkPrice {
            symbol: bbticker.symbol.inner,
            price: prefix_mul.price2base(bbticker.mark_price),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbDepth {
    pub s: BbSymbol,
    pub a: Vec<Vec<String>>, // Ask
    pub b: Vec<Vec<String>>, // Bid
    pub ts: i64,             // Timestamp
    pub u: u64,              // Update
    pub seq: u64,            // Sequence
    pub cts: u64,            // Client Timestamp
}

impl From<BbDepth> for Depth {
    fn from(bb: BbDepth) -> Self {
        let symbol = bb.s.inner;
        let prefix_mul = bb.s.prefix_mul as f64;

        let bids =
            bb.b.iter()
                .filter_map(|entry| {
                    if entry.len() == 2 {
                        Some(DepthEntry {
                            price: prefix_mul.price2base(entry[0].parse().unwrap_or(0.0)),
                            amount: prefix_mul.qty2base(entry[1].parse().unwrap_or(0.0)),
                        })
                    } else {
                        None
                    }
                })
                .collect();

        let asks =
            bb.a.iter()
                .filter_map(|entry| {
                    if entry.len() == 2 {
                        Some(DepthEntry {
                            price: prefix_mul.price2base(entry[0].parse().unwrap_or(0.0)),
                            amount: prefix_mul.qty2base(entry[1].parse().unwrap_or(0.0)),
                        })
                    } else {
                        None
                    }
                })
                .collect();

        Depth {
            symbol,
            bids,
            asks,
            timestamp: bb.ts,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct LeverageFilter {
    pub min_leverage: String,
    pub max_leverage: String,
    pub leverage_step: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PriceFilter {
    #[serde(deserialize_with = "de_from_str")]
    pub min_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub max_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub tick_size: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct LotSizeFilter {
    #[serde(deserialize_with = "de_from_str")]
    pub min_order_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub qty_step: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbInstrumentInner {
    pub symbol: BbSymbol,
    contract_type: String,
    status: String,
    base_coin: String,
    quote_coin: String,
    launch_time: String,
    delivery_time: String,
    delivery_fee_rate: String,
    price_scale: String,
    leverage_filter: LeverageFilter,
    price_filter: PriceFilter,
    lot_size_filter: LotSizeFilter,
    unified_margin_trade: bool,
    pub funding_interval: i64,
    settle_coin: String,
    copy_trading: String,
    #[serde(deserialize_with = "de_from_str")]
    pub upper_funding_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub lower_funding_rate: f64,
    #[serde(default)]
    #[serde(deserialize_with = "de_from_str")]
    min_notional_value: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbInstrument {
    pub category: String,
    pub list: Vec<BbInstrumentInner>,
    pub next_page_cursor: String,
}

impl From<BbInstrumentInner> for Instrument {
    fn from(inner: BbInstrumentInner) -> Self {
        let prefix_mul = inner.symbol.prefix_mul as f64;
        Instrument {
            symbol: inner.symbol.inner,
            state: match inner.status.as_str() {
                "PreLaunch" => InsState::Close, // 根据需要调整
                "Trading" => InsState::Normal,
                "Delivering" => InsState::Normal,
                "Closed" => InsState::Close,
                _ => InsState::Close, // 默认值
            },
            price_tick: inner.price_filter.tick_size,
            amount_tick: inner.lot_size_filter.qty_step,
            price_precision: inner.price_filter.tick_size.log10() as i32,
            amount_precision: inner.lot_size_filter.qty_step.log10() as i32,
            min_qty: inner.lot_size_filter.min_order_qty,
            min_notional: inner.min_notional_value,
            price_multiplier: prefix_mul,
            amount_multiplier: 1.0 / prefix_mul,
        }
    }
}

impl From<BbTickerInner> for Funding {
    fn from(bb: BbTickerInner) -> Self {
        Funding {
            symbol: bb.symbol.inner,
            funding_rate: bb.funding_rate,
            next_funding_at: bb.next_funding_time,
            funding_interval: None,
            min_funding_rate: 0.0,
            max_funding_rate: 0.0,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbOrderResult {
    pub order_id: String,
    pub order_link_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbOrderListResultInner {
    pub category: String,
    pub symbol: String,
    pub order_id: String,
    pub order_link_id: String,
    pub create_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbPosition {
    pub position_idx: i32, // 持仓索引
    pub symbol: BbSymbol,  // 交易对符号
    pub side: OrderSide,   // 买卖方向 (Buy/Sell)
    #[serde(deserialize_with = "de_from_str")]
    pub size: f64, // 持仓数量
    #[serde(deserialize_with = "de_from_str")]
    pub avg_price: f64, // 平均价格
    #[serde(deserialize_with = "de_from_str")]
    pub leverage: u8, // 杠杆倍数
    #[serde(deserialize_with = "de_from_str")]
    pub unrealised_pnl: f64, // 未实现盈亏
    #[serde(deserialize_with = "de_from_str")]
    pub created_time: i64, // 创建时间
    pub seq: i64,          // 持仓序号
}

impl From<BbPosition> for Position {
    fn from(bb_pos: BbPosition) -> Self {
        let prefix_mul = bb_pos.symbol.prefix_mul as f64;

        Position {
            symbol: bb_pos.symbol.inner,
            timestamp: bb_pos.created_time,
            margin_mode: if bb_pos.position_idx == 0 {
                MarginMode::Isolated
            } else {
                MarginMode::Cross
            },
            side: get_pos_side(bb_pos.position_idx).unwrap_or_default(),
            leverage: bb_pos.leverage,
            amount: prefix_mul.qty2base(bb_pos.size),
            entry_price: prefix_mul.price2base(bb_pos.avg_price),
            unrealized_pnl: bb_pos.unrealised_pnl,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbBalance {
    // 账户余额相关信息
    pub total_equity: String, // 总资产

    // #[serde(rename = "accountIMRate")]
    // pub account_im_rate: String, // 账户IM利率
    // pub total_margin_balance: String,    // 总保证金余额
    // pub total_initial_margin: String,    // 总初始保证金
    // pub account_type: String,            // 账户类型
    pub total_available_balance: String, // 可用余额
    #[serde(rename = "accountMMRate")]
    pub account_mm_rate: String, // 账户MM利率
    #[serde(rename = "totalPerpUPL")]
    pub total_perp_upl: String, // 总永续盈亏
    // pub total_wallet_balance: String,    // 总钱包余额
    // pub total_maintenance_margin: String, // 总维持保证金
    pub coin: Vec<CoinBalance>, // 资产信息
}

impl From<&BbBalance> for Balance {
    fn from(value: &BbBalance) -> Self {
        Self {
            asset: "USD".to_string(),
            balance: value.total_equity.parse().unwrap_or_default(),
            available_balance: value.total_available_balance.parse().unwrap_or_default(),
            unrealized_pnl: value.total_perp_upl.parse().unwrap_or_default(),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CoinBalance {
    pub available_to_borrow: String, // 可借资产
    #[serde(deserialize_with = "de_from_str")]
    pub bonus: f64, // 奖励
    #[serde(deserialize_with = "de_from_str")]
    pub accrued_interest: f64, // 应计利息
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "totalOrderIM")]
    pub total_order_im: f64, // 总订单IM
    #[serde(deserialize_with = "de_from_str")]
    pub equity: f64, // 资产
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "totalPositionMM")]
    pub total_position_mm: f64, // 总仓位MM
    #[serde(deserialize_with = "de_from_str")]
    pub usd_value: f64, // USD价值
    #[serde(deserialize_with = "de_from_str")]
    pub spot_hedging_qty: f64, // 现货对冲数量
    #[serde(deserialize_with = "de_from_str")]
    pub unrealised_pnl: f64, // 浮动盈亏
    pub collateral_switch: bool,     // 保证金切换
    #[serde(deserialize_with = "de_from_str")]
    pub borrow_amount: f64, // 借款金额
    #[serde(deserialize_with = "de_from_str")]
    #[serde(rename = "totalPositionIM")]
    pub total_position_im: f64, // 总仓位IM
    #[serde(deserialize_with = "de_from_str")]
    pub wallet_balance: f64, // 钱包余额
    #[serde(deserialize_with = "de_from_str")]
    pub cum_realised_pnl: f64, // 累计实现盈亏
    #[serde(deserialize_with = "de_from_str")]
    pub locked: f64, // 锁仓量
    pub margin_collateral: bool,     // 保证金抵押
    pub coin: String,                // 资产类型
}

impl From<CoinBalance> for Balance {
    // 转换方法：将 BbBalance 转换为 Balance
    fn from(value: CoinBalance) -> Self {
        Self {
            asset: value.coin.to_string(),
            balance: value.equity,
            available_balance: 0.,
            unrealized_pnl: value.unrealised_pnl,
        }
    }
}

// availableWithdrawal	string	请求中第一个币种的可划转余额
// availableWithdrawalMap	Object	每个请求币种的可划转余额的对象。在映射中，键是请求的币种，值是相应的金额(字符串)
// 例如, "availableWithdrawalMap":{"BTC":"4.********","SOL":"33.********","XRP":"10805.********","ETH":"17.********"}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbAvailableWithdrawal {
    #[serde(deserialize_with = "de_from_str")]
    pub available_withdrawal: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbAccountBalance {
    pub account_type: String,   // 账户类型
    pub biz_type: i32,          // 业务类型
    pub account_id: String,     // 账户ID
    pub member_id: String,      // 会员ID
    pub balance: BbCoinBalance, // 资产信息
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbCoinBalance {
    pub coin: String, // 资产类型
    #[serde(deserialize_with = "de_from_str")]
    pub wallet_balance: f64, // 钱包余额
    #[serde(deserialize_with = "de_from_str")]
    pub transfer_balance: f64, // 转账余额
    #[serde(deserialize_with = "de_from_str")]
    pub bonus: f64, // 奖励
    pub transfer_safe_amount: String, // 转账安全金额
    pub ltv_transfer_safe_amount: String, // LTV转账安全金额
}

impl From<BbCoinBalance> for Balance {
    // 转换方法：将 BbAccountBalance 转换为 Balance
    fn from(value: BbCoinBalance) -> Self {
        Self {
            asset: value.coin,
            balance: f64::max(value.wallet_balance, 0.0001),
            available_balance: f64::max(value.transfer_balance, 0.0001),
            unrealized_pnl: 0.0001,
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BbFeeRate {
    pub symbol: String,
    pub taker_fee_rate: String,
    pub maker_fee_rate: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbExecution {
    pub symbol: String,
    pub order_id: String,
    pub order_link_id: String,
    pub side: String,
    pub order_price: String,
    pub order_qty: String,
    pub leaves_qty: String,
    pub create_type: String,
    pub order_type: String,
    pub stop_order_type: String,
    pub exec_fee: String,
    pub exec_id: String,
    pub exec_price: String,
    pub exec_qty: String,
    pub exec_type: String,
    pub exec_value: String,
    pub exec_time: String,
    pub fee_currency: String,
    pub is_maker: bool,
    pub fee_rate: String,
    pub trade_iv: String,
    pub mark_iv: String,
    pub mark_price: String,
    pub index_price: String,
    pub underlying_price: String,
    pub block_trade_id: String,
    pub closed_size: String,
    pub seq: i64,
}

impl From<BbExecution> for FundingFee {
    fn from(value: BbExecution) -> Self {
        Self {
            symbol: Symbol::default(),
            funding_fee: value.exec_fee.parse().unwrap_or_default(),
            timestamp: value.exec_time.parse().unwrap_or_default(),
        }
    }
}

impl From<BbFeeRate> for FeeRate {
    // 转换方法：将 BbBalance 转换为 Balance
    fn from(value: BbFeeRate) -> Self {
        Self {
            taker: value.taker_fee_rate.parse().unwrap_or_default(),
            maker: value.maker_fee_rate.parse().unwrap_or_default(),
            buyer: 0.0,
            seller: 0.0,
        }
    }
}

#[derive(Debug, PartialEq, Eq, Serialize, Deserialize, Clone)]
pub enum BbTimeInForce {
    GTC,
    IOC,
    FOK,
    PostOnly,
    RPI,
}

impl From<BbTimeInForce> for TimeInForce {
    fn from(bb_time_in_force: BbTimeInForce) -> Self {
        match bb_time_in_force {
            BbTimeInForce::GTC => TimeInForce::GTC,
            BbTimeInForce::IOC => TimeInForce::IOC,
            BbTimeInForce::FOK => TimeInForce::FOK,
            BbTimeInForce::PostOnly => TimeInForce::PostOnly,
            BbTimeInForce::RPI => TimeInForce::PostOnly,
        }
    }
}

impl From<TimeInForce> for BbTimeInForce {
    fn from(time_in_force: TimeInForce) -> Self {
        match time_in_force {
            TimeInForce::GTC => BbTimeInForce::GTC,
            TimeInForce::IOC => BbTimeInForce::IOC,
            TimeInForce::FOK => BbTimeInForce::FOK,
            TimeInForce::PostOnly => BbTimeInForce::PostOnly,
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct NewOrderRequest {
    pub category: Cow<'static, str>,
    pub symbol: String,
    pub side: String,
    pub order_type: String,
    pub qty: String,
    pub price: Option<String>,
    pub time_in_force: BbTimeInForce,
    pub position_idx: Option<i32>,
    pub order_link_id: Option<String>,
}

// impl From<Order> for NewOrderRequest {
//     fn from(order: Order) -> Self {
//         let time_in_force = BbTimeInForce::from(order.time_in_force);
//         let symbol =
//         Self {
//             category: "linear",
//             symbol: todo!(),
//             side: todo!(),
//             order_type: todo!(),
//             qty: todo!(),
//             price: todo!(),
//             time_in_force,
//             position_idx: todo!(),
//             order_link_id: todo!(),
//         }
//     }
// }

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct NewBatchOrderRequest {
    pub symbol: String,
    pub side: String,
    pub order_type: String,
    pub qty: String,
    pub price: Option<String>,
    pub time_in_force: Option<String>,
    pub position_idx: Option<i32>,
    pub order_link_id: Option<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct NewBatchOrderListRequest {
    pub category: String,
    pub request: Vec<NewBatchOrderRequest>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct NewOrderListRequest {
    pub category: String,
    pub request: Vec<NewOrderRequest>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct BbTickerSnapshot {
    pub topic: String,
    #[serde(rename = "type")]
    pub type_: String,
    pub data: BbTickerData,
    pub cs: u64,
    pub ts: i64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BbTickerData {
    pub symbol: BbSymbol,
    pub tick_direction: Option<String>,
    pub price_24h_pcnt: Option<String>,
    pub last_price: Option<String>,
    pub prev_price_24h: Option<String>,
    pub high_price_24h: Option<String>,
    pub low_price_24h: Option<String>,
    pub prev_price_1h: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub mark_price: f64,
    pub index_price: Option<String>,
    pub open_interest: Option<String>,
    pub open_interest_value: Option<String>,
    pub turnover_24h: Option<String>,
    pub volume_24h: Option<String>,
    #[serde(deserialize_with = "de_from_str")]
    pub next_funding_time: i64,
    #[serde(deserialize_with = "de_from_str")]
    pub funding_rate: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub bid_1_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub bid_1_size: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub ask_1_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub ask_1_size: f64,
}

impl From<BbTickerSnapshot> for BboTicker {
    fn from(b: BbTickerSnapshot) -> Self {
        let bb_ticker = b.data;
        let symbol = bb_ticker.symbol;
        let prefix_mul = symbol.prefix_mul as f64;

        let bid_price = prefix_mul.price2base(bb_ticker.bid_1_price);
        let bid_qty = prefix_mul.qty2base(bb_ticker.bid_1_size);

        let ask_price = prefix_mul.price2base(bb_ticker.ask_1_price);
        let ask_qty = prefix_mul.qty2base(bb_ticker.ask_1_size);

        BboTicker {
            symbol: symbol.inner,
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
            timestamp: b.ts,
        }
    }
}

impl From<BbTickerSnapshot> for MarkPrice {
    fn from(b: BbTickerSnapshot) -> Self {
        let bb_ticker = b.data;
        let symbol = bb_ticker.symbol;
        let prefix_mul = symbol.prefix_mul as f64;

        let mark_price = prefix_mul.price2base(bb_ticker.mark_price);

        MarkPrice {
            symbol: symbol.inner,
            price: mark_price,
        }
    }
}

impl From<BbTickerSnapshot> for Funding {
    fn from(b: BbTickerSnapshot) -> Self {
        let bb_ticker = b.data;
        let symbol = bb_ticker.symbol;

        let funding_rate = bb_ticker.funding_rate;
        let next_funding_time = bb_ticker.next_funding_time;

        Self {
            symbol: symbol.inner,
            funding_rate,
            next_funding_at: next_funding_time,
            funding_interval: None,
            min_funding_rate: 0.,
            max_funding_rate: 0.,
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
pub struct PublicTradeSnapshot {
    pub topic: String,
    pub r#type: String,
    pub ts: u64,
    pub data: Vec<PublicTradeData>,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct PublicTradeData {
    #[serde(rename = "T")]
    pub timestamp: i64,

    #[serde(rename = "s")]
    pub symbol: BbSymbol,

    #[serde(rename = "S")]
    pub side: OrderSide,

    #[serde(rename = "v")]
    #[serde(deserialize_with = "de_from_str")]
    pub amount: f64,

    #[serde(rename = "p")]
    #[serde(deserialize_with = "de_from_str")]
    pub price: f64,

    #[serde(rename = "L")]
    pub change_side: String,

    #[serde(rename = "i")]
    pub id: String,

    #[serde(rename = "BT")]
    pub big_trade: bool,
}

impl From<PublicTradeData> for Trade {
    fn from(value: PublicTradeData) -> Self {
        Trade {
            id: value.id,
            symbol: value.symbol.inner,
            timestamp: value.timestamp,
            price: value.price,
            amount: value.amount,
            side: value.side,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbOrderBookSnapshot {
    pub topic: String,
    pub r#type: String,
    pub ts: u64,
    pub data: BbOrderBookData,
    pub cts: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BbOrderBookData {
    pub s: BbSymbol,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    pub b: Vec<(f64, f64)>,
    #[serde(deserialize_with = "deserialize_f64_pairs")]
    pub a: Vec<(f64, f64)>,
    pub u: u64,   // Update ID
    pub seq: u64, // Sequence number
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbPriRsp<T> {
    // pub id: String,
    pub topic: String,
    pub data: T,
    #[serde(rename = "creationTime")]
    pub creation_time: u64,
}

impl From<BbOrderBookSnapshot> for Depth {
    fn from(value: BbOrderBookSnapshot) -> Self {
        Depth {
            symbol: Symbol::default(),
            bids: value.data.b.into_iter().map(DepthEntry::from).collect(),
            asks: value.data.a.into_iter().map(DepthEntry::from).collect(),
            timestamp: value.ts.try_into().unwrap_or_default(),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub enum BbOrderStatus {
    New,
    PartiallyFilled,
    Filled,
    Canceled,
    Rejected,
    Expired,
    PendingCancel,
}

impl From<BbOrderStatus> for OrderStatus {
    fn from(status: BbOrderStatus) -> Self {
        match status {
            BbOrderStatus::New => OrderStatus::Open,
            BbOrderStatus::PartiallyFilled => OrderStatus::PartiallyFilled,
            BbOrderStatus::Filled => OrderStatus::Filled,
            BbOrderStatus::Canceled => OrderStatus::Canceled,
            BbOrderStatus::Rejected => OrderStatus::Canceled,
            BbOrderStatus::Expired => OrderStatus::Canceled,
            BbOrderStatus::PendingCancel => OrderStatus::Canceled,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbOrderData {
    pub category: Option<String>,    // 产品类型
    pub order_id: String,            // 订单ID
    pub order_link_id: String,       // 用户自定义ID
    pub is_leverage: Option<String>, // 是否借贷 (仅适用于 spot 类型的统一账户)
    pub block_trade_id: String,      // 大宗交易订单ID
    pub symbol: BbSymbol,            // 合约名称
    #[serde(deserialize_with = "de_from_str")]
    pub price: f64, // 订单价格
    #[serde(deserialize_with = "de_from_str")]
    pub qty: f64, // 订单数量
    pub side: OrderSide,             // 方向: Buy 或 Sell
    pub position_idx: i32,           // 仓位标识
    pub order_status: BbOrderStatus, // 订单状态
    pub create_type: Option<String>, // 订单创建类型 (仅适用于 linear 或 inverse)
    pub cancel_type: Option<String>, // 订单被取消类型
    pub reject_reason: Option<String>, // 拒绝原因
    #[serde(deserialize_with = "de_from_str")]
    pub avg_price: f64, // 平均成交价格
    pub leaves_qty: Option<String>,  // 剩余未成交数量
    pub leaves_value: Option<String>, // 剩余未成交价值
    #[serde(deserialize_with = "de_from_str")]
    pub cum_exec_qty: f64, // 累计成交数量
    pub cum_exec_value: String,      // 累计成交价值
    pub cum_exec_fee: String,        // 累计成交手续费
    pub closed_pnl: Option<String>,  // 平仓盈亏
    pub fee_currency: Option<String>, // 手续费币种
    pub time_in_force: BbTimeInForce, // 执行策略
    pub order_type: OrderType,       // 订单类型: Market, Limit
    pub stop_order_type: Option<String>, // 条件单类型
    pub oco_trigger_by: Option<String>, // OCO订单的触发类型
    pub order_iv: Option<String>,    // 隐含波动率
    pub market_unit: Option<String>, // qty的单位选择
    pub trigger_price: Option<String>, // 触发价格
    pub take_profit: Option<String>, // 止盈价格
    pub stop_loss: Option<String>,   // 止损价格
    pub tpsl_mode: Option<String>,   // 止盈止损模式
    pub tp_limit_price: Option<String>, // 止盈转限价单价格
    pub sl_limit_price: Option<String>, // 止损转限价单价格
    pub tp_trigger_by: Option<String>, // 止盈的触发价格类型
    pub sl_trigger_by: Option<String>, // 止损的触发价格类型
    pub trigger_direction: i32,      // 触发方向
    pub trigger_by: Option<String>,  // 触发价格类型
    pub last_price_on_created: Option<String>, // 下单时市场价格
    pub reduce_only: bool,           // 是否仅减仓
    pub close_on_trigger: bool,      // 是否触发后平仓委托
    pub place_type: Option<String>,  // 下单方式 (期权)
    pub smp_type: Option<String>,    // SMP执行类型
    pub smp_group: i32,              // SMP组ID
    pub smp_order_id: Option<String>, // 触发SMP的交易对手订单ID
    pub created_time: String,        // 创建时间戳 (毫秒)
    pub updated_time: String,        // 更新时间戳 (毫秒)
}

impl From<BbOrderData> for Order {
    fn from(bb_order: BbOrderData) -> Self {
        let id = bb_order.order_id;
        // 客户自定义ID（此处可能为空）
        let cid = if bb_order.order_link_id.is_empty() {
            None
        } else {
            Some(bb_order.order_link_id)
        };
        let timestamp = bb_order.created_time.parse::<i64>().unwrap_or_default();

        let status = bb_order.order_status.into();

        // 交易对符号
        let symbol = bb_order.symbol.inner;

        let prefix_mul = bb_order.symbol.prefix_mul as f64;

        // 订单方向
        let side = bb_order.side;

        // 仓位方向
        let pos_side = get_pos_side(bb_order.position_idx);

        // 订单价格
        let price = prefix_mul.price2base(bb_order.price);

        // 基准货币数量
        let amount = prefix_mul.qty2base(bb_order.qty);

        // 已成交数量
        let filled = prefix_mul.qty2base(bb_order.cum_exec_qty);

        // 已成交均价
        let filled_avg_price = prefix_mul.price2base(bb_order.avg_price);

        // 创建 Order 实例
        Order {
            id,
            cid,
            timestamp,
            status,
            symbol,
            order_type: bb_order.order_type,
            side,
            pos_side,
            time_in_force: bb_order.time_in_force.into(),
            price: Some(price),
            amount: Some(amount),
            quote_amount: None,
            filled,
            filled_avg_price,
            source: OrderSource::Order,
        }
    }
}

fn get_pos_side(position_idx: i32) -> Option<PosSide> {
    match position_idx {
        1 => Some(PosSide::Long),
        2 => Some(PosSide::Short),
        _ => None,
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
struct AccountData {
    account_im_rate: String,
    account_mm_rate: String,
    total_equity: String,
    total_wallet_balance: String,
    total_margin_balance: String,
    total_available_balance: String,
    total_perp_upl: String,
    total_initial_margin: String,
    total_maintenance_margin: String,
    coin: Vec<CoinData>,
    account_ltv: String,
    account_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
struct CoinData {
    coin: String,
    equity: String,
    usd_value: String,
    wallet_balance: String,
    available_to_withdraw: String,
    available_to_borrow: String,
    borrow_amount: String,
    accrued_interest: String,
    total_order_im: String,
    total_position_im: String,
    total_position_mm: String,
    unrealised_pnl: String,
    cum_realised_pnl: String,
    bonus: String,
    collateral_switch: bool,
    margin_collateral: bool,
    locked: String,
    spot_hedging_qty: String,
}

impl From<CoinData> for Balance {
    fn from(coin_data: CoinData) -> Self {
        Balance {
            asset: coin_data.coin,
            balance: f64::max(coin_data.equity.parse().unwrap_or_default(), 0.0001),
            available_balance: f64::max(
                coin_data.available_to_withdraw.parse().unwrap_or_default(),
                0.0001,
            ),
            unrealized_pnl: coin_data.unrealised_pnl.parse().unwrap_or(0.0),
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct OrderRequestInner {
    pub symbol: String,
    pub order_id: Option<String>,
    pub order_link_id: Option<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct OrderRequest {
    pub category: String,
    pub request: Vec<OrderRequestInner>,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct BatchOrderInner {
    pub category: String,

    pub symbol: String,

    pub order_id: String,

    pub order_link_id: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct BbMsg {
    pub code: i32,
    pub msg: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct BbList<T> {
    pub list: Vec<T>,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct RiskResp {
    pub symbol: BbSymbol,
    #[serde(deserialize_with = "de_from_str")]
    pub risk_limit_value: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub max_leverage: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct AccountStatus {
    pub margin_mode: String,
    pub updated_time: String,
    pub unified_margin_status: i32,
    pub dcp_status: String,
    pub time_window: i32,
    pub smp_group: i32,
    pub is_master_trader: bool,
    pub spot_hedging_status: String,
}

impl From<AccountStatus> for AccountMode {
    fn from(status: AccountStatus) -> Self {
        match (status.margin_mode.as_str(), status.unified_margin_status) {
            (.., 1) => AccountMode::Classic,
            ("PORTFOLIO_MARGIN", ..) => AccountMode::Portfolio,
            (.., 3..=6) => AccountMode::SpotAndSwap,
            // 如果遇到未知情况，默认返回经典模式
            _ => AccountMode::Classic,
        }
    }
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct ChainConfig {
    pub coin: String,
    pub chain: String,
    pub coin_show_name: String,
    pub chain_type: String,
    pub block_confirm_number: u32,
    pub min_deposit_amount: String,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct ChainConfigList {
    pub config_list: Vec<ChainConfig>,
    pub next_page_cursor: String,
}

impl From<BbBalance> for AccountInfo {
    fn from(b: BbBalance) -> Self {
        Self {
            total_mmr: b.account_mm_rate.parse::<f64>().unwrap_or(0.0001),
            total_equity: b.total_equity.parse::<f64>().unwrap_or(0.0001),
            total_available: b.total_available_balance.parse::<f64>().unwrap_or(0.0),
        }
    }
}

#[derive(Debug, Serialize)]
pub struct BatchCancelOrderRequest {
    pub category: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub symbol: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub base_coin: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub settle_coin: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub order_filter: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub stop_order_type: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct BatchCancelOrderResponse {
    pub ret_code: i32,
    pub ret_msg: String,
    pub result: BatchCancelOrderResult,
    pub ret_ext_info: Value,
    pub time: i64,
}

#[derive(Debug, Deserialize)]
pub struct BatchCancelOrderResult {
    pub list: Vec<CancelOrderItem>,
    pub success: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CancelOrderItem {
    pub order_id: String,
    pub order_link_id: String,
}

#[derive(Debug, Deserialize)]
pub struct MarkPriceKlineResponse {
    pub category: String,
    pub symbol: String,
    pub list: Vec<MarkPriceKline>,
}

#[derive(Debug, Deserialize)]
pub struct MarkPriceKline {
    #[serde(rename = "0")]
    pub start_time: String,
    #[serde(rename = "1")]
    pub open_price: String,
    #[serde(rename = "2")]
    pub high_price: String,
    #[serde(rename = "3")]
    pub low_price: String,
    #[serde(rename = "4")]
    pub close_price: String,
}

// 添加响应数据结构
#[derive(Debug, Deserialize)]
pub struct DepositAddressResponse {
    pub coin: String,
    pub chains: Vec<ChainInfo>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct ChainInfo {
    #[serde(rename = "chainType")]
    pub chain_type: String,
    #[serde(rename = "addressDeposit")]
    pub address_deposit: String,
    #[serde(rename = "tagDeposit")]
    pub tag_deposit: String,
    #[serde(deserialize_with = "deserialize_chain_opt")]
    pub chain: Option<Chain>,
    #[serde(rename = "batchReleaseLimit")]
    pub batch_release_limit: String,
    #[serde(rename = "contractAddress")]
    pub contract_address: String,
}

/// 自定义反序列化函数：将未知链标识符映射为 None
fn deserialize_chain_opt<'de, D>(deserializer: D) -> Result<Option<Chain>, D::Error>
where
    D: Deserializer<'de>,
{
    let s: &str = Deserialize::deserialize(deserializer)?;
    match s {
        "ETH" => Ok(Some(Chain::Erc20)),
        "TRX" => Ok(Some(Chain::Trc20)),
        "BSC" => Ok(Some(Chain::Bep20)),
        "SOL" => Ok(Some(Chain::Sol)),
        "MATIC" => Ok(Some(Chain::Polygon)),
        "ARBI" => Ok(Some(Chain::ArbitrumOne)),
        "OP" => Ok(Some(Chain::Optimism)),
        "TON" => Ok(Some(Chain::Ton)),
        "CAVAX" => Ok(Some(Chain::AVAXC)),
        _ => Ok(None), // 未知值返回 None
    }
}

/// K线响应数据结构
#[derive(Debug, Deserialize)]
pub struct BbKlineResponse {
    pub category: String,
    pub symbol: String,
    pub list: Vec<Vec<String>>, // 字符串数组构成的K线数据
}

/// 将Bybit的K线间隔转换为系统K线间隔
pub fn convert_interval(interval: &str) -> Result<KlineInterval> {
    match interval {
        "1" => Ok(KlineInterval::Min1),
        "3" => Ok(KlineInterval::Min3),
        "5" => Ok(KlineInterval::Min5),
        "15" => Ok(KlineInterval::Min15),
        "30" => Ok(KlineInterval::Min30),
        "60" => Ok(KlineInterval::Hour1),
        "120" => Ok(KlineInterval::Hour2),
        "240" => Ok(KlineInterval::Hour4),
        "360" => Ok(KlineInterval::Hour6),
        "720" => Ok(KlineInterval::Hour12),
        "D" => Ok(KlineInterval::Day1),
        "W" => Ok(KlineInterval::Week1),
        "M" => Ok(KlineInterval::Month1),
        _ => Err(qerror!("不支持的K线间隔: {}", interval)),
    }
}

/// 将系统K线间隔转换为Bybit的K线间隔
pub fn convert_interval_to_bybit(interval: &KlineInterval) -> Result<String> {
    match interval {
        KlineInterval::Min1 => Ok("1".to_string()),
        KlineInterval::Min3 => Ok("3".to_string()),
        KlineInterval::Min5 => Ok("5".to_string()),
        KlineInterval::Min15 => Ok("15".to_string()),
        KlineInterval::Min30 => Ok("30".to_string()),
        KlineInterval::Hour1 => Ok("60".to_string()),
        KlineInterval::Hour2 => Ok("120".to_string()),
        KlineInterval::Hour4 => Ok("240".to_string()),
        KlineInterval::Hour6 => Ok("360".to_string()),
        KlineInterval::Hour8 => Err(qerror!("不支持的K线间隔: {}", interval)), // Bybit不支持8小时
        KlineInterval::Hour12 => Ok("720".to_string()),
        KlineInterval::Day1 => Ok("D".to_string()),
        KlineInterval::Day3 => Err(qerror!("不支持的K线间隔: {}", interval)), // Bybit不支持3天
        KlineInterval::Week1 => Ok("W".to_string()),
        KlineInterval::Month1 => Ok("M".to_string()),
    }
}

#[derive(Debug, Deserialize)]
pub struct BbKlineWsResponse {
    pub topic: String,
    #[serde(rename = "type")]
    pub type_: String,
    pub ts: u64,
    pub data: Vec<BbKlineData>,
}

#[derive(Debug, Deserialize)]
pub struct BbKlineData {
    pub start: u64,
    pub end: u64,
    pub interval: String,
    #[serde(deserialize_with = "de_from_str")]
    pub open: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub close: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub high: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub low: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub volume: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub turnover: f64,
    pub confirm: bool,
    pub timestamp: u64,
}

#[derive(Debug, PartialEq, Eq, Serialize, Deserialize, Clone)]
pub struct KlineWsParams {
    pub symbols: Arc<Vec<Symbol>>,
    pub interval: KlineInterval,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BbFastExecData {
    pub symbol: BbSymbol,
    pub order_id: String,
    // pub is_maker: bool,
    // pub order_link_id: String,
    pub side: OrderSide,
    // pub exec_id: String,
    #[serde(deserialize_with = "de_from_str")]
    pub exec_price: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub exec_qty: f64,
    #[serde(deserialize_with = "de_from_str")]
    pub exec_time: i64,
}

impl From<BbFastExecData> for Order {
    fn from(data: BbFastExecData) -> Self {
        Self {
            id: data.order_id,
            cid: None,
            timestamp: data.exec_time,
            status: OrderStatus::PartiallyFilled,
            symbol: data.symbol.inner,
            order_type: OrderType::Limit,
            side: data.side,
            pos_side: None,
            time_in_force: TimeInForce::GTC,
            price: None,
            amount: None,
            quote_amount: None,
            filled: data.exec_price,
            filled_avg_price: data.exec_qty,
            source: OrderSource::UserTrade,
        }
    }
}

use binance::future::ws::BinanceFutureWs;
// use apex::swap::ws::ApexSwapWs;
// use apex::swap::ws::ApexSwapWs;
use binance::margin::ws::BinanceMarginWs;
use binance::spot::ws::BinanceSpotWs;
use binance::swap::ws::BinanceSwapWs;
use bingx::spot::ws::BingxSpotWs;
use bingx::swap::ws::BingxSwapWs;
use bitget::margin::ws::BitgetMarginWs;
use bitget::spot::ws::BitgetSpotWs;
use bitget::swap::ws::BitgetSwapWs;
use bitmart::spot::ws::BitmartSpotWs;
use bitmart::swap::ws::BitmartSwapWs;
use bitmex::spot::ws::BitmexSpotWs;
use bitmex::swap::ws::BitmexSwapWs;
use bybit::spot::ws::BybitSpotWs;
use bybit::swap::ws::BybitSwapWs;
use coinbase::spot::ws::CoinbaseSpotWs;
use coinex::spot::ws::CoinexSpotWs;
use coinex::swap::ws::CoinexSwapWs;
use coinw::spot::ws::CoinwWs as CoinwSpotWs;
use coinw::swap::ws::CoinwWs as CoinwSwapWs;
use crypto::spot::ws::CryptoSpotWs;
use crypto::swap::ws::CryptoSwapWs;
use deepcoin::spot::ws::DeepcoinSpotWs;
use deepcoin::swap::ws::DeepcoinSwapWs;
use deribit::option::ws::DtOptionWs;
use deribit::spot::ws::DeribitSpotWs;
use deribit::swap::ws::DeribitSwapWs;
use dydx::swap::ws::DydxSwapWs;
use gate::margin::ws::GateMarginWs;
use gate::spot::ws::GateSpotWs;
use gate::swap::ws::GateSwapWs;
use huobi::spot::ws::HuobiSpotWs;
use huobi::swap::ws::HuobiSwapWs;
use hyperliquid::spot::ws::HyperLiquidSpotWs;
use hyperliquid::swap::ws::HyperLiquidSwapWs;
use kraken::spot::ws::KrakenSpotWs;
use kucoin::spot::ws::KucoinSpotWs;
use kucoin::swap::ws::KucoinSwapWs;
use mexc::spot::ws::MexcSpotWs;
use mexc::swap::ws::MexcSwapWs;
use okx::deribit_future::ws::OkxDeribitFutureWs;
use okx::margin::ws::OkxMarginWs;
use okx::spot::ws::OkxSpotWs;
use okx::swap::ws::OkxSwapWs;
use okx::swap_usd::ws::OkxSwapUsdWs;
use phemex::spot::ws::PhemexSpotWs;
use phemex::swap::ws::PhemexSwapWs;
use quant_common::Result;
use quant_common::base::{Exchange, Subscribes, WebSocket, WsHandler, model::ExConfig};
use quant_common::qerror;
use whitebit::spot::ws::WhitebitSpotWs;
use whitebit::swap::ws::WhitebitSwapWs;
use xt::spot::ws::XTSpotWs;
use xt::swap::ws::XTSwapWs;
// use zoomex::spot::ws::ZoomexSpotWs;
// use zoomex::swap::ws::ZoomexSwapWs;

macro_rules! define_exchanges_ws {
    ($($name:ident => $ws:ident),*) => {

        #[allow(clippy::large_enum_variant)]
        pub enum ExchangeWs {
            $(
                $name($ws),
            )*
            NotImplemented(Exchange),
        }

        pub async fn create_public_ws(exchange: Exchange) -> Result<ExchangeWs> {
            match exchange {
                $(
                    Exchange::$name => Ok(ExchangeWs::$name($ws::new(exchange.config()).await)),
                )*
                _ => Err(qerror!("交易所 {:?} 未实现", exchange)),
            }
        }

        pub async fn create_private_ws(config: ExConfig) -> Result<ExchangeWs> {
            match config.exchange {
                $(
                    Exchange::$name => Ok(ExchangeWs::$name($ws::new(config).await)),
                )*
                _ => Err(qerror!("交易所 {:?} 未实现", config.exchange)),
            }
        }

        impl WebSocket for ExchangeWs {
            fn exchange(&self) -> Exchange {
                match self {
                    $(
                        ExchangeWs::$name(ws) => ws.exchange(),
                    )*
                    ExchangeWs::NotImplemented(exchange) => *exchange,
                }
            }

            async fn run<H: WsHandler>(&mut self, subs: Subscribes<H>) -> Result<()> {
                match self {
                    $(
                        ExchangeWs::$name(ws) => ws.run(subs).await,
                    )*
                    ExchangeWs::NotImplemented(exchange) => Err(qerror!("交易所 {:?} 的WebSocket尚未实现", exchange)),
                }
            }
        }
    };
}

define_exchanges_ws!(
    BinanceSwap => BinanceSwapWs,
    BinanceSpot => BinanceSpotWs,
    BinanceMargin => BinanceMarginWs,
    BinanceFuture => BinanceFutureWs,
    GateSwap => GateSwapWs,
    GateSpot => GateSpotWs,
    GateMargin => GateMarginWs,
    CoinexSwap => CoinexSwapWs,
    CoinexSpot => CoinexSpotWs,
    BitgetSwap => BitgetSwapWs,
    BitgetSpot => BitgetSpotWs,
    BitgetMargin => BitgetMarginWs,
    OkxSwap => OkxSwapWs,
    OkxSpot => OkxSpotWs,
    OkxMargin => OkxMarginWs,
    OkxFuture => OkxDeribitFutureWs,
    OkxSwapUsd => OkxSwapUsdWs,
    HuobiSwap => HuobiSwapWs,
    HuobiSpot => HuobiSpotWs,
    KucoinSwap => KucoinSwapWs,
    KucoinSpot => KucoinSpotWs,
    KucoinMargin => KucoinSpotWs,
    BitmexSwap => BitmexSwapWs,
    BitmexSpot => BitmexSpotWs,
    BingxSwap => BingxSwapWs,
    BingxSpot => BingxSpotWs,
    CryptoSwap => CryptoSwapWs,
    CryptoSpot => CryptoSpotWs,
    CoinbaseSpot => CoinbaseSpotWs,
    KrakenSpot => KrakenSpotWs,
    BybitSwap => BybitSwapWs,
    BybitSpot => BybitSpotWs,
    DeepcoinSwap => DeepcoinSwapWs,
    DeepcoinSpot => DeepcoinSpotWs,
    CoinwSwap => CoinwSwapWs,
    CoinwSpot => CoinwSpotWs,
    MexcSpot => MexcSpotWs,
    MexcSwap => MexcSwapWs,
    DydxSwap => DydxSwapWs,
    PhemexSwap => PhemexSwapWs,
    PhemexSpot => PhemexSpotWs,
    HyperLiquidSwap => HyperLiquidSwapWs,
    HyperLiquidSpot => HyperLiquidSpotWs,
    DeribitSwap => DeribitSwapWs,
    DeribitSpot => DeribitSpotWs,
    DeribitOption => DtOptionWs,
    BitmartSwap => BitmartSwapWs,
    BitmartSpot => BitmartSpotWs,
    // ZoomexSwap => ZoomexSwapWs,
    // ZoomexSpot => ZoomexSpotWs,
    WhitebitSwap => WhitebitSwapWs,
    WhitebitSpot => WhitebitSpotWs,
    XTSwap => XTSwapWs,
    XTSpot => XTSpotWs
    // ApexSwap => ApexSwapWs,
);

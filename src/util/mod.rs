pub(crate) mod signature;

use std::sync::atomic::{AtomicU64, Ordering};

use lazy_static::lazy_static;
use log::info;
use quant_common::{time_ms, Result};
use rand::Rng;

lazy_static! {
    static ref CUR_NONCE: AtomicU64 = AtomicU64::new(time_ms() as u64);
}
pub(crate) fn next_nonce() -> u64 {
    let nonce = CUR_NONCE.fetch_add(1, Ordering::Relaxed);
    let now_ms = time_ms() as u64;
    if nonce > now_ms + 1000 {
        info!("nonce progressed too far ahead {nonce} {now_ms}");
    }
    // more than 300 seconds behind
    if nonce + 300000 < now_ms {
        CUR_NONCE.fetch_max(now_ms, Ordering::Relaxed);
    }
    nonce
}

///根据指定的小数位数（WIRE_DECIMALS）对浮点数进行格式化，并去除末尾不必要的零和小数点。
pub(crate) fn float_to_string_for_hashing(x: f64, wire_decimals: u8) -> String {
    let mut x = format!("{:.*}", wire_decimals.into(), x);
    if x.contains(".") {
        while x.ends_with('0') {
            x.pop();
        }
        if x.ends_with('.') {
            x.pop();
        }
    }
    if x == "-0" {
        "0".to_string()
    } else {
        x
    }
}

///用于截断或向上取整浮点数到指定的小数位数。根据 round_up 参数的值，可以选择截断或向上取整。
/// 参数
///float: f64: 需要处理的浮点数。
///decimals: u32: 指定的小数位数。
///round_up: bool: 是否向上取整。
/// 示例
/// 假设输入 float = 123.456，decimals = 2，round_up = false：
/// 计算 pow10: 100.0
/// 乘法和截断: (123.456 * 100.0) as u64 -> 12345
/// 结果: 12345 as f64 / 100.0 -> 123.45
/// 假设输入 float = 123.456，decimals = 2，round_up = true：
/// 计算 pow10: 100.0
/// 乘法和截断: (123.456 * 100.0) as u64 -> 12345
/// 向上取整: 12345 + 1 -> 12346
/// 结果: 12346 as f64 / 100.0 -> 123.46
pub fn truncate_float(float: f64, decimals: u32, round_up: bool) -> f64 {
    let pow10 = 10i64.pow(decimals) as f64;
    let mut float = (float * pow10) as u64;
    if round_up {
        float += 1;
    }
    float as f64 / pow10
}

/// 输入 "123.456":
/// 找到小数点位置为 3。
/// 小数部分为 "456"，长度为 3。
/// 返回 3。
///
/// 输入 "123":
/// 没有找到小数点。
/// 返回 0。
///
/// 输入 "0.00123":
/// 找到小数点位置为 1。
/// 小数部分为 "00123"，长度为 5。
/// 返回 5。
///
/// 输入 "123.000":
/// 找到小数点位置为 3。
/// 小数部分为 "000"，长度为 3。
/// 返回 3。
fn count_decimal_places(num_str: &str) -> (usize, usize) {
    // 使用 find('.') 方法查找字符串中第一个小数点的位置。
    // 如果找到小数点，find 方法返回小数点的索引；如果未找到，则返回 None。
    if let Some(decimal_pos) = num_str.find('.') {
        // 使用切片操作 num_str[decimal_pos + 1..] 获取小数部分的子字符串。
        // 使用 len() 方法计算整数和小数部分各自的长度，并返回该长度。
        (
            num_str[..decimal_pos].len(),
            num_str[decimal_pos + 1..].len(),
        )
    } else {
        (num_str.len(), 0)
    }
    // 如果没有找到小数点（else 分支），则直接返回 字符串的长度和0 ，表示该数字没有小数部分。
}
#[derive(Debug)]
pub struct ValidationError {
    message: String,
}

impl std::fmt::Display for ValidationError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "{}", self.message)
    }
}

pub(crate) fn validate_price(
    price: f64,
    sz_decimals: usize,
    max_decimals: usize,
    validate_decimal_places: usize,
) -> Result<f64, ValidationError> {
    let price_str = format!("{}", price);
    //计算总的位数(小数位和整数位的个数)
    let significant_figures = count_significant_figures(&price_str);
    //计算整数的个数和小数位的个数
    let decimal_places = count_decimal_places(&price_str);
    //获取浮点数的小数部分,返回 0.0，则表示价格没有小数部分，即价格是整数。
    if price.fract() == 0.0 {
        return Ok(price);
    }
    let mut adjusted_price = price;

    //不是整数，如果整数的部分大于有效位数，则按有效位数进行截断或舍入(截断小数部分，只保留整数部分)。
    if decimal_places.0 >= validate_decimal_places {
        //if validate_decimal_places == 5 =>92480.7 => 92480
        adjusted_price = truncate_float(adjusted_price, 0, false);
    } else if significant_figures > validate_decimal_places {
        //整数部分全保留，小数部分多余的直接截断
        if get_integer_part(adjusted_price) != 0 {
            //if validate_decimal_places == 5 =>2126.99 =>2126.9
            adjusted_price = truncate_float(
                adjusted_price,
                (validate_decimal_places - decimal_places.0) as u32,
                false,
            );
        } else {
            //如果整数部分是
            // 0.012345 是有效的
            // 如果整数部分是 0.112345是无效的，需将它变得有效:0.11234
            let i = count_leading_zeros_in_decimal_part(adjusted_price);
            adjusted_price =
                truncate_float(adjusted_price, (validate_decimal_places + i) as u32, false);
        }
    }
    let price_str = format!("{}", adjusted_price);
    let decimal_places = count_decimal_places(&price_str); // 保留此行
                                                           // Check decimal places
    if decimal_places.1 > max_decimals - sz_decimals {
        // Truncate or round the price to the allowed decimal places
        adjusted_price = truncate_float(adjusted_price, (max_decimals - sz_decimals) as u32, false);
    }
    Ok(adjusted_price)
}

//统计浮点数中小数点后面的前导0的个数
pub fn count_leading_zeros_in_decimal_part(num: f64) -> usize {
    // 将浮点数转换为字符串
    let num_str = format!("{}", num);

    // 查找小数点的位置
    if let Some(decimal_pos) = num_str.find('.') {
        // 获取小数部分
        let decimal_part = &num_str[decimal_pos + 1..];

        // 统计前导零的数量
        let leading_zeros = decimal_part.chars().take_while(|&c| c == '0').count();

        leading_zeros
    } else {
        // 如果没有小数点，返回 0
        0
    }
}
//获取浮点数的整数部分
pub fn get_integer_part(x: f64) -> i64 {
    if x >= 0.0 {
        x.floor() as i64
    } else {
        x.ceil() as i64
    }
}

/// 示例
/// 输入 size = 123.456, sz_decimals = 2:
/// 因子为 100.0。
///
/// 计算 123.456 * 100.0 = 12345.6。
/// 四舍五入 12345.6 得到 12346.0。
/// 结果为 12346.0 / 100.0 = 123.46。
///
/// 输入 size = 123.454, sz_decimals = 2:
/// 因子为 100.0。
///
/// 计算 123.454 * 100.0 = 12345.4。
/// 四舍五入 12345.4 得到 12345.0。
// 结果为 12345.0 / 100.0 = 123.45。
pub fn round_size(size: f64, sz_decimals: usize) -> f64 {
    let factor = 10f64.powi(sz_decimals as i32);
    (size * factor).round() / factor
}

/// 输入 "123.450":
///
/// 去除末尾的零和小数点后变为 "123.45"
///
/// 去除小数点后变为 "12345"
///
/// 统计数字字符得到 5
///
/// 输入 "0.001230":
///
/// 去除末尾的零和小数点后变为 "0.00123"
///
/// 去除小数点后变为 "000123"
///
/// 统计数字字符得到 6 （注意这里包括前导零）
pub fn count_significant_figures(num_str: &str) -> usize {
    let num_str = num_str
        .trim_end_matches('0') //去除字符串末尾的所有零。
        .trim_end_matches('.'); //去除字符串末尾的小数点（如果存在）。 这一步确保了不会将多余的零和小数点计入有效数字。
                                //将字符串中的小数点去掉。这样可以方便后续统计数字字符。
    let num_str = num_str.replace(".", "");
    // 使用 chars() 将字符串转换为字符迭代器。
    // 使用 filter(|c| c.is_digit(10)) 筛选出所有的十进制数字字符。
    // 使用 count() 统计这些数字字符的数量。
    num_str.chars().filter(|c| c.is_ascii_digit()).count()
}
// pub(crate) fn generate_random_key() -> Result<[u8; 32]> {
//     let mut arr = [0u8; 32];
//     let _ = thread_rng()
//         .try_fill(&mut arr[..])
//         .map_err(|e| qerror!("Failed to generate random key: {}", e));
//     Ok(arr)
// }

// 生成 128 位十六进制字符串
pub fn generate_128bit_hex() -> String {
    // 创建随机数生成器
    let mut rng = rand::thread_rng();

    // 生成 16 个随机字节 (128 位 = 16 字节)
    let bytes: [u8; 16] = rng.gen();

    // 将字节转换为十六进制字符串
    let hex_str = bytes
        .iter()
        .map(|b| format!("{:02x}", b)) // 每个字节转为 2 个十六进制字符
        .collect::<Vec<String>>()
        .join("");

    // 添加 "0x" 前缀
    format!("0x{}", hex_str)
}

// 验证生成的字符串是否符合要求
pub fn validate_128bit_hex(s: &str) -> bool {
    s.starts_with("0x") &&                  // 以 "0x" 开头
    s.len() == 34 &&                       // 总长度 = 2 ("0x") + 32 (十六进制字符)
    s[2..].chars().all(|c| c.is_ascii_hexdigit()) // 剩余部分是十六进制字符
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn float_to_string_for_hashing_test() {
        assert_eq!(validate_price(0.00001234, 2, 8, 5).unwrap(), 0.00001);
        assert_eq!(float_to_string_for_hashing(0., 8), "0".to_string());
        assert_eq!(float_to_string_for_hashing(-0., 8), "0".to_string());
        assert_eq!(float_to_string_for_hashing(-0.0000, 8), "0".to_string());
        assert_eq!(
            float_to_string_for_hashing(0.00076000, 8),
            "0.00076".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(0.00000001, 8),
            "0.00000001".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(0.12345678, 8),
            "0.12345678".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(87654321.12345678, 8),
            "87654321.12345678".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(987654321.00000000, 8),
            "987654321".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(87654321.1234, 8),
            "87654321.1234".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(0.000760, 8),
            "0.00076".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(0.00076, 8),
            "0.00076".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(987654321.0, 8),
            "987654321".to_string()
        );
        assert_eq!(
            float_to_string_for_hashing(987654321., 8),
            "987654321".to_string()
        );
    }
}

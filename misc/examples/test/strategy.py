"""
strategy.py

测试策略模块 - 继承自BaseStrategy
只在start函数中查询accountid为0的余额
"""
import copy
import time
import traderv2 # type: ignore
import base_strategy
import requests

class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader: traderv2.TraderV2):
        """
        初始化策略对象。

        Args:
            cex_configs (list): CEX 交易所配置列表。
            dex_configs (list): DEX 交易所配置列表。
            config (dict): 策略配置。
            trader (trader.Trader): Rust Trader 对象。
        """
        self.cex_configs = cex_configs
        self.has_account = len(cex_configs) > 0
        self.dex_configs = dex_configs
        self.trader = trader
        self.config = config
        self.symbol = "BTC_USDT"

        self.last_bbo_trigger = 0
        self.pending_cid = ""
        self.pending_order_time = {}

        # 默认配置
        if not config:
            self.config = {
                "send_to_web": False,
            }

    def name(self):
        """
        返回策略的名称。

        Returns:
            str: 策略名称。
        """
        return "测试策略"

    def start(self):
        """
        策略启动函数 - 仅查询accountid为0的余额
        """
        try:
            self.trader.log("启动测试策略", web=self.config.get("send_to_web", True))

            instrument = self.trader.get_instrument(0, 'PEPE_USDT')
            self.trader.log(f"获取合约信息: {instrument}", web=self.config.get("send_to_web", True))

            # 获取历史资金费率
            funding_rate_history = self.trader.get_funding_rate_history(0, symbol="BTC_USDT", since_secs=None, limit=100)
            self.trader.log(f"历史资金费率: {funding_rate_history}", level="DEBUG", color="blue")

            # # GET请求测试
            # url = "https://api.gateio.ws/api/v4/futures/usdt/contracts/BTC_USDT"
            # res = self.trader.http_request(url, "GET", None, None)
            # self.trader.log(f"GET请求结果: {res}", web=self.config.get("send_to_web", True))

            # # POST请求测试 - 使用JSONPlaceholder公共测试API
            # post_url = "https://jsonplaceholder.typicode.com/posts"
            # post_data = {
            #     "title": "测试POST请求",
            #     "body": "这是一个测试POST请求的内容",
            #     "userId": 1
            # }
            # headers = {
            #     "Content-Type": "application/json"
            # }
            # # 直接传递Python字典，Rust端会自动序列化为JSON
            # post_res = self.trader.http_request(post_url, "POST", post_data, headers)
            # self.trader.log(f"POST请求结果: {post_res}", web=self.config.get("send_to_web", True))

            # # 另一个POST测试 - httpbin.org echo服务
            # echo_url = "https://httpbin.org/post"
            # echo_data = {
            #     "strategy_name": "test_strategy",
            #     "timestamp": "2024-01-01T00:00:00Z",
            #     "test_data": {"key1": "value1", "key2": "value2"}
            # }
            # # 直接传递Python字典，Rust端会自动序列化为JSON
            # echo_res = self.trader.http_request(echo_url, "POST", echo_data, headers)
            # self.trader.log(f"Echo POST请求结果: {echo_res}", web=self.config.get("send_to_web", True))

            # balances = self.trader.get_balances(0)
            # self.trader.log(f"账户ID {0} 的余额信息: {balances}", web=self.config.get("send_to_web", True))

            # # 查询accountid为0的余额
            # if self.has_account:
            #     account_id = 0
            #     cmd =     {
            #         "account_id": 0,
            #         "method": "UsdtBalance",
            #         "sync": True
            #     }

            #     result = self.trader.publish(cmd)

            #     # 记录余额信息到日志
            #     self.trader.log(f"账户ID {account_id} 的余额信息: {result}", web=self.config.get("send_to_web", True))
            # else:
            #     self.trader.log("没有配置账户信息，无法查询余额", web=self.config.get("send_to_web", True))

            # order = {
            #     "cid": traderv2.create_cid(self.cex_configs[0]["exchange"]),
            #     "symbol": self.symbol,
            #     "order_type": "Market",
            #     "side": "Buy",
            #     "pos_side": "Long",
            #     "price": 100.,
            #     "amount": 0.781,
            #     "time_in_force": "GTC"
            # }
            # params = {
            #     "is_dual_side": False,
            #     "market_order_mode": "Normal"
            # }

            # res = self.trader.place_order(0, order, params, sync=True)
            # self.trader.log(f"下单结果: {res}", web=self.config.get("send_to_web", True))

            # orders = []
            # for i in range(5):
            #     order_copy = copy.deepcopy(order)
            #     order_copy['cid'] = traderv2.create_cid(self.cex_configs[0]["exchange"])
            #     orders.append(order_copy)
            # self.trader.log(f"批量下单订单: {orders}", web=self.config.get("send_to_web", True))
            # res = self.trader.batch_place_order(0, orders, params, sync=False)
            # self.trader.log(f"批量下单结果: {res}", web=self.config.get("send_to_web", True))

            # # 撤单
            # res = self.trader.cancel_order(0, res["Ok"], self.symbol)
            # self.trader.log(f"撤单结果: {res}", web=self.config.get("send_to_web", True))
        except Exception as e:
            self.trader.log(f"测试策略启动错误: {str(e)}", web=self.config.get("send_to_web", True))

    def subscribes(self):
        """
        返回策略订阅的事件列表。
        测试策略不需要订阅任何数据

        Returns:
            list: 空列表，表示不订阅任何数据
        """
        return [
            {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [
                        # {
                        #     "OrderAndFill": [self.symbol]
                        # },
                        # {
                        #     "Order": [self.symbol]
                        # },
                        # {
                        #     "Bbo": [self.symbol]
                        # },
                        # {
                        #     "FundingFee": [self.symbol]
                        # },
                        {
                            "Kline": {
                                "symbols": [self.symbol],
                                "interval": "1m"
                            }
                        }
                    ]
                }
            },
            # {
            #     "sub": {
            #         "SubscribeTimer": {
            #             "update_interval": {
            #                 "secs": 1,
            #                 "nanos": 0
            #             },
            #             "name": "post_order"  # 每10秒检查一次订单状态
            #         }
            #     }
            # },
            # {
            #     "account_id": 0,
            #     "sub": {
            #         "SubscribeWs": [
            #             {
            #                 "Bbo": ["BTC_USDT"]
            #             }
            #         ]
            #     }
            # },
        ]

    def on_timer_subscribe(self, timer_name):
        """
        处理定时器事件

        根据不同的定时器名称执行相应的定时任务

        参数:
            timer_name: 定时器名称
        """
        self.trader.log(f"定时器事件: {timer_name}", web=self.config.get("send_to_web", True))

        # cid = traderv2.create_cid(self.cex_configs[0]["exchange"])
        # if self.pending_cid != "":
        #     self.trader.log(f"pending_cid: {self.pending_cid}", level="WARN", web=self.config.get("send_to_web", True))
        # self.pending_cid = cid
        # order = {
        #     "cid": cid,
        #     "symbol": self.symbol,
        #     "order_type": "Limit",
        #     "side": "Buy",
        #     "pos_side": "Long",
        #     "price": 64931.4,
        #     "amount": 0.0007,
        #     "time_in_force": "PostOnly"
        # }
        # params = {
        #     "is_dual_side": False,
        #     # "market_order_mode": "Normal"
        # }

        # cmd = self.trader.place_order(0, order, params, sync=False, generate=True)['Ok']
        # self.pending_order_time[cid] = time.time() * 1000
        # return {
        #     'cmds': [cmd]
        # }

    def on_order_submitted(self, account_id, order_id_result, order):
        """
        订单提交成功时触发的方法。

        Args:
            account_id (str): 账户 ID。

            order_result (str): 包含订单 ID 的 Result 可能为 Err。
            order (dict): 订单信息。
        """
        # id = order_id_result.get("Ok")
        cid = order.get("cid")
        if cid in self.pending_order_time:
            # 计算延迟
            delay = time.time() * 1000 - self.pending_order_time[cid]
            if delay > 500:
                self.trader.log(f"{cid}订单提交延迟: {delay:.2f}ms", level="WARN", web=self.config.get("send_to_web", True))
            del self.pending_order_time[cid]
        self.pending_cid = ""
        self.trader.log(f"订单提交结果: {order_id_result}， {order}", web=self.config.get("send_to_web", True))
        if id:
            # 撤单
            cmd = self.trader.cancel_order(0, self.symbol, order_id=None, cid=cid, sync=False, generate=True)['Ok']

            cmds = [cmd]
            return {
                'cmds': cmds
            }

    def on_order_canceled(self, account_id, result, id, symbol):
        """
        订单取消成功时触发的方法。

        Args:
            account_id (str): 账户 ID。
            order_id (str): 订单 ID。
            id (str): 撤单时传入的订单id/订单cid。
            symbol(str): 撤单时传入的symbol。
        """
        self.trader.log(f"订单取消结果: {result}, {id}, {symbol}", web=self.config.get("send_to_web", True))

    def on_funding_fee(self, account_id, context, funding_fee):
        """
        资金费结算时触发的方法。
        """
        self.trader.log(f"资金费结算: {funding_fee}", web=self.config.get("send_to_web", True))

    def on_stop(self):
        """
        策略停止时的回调函数
        """
        self.trader.log("测试策略已停止", web=self.config.get("send_to_web", True))

    def on_order_and_fill(self, account_id, order):
        """
        订单/用户私有成交更新时触发的方法, 订单频道和用户成交频道哪个快推哪个。

        Args:
            account_id (str): 账户 ID。
            order: 订单对象。
        """
        self.trader.log(f"订单/用户私有成交更新: {order}", level="INFO", color="blue")

    def on_order(self, account_id, order):
        """
        订单回调函数
        """
        now = time.time() * 1000
        self.trader.log(f"订单回调: {order} {now}", web=self.config.get("send_to_web", True))

    def on_bbo(self, exchange, bbo):
        """
        买卖盘回调函数
        """
        pass

    def on_position(self, account_id, positions):
        """
        持仓回调函数
        """
        self.trader.log(f"持仓回调: {positions}", web=self.config.get("send_to_web", True))

    def on_kline(self, exchange, kline):
        """
        行情K线更新时触发的方法。

        Args:
            exchange (str): 交易所名称。
            kline: 行情K线对象。
        """
        self.trader.log(f"行情K线更新: {kline}", web=self.config.get("send_to_web", True))

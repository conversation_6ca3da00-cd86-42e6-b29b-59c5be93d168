strategy_path = "/home/<USER>/codes/test_strategy/strategy.py"

# 交易所配置
[[exchanges]]
exchange = "BinanceSwap"
is_colo = false
is_testnet = false
is_unified = false
key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
passphrase = ""
rebate_rate = 0
secret = "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT\n-----END PRIVATE KEY-----"
use_ws_api = false

# 日志配置
[log]
level = "debug"

# 执行引擎配置
[execution_engine]
typ = "Stand"
task_count = 500

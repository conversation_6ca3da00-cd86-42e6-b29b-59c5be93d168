# HyperliquidSwap func_name_ext 扩展参数说明、
>  ```rust
>   /// 参数params extra
    /// 
    /// key1:"is_cross"
    /// value1:true or false if updating cross-leverage,
    /// 当设置杠杆时，is_cross 的值取决于保证金模式（margin mode）：
    /// 如果使用逐仓模式（Isolated Margin），is_cross 为 false
    /// 如果使用全仓模式（Cross Margin），is_cross 为 true
    async fn set_leverage_ext(&self, params: SetLeverageParams) -> quant_common::Result<()>
> ```
[package]
name = "bybit"
version = "0.1.0"
edition = "2024"

[dependencies]
quant_common = {git = "ssh://***************:8022/newquant/quant_common.git", branch = "dev"}

serde = { version = "1.0", features = ["derive"] }
sonic-rs = "0.3"
reqwest = "0.12.9"
rustc-hash = "2.0.0"
tracing = "0.1.40"
url = "2.5"
serde_urlencoded = "0.7.1"
tokio = "1.41.1"
tracing-subscriber = "0.3.18"
futures-util = "0.3.31"
hmac = "0.12.1"
sha2 = "0.10.8"
tokio-tungstenite = "0.24.0"
hex = "0.4.3"
uuid = { version = "1.3.0", features = ["v4"] }
once_cell = "1.19"
serde_plain = "1.0"

[dev-dependencies]
exchange-validator = { git = "ssh://***************:8022/exchanges/exchange-validator.git", branch = "main" }

[package]
name = "okx"
version = "0.1.0"
edition = "2021"

[dependencies]
quant_common = {git = "ssh://***************:8022/newquant/quant_common.git", branch = "main"}


bincode = "2.0.0-rc.3"
futures-util = "0.3"
log = "0.4"
reqwest = { version = "0.12", default-features = false, features = [
    "rustls-tls",
    "json",
] }
rustc-hash = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_plain = "1.0"
serde_urlencoded = "0.7"
sonic-rs = { version = "0.3", default-features = false }
tokio = { version = "1.40", default-features = false, features = [
    "rt",
    "macros",
] }
tokio-tungstenite = { version = "0.24", features = ["native-tls-vendored"] }
toml = "0.8"
tracing = "0.1"
tracing-subscriber = { version = "0.3.18", default-features = false, features = [
    "time",
    "fmt",
    "std",
    "tracing-log",
    "env-filter",
    "ansi",
    "json",
] }
url = "2.5"

hmac = "0.12"
once_cell = "1.19"
sha2 = "0.10.8"
time = { version = "0.3.34", features = ["macros"] }
flate2 = { version = "1.0", features = ["rust_backend"] }
hex = "0.4"
chrono = { version = "0.4", features = ["serde"] }
base64 = "0.22.1"
fastwebsockets = "0.8.0"
async-channel = "2.3.1"

[dev-dependencies]
exchange-validator = { git = "ssh://***************:8022/exchanges/exchange-validator.git", branch = "main" }

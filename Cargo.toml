[package]
name = "gate"
version = "0.1.0"
edition = "2024"

[dependencies]
quant_common = { git = "ssh://***************:8022/newquant/quant_common.git", branch = "dev" }

log = "0.4"
serde = { version = "1.0", features = ["derive"] }
hmac = "0.12"
once_cell = "1.19"
sha2 = "0.10.8"
const-str = { version = "0.5", features = ["proc"] }
rustc-hash = "2.0"
serde_plain = "1.0"
reqwest = { version = "0.12", default-features = false, features = [
    "rustls-tls",
    "json",
] }
sonic-rs = { version = "0.3", default-features = false }
url = "2.5"
serde_urlencoded = "0.7"
tokio = { version = "1.40", default-features = false, features = [
    "rt",
    "macros",
] }
futures-util = "0.3"
tokio-tungstenite = { version = "0.24", features = ["native-tls-vendored"] }
serde_json = "1.0"
bytes = "1.0"
async-channel = "2.3.1"
fastwebsockets = "0.8.0"
hex = "0.4"

[dev-dependencies]
tracing-subscriber = "*"
toml = "0.8.19"
exchange-validator = { git = "ssh://***************:8022/exchanges/exchange-validator.git", branch = "dev" }

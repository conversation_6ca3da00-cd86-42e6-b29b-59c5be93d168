[package]
name = "hyperliquid"
version = "0.1.0"
edition = "2021"

[dependencies]
quant_common = { git = "ssh://***************:8022/newquant/quant_common.git", branch = "main" }
async-channel = "2.3.1"
base64 = "0.22.1"
bincode = "2.0.0-rc.3"
chrono = { version = "0.4", features = ["serde"] }
ed25519-dalek = "2.1.1"
futures-util = "0.3"
hex = "0.4"
hmac = "0.12"
log = "0.4"
once_cell = "1.19"
reqwest = { version = "0.12", default-features = false, features = [
    "rustls-tls",
    "json",
] }
rustc-hash = "2.0"
sha2 = "0.10.8"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_plain = "1.0"
serde_urlencoded = "0.7"
sonic-rs = { version = "0.3", default-features = false }
tokio = { version = "1.40", default-features = false, features = [
    "rt",
    "macros",
] }
tokio-tungstenite = { version = "0.24", features = ["native-tls-vendored"] }
toml = "0.8"
tracing = "0.1"
tracing-subscriber = { version = "0.3.18", default-features = false, features = [
    "time",
    "fmt",
    "std",
    "tracing-log",
    "env-filter",
    "ansi",
    "json",
] }
time = { version = "0.3.34", features = ["macros"] }
flate2 = { version = "1.0", features = ["rust_backend"] }
url = "2.5"
fastwebsockets = "0.8.0"
rand = { version = "0.8", default-features = false }
lazy_static = "1.5.0"
ethers = {version = "2.0.14", features = ["eip712", "abigen"]}
rmp-serde = "1.3.0"
rayon = "1.10.0"

[dev-dependencies]
exchange-validator = { git = "ssh://***************:8022/exchanges/exchange-validator.git", branch = "main" }


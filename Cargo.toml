[package]
edition = "2024"
name = "quant_api"
version = "0.1.0"

[workspace]
members = ["."]

[dependencies]
quant_common = { git = "ssh://***************:8022/newquant/quant_common.git", branch = "dev" }

binance = { git = "ssh://***************:8022/exchanges/binance.git", branch = "dev" }
bingx = { git = "ssh://***************:8022/exchanges/bingx.git", branch = "dev" }
bitget = { git = "ssh://***************:8022/exchanges/bitget.git", branch = "dev" }
bitmex = { git = "ssh://***************:8022/exchanges/bitmex.git", branch = "dev" }
bybit = { git = "ssh://***************:8022/exchanges/bybit.git", branch = "dev" }
coinbase = { git = "ssh://***************:8022/exchanges/coinbase.git", branch = "dev" }
coinex = { git = "ssh://***************:8022/exchanges/coinex.git", branch = "dev" }
crypto = { git = "ssh://***************:8022/exchanges/crypto.git", branch = "dev" }
# gate = { git = "ssh://***************:8022/exchanges/gate.git", branch = "dev" }
gate = { path = "../gate" }
huobi = { git = "ssh://***************:8022/exchanges/huobi.git", branch = "dev" }
kraken = { git = "ssh://***************:8022/exchanges/kraken.git", branch = "dev" }
kucoin = { git = "ssh://***************:8022/exchanges/kucoin.git", branch = "dev" }
okx = { git = "ssh://***************:8022/exchanges/okx.git", branch = "dev" }
deepcoin = { git = "ssh://***************:8022/exchanges/deepcoin.git", branch = "dev" }
coinw = { git = "ssh://***************:8022/exchanges/coinw.git", branch = "dev" }
# apex = { git = "ssh://***************:8022/exchanges/apex.git", branch = "dev" }
mexc = { git = "ssh://***************:8022/exchanges/mexc.git", branch = "dev" }
hyperliquid = { git = "ssh://***************:8022/exchanges/hyperliquid.git", branch = "dev" }
dydx = { git = "ssh://***************:8022/exchanges/dydx.git", branch = "dev" }
phemex = { git = "ssh://***************:8022/exchanges/phmex.git", branch = "dev" }
deribit = { git = "ssh://***************:8022/exchanges/deribit.git", branch = "dev" }
# zoomex = { git = "ssh://***************:8022/exchanges/zoomex.git", branch = "dev" }
bitmart = { git = "ssh://***************:8022/exchanges/bitmart.git", branch = "dev" }
whitebit = { git = "ssh://***************:8022/exchanges/whitebit.git", branch = "dev" }
xt = { git = "ssh://***************:8022/exchanges/xt.git", branch = "dev" }

async-trait = "0.1.77"
sonic-rs = { version = "0.3", default-features = false }
tracing = "0.1"
async-channel = "2.3"
once_cell = "1.21.3"
strum = { version = "0.26", default-features = false }
dashmap = "6.1.0"
papaya = "0.2.1"
tokio = { version = "1.40", features = ["full"] }
futures = "0.3.31"

[dev-dependencies]
tokio = { version = "1.40", default-features = false, features = [
    "rt",
    "macros",
] }
tracing = "0.1"
tracing-subscriber = { version = "0.3.18", default-features = false, features = [
    "time",
    "fmt",
    "std",
    "tracing-log",
    "env-filter",
    "ansi",
    "json",
] }
